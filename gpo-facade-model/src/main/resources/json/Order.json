{"systemId": "gpo/soda", "order": {"id": {"orderId": "", "rootOrderNo": "", "parentOrderNo": "", "orderNo": "", "external": {"externalOrderNo": ""}}, "relationship": {"parent": {"trfList": [{"refSystemId": 0, "trfNo": ""}]}, "children": {"customerList": [{"customerInstanceId": ""}], "product": {"productInstanceId": ""}, "sampleList": [{"sampleInstanceId": "", "sampleNo": ""}], "testSampleList": [{"testSampleInstanceId": "", "testSampleNo": ""}], "testLineList": [{"testLineInstanceId": "", "testItemNo": ""}], "testMatrixList": [{"testMatrixId": ""}], "reportList": [{"reportId": "", "reportNo": ""}], "quotationList": [{"quotationId": "", "quotationNo": ""}], "invoiceList": [{"invoiceId": "", "invoiceNo": ""}]}}, "lab": {"labId": 46, "labCode": "GZ SL", "locationId": 2, "locationCode": "GZ", "buId": 1, "buCode": "SL"}, "header": {"orderStatus": 0, "serviceType": 0, "orderType": "", "operationMode": 0, "productCategory": "", "productSubCategory": "", "groupId": 0, "tat": 0, "orderExpectDueDate": "2023-09-01 09:05:15", "cuttingExpectDueDate": "2023-09-01 09:05:15", "jobExpectDueDate": "2023-09-01 09:05:15", "subcontractExpectDueDate": "2023-09-01 09:05:15", "reportExpectDueDate": "2023-09-01 09:05:15"}, "payment": {"paymentStatus": 0, "currency": "", "totalAmount": 0.0, "mainCurrencyTotalAmount": 0.0}, "contactPersonList": [{"contactUsage": "cs/or/cr", "contactName": "", "contactEmail": "", "contactPhone": "", "contactTelephone": "", "contactMobile": "", "contactFAX": "", "regionAccount": "", "responsibleTeamCode": ""}], "customerList": [{"customerInstanceId": "", "customerUsage": 0, "bossNo": 0, "customerGroupCode": "", "customerName": "", "customerAddress": "", "languageList": [{"languageId": 0, "customerName": "", "customerAddress": ""}], "customerContactList": [{"customerContactId": "", "customerContactAddressId": "", "bossContactId": 0, "bossSiteUseId": 0, "contactName": "", "contactTelephone": "", "contactMobile": "", "contactFAX": "", "contactEmail": ""}]}], "product": {"productInstanceId": "", "templateId": "", "productAttrList": [{"seq": 0, "labelName": "", "customerLabel": "", "dataType": "", "labelCode": "", "value": "", "displayInReport": "", "languageList": [{"languageId": 0, "labelName": "", "customerLabel": "", "value": ""}]}]}, "sampleList": [{"sampleInstanceId": "", "templateId": "", "sampleNo": "", "externalSampleNo": "", "sampleAttrList": [{"seq": 0, "labelName": "", "customerLabel": "", "dataType": "", "labelCode": "", "value": "", "displayInReport": "", "languageList": [{"languageId": 0, "labelName": "", "customerLabel": "", "value": ""}]}]}], "serviceRequirement": {"report": {"reportLanguage": 1, "reportHeader": "LUCKSON SHOES COMPANY LIMITED", "reportAddress": "18 CARNARVON ROAD,TST KOWLOON,Hong Kong", "accreditation": "", "needConclusion": 0, "needDraft": 0, "needPhoto": 0, "splitReportBy": "Sample/TestLine/Matrix", "languageList": [{"lanugaeId": "", "reportHeader": "", "reportAddress": ""}], "softcopy": {"required": null, "deliveryTo": "1", "failedDeliveredTo": "", "deliveryCc": null, "deliveryOthers": null, "deliveryWay": null}, "hardcopy": {"required": null, "deliveryTo": "1", "deliveryCc": null, "deliveryOthers": null, "deliveryWay": null}}, "sample": {"sampleSaveDuration": "", "liquid": "", "returnResidueSample": {"required": null, "deliveryTo": "1", "deliveryCc": null, "deliveryOthers": null, "deliveryWay": null}, "returnTestSample": {"required": null, "deliveryTo": "1", "deliveryCc": null, "deliveryOthers": null, "deliveryWay": null}}, "invoice": {"invoiceType": 1, "invoiceTitle": "", "needProformaInvoice": "", "taxNo": "", "registerAddr": "", "registerPhone": "", "bankName": "", "bankNumber": "", "invoice": {"required": null, "deliveryTo": "1", "deliveryCc": null, "deliveryOthers": null, "deliveryWay": null}}, "otherRequestRemark": ""}, "flags": {"selfTestFlag": 0, "toDMFlag": 0}, "others": {"pending": {"pendingFlag": 0, "pendingType": 0, "pendingRemark": ""}, "orderRemark": ""}, "processList": [{"nodePoint": "create/sample-confirm/lab-in/lab-out/softcopy-delivery", "completedDateTime": "2023-10-08 10:00", "operator": "mike-miao", "remark": "confirmOrder"}], "attachmentList": [{"fileName": "", "fileType": 0, "objectType": 0, "objectId": "", "objectData": "datasheet", "cloudId": "", "filePath": "", "languageId": "", "description": "", "languageList": {"languageId": "", "description": ""}}]}, "testSampleList": [{"id": {"testSampleInstanceId": "", "testSampleNo": "", "external": {"externalSampleId": "", "externalSampleNo": ""}}, "relationship": {"parent": {"order": {"orderId": "", "orderNo": ""}}, "parallel": {"testLineList": [{"testLineInstanceId": "", "testItemNo": ""}]}}, "header": {"parentTestSampleId": "", "testSampleGroupList": [{"testSampleInstanceId": "", "mainSampleFlag": 0}], "testSampleType": 0, "category": "phy/chem", "testSampleSeq": 0}, "materialAttr": {"materialDescription": "", "materialOtherSampleInfo": "", "materialEndUse": "", "applicableFlag": 0, "materialRemark": "", "materialName": "", "materialColor": "", "materialComposition": "", "extFields": {}}, "limitGroupList": [{"limitGroupId": "", "limitGroupName": "", "limitGroupTypeId": "", "limitGroupTypeName": "", "ppBaseId": "", "languageList": [{"languageId": 1, "limitGroupName": "", "limitGroupTypeName": ""}]}], "conclusion": {"conclusionCode": "", "customerConclusion": "", "reviewConclusion": "", "conclusionRemark": ""}, "attachmentList": [{"fileName": "", "fileType": 0, "objectType": 0, "objectId": "", "objectData": "datasheet", "cloudId": "", "filePath": "", "languageId": "", "description": "", "languageList": {"languageId": "", "description": ""}}]}], "testLineList": [{"id": {"testLineInstanceId": "", "testLineBaseId": 0, "testLineId": 0, "testLineVersionId": 0, "testItemNo": "", "external": {"testLineMappingId": 0, "externalId": "", "externalCode": ""}}, "relationship": {"parent": {"order": {"orderId": "", "orderNo": ""}, "pp": {"ppTlRelId": ""}}, "children": {"testConditionGroupList": [{"testConditionGroupId": ""}], "analyteList": [{"analyteInstanceId": ""}]}, "parallel": {"testSampleList": [{"testSampleInstanceId": "", "testSampleNo": ""}]}}, "ppTestLineRelList": [{"id": {"ppTlRelId": "", "ppArtifactRelId": 0, "rootPPBaseId": 0, "ppBaseId": 0, "ppNo": 0, "ppVersionId": 0}, "header": {"ppName": "", "ppNotes": "", "sectionId": 0, "sectionLevel": 0, "sectionName": "", "aid": 0}, "languageList": [{"languageId": 0, "ppName": "", "ppNotes": ""}]}], "header": {"testLineType": 0, "evaluationAlias": "", "evaluationName": "", "testLineStatus": 0, "testLineSeq": 0, "labTeam": "", "productLineAbbr": "", "testRequest": "", "testLineRemark": ""}, "labSectionList": [{"labSectionId": 0, "labSectionBaseId": 0}], "citation": {"citationId": 0, "citationType": 0, "citationVersionId": 0, "citationSectionId": 0, "citationSectionName": "", "citationName": "", "citationFullName": "", "languageList": [{"languageId": 0, "citationSectionName": "", "citationName": "", "citationFullName": ""}]}, "wi": {"wiForCS": "", "wiForSample": "", "wiForTest": ""}, "analyteList": [{"id": {"analyteInstanceId": "", "analyteBaseId": 0, "analyteId": 0}, "header": {"analyteName": "", "analyteSeq": 0, "unitBaseId": 0, "unitId": 0, "reportUnit": "", "casNo": ""}, "languageList": [{"languageId": 0, "analyteName": "", "reportUnit": ""}]}], "conclusion": {"conclusionCode": "", "customerConclusion": "", "reviewConclusion": "", "conclusionRemark": ""}, "languageList": [{"languageId": 0, "evaluationAlias": "", "evaluationName": "", "testRequest": ""}]}], "conditionGroupList": [{"id": {"conditionGroupId": ""}, "relationship": {"parent": {"testLineInstanceId": ""}, "children": {"testMatrixList": [{"testMatrixId": ""}]}}, "header": {"conditionGroupName": "", "combinedConditionDescription": "", "requirement": ""}, "languageList": [{"languageId": 0, "conditionGroupName": "", "combinedConditionDescription": ""}]}], "testMatrixList": [{"id": {"testMatrixId": "虚拟TL PPID", "testMatrixNo": "", "external": {"externalTestMatrixId": ""}}, "relationship": {"parent": {"testLineList": {"testLineInstanceId": "", "testItemNo": ""}, "testSampleList": {"testSampleInstanceId": "", "testSampleNo": "A", "externalTestSampleNo": ""}, "testConditionGroupList": {"testConditionGroupId": ""}}, "children": {"applicationFactorIdList": [], "conditionList": [], "positionList": [], "specimenList": [], "testDataList": []}}, "header": {"testMatrixGroupId": 0}, "productAttributeList": [{"productAttributeId": "", "productAttributeName": "", "limitGroupId": "", "languageList": [{"languageId": 1, "productAttributeName": ""}]}], "conditionList": [{"id": {"conditionInstanceId": "", "conditionId": 0, "conditionTypeId": 0}, "header": {"conditionType": 0, "conditionTypeName": "", "conditionName": "", "conditionDesc": "", "conditionSeq": 0}, "languageList": [{"languageId": 0, "conditionTypeName": "", "conditionName": "", "conditionDesc": ""}]}], "specimenList": [{"id": {"specimenInstanceId": "", "specimenNo": 0}, "header": {"specimenType": 0, "specimenDescription": ""}, "languageList": [{"languageId": 0, "specimenDescription": ""}]}], "positionList": [{"id": {"positionInstanceId": ""}, "header": {"usageTypeId": 0, "usageTypeName": "", "positionName": "", "positionDescription": "", "positionSeq": 0}, "languageList": [{"languageId": 0, "positionName": "", "positionDescription": ""}]}], "attachmentList": [{"fileName": "", "fileType": 0, "objectType": 0, "objectId": "", "objectData": "datasheet", "cloudId": "", "filePath": "", "languageId": "", "description": "", "languageList": {"languageId": "", "description": ""}}], "conclusion": {"conclusionCode": "", "customerConclusion": "", "reviewConclusion": "", "conclusionRemark": ""}}], "jobList": [{"id": {"jobId": "", "jobNo": ""}, "relationship": {"parent": {"order": {"orderId": "", "orderNo": ""}}, "children": {"testLineList": [{"testLineInstanceId": ""}], "testSampleList": [{"testSampleInstanceId": ""}], "subReportList": [{"subReportId": "", "subReportNo": ""}]}}, "labSection": {"labSectionBaseId": "", "labSectionId": ""}, "header": {"owner": "", "jobExpectDueDate": "", "jobStatus": "", "jobRemark": ""}, "processList": [{"nodePoint": "create/labIn/labOut", "completedDateTime": "2023-10-08 10:00", "operator": "mike-miao", "remark": "confirmOrder"}]}], "subcontractList": [{"id": {"subcontractId": "", "subcontractNo": ""}, "relationship": {"parent": {"order": {"orderId": "", "orderNo": ""}}, "children": {"testLineList": [{"testLineInstanceId": ""}], "testSampleList": [{"testSampleInstanceId": ""}], "subReportList": [{"subReportId": "", "subReportNo": ""}]}}, "header": {"subcontractExpectDueDate": "", "subcontractStatus": "", "subcontractRemark": ""}, "subcontractTo": {"labId": 46, "labCode": "GZ SL", "labContact": {"contactName": null, "telephone": null, "email": null}}, "executeOrder": {"referenceNo": "", "contactName": "", "executeOrderExpectDueDate": ""}, "payment": {"subcontractFee": "", "subcontractCurrency": ""}, "serviceRequirement": {"report": {"reportLanguage": 0, "reportHeader": "", "reportAddress": "", "accreditation": 0, "needConclusion": 0, "needDraft": 0, "needPhoto": 0, "languageList": [{"languageId": 0, "reportHeader": "", "reportAddress": ""}]}, "otherRequestRemark": ""}, "processList": [{"nodePoint": "create/labIn/labOut", "completedDateTime": "2023-10-08 10:00", "operator": "mike-miao", "remark": "confirmOrder"}]}], "testDataList": [{"id": {"testResultId": ""}, "relationship": {"parent": {"testMatrixId": ""}}, "header": {"testLineRemarkDisplayInReport": "", "testResultDisplayInReport": "", "testLineRemark": "", "testLineFailRemarkDisplayInReport": "", "testLineFailRemark": "", "reportingTestLineListType": "出报告时要出 1表示Full Testline List， 0表示Testling List", "ppNotesDisplayInReport": ""}, "testResult": {"testResultFullName": "", "testResultFullNameRel": {"upSpecimenInstanceId": "", "specimenInstanceId": "", "procedureConditionInstanceId": "", "parentConditionInstanceId": "", "conditionInstanceId": "", "positionInstanceId": "", "analyteInstanceId": ""}, "testResultSeq": 0, "resultValue": "", "resultValueRemark": "", "resultUnit": "", "failFlag": 0}, "reportLimit": {"limitGroup": "", "limitValueFullName": "", "limitValueFullNameRel": {"talBaseId": 0, "manualRequirement": 0, "limitValue1": "", "limitValue2": "", "operatorName": "", "reportDescription": ""}, "limitUnit": ""}, "languageList": [{"languageId": 0, "testResultFullName": "", "limitValueFullName": ""}]}], "conclusionList": [{"reportId": "", "conclusionInstanceId": "", "conclusionLevelId": "604", "conclusionLevelName": "TestLine&OriginalSample", "testLineInstanceId": "", "sampleInstanceId": "", "conclusion": {"conclusionCode": "", "customerConclusion": "", "reviewConclusion": "", "conclusionRemark": ""}}, {"reportId": "", "conclusionInstanceId": "", "conclusionLevelId": "606", "conclusionLevelName": "Section", "ppVersionId": "", "sectionId": "", "conclusion": {"conclusionCode": "", "customerConclusion": "", "reviewConclusion": "", "conclusionRemark": ""}}, {"reportId": "", "conclusionInstanceId": "", "conclusionLevelId": "607", "conclusionLevelName": "Section&TestLine", "ppVersionId": "", "sectionId": "", "testLineInstanceId": "", "conclusion": {"conclusionCode": "", "customerConclusion": "", "reviewConclusion": "", "conclusionRemark": ""}}, {"reportId": "", "conclusionInstanceId": "", "conclusionLevelId": "608", "conclusionLevelName": "Section&TL&OriginalSample", "ppVersionId": "", "sectionId": "", "sampleInstanceId": "", "ppSampleRelId": "", "conclusion": {"conclusionCode": "", "customerConclusion": "", "reviewConclusion": "", "conclusionRemark": ""}}, {"reportId": "", "conclusionInstanceId": "", "conclusionLevelId": "609", "conclusionLevelName": "PP&TestLine", "ppVersionId": "", "testLineInstanceId": "", "ppArtifactRelId": "", "conclusion": {"conclusionCode": "", "customerConclusion": "", "reviewConclusion": "", "conclusionRemark": ""}}, {"reportId": "", "conclusionInstanceId": "", "conclusionLevelId": "610", "conclusionLevelName": "PP&TestLine&OriginalSample", "ppVersionId": "", "testLineInstanceId": "", "ppArtifactRelId": "", "ppSampleRelId": "", "sampleInstanceId": "", "conclusion": {"conclusionCode": "", "customerConclusion": "", "reviewConclusion": "", "conclusionRemark": ""}}, {"reportId": "", "conclusionInstanceId": "", "conclusionLevelId": "611", "conclusionLevelName": "PP", "ppVersionId": "", "conclusion": {"conclusionCode": "", "customerConclusion": "", "reviewConclusion": "", "conclusionRemark": ""}}], "reportList": [{"id": {"reportId": "", "rootReportNo": "", "parentReportNo": "", "reportNo": ""}, "relationship": {"parent": {"order": {"orderId": "", "orderNo": ""}, "trfList": [{"refSystemId": 0, "trfNo": ""}]}, "children": {"reportMatrixList": [{"testMatrixId": "虚拟TL PPID"}], "subReportList": [{"subReportId": "", "subReportNo": ""}], "conclusionList": [{"conclusionInstanceId": ""}]}}, "header": {"reportDueDate": "2023-09-01 09:05:15", "reportStatus": 0, "certificateName": ""}, "conclusion": {"conclusionCode": "", "customerConclusion": "", "reviewConclusion": "", "conclusionRemark": ""}, "attachmentList": [{"fileName": "", "fileType": 0, "objectType": 0, "objectId": "", "objectData": "datasheet", "cloudId": "", "filePath": "", "languageId": "", "description": "", "languageList": {"languageId": "", "description": ""}}], "processList": [{"nodePoint": "create/generate/approve/confirm/softcopy-delivery", "completedDateTime": "2023-10-08 10:00", "operator": "mike-miao", "remark": "confirmOrder"}]}], "subReportList": [{"id": {"subReportId": "", "subReportNo": "", "external": {"systemId": "", "externalObjectNo": ""}}, "header": {"subReportStatus": ""}, "attachmentList": [{"fileName": "", "fileType": 0, "objectType": 0, "objectId": "", "objectData": "datasheet", "cloudId": "", "filePath": "", "languageId": "", "description": "", "languageList": {"languageId": "", "description": ""}}]}], "quotationList": [{"id": {"quotationId": "", "quotationNo": ""}, "relationship": {"parent": {"order": {"orderId": "", "orderNo": ""}, "reportList": [{"reportId": "", "reportNo": ""}]}, "children": [{"invoiceList": [{"invoiceId": ""}]}]}, "header": {"currency": "", "netAmount": 0.0, "vatAmount": 0.0, "totalAmount": 0.0, "discount": 0.0, "adjustmentAmount": 0.0, "finalAmount": 0.0, "quotationVersionId": "", "quotationStatus": 0}, "payer": {"customerUsage": 0, "bossNo": 0, "customerGroupCode": "", "customerName": "", "customerAddress": "", "languageList": [{"languageId": 0, "customerName": "", "customerAddress": ""}], "customerContactList": [{"customerContactId": "", "customerContactAddressId": "", "bossContactId": 0, "bossSiteUseId": 0, "contactName": "", "contactTelephone": "", "contactMobile": "", "contactFAX": "", "contactEmail": ""}]}, "serviceItemList": [{"serviceItemName": "", "ppNo": 0, "testLineId": 0, "citationId": 0, "citationType": 0, "citationName": "", "evaluationAlias": "", "serviceItemListUnitPrice": 0.0, "serviceItemSalesUnitPrice": 0.0, "serviceItemDiscount": 0.0, "quantity": 0, "serviceItemNetAmount": 0.0, "serviceItemVATAmount": 0.0, "serviceItemTotalAmount": 0.0, "languageList": [{"languageId": 0, "serviceItemName": ""}], "externalInfo": {"testItemId": "", "testItemName": "", "testCitationId": 0, "testCitationName": "", "languageList": [{"languageId": 0, "testItemName": "", "testCitationName": ""}]}}], "attachmentList": [{"fileName": "", "fileType": 0, "objectType": 0, "objectId": "", "objectData": "datasheet", "cloudId": "", "filePath": "", "languageId": "", "description": "", "languageList": {"languageId": "", "description": ""}}], "processList": [{"nodePoint": "create/generate/confirm/delivery", "completedDateTime": "2023-10-08 10:00", "operator": "mike-miao", "remark": "confirmOrder"}]}], "invoiceList": [{"id": {"invoiceId": "", "invoiceNo": ""}, "relationship": {"parent": {"order": {"orderId": "", "orderNo": ""}, "quotationList": [{"quotationId": "", "quotationNo": ""}]}}, "header": {"currency": "", "netAmount": 0.0, "vatAmount": 0.0, "totalAmount": 0.0, "prePaidAmount": 0.0, "invoiceStatus": 0}, "attachmentList": [{"fileName": "", "fileType": 0, "objectType": 0, "objectId": "", "objectData": "datasheet", "cloudId": "", "filePath": "", "languageId": "", "description": "", "languageList": {"languageId": "", "description": ""}}], "processList": [{"nodePoint": "create/generate/confirm/delivery", "completedDateTime": "2023-10-08 10:00", "operator": "mike-miao", "remark": "confirmOrder"}]}]}