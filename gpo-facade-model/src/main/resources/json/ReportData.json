{"trfList": [{"refSystemId": 0, "trfNo": "", "serviceLevel": 0, "trfTemplateOwner": "", "trfTemplateId": "", "trfTemplateName": "", "trfSubmissionDate": "2023-09-01 09:05:15", "orderList": [{"systemId": 0, "orderNo": ""}]}], "orderList": [{"systemId": 0, "orderId": "", "orderNo": "", "originalOrderNo": "", "orderStatus": 0, "serviceType": 0, "orderType": "", "operationType": 0, "operationMode": 0, "productCategory": "", "productSubCategory": "", "groupId": 0, "idbLab": "", "tat": 0, "serviceStartDate": "2023-09-01 09:05:15", "testingStartDate": "2023-09-01 09:05:15", "testingEndDate": "2023-09-01 09:05:15", "serviceConfirmDate": "2023-09-01 09:05:15", "cuttingExpectDueDate": "2023-09-01 09:05:15", "orderExpectDueDate": "2023-09-01 09:05:15", "jobExpectDueDate": "2023-09-01 09:05:15", "subcontractExpectDueDate": "2023-09-01 09:05:15", "reportExpectDueDate": "2023-09-01 09:05:15", "softCopyDeliveryDate": "2023-09-01 09:05:15", "createBy": "", "createDate": "2023-09-01 09:05:15", "payment": {"paymentStatus": 0, "currency": "", "totalAmount": 0.0, "mainCurrencyTotalAmount": 0.0}, "contactPersonList": [{"contactUsage": "", "contactName": "", "contactEmail": "", "contactPhone": "", "contactTelephone": "", "contactMobile": "", "contactFAX": "", "regionAccount": "", "responsibleTeamCode": ""}], "flags": {"selfTestFlag": 0, "toDMFlag": 0}, "others": {"pending": {"pendingFlag": 0, "pendingType": 0, "pendingRemark": ""}, "orderRemark": ""}, "customerList": [{"customerUsage": 0, "bossNo": 0, "customerGroupCode": "", "customerName": "", "customerAddress": "", "languageList": [{"languageId": 0, "customerName": "", "customerAddress": ""}], "customerContactList": [{"customerContactId": "", "customerContactAddressId": "", "bossContactId": 0, "bossSiteUseId": 0, "contactName": "", "contactTelephone": "", "contactMobile": "", "contactFAX": "", "contactEmail": ""}]}], "product": {"productInstanceId": "", "templateId": "", "productAttrList": [{"seq": 0, "labelName": "", "customerLabel": "", "dataType": "", "labelCode": "", "value": "", "displayInReport": "", "languageList": [{"languageId": 0, "labelName": "", "customerLabel": "", "value": ""}]}]}, "sampleList": [{"testSampleInstanceId": "", "templateId": "", "sampleNo": "", "externalSampleNo": "", "sampleAttrList": [{"seq": 0, "labelName": "", "customerLabel": "", "dataType": "", "labelCode": "", "value": "", "displayInReport": "", "languageList": [{"languageId": 0, "labelName": "", "customerLabel": "", "value": ""}]}]}], "serviceRequirement": {"report": {"reportLanguage": 0, "reportHeader": "", "reportAddress": "", "accreditation": 0, "needConclusion": 0, "needDraft": 0, "needPhoto": 0, "languageList": [{"languageId": 0, "reportHeader": "", "reportAddress": ""}]}, "otherRequestRemark": ""}, "attachmentList": [{"fileName": "", "fileType": 0, "objectType": 0, "objectId": "", "cloudId": "", "filePath": "", "languageId": 0}], "trfList": [{"refSystemId": 0, "trfNo": ""}]}], "reportList": [{"systemId": 0, "orderNo": "", "reportId": "", "reportNo": "", "reportDueDate": "2023-09-01 09:05:15", "approveBy": "", "approveDate": "2023-09-01 09:05:15", "softCopyDeliveryDate": "2023-09-01 09:05:15", "originalReportNo": "", "reportStatus": 0, "certificateName": "", "createBy": "", "createDate": "2023-09-01 09:05:15", "lab": {"labId": 0, "labCode": "", "locationId": 0, "locationCode": "", "buId": 0, "buCode": ""}, "conclusion": {"conclusionCode": "", "customerConclusion": "", "reviewConclusion": "", "conclusionRemark": ""}, "reportMatrixList": [{"subReportNo": "", "testMatrixId": "", "testMatrixGroupId": 0, "testConditionGroupId": "", "conditionList": [{"conditionInstanceId": "", "conditionId": 0, "conditionType": 0, "conditionTypeId": 0, "conditionTypeName": "", "conditionName": "", "conditionDesc": "", "conditionSeq": 0, "languageList": [{"languageId": 0, "conditionTypeName": "", "conditionName": "", "conditionDesc": ""}]}], "testSampleInstanceId": "", "specimenList": [{"specimenType": 0, "specimenInstanceId": "", "specimenNo": 0, "specimenDescription": "", "languageList": [{"languageId": 0, "specimenDescription": ""}]}], "testLineInstanceId": "", "analyteInstanceId": "", "positionList": [{"positionInstanceId": "", "usageTypeId": 0, "usageTypeName": "", "positionName": "", "positionDescription": "", "positionSeq": 0, "languageList": [{"languageId": 0, "positionName": "", "positionDescription": ""}]}], "testMatrixFileList": [{"fileName": "", "fileType": 0, "objectType": 0, "objectId": "", "cloudId": "", "filePath": "", "languageId": 0}]}], "subReportList": [{"sourceType": 0, "objectNo": "", "subReportId": "", "subReportNo": "", "externalObjectNo": "", "subReportFileList": [{"fileName": "", "fileType": 0, "objectType": 0, "objectId": "", "cloudId": "", "filePath": "", "languageId": 0}]}], "reportFileList": [{"fileName": "", "fileType": 0, "objectType": 0, "objectId": "", "cloudId": "", "filePath": "", "languageId": 0}]}], "testSampleList": [{"orderNo": "", "systemId": 0, "testSampleInstanceId": "", "parentTestSampleId": "", "testSampleGroupList": [{"testSampleInstanceId": "", "mainSampleFlag": 0}], "testSampleNo": "", "externalSampleNo": "", "testSampleType": 0, "category": "", "testSampleSeq": 0, "materialAttr": {"materialDescription": "", "materialOtherSampleInfo": "", "materialEndUse": "", "applicableFlag": 0, "materialRemark": "", "materialName": "", "materialColor": "", "materialTexture": "", "extFields": {}}, "conclusion": {"conclusionCode": "", "customerConclusion": "", "reviewConclusion": "", "conclusionRemark": ""}, "testSamplePhoto": [{"fileName": "", "fileType": 0, "objectType": 0, "objectId": "", "cloudId": "", "filePath": "", "languageId": 0}]}], "testLineList": [{"orderNo": "", "systemId": 0, "testLineInstanceId": "", "testItemNo": "", "testLineType": 0, "testLineBaseId": 0, "testLineId": 0, "testLineVersionId": 0, "evaluationAlias": "", "evaluationName": "", "testLineStatus": 0, "testLineSeq": 0, "labSectionBaseId": 0, "labTeam": "", "productLineAbbr": "", "testLineRemark": "", "external": {"testLineMappingId": 0, "externalId": "", "externalCode": ""}, "citation": {"citationId": 0, "citationType": 0, "citationVersionId": 0, "citationSectionId": 0, "citationSectionName": "", "citationName": "", "citationFullName": "", "languageList": [{"languageId": 0, "citationSectionName": "", "citationName": "", "citationFullName": ""}]}, "wi": {"wiForCS": "", "wiForSample": "", "wiForTest": ""}, "analyteList": [{"analyteInstanceId": "", "analyteBaseId": 0, "analyteId": 0, "analyteName": "", "analyteSeq": 0, "analyteLimitVersionId": 0, "unitBaseId": 0, "reportUnit": "", "casNo": "", "languageList": [{"languageId": 0, "analyteName": "", "reportUnit": ""}]}], "ppTestLineRelList": [{"ppTlRelId": "", "ppArtifactRelId": 0, "ppBaseId": 0, "rootPPBaseId": 0, "ppNo": 0, "ppVersionId": 0, "ppName": "", "ppNotes": "", "sectionId": 0, "sectionLevel": 0, "sectionName": "", "aid": 0, "languageList": [{"languageId": 0, "ppName": "", "ppNotes": ""}]}], "conclusion": {"conclusionCode": "", "customerConclusion": "", "reviewConclusion": "", "conclusionRemark": ""}, "languageList": [{"languageId": 0, "evaluationAlias": "", "evaluationName": ""}]}], "testResultList": [{"orderNo": "", "systemId": 0, "testMatrixId": "", "subReportNo": "", "testResult": {"testResultFullName": "", "testResultFullNameRel": {"upSpecimenInstanceId": "", "specimenInstanceId": "", "procedureConditionInstanceId": "", "parentConditionInstanceId": "", "conditionInstanceId": "", "positionInstanceId": "", "analyteInstanceId": ""}, "resultValue": "", "resultValueRemark": "", "resultUnit": "", "failFlag": 0}, "testResultSeq": 0, "reportLimit": {"limitGroup": "", "limitValueFullName": "", "limitValueFullNameRel": {"talBaseId": 0, "manualRequirement": 0, "limitValue1": "", "limitValue2": "", "operatorName": "", "reportDescription": ""}, "limitUnit": ""}, "languageList": [{"languageId": 0, "testResultFullName": "", "limitValueFullName": ""}]}], "reportConclusionList": [{"orderNo": "", "systemId": 0, "conclusionInstanceId": "", "conclusionLevelId": 0, "objectId": "", "conclusionId": "", "conclusionCode": "", "customerConclusionId": "", "customerConclusion": "", "conclusionRemark": "", "testLineInstanceId": "", "sampleInstanceId": "", "sectionId": 0, "ppArtifactRelId": "", "ppSampleRelId": ""}], "conditionGroupList": [{"orderNo": "", "systemId": 0, "conditionGroupId": "", "combinedConditionDescription": "", "requirement": "", "ppConditionGroupList": [{"id": "", "conditionGroupId": "", "ppTestLineRelId": "", "languageType": 0, "groupFootNotes": "", "ppNo": 0, "ppName": "", "sampleNos": ""}], "languageList": [{"languageId": 0, "combinedConditionDescription": ""}]}], "quotationList": [{"orderNo": "", "systemId": 0, "quotationNo": "", "payer": {"customerUsage": 0, "bossNo": 0, "customerGroupCode": "", "customerName": "", "customerAddress": "", "languageList": [{"languageId": 0, "customerName": "", "customerAddress": ""}], "customerContactList": [{"customerContactId": "", "customerContactAddressId": "", "bossContactId": 0, "bossSiteUseId": 0, "contactName": "", "contactTelephone": "", "contactMobile": "", "contactFAX": "", "contactEmail": ""}]}, "currency": "", "netAmount": 0.0, "vatAmount": 0.0, "totalAmount": 0.0, "discount": 0.0, "adjustmentAmount": 0.0, "finalAmount": 0.0, "quotationVersionId": "", "quotationStatus": 0, "serviceItemList": [{"serviceItemName": "", "ppNo": 0, "testLineId": 0, "citationId": 0, "citationType": 0, "citationName": "", "evaluationAlias": "", "serviceItemListUnitPrice": 0.0, "serviceItemSalesUnitPrice": 0.0, "serviceItemDiscount": 0.0, "quantity": 0, "serviceItemNetAmount": 0.0, "serviceItemVATAmount": 0.0, "serviceItemTotalAmount": 0.0, "languageList": [{"languageId": 0, "serviceItemName": ""}], "externalInfo": {"testItemId": "", "testItemName": "", "testCitationId": 0, "testCitationName": "", "languageList": [{"languageId": 0, "testItemName": "", "testCitationName": ""}]}}], "quotationFileList": [{"fileName": "", "fileType": 0, "objectType": 0, "objectId": "", "cloudId": "", "filePath": "", "languageId": 0}]}], "invoiceList": [{"orderNo": "", "systemId": 0, "invoiceNo": "", "quotationNos": [""], "currency": "", "netAmount": 0.0, "vatAmount": 0.0, "totalAmount": 0.0, "prePaidAmount": 0.0, "invoiceStatus": 0, "invoiceFileList": [{"fileName": "", "fileType": 0, "objectType": 0, "objectId": "", "cloudId": "", "filePath": "", "languageId": 0}], "reportNo": ""}]}