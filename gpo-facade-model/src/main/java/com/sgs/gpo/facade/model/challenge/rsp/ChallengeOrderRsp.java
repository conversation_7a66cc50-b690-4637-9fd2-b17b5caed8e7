package com.sgs.gpo.facade.model.challenge.rsp;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ChallengeOrderRsp {
    private String labCode;
    private String orderNo;
    private String reportNo;
    private String subcontractOrderNo;
    private String subcontractFromLab;
    private String subcontractFromBU;
    private String subcontractFromContact;
    private String subcontractFromEmail;
    private String createBy;
    private Date createDate;
    private Date sampleConfirmDate;
    private String kAName;
    private String reportTemplate;
    private String fCode;
    private Date orderExpectDueDate;
    private String orderRemark;
    private Integer orderStatus;
    private Integer reportLanguage;
    private String buyerName;
    private String serviceType;
    private String accreditation;
    private List<Sample> samples;
    @Data
    public static class Sample {
        private String sampleInstanceID;
        private String sampleNo;
        private Integer sampleType;
        private String sampleDescription;
        private String sampleChineseDescription;
        private Date sampleConfirmDate;
        private List<TestLine> testLines;
        @Data
        public static class TestLine {
             private String testLineInstanceID;
             private Integer ppNo;
             private Integer ppVersionID;
             private Integer testLineID;
             private String testLineName;
             private Integer citationType;
             private Integer citationID;
             private String citationName;
             private String labSection;
             private List<Analyte> analytes;
             @Data
             public  static class Analyte {
                 private Integer analyteCode;
                 private String analyteName;
                 private String reportUnit;
                }
        }
    }
}
