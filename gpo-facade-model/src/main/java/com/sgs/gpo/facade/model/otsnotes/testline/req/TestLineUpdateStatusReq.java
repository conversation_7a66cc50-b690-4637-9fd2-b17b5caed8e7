package com.sgs.gpo.facade.model.otsnotes.testline.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 * @title: UpdateTestMatrixStatusReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/9/1 16:41
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TestLineUpdateStatusReq extends BaseRequest {
    private Set<String> testLineInstanceIdList;
    private Integer testLineStatus;
    private String trigger;
    private String action;
    private String remark;
}
