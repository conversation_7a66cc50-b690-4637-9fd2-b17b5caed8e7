package com.sgs.gpo.facade.model.preorder.enquiry.rsp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@JsonIgnoreProperties(value = {"handler"})
public class QuotationHeadRsp implements Serializable {

    private static final long serialVersionUID = -4263583611901659033L;
    private String id;
    private String version;
    private String statusDesc;
    private Integer status;
    private BigDecimal totalAmount;
    private String orderId;
    private Integer discountApplyStatus;
    private String discountApplyStatusDesc;
    private String currencyCode;
    private List<String> orderIds;
    private List<QuotationHeadRsp> quotationHeadDTOS;
    private Date confirmedDate;
    private String quotationNo;
    private boolean freeQuotation;

}
