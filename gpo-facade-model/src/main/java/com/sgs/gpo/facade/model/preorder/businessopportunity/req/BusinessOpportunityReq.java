package com.sgs.gpo.facade.model.preorder.businessopportunity.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: 查询商机列表请求参数
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2025/03/04 15:20
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BusinessOpportunityReq {
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String createBy;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String customerNo;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String customerId;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String obscure;
    private Integer page;
    private Integer pageSize;
}
