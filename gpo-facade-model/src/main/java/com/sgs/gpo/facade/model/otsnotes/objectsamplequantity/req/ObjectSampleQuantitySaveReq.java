package com.sgs.gpo.facade.model.otsnotes.objectsamplequantity.req;

import com.sgs.framework.core.base.BaseQueryReq;
import com.sgs.gpo.facade.model.otsnotes.objectsamplequantity.bo.ObjectSampleQuantityBO;
import lombok.Data;

import java.util.List;

@Data
public class ObjectSampleQuantitySaveReq extends BaseQueryReq {

    private  String id;

    private String objectType;

    private String objectNo;

    private String objectId;

    private Integer stylesQty;

    private Integer piecesQty;

    private Integer partsQty;

    private String location;

    private String remark;
}
