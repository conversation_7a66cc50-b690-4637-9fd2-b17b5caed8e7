package com.sgs.gpo.facade.model.preorder.order.rsp.orderToSGSMartInfo;

import com.sgs.gpo.facade.model.preorder.order.dto.OrderExtTrfTemplateDTO;
import lombok.Data;

import java.util.List;

@Data
public class OrderToSGSMartInfoRsp {

    //页面下拉选择列表
    List<OrderExtTrfTemplateDTO> trfTemplateList;
    List<CustomerDepartment> customerDepartmentDTOList;
    List<SgsMartAccount> sgsMartAccountList;


    boolean orderToTrfFlag;
    boolean deptIsRequired;
    String templateId;
    String templateName;
    String sgsMartUserId;
    String sgsMartAccount;
    String kACustomerDeptCode;

}
