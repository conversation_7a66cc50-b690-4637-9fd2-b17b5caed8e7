package com.sgs.gpo.facade.model.dashboard.rsp;

import com.sgs.gpo.facade.model.echarts.Pie;
import com.sgs.gpo.facade.model.trims.labsection.rsp.LabSectionRsp;
import lombok.Data;

import java.util.List;

@Data
public class TodaySummaryRsp {
    /**饼图order*/
    private List<Pie> orderStatusList;

    /**饼图report*/
    private List<Pie> reportStatusList;

    /**饼图matrix*/
    private List<Pie> testMatrixStatusList;

    /**
     * LabSection数据集合
     */
    private List<LabSectionRsp> labSectionList;
}
