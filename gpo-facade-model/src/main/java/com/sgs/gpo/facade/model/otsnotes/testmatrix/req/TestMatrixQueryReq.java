package com.sgs.gpo.facade.model.otsnotes.testmatrix.req;

import com.sgs.framework.core.base.BaseQueryReq;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixIdBO;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/7/3 10:49
 */
@Data
public class TestMatrixQueryReq extends BaseQueryReq<TestMatrixIdBO> {

    private Set<String> orderIdList;
    private Set<String> reportIdList;
    private Set<String> orderNoList;
    private Set<String> testLineInstanceIdList;
    private Set<String> testItemNoList;
    private Set<String> testMatrixIdList;
    private Set<String> testSampleInstanceIdList;
}
