package com.sgs.gpo.facade.model.trackingtool;

import com.sgs.gpo.facade.model.otsnotes.conclusion.ConclusionDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
public class TrackingToolBaseDataRsp implements Serializable {

    private String reportId;
    private String reportNo;
    private String reportStatus;
    private String reportDueDate;
    private String softcopyDeliveryDate;
    private Date deliverPrelimDate;
    private String applicantName;
    private String buyerName;
    private String payerName;
    private String csName;
    private String orderId;
    private String orderNo;
    private String serviceType;
    private Integer paymentStatus;
    private String customerRefNo;
    private String sampleDescriptions;
    private String styleNo;
    private String itemNo;
    private String skuNo;
    private String engineer;
    private ConclusionDTO reportConclusion;
    private OperationHistoryPendingRsp orderPending;
    private OperationHistoryPendingRsp reportPending;
    private OperationHistoryPendingRsp jobPending;

}
