package com.sgs.gpo.facade.model.todolist;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

@Data
public class ToDoListStatusReq extends BaseRequest {

    /**
     * 业务对象ID
     */
    private String objectId;

    /**
     * 业务对象编码
     */
    private String objectNo;

    /**
     * 待办状态
     */
    private String status;

    /**
     * 权限
     */
    private String sgsToken;

    /**
     * Data
     */
    private String data;

}
