package com.sgs.gpo.facade.model.preorder.enquiry.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sgs.framework.core.base.BaseProductLine;
import lombok.Data;

import java.util.Date;

@Data
public class EnquiryCustomerDTO extends BaseProductLine {

    private String Id;
    /**
     * GeneralOrderID VARCHAR(36) 必填<br>
     * FK TB_Order
     */
    private String enquiryId;

    /**
     * CustomerID VARCHAR(36)<br>
     * relate to TB_Customer PK
     */
    private String customerId;

    /**
     * CustomerGroupID VARCHAR(50)<br>
     *
     */
    private String customerGroupId;

    /**
     * CustomerAddressCN VARCHAR(500)<br>
     * The chinese address of customer
     */
    @JsonProperty("addressCn")
    @JSONField(name="addressCn")
    private String customerAddressCn;

    /**
     * CustomerAddressEN VARCHAR(500)<br>
     * The  english address of customer
     */
    @JsonProperty("addressEn")
    @JSONField(name="addressEn")
    private String customerAddressEn;

    /**
     * CustomerNameCN VARCHAR(250)<br>
     * The chinese name of customer
     */
    @JsonProperty("customerNameCn")
    @JSONField(name="customerNameCn")
    private String customerNameCn;

    /**
     * CustomerNameEN VARCHAR(250)<br>
     * The english name of customer
     */
    @JsonProperty("customerNameEn")
    @JSONField(name="customerNameEn")
    private String customerNameEn;

    /**
     * ContactPersonEmail VARCHAR(100)<br>
     * The email of the contact
     */
    @JsonProperty("email")
    @JSONField(name="customerPersonEmail")
    private String contactPersonEmail;

    /**
     * ContactPersonFax VARCHAR(100)<br>
     * The fax of the contact
     */
    @JsonProperty("fax")
    @JSONField(name="customerPersonFax")
    private String contactPersonFax;

    /**
     * ContactPersonPhone1 VARCHAR(50)<br>
     * The mobile of the contact
     */
    @JsonProperty("telephone")
    @JSONField(name="customerPersonPhone1")
    private String contactPersonPhone1;

    /**
     * ContactPersonName VARCHAR(50)<br>
     * The name of the contact
     */
    @JsonProperty("contactName")
    @JSONField(name="customerPersonName")
    private String contactPersonName;

    /**
     * ContactPersonRemark VARCHAR(500)<br>
     * The remark of the contact
     */
    private String contactPersonRemark;

    /**
     * ContactPersonPhone2 VARCHAR(50)<br>
     * The telephones of the contact
     */
    @JsonProperty("mobile")
    @JSONField(name="customerPersonPhone2")
    private String contactPersonPhone2;

    /**
     * CustomerCredit VARCHAR(250)<br>
     * The credit of customer
     */
    private String customerCredit;

    /**
     * CustomerUsage VARCHAR(50)<br>
     * Usage: Applicant , Payer, Report to
     */
    private String customerUsage;

    /**
     * AccountID BIGINT(19)<br>
     * Customer Boss Account ID
     */
    private Long accountID;

    /**
     * BossNumber BIGINT(19)<br>
     * Customer Boss Number
     */
    @JsonProperty("number")
    @JSONField(name="bossNumber")
    private Long bossNumber;

    /**
     * ContactAddressID VARCHAR(36)<br>
     *
     */
    @JsonProperty("customerContactId")
    @JSONField(name="customerContactId")
    private String contactAddressId;

    /**
     * BuyerGroup VARCHAR(50)<br>
     * TB_CustomerGroup FK
     */
    @JsonProperty("customerGroupCode")
    @JSONField(name="customerGroupCode")
    private String buyerGroup;

    /**
     * BuyerGroupName VARCHAR(100)<br>
     *
     */
    @JsonProperty("customerGroupName")
    @JSONField(name="customerGroupName")
    private String buyerGroupName;

    /**
     * SupplierNo VARCHAR(50)<br>
     *
     */
    private String supplierNo;

    /**
     * LogoCloudID VARCHAR(250)<br>
     *
     */
    private String logoCloudId;

    /**
     * OrganizationName VARCHAR(500)<br>
     *
     */
    private String organizationName;

    /**
     * IsAsApplicant TINYINT(3)<br>
     *
     */
    private Integer isAsApplicant;

    /**
     * ReportDeliveredTo VARCHAR(500)<br>
     * Customer Report Delivered To
     */
    private String reportDeliveredTo;

    /**
     * FailedReportDeliveredTo VARCHAR(500)<br>
     * Customer Failed Report Delivered To
     */
    private String failedReportDeliveredTo;

    /**
     * BossSiteUseID BIGINT(19)<br>
     *
     */
    private Long bossSiteUserId;

    /**
     * BossContactID BIGINT(19)<br>
     *
     */
    private Long bossContactId;

    /**
     *
     */
    private String bossLocationCode;
    /**
     *
     */
    private String monthlyPayment;
    /**
     *
     */
    private String primaryFlag;
    /**
     *
     */
    private int oldVersionId;

    /**
     * active_indicator INTEGER(10) 默认值[1] 必填<br>
     * 0: inactive, 1: active
     */
    private Integer activeIndicator;

    /**
     * created_by VARCHAR(50)<br>
     * Founder of the UserName
     */
    private String createdBy;

    /**
     * created_date TIMESTAMP(19)<br>
     * Creation time
     */
    private Date createdDate;

    /**
     * modified_by VARCHAR(50)<br>
     * Edit the UserName (like CreateBy for the first time)
     */
    private String modifiedBy;

    /**
     * modified_date TIMESTAMP(19)<br>
     * Edit the Date (like CreateDate for the first time)
     */
    private Date modifiedDate;

    /**
     * paymentTermName VARCHAR(50)<br>
     * Edit the paymentTermName
     */
    private String paymentTermName;

    /**
     * BossSiteUseID BIGINT(19)<br>
     *
     */
    private Long bossSiteUseId;

    private String sgsMartAccount;

    private String sgsMartUserId;

}
