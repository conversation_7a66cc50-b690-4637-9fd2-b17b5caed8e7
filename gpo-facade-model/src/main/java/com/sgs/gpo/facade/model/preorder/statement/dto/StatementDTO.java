package com.sgs.gpo.facade.model.preorder.statement.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 对账单实体类
 * 对应对账单的各个字段
 */
@Data
public class StatementDTO implements Serializable {

    // 对账单日期范围（开始日期）
    private String startDate;

    // 对账单日期范围（结束日期）
    private String endDate;

    // 币种
    private String currency;

    // 报价账户信息
    private String quotationAccountInfo;

    // 银行账号
    private String bankAccountNumber;

    // 银行名称
    private String bankName;

    // 银行地址
    private String bankAddress;

    // 发票类型
    private String vatType;

    // 报告编号
    private String reportNo;

    // 报价单号
    private String orderNo;

    // 订单状态
    private String orderStatus;

    // 客户名称/送检主体
    private String applicantNameCn;
    // 客户名称/送检主体 英文
    private String applicantNameEn;

    // 发票抬头
    private String payerNameCn;
    // 发票抬头
    private String payerNameEn;

    // 样品名称
    private String sampleName;
    // 样品顺序
    private String sampleSeq;
    // 收费类型
    private String chargeType;
    // 生产商 中文
    private String manufacturerCn;
    // 生产商 英文
    private String manufacturerEn;

    // 样品批号
    private String sampleBatchNoCn;
    // 样品批号
    private String sampleBatchNoEn;

    // 生产日期
    private String productionDateCn;
    // 生产日期
    private String productionDateEn;

    // 样品其他信息
    private String otherSampleInfoCn;
    // 样品其他信息
    private String otherSampleInfoEn;

    // 测试项目
    private String serviceItemNameCn;
    // 测试项目
    private String serviceItemNameEn;

    // 测试方法
    private String citationName;

    // 周期
    private String tat;

    // 送样日期
    private String createdDate;

    // 报告出具时间
    private String referenceDueDate;

    // 标准报价
    private String unitPrice;

    // 折扣
    private String discount;

    // 税前金额
    private String netAmount;

    // 税后总价（单项）
    private String afterTaxAmount;

    // 业务对接人员
    private String discountPerson;

    // 联系人
    private String contactPerson;

    // 联系人邮箱
    private String contactEmail;

    // 补充信息
    private String supplementaryInfo;

    // 订单税后总额
    private String totalPrice;

    // 买家
    private String buyerCn;
    // 买家
    private String buyerEn;

    // 代理商
    private String agentCn;
    // 代理商
    private String agentEn;

    // 供应商
    private String supplierCn;
    // 供应商
    private String supplierEn;

    // 规格/等级
    private String levelCn;
    // 规格/等级
    private String levelEn;

    // 生产商地址 中文
    private String manufacturerAddressCn;
    // 生产商地址 英文
    private String manufacturerAddressEn;

}
