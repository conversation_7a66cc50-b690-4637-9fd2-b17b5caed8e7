package com.sgs.gpo.facade.model.searchvalid.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: SearchListValidReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/4 16:03
 */
@Data
public class SearchValidReq extends BaseRequest {
    private Object requestReqObj;
    private String objectCode;
    private String searchCode;
    /*
    有些对象下定义多个Sub对象，比如Report List下有多个Tab页，此参数定义为类似Tab页Code的参数
    **/
    private String subSectionCode;
}
