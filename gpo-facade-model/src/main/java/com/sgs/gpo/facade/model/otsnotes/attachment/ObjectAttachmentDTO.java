package com.sgs.gpo.facade.model.otsnotes.attachment;

import lombok.Data;

import java.util.Date;

@Data
public class ObjectAttachmentDTO {
    private String objectType;
    private String objectNo;
    private String objectId;
    private String objectData;
    private String cloudId;
    private String filePath;
    private String fileName;
    private String fileType;
    private Integer attachType;
    private boolean displayInReport;
    private Integer generateStatus;
    private String generateErrorMessage;
    private Integer languageId;
    private Date createdDate;
    private String createdBy;
    private Date modifiedDate;
    private String modifiedBy;
}
