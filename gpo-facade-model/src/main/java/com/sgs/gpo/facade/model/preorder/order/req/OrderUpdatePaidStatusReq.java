package com.sgs.gpo.facade.model.preorder.order.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OrderUpdatePaidStatusReq extends BaseRequest {

   private String orderNo;
   private String systemID;
   private String bossCustomerNo;
   private String paymentCustomerName;
   private String bossCustomerName;
   private BigDecimal confirmedAmount;
   private String bossOrderNo;
   private String bossInvoiceNo;
   private String receiptsNo;
   private BigDecimal paymentAmount;
   private String paymentCurrency;
   private BigDecimal paymentBalance;
   private Date receiptsDate;
   private String dataMatchingKey;
   private String dataMatchingValue;
   private String action;
   private String orgCode;
   private String currentName;

   private List<String> orderNoList;
   private String opType;
   private String remark;
}
