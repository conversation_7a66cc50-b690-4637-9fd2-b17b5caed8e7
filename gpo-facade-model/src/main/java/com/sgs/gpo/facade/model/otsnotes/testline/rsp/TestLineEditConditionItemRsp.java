package com.sgs.gpo.facade.model.otsnotes.testline.rsp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: TestLineEditConditionRsp
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/26 15:39
 */
@Data
@ApiModel(value = "", description = "编辑TestLine时Condition的选项")
public class TestLineEditConditionItemRsp {
    @ApiModelProperty(notes = "")
    private Long conditionBaseId;
    @ApiModelProperty(notes = "")
    private Integer testConditionId;
    @ApiModelProperty(notes = "")
    private String testConditionName;
    @ApiModelProperty(notes = "")
    private String testConditionDesc;
    @ApiModelProperty(notes = "")
    private String testConditionValue;

    @ApiModelProperty(notes = "ClientSpecified default 0, 1为手动录入")
    private Integer clientSpecified;
    @ApiModelProperty(notes = "")
    private Integer testConditionSeq;
    @ApiModelProperty(notes = "")
    private Boolean defaultCondition;
    @ApiModelProperty(notes = "DefaultBy,判断 默认值来源   1:From TestLine;2:From Pp")
    private Integer defaultBy;
    @ApiModelProperty(notes = "")
    private Integer productCondition;
    //用来标记是否是推荐选中数据
    private Boolean recommend;
    private boolean selected;
}
