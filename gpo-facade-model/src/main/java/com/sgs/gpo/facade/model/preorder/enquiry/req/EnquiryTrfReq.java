package com.sgs.gpo.facade.model.preorder.enquiry.req;

import com.sgs.framework.core.base.BaseIdBO;
import com.sgs.framework.core.base.BaseQueryReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 * @title: EnquiryTrfReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EnquiryTrfReq extends BaseQueryReq<BaseIdBO> {
    private String labCode;
    private Set<String> enquiryIdList;
    private Set<String> enquiryNoList;
    private String refNo;
    private String externalOrderNo;
    private Set<Integer> refSystemIdList;
}
