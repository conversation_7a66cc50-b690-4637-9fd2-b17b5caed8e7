package com.sgs.gpo.facade.model.trackingtool;

import com.sgs.framework.core.base.StdBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class TrackingToolDataReq extends StdBaseRequest {
    @ApiModelProperty(value = "labCode")
    private String labCode;

    @ApiModelProperty(value = "report create date(start)")
    private String reportCreateStartDate;

    @ApiModelProperty(value = "report create date(end)")
    private String reportCreateEndDate;

    @ApiModelProperty(value = "report due date(start)")
    private String reportDueStartDate;

    @ApiModelProperty(value = "report due date(end)")
    private String reportDueEndDate;

    @ApiModelProperty(value = "reportNo")
    private String reportNo;

    @ApiModelProperty(value = "Batch Report No(换行符分隔)")
    private String reportNos;

    @ApiModelProperty(value = "Report Status")
    private List<String> reportStatusList;

    @ApiModelProperty(value = "OrderNo")
    private String orderNo;

    //@ApiModelProperty(value = "styleNo")
    //private String styleNo;

    //@ApiModelProperty(value = "itemNo")
    //private String itemNo;

    //@ApiModelProperty(value = "SKUNo")
    //private String skuNo;

    @ApiModelProperty(value = "Applicant Name")
    private String applicantName;

    @ApiModelProperty(value = "CS Name")
    private String csName;

    @ApiModelProperty(value = "BuyerName")
    private String buyerName;

    @ApiModelProperty(value = "PayerName")
    private String payerName;

    //@ApiModelProperty(value = "CustomerRefNo")
    //private String customerRefNo;

    //@ApiModelProperty(value = "Sample Descriptions")
    //private String sampleDes;

    //@ApiModelProperty(value = "Service Type")
    //private String serviceType;

    //@ApiModelProperty(value = "Payment Status")
    //private String paymentStatus;

    @ApiModelProperty(value = "is job or subcontract(job:1 subcontract:2)")
    private String isJobOrSubContract;

    @ApiModelProperty(value = "JobNo")
    private String jobNo;

    @ApiModelProperty(value = "Job Status")
    private String jobStatus;

    @ApiModelProperty(value = "subcontractNo")
    private String subcontractNo;

    @ApiModelProperty(value = "subcontract Status")
    private String subcontractStatus;

    @ApiModelProperty(value = "ServiceType")
    private String serviceType;


    private Integer labId;
    private List<String> reportNosList;

}
