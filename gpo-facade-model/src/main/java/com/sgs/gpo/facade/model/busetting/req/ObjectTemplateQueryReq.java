package com.sgs.gpo.facade.model.busetting.req;

import com.sgs.framework.core.base.BaseQueryReq;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.model.common.object.ObjectIdBO;
import com.sgs.framework.model.common.object.busetting.template.ObjectTemplateIdBO;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2023/12/31 10:22
 */
@Data
public class ObjectTemplateQueryReq extends BaseQueryReq<ObjectTemplateIdBO> {
    /**业务对象*/
    private ObjectIdBO object;
    /**实验室信息*/
    private Lab lab;

    @Deprecated
    private String caseType;
    private String orderModel;
    private String locationCode;
    private String templateCode;
}
