package com.sgs.gpo.facade.model.preorder.enquiry.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

@NoArgsConstructor
@Data
public class EnquiryBatchUpdateReq extends BaseRequest {
    private Set<String> enquiryNoList;
    private String orgEnquiryNo;
    private List<BatchUpdateSectionReq> updateSection;
    private boolean sameAsApplicantFlag = false;
    private boolean sameAsApplicantShow = false;
    @Data
    public static class BatchUpdateSectionReq {
        private String attributeCode;
        private String attributeName;
        private Boolean checkAll;
        private Boolean isIndeterminate;
        private List<BatchUpdateSubSectionReq> subSectionReqList;
    }
    @Data
    public static class BatchUpdateSubSectionReq {
        private String attributeCode;
        private String attributeName;
        private String displayInReport;
    }
}
