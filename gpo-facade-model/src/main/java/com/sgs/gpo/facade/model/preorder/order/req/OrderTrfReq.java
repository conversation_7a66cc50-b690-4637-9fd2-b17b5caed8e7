package com.sgs.gpo.facade.model.preorder.order.req;

import com.sgs.framework.core.base.BaseIdBO;
import com.sgs.framework.core.base.BaseQueryReq;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @title: OrderTrfReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/26 14:08
 */
@Data
public class OrderTrfReq extends BaseQueryReq<BaseIdBO> {
    private String labCode;
    private Set<String> orderIdList;
    private Set<String> orderNoList;
    private String refNo;
    private String externalOrderNo;
    private Set<Integer> refSystemIdList;
}
