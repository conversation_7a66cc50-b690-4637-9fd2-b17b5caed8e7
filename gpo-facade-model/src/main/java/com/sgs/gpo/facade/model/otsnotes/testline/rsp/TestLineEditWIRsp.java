package com.sgs.gpo.facade.model.otsnotes.testline.rsp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: TestLineEditWIRsp
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/26 15:39
 */
@Data
@ApiModel(value = "TestLineEditWIRsp", description = "TestLineEditWIRsp")
public class TestLineEditWIRsp {
    private Integer categoryId;
    @ApiModelProperty(notes = "categoryName ")
    private String categoryName;
    @ApiModelProperty(value = "workInstructionId",notes = "workInstructionId")
    private String workInstructionId;
    @ApiModelProperty(value = "workingInstructionText",notes = "workingInstructionText:按主语言展示")
    private String workingInstructionText;
    @ApiModelProperty(value = "workingInstructionText",notes = "workInstructionName:按主语言展示")
    private String workInstructionName;

}
