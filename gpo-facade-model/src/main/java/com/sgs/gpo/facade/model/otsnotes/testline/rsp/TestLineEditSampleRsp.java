package com.sgs.gpo.facade.model.otsnotes.testline.rsp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: TestLineEditSampleRsp
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/26 15:44
 */
@Data
@ApiModel(value = "TestLineEditSampleRsp" ,description = "")
public class TestLineEditSampleRsp {
    @ApiModelProperty(value = "sampleId",notes = "")
    private String sampleId;
    @ApiModelProperty(value = "sampleNo",notes = "样品编号")
    private String sampleNo;
    @ApiModelProperty(value = "sampleType",notes = "样品类型")
    private Integer sampleType;
    @ApiModelProperty(value = "testMatrixId",notes = "")
    private String testMatrixId;
    @ApiModelProperty(value = "sampleType",notes = "序号")
    private Integer sampleSeq;

    private boolean selected;
}
