package com.sgs.gpo.facade.model.condition.req;

import com.sgs.framework.core.base.BaseQueryReq;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.model.test.condition.conditiongroup.v2.ConditionGroupIdBO;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class ConditionGroupQueryReq extends BaseQueryReq<ConditionGroupIdBO> {

    private Lab lab;

    private Set<String> testLineInstanceIdList;

    private Set<String> orderInstanceIdList;

    private Set<String> testConditionGroupIdList;

}
