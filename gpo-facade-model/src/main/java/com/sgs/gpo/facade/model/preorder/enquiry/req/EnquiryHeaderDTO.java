package com.sgs.gpo.facade.model.preorder.enquiry.req;

import com.sgs.framework.core.base.BaseProductLine;
import com.sgs.gpo.facade.model.preorder.annotation.ObjectSetting;
import com.sgs.gpo.facade.model.tag.rsp.TagRsp;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class EnquiryHeaderDTO extends BaseProductLine {

    private String enquiryId;

    private String enquiryNo;

    private Integer enquiryStatus;

    private String enquiryStatusStr;

    private String preOrderId;

    @ObjectSetting(code = "caseType")
    private String enquiryType;

    @ObjectSetting(code = "orderExpectDueDate")
    private Date expectedOrderDueDate;
    @ObjectSetting(code = "remark")
    private String remark;

    private Date createdDate;

    private String createdBy;

    @ObjectSetting(code = "referenceEnquiryNo")
    private String referenceEnquiryNo;

    @ObjectSetting(code = "cSName")
    private String cSName;
    // CSA
    @ObjectSetting(code = "enquiryCsa")
    private String csa;
    @ObjectSetting(code = "cSContact")
    private String cSContact;

    @ObjectSetting(code = "cSEmail")
    private String cSEmail;

    private String caseType;

    private String serviceType;
    @ObjectSetting(code = "serviceLevel")
    private String serviceLevel;
    @ObjectSetting(code = "parcelNo")
    private List<String> parcelNo;

    @ObjectSetting(code = "enquirySalesPerson")
    private String salesPerson;
    @ObjectSetting(code = "enquiryProductCategory")
    private String[] productCategories;
    private String productCategory;
    private String productSubCategory;

    @ObjectSetting(code = "paymentStatus")
    private Integer paymentStatus;

    @ObjectSetting(code = "enquirySampleConfirmDate")
    private Date enquirySampleConfirmDate;

    private int dateEditFlag;

    /**
     *
     */
    private Long organizationId;
    /**
     * OrganizationName VARCHAR(200)<br>
     *
     */
    private String organizationName;
    /**
     *
     */
    private String legalEntityCode;
    /**
     *
     */
    private String legalEntityName;

    private Integer buId;
    /**
     *
     */
    private String buCode;
    /**
     *
     */
    private Long labId;
    /**
     * orderLabInfo->Lab
     */
    private String labCode;
    /**
     *
     */
    private String labName;
    /**
     *
     */
    private Integer locationId;
    /**
     *
     */
    private String locationCode;

    @ObjectSetting(code = "responsibleTeamCode")
    private String responsibleTeamCode;

    private String newEnquiryId;

    private Integer vatType;

    @ObjectSetting(code = "enquirySampleReceivingDate")
    private Date enquirySampleReceivingDate;

    private Date generateOrderDate;

    private Integer tat;

    /**
     * 动态 tag value JSON
     */
    private String tagValueJson;

    private List<TagRsp> tagValueSelectDTOS;

    @ObjectSetting(code = "certificateProgram")
    private String certificateProgram;

    private List<ReferenceDTO> references;

    private String kACustomerDeptCode;
    private Integer toDMFlag;

    private BigDecimal subcontractFee;

    private String subcontractFeeCurrency;

    private String trfNo;

    private Integer sameAsApplicantFlag;

    private int toBossFlag;

    private boolean bindStatus;

    private boolean unbindStatus;

    private boolean trfBindStatus;

    private boolean trfUnbindStatus;

    @ObjectSetting(code = "trfSubmissionDate")
    private Date trfSubmissionDate;
    private Integer needToBossFlag;

   private Integer pendingFlag;

   private Integer templateFlag;
}
