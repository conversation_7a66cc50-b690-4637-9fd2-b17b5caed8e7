package com.sgs.gpo.facade.model.todolist;

import com.sgs.framework.core.base.BaseQueryReq;
import lombok.Data;

import java.util.List;

@Data
public class ToDoListQueryReq extends BaseQueryReq {

    /**
     * 待开单类型 Subcontract/Trf
     */
    private String type;
    /**
     * 数据来源 LabCode或者Customer
     */
    private String from;
    /**
     * SubcontractNo/TrfNo
     */
    private String referenceNo;
    private String referenceNoBatch;
    private List<String> referenceNoList;
    private String orderNo;
    /**
     * 发起方联系人名称
     */
    private String contact;
    /**
     * 待开单数据状态
     */
    private String status;
    private List<String> statusList;
    /**
     * 数据创建时间
     */
    private String createdDateStart;
    /**
     * 数据创建时间
     */
    private String createdDateEnd;
    /**
     * DueDate开始
     */
    private String dueDateStart;
    private Long dueDateStartLong;
    /**
     * DueDate结束
     */
    private String dueDateEnd;
    private Long dueDateEndLong;

    private String labCode;

    private Integer pendFlag;

    private List<String> toDoIdList;

    private List<String> objectIdList;
}
