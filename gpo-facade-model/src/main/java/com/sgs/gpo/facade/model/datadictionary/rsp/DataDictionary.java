package com.sgs.gpo.facade.model.datadictionary.rsp;

import com.sgs.framework.core.base.BaseProductLine;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/14 21:23
 */
@Data
public class DataDictionary extends BaseProductLine {
    private String id;
    private Integer systemID;
    private String bUID;
    private String buCode;
    private String languageCode;
    private Integer locationID;
    private String sysKeyGroup;
    private String sysKey;
    private String sysValue;
    private Integer sysKeyDefaultFlag;
    private Integer sequenceNo;
    private String description;
    private Integer activeIndicator;
}
