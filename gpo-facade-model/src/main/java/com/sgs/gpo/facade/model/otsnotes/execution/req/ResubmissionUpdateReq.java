package com.sgs.gpo.facade.model.otsnotes.execution.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.Date;
import java.util.Set;

@Data
public class ResubmissionUpdateReq extends BaseRequest {
    private String resubmissionId;
    private Integer status;
    private String executionType;
    private String executionNo;
    private String reportNo;
    private Date resubmitDate;
    private Date expectDate;
    private String remark;
    private String detail;

    private Set<String> resubmissionIdList;
    private String modifiedBy;
    private Date modifiedDate;
    private Date createdDate;
    private String createdBy;
}
