package com.sgs.gpo.facade.model.otsnotes.testline.rsp;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title: TestLineEditConditionRsp
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/26 15:39
 */
@Data
public class TestLineEditConditionRsp {
    private boolean conditionTypeBlock;
    private boolean procedureCondition;
    private Integer testConditionTypeId;
    private Long conditionTypeBaseId;
    private String testConditionTypeName;
    private Integer testConditionTypeBlockLevel;

    private List<TestLineEditConditionItemRsp> conditionItemInputList;
    private List<TestLineEditConditionItemRsp> conditionItemList;
}
