package com.sgs.gpo.facade.model.scantool.rsp;

import lombok.Data;

@Data
public class ScanToolFile {

    //扫描到的条码/二维码
    private String scanCode;
    //类型 -  OrderNo TestItemNo
    private String scanCodeType;
    //附件类别ID
    private String attchTypeId;
    //附件类别名称
    private String attchTypeName;
    //文件类别ID
    private String fileTypeId;
    //文件类别名称
    private String fileTypeName;
    //文件名
    private String fileName;
    //创建者中文名
    private String createdBy;
    //创建时间
    private String createDate;
    //文件路径
    private String filePath;


    private String sampleNo;

    private String testLine;

    private String testStandard;

    private String fileUrl;





}
