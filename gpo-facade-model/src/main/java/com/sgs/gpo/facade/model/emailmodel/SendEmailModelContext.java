package com.sgs.gpo.facade.model.emailmodel;

import com.sgs.framework.core.base.BaseProductLine;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class SendEmailModelContext extends BaseProductLine {
    private String sgsToken;
    private String from;
    private String to;
    private String sender;
    private String respCs;
    private String respCsName;
    private String lab;
    private String  systemId;
    private String  cc;
    private Map<String, String> generalFields;
    private Map<String, Object> dffFields;
    private EmailAddressDTO emailAddress;
    private List<EmailAttachmentDTO> attachmentList;

}
