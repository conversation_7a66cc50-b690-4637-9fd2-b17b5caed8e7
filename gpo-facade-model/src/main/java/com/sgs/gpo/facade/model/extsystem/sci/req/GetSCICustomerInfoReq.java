package com.sgs.gpo.facade.model.extsystem.sci.req;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.preorder.facade.model.dto.dff.DFFFormRspDTO;
import com.sgs.preorder.facade.model.info.ProductInfo;
import com.sgs.preorder.facade.model.info.ProductSampleInfo;
import com.sgs.preorder.facade.model.rsp.ProductSampleRsp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class GetSCICustomerInfoReq extends BaseRequest {
    @ApiModelProperty(value = "第三方系统对应的ID编号( 1:Customer Portal; 2: SGSMart; 3: Online Booking（OLB）; 4: Semir（森马）; 5: <PERSON><PERSON>（安踏）; 6: 抽检工具; " +
            "7:Shein（希音）;8：SubContract ); 9：FastFish; 10：Camel;11：PeaceBird;12：TIC; 13：LiNing;14：Shein Supplier;10017：UNIQLO")
    private Integer refSystemId;

    @ApiModelProperty("绑定的TrfNo")
    private String trfNo;

    @ApiModelProperty("操作人")
    private String operator;

    @ApiModelProperty("操作时间")
    private String operationTime;

    @ApiModelProperty("要绑定的OrderNo/EnquiryNo")
    private String orderNo;

    @ApiModelProperty("要绑定的对象类型")
    private String objectType;

    private List<ProductSampleRsp> productSampleRsps;

    private boolean checkFlag = false;

    private Map<String, DFFFormRspDTO> sgsMartDffMap;
}
