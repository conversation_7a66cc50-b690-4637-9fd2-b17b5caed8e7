package com.sgs.gpo.facade.model.subcontract.rsp;

import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractBO;
import com.sgs.framework.model.test.testline.v2.TestLineBO;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixBO;
import com.sgs.framework.model.test.testsample.v2.TestSampleBO;
import lombok.Data;

import java.util.List;

@Data
public class SubcontractReportMatrixQueryRsp {

    private String systemId;

    private String langId;

    private List<TestLineBO> testLineList;

    private List<TestMatrixBO> testMatrixList;

    private List<ReportBO> reportList;
}
