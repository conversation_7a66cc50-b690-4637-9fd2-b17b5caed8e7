package com.sgs.gpo.facade.model.trackingtool;

import com.sgs.framework.model.test.testline.TestLineBO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


@Data
public class TrackingToolDataRsp extends TrackingToolBaseDataRsp implements Serializable {
    private String jobNo;
    private String jobStatus;
    private Date labInDate;
    private Date labOutDate;
    private String jobRemark;
    private Date jobExpectedDueDate;

    private String subcontractNo;
    private String subcontractStatus;
    private String subcontractRemark;
    private Date subcontractExpectDueDate;

    private List<TestLineBO> testLineList;



}
