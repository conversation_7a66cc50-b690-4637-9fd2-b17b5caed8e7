package com.sgs.gpo.facade.model.otsnotes.testline.rsp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: TestLineEditCitationRsp
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/26 15:38
 */
@Data
public class TestLineEditCitationRsp {
    @ApiModelProperty(notes = "citationBaseId")
    private Long citationBaseId;
    @ApiModelProperty(notes = "citation Version Id")
    private Integer citationVersionId;
    @ApiModelProperty(notes = "citation Id")
    private Integer citationId;
    @ApiModelProperty(notes = "citation type :2 Regulation  3 Standard")
    private Integer citationType;
    @ApiModelProperty(notes = "citation 的名称")
    private String citationName;
    @ApiModelProperty(notes = "Citation Section Name")
    private String citationFullName;
    @ApiModelProperty(notes = "是否客户指定")
    private boolean clientSpecifiedFlag;
    @ApiModelProperty(notes = "citation Section id")
    private Integer citationSectionId;
    private boolean selected;
}
