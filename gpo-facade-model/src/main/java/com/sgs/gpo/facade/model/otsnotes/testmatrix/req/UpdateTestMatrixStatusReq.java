package com.sgs.gpo.facade.model.otsnotes.testmatrix.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 * @title: UpdateTestMatrixStatusReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/9/1 16:41
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateTestMatrixStatusReq extends BaseRequest {
    private Set<String> testMatrixIdList;
    private Integer matrixStatus;
    private String action;
}
