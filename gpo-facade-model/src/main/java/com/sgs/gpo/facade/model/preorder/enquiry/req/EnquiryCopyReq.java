package com.sgs.gpo.facade.model.preorder.enquiry.req;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.model.order.enquiry.EnquiryIdBO;
import lombok.Data;

import java.util.List;

@Data
public class EnquiryCopyReq extends BaseRequest {
    /**对象信息*/
    private EnquiryIdBO object;
    private String enquiryId;
    private Integer num;
    private Integer copyFcmFlag;
    List<String> sectionTypeList;

    private String regular = "1";

    private Integer copyFcm;

    private String salesPerson;

    UserLabDTO saleUserLabDTO;

}
