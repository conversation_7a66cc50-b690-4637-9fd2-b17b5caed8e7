package com.sgs.gpo.facade.model.preorder.enquiry.req;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel(value = "标签值对象", description = "TagValue")
public class TagValueItemReq {
    private String tagId;
    private String productLineCode;
    private String tagValue;
    private Integer sequence;
    private Boolean isDefault;
    private String id;
    private Byte activeIndicator;
}
