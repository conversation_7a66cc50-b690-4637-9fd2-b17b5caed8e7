package com.sgs.gpo.facade.model.preorder.order.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.Set;

@Data
public class OrderAttachmentQueryReq extends BaseRequest {
    private Set<String> orderIdList;
    private Set<String> reportNoList;
    private Set<String> businessTypeList;

    private Set<String> orderNoList;
    private Set<String> testLineIdList;
    private Set<String> testMatrixIdList;
    private Set<String> sampleNoList;
    private Set<String> jobNoList;
}
