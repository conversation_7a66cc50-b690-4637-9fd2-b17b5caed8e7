package com.sgs.gpo.facade.model.otsnotes.subcontract.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @title: UpdateSubContractExternalReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/12/21 17:16
 */
@Data
public class UpdateSubContractExternalReq extends BaseRequest {
    private String subcontractNo;
    private String externalNo;
    private Date externalOrderDueDate;
    private String externalOrderCs;
    private Integer systemId;
    private Integer subcontractType;
}
