package com.sgs.gpo.facade.model.preorder.enquiry.req;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.core.base.BaseResponse;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @title: EnquiryIdReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2024/1/16 22:05
 */
@Data
public class EnquiryIdReq extends BaseRequest {
    private Set<String> enquiryIdList;
    private Set<String> enquiryNoList;
}
