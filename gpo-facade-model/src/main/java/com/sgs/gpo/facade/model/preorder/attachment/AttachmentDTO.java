package com.sgs.gpo.facade.model.preorder.attachment;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.Date;
import java.util.List;

//Order 附件
@Data
public class AttachmentDTO extends BaseRequest {

	private static final long serialVersionUID = 1L;
	/**文件id*/
	private String ID;
	/**文件id*/
	private String fileID;
	/**订单id*/
	private String generalOrderID;
	private String objectID;
	/**业务类型*/
	/*@QueryParam("businessType")  */
	private String businessType;
	private String cloudID;
	/**模版编号*/
	private String reportNo;
	/**订单号*/
	/*@QueryParam("orderNo")  */
	private String orderNo;
	private String attachmentName;
	private String attachmentType;
	private String suffixes;
	private String path;
	private String thumbnailCloudId;

	private boolean nullObjectId;
	//定义查询来源
	private String source;
	/**
	 * 需要排除的biz type
	 */
	private String excludeBusinessType;

	private String labCode;

	private Date createdDate;
	private String createdBy;
	private Date modifiedDate;
	private String modifiedBy;
	private int activeIndicator;
	private String enquiryId;

	/**
	 * 样品号
	 */
	private String sampleNo;

	/**
	 * 附件类型
	 */
	private Integer photoType;

	/**
	 * 流水号
	 */
	private String suffixNum;

	private Integer toCp;

	private String testLineInstanceId;

	private String testLineItemName;
	private String testStandardName;

	private Integer usedReport;

	private String lastDownloadBy;
	private Date lastDownloadDate;

	private List<String>  objectIds;

	private String size;

}
