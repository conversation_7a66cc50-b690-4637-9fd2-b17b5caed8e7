package com.sgs.gpo.facade.model.dashboard.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;
import  java.util.Date;
import java.util.List;

@Data
public class DashBoardDetailQueryRep extends BaseRequest {

    private String   dashBoardStatus;
    private String   dashBoardType;
    private String   labCode;
    private String   engineer;
    private Boolean dashboard = true;
    private Boolean isPending = false;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date currentDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date startDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date endDate;
    private Boolean delay = false;
    private List<Long> labSectionBaseIdList;
}
