package com.sgs.gpo.facade.model.datadictionary.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/14 18:54
 */
@Data
public class DataDictionarySearchReq extends BaseRequest {

    /**系统ID*/
    private Integer systemId;
    /**业务部门ID*/
    private Integer buId;
    /**业务部门编码*/
    private String buCode;
    /**区域ID*/
    private Integer locationId;
    /**语言ID*/
    private Integer languageId;
    /**字典分组*/
    private List<String> groupList;
    /**禁用*/
    private Boolean disabled;

}
