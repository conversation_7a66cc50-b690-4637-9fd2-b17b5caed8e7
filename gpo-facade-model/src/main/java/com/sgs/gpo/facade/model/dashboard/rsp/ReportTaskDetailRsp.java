package com.sgs.gpo.facade.model.dashboard.rsp;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ReportTaskDetailRsp {

    private String orderId;

    private String orderNo;

    private String extReportNo;

    private String reportNo;

    private Integer reportStatus;

    private String subLab;

    private String tse;

    private String reportDueDate;

    private String timeSlot;

    private String sectionScope;

    private String testCPLRate;

    private String sendDraft;

    private String reportDelayType;

    private Integer isPending;

    private Integer testLineStatus;

    private String tliId;

    private String beenApproved;

    private Long labSectionBaseId;

    private String subContractLabCode;

}
