package com.sgs.gpo.facade.model.preorder.enquiry.req;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.model.enums.CustomerType;
import com.sgs.gpo.facade.model.preorder.annotation.CustomCustomer;
import com.sgs.gpo.facade.model.preorder.annotation.ObjectSetting;
import com.sgs.gpo.facade.model.preorder.attachment.AttachmentDTO;
import lombok.Data;

import java.util.List;

@Data
public class EnquiryCreateReq extends BaseRequest {

    /**
     * lab信息
     */
    private UserLabDTO lab;

    /**
     *Enquiry 主信息
     */
    private EnquiryHeaderDTO headers;

    /**
     * Customer 信息-Subcontract From
     */
    @CustomCustomer(CustomerType.SUBCONTRACT_FROM)
    private EnquiryCustomerDTO subcontractFrom;
    /**
     * Customer 信息-applicant
     */
    @CustomCustomer(CustomerType.Applicant)
    private EnquiryCustomerDTO applicant;

    /**
     * Customer 信息-payer
     */
    @CustomCustomer(CustomerType.Payer)
    private EnquiryCustomerDTO payer;

    /**
     * Customer 信息-buyer
     */
    @CustomCustomer(CustomerType.Buyer)
    private EnquiryCustomerDTO buyer;

    /**
     * Customer 信息-agent
     */
    @CustomCustomer(CustomerType.Agent)
    private EnquiryCustomerDTO agent;

    /**
     * Customer 信息-supplier
     */
    @CustomCustomer(CustomerType.Supplier)
    private EnquiryCustomerDTO supplier;

    /**
     * Customer 信息-manufacture
     */
    @CustomCustomer(CustomerType.Manufacture)
    private EnquiryCustomerDTO manufacture;

    /**
     * Customer 信息-oem
     */
    @CustomCustomer(CustomerType.OEM)
    private EnquiryCustomerDTO oem;


    /**
     * Enquiry ServiceRequirement
     */
    @ObjectSetting
    private EnquiryTestRequestDTO testRequest;
    private Integer languageID;
    /**
     * DFF 信息
     */
    private List<EnquiryProductSampleDTO> productSampleRspList;
    /**
     * 附件信息
     */
    private List<AttachmentDTO> orderAttachmentDTOS;

}
