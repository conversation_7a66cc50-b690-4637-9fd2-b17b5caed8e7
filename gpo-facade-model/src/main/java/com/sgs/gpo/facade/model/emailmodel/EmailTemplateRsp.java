package com.sgs.gpo.facade.model.emailmodel;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class EmailTemplateRsp implements Serializable {

    private String body;
    private String buCode;
    private String countryName;
    private Object customerId;
    private String desc;
    private String id;
    private Boolean isDefault;
    private Boolean isGeneral;
    private Object lab;
    private String languageCode;
    private String locationCode;
    private String name;
    private PreferenceDTO preference;
    private String reportPDFName;
    private String subject;
    private String type;
    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getBuCode() {
        return buCode;
    }

    public void setBuCode(String buCode) {
        this.buCode = buCode;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public Object getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Object customerId) {
        this.customerId = customerId;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Boolean isIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    public Boolean isIsGeneral() {
        return isGeneral;
    }

    public void setIsGeneral(Boolean isGeneral) {
        this.isGeneral = isGeneral;
    }

    public Object getLab() {
        return lab;
    }

    public void setLab(Object lab) {
        this.lab = lab;
    }

    public String getLanguageCode() {
        return languageCode;
    }

    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public PreferenceDTO getPreference() {
        return preference;
    }

    public void setPreference(PreferenceDTO preference) {
        this.preference = preference;
    }

    public String getReportPDFName() {
        return reportPDFName;
    }

    public void setReportPDFName(String reportPDFName) {
        this.reportPDFName = reportPDFName;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    @Data
    public static class PreferenceDTO {
        private Object ccRemark;
        private Object deliveryAttachment;
        private Object emailFrom;
        private Object overallRemark;
        private Object remarkAttachment;
        private Object subjectRemark;
        private Object toRemark;
        private List<String> attachmentType;
        private List<String> bcc;
        private List<String> cc;
        private List<String> to;

        public Object getCcRemark() {
            return ccRemark;
        }

        public void setCcRemark(Object ccRemark) {
            this.ccRemark = ccRemark;
        }

        public Object getDeliveryAttachment() {
            return deliveryAttachment;
        }

        public void setDeliveryAttachment(Object deliveryAttachment) {
            this.deliveryAttachment = deliveryAttachment;
        }

        public Object getEmailFrom() {
            return emailFrom;
        }

        public void setEmailFrom(Object emailFrom) {
            this.emailFrom = emailFrom;
        }

        public Object getOverallRemark() {
            return overallRemark;
        }

        public void setOverallRemark(Object overallRemark) {
            this.overallRemark = overallRemark;
        }

        public Object getRemarkAttachment() {
            return remarkAttachment;
        }

        public void setRemarkAttachment(Object remarkAttachment) {
            this.remarkAttachment = remarkAttachment;
        }

        public Object getSubjectRemark() {
            return subjectRemark;
        }

        public void setSubjectRemark(Object subjectRemark) {
            this.subjectRemark = subjectRemark;
        }

        public Object getToRemark() {
            return toRemark;
        }

        public void setToRemark(Object toRemark) {
            this.toRemark = toRemark;
        }

        public List<String> getAttachmentType() {
            return attachmentType;
        }

        public void setAttachmentType(List<String> attachmentType) {
            this.attachmentType = attachmentType;
        }

        public List<String> getBcc() {
            return bcc;
        }

        public void setBcc(List<String> bcc) {
            this.bcc = bcc;
        }

        public List<String> getCc() {
            return cc;
        }

        public void setCc(List<String> cc) {
            this.cc = cc;
        }

        public List<String> getTo() {
            return to;
        }

        public void setTo(List<String> to) {
            this.to = to;
        }
    }
}
