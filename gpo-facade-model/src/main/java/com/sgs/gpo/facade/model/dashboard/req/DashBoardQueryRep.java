package com.sgs.gpo.facade.model.dashboard.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "仪表盘请求对象", description = "其中LabCode、StartDate、EndDate、CurrentDate都是以后台为准，前台无需传参！")
public class DashBoardQueryRep extends BaseRequest {
    /**当前Lab*/
    @JsonIgnore
    private String labCode;
    @JsonIgnore
    private Integer labId;
    /**开始时间*/
    @JsonIgnore
    private Date startDate;
    /**完成时间*/
    @JsonIgnore
    private Date endDate;
    /**当前时间*/
    private Date currentDate;
    /**工程师*/
    @ApiModelProperty(value = "工程师域账号列表")
    private List<String> engineerList;
    /**实验室部门*/
    @ApiModelProperty(value = "实验室部门列表")
    private List<Long> labSectionBaseIdList;
    /**实验室团队*/
    @ApiModelProperty(value = "实验室团队列表")
    private List<String> labTeamList;
    /**报告状态*/
    @ApiModelProperty(value = "报告状态")
    private Integer reportStatus;
}
