package com.sgs.gpo.facade.model.preorder.enquiry.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
public class EnquiryProductDTO {
    private String id;
    private String enquiryId;
    private String refSampleId;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String productLibraryId;
    @JsonProperty("careLabelId")
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String carelabelInstanceId;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String communicationLogId;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String buyerOrgannization1;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String buyerOrgannization2;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String buyerOrgannization3;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String buyerOrgannizationCode1;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String buyerOrgannizationCode2;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String buyerOrgannizationCode3;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String buyerAliase;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String buyerSourcingOffice;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String countryOfOrigin;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String countryOfDestination;
    private String dFFFormID;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String supplier;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String supplierNo;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String factoryID;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String factoryName;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String firstFpuNo;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String firstPassFpuNo;
    /*@JsonInclude(JsonInclude.Include.NON_DEFAULT)*/
    private String firstTimeApplicationFlag;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String fPUNo;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String fPUReportNo;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String gpuNo;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String lotNo;
    /*@JsonInclude(JsonInclude.Include.NON_DEFAULT)*/
    private Integer noOfSample;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String otherSampleInformation;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String peformanceCode;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String pONo;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String previousReportNo;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String trimReportNo;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String fabricReport;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String productCategory1;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String productCategory2;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String styleNo;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refCode1;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refCode2;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refCode3;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refCode4;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refCode5;
    //add by vincent 2018年7月4日 start
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refCode6;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refCode7;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refCode8;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refCode9;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String refCode10;
    //add by vincent 2018年7月4日 end
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String productColor;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String productDescription;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String productionStage;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String sampleID;
    /*@JsonInclude(JsonInclude.Include.NON_DEFAULT)*/
    private Date sampleReceivedDate;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String ageGroup;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String endUse1;
    //add by vincent 2018年7月4 start
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute1;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute2;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute3;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute4;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute5;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute6;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute7;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute8;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute9;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute10;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute11;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute12;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute13;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute14;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute15;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute16;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute17;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialCustomerAttribute18;
    //add by vincent 2018年7月4 end
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialProductAttribute1;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialProductAttribute2;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialProductAttribute3;
    //add by vincent 2018年7月4日 start
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialProductAttribute4;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialProductAttribute5;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialProductAttribute6;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialProductAttribute7;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialProductAttribute8;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialProductAttribute9;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialProductAttribute10;
    //add by vincent 2018年7月4日 end
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String construction;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String yarnCount;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String threadCount;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String fiberComposition;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String fiberWeight;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String fabricWidth;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String season;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String size;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String specialFinishing;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String collection;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String careLabelFlag;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String careLabel;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String careLabelWording;
    private String headerId;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String productType;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String productItemNo;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String cancelFlag;
    /**
     *
     */
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String createdBy;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private Date createdDate;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String modifiedBy;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private Date modifiedDate;

    private Integer languageID;
    private String languageCode;

    private Integer activeIndicator;

    private String itemNo;
    private String vendorNo;
}
