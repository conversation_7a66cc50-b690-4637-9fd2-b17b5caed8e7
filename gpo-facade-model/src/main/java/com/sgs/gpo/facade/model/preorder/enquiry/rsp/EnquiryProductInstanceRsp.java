package com.sgs.gpo.facade.model.preorder.enquiry.rsp;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 */

@Data
public class EnquiryProductInstanceRsp implements Serializable {
    /**
     * id VARCHAR(36) 必填<br>
     */
    private String id;

    /**
     * enquiry_id VARCHAR(36)<br>
     */
    private String enquiryId;

    /**
     * product_library_id VARCHAR(36)<br>
     */
    private String productLibraryId;

    /**
     * careLabel_instance_id VARCHAR(36)<br>
     */
    private String carelabelInstanceId;

    /**
     * communication_log_id VARCHAR(36)<br>
     * ID,Primary key
     */
    private String communicationLogId;

    /**
     * buyer_organnization1 VARCHAR(200)<br>
     */
    private String buyerOrgannization1;

    /**
     * buyer_organnization2 VARCHAR(200)<br>
     */
    private String buyerOrgannization2;

    /**
     * buyer_organnization3 VARCHAR(200)<br>
     */
    private String buyerOrgannization3;

    /**
     * buyer_organnization_code1 VARCHAR(200)<br>
     */
    private String buyerOrgannizationCode1;

    /**
     * buyer_organnization_code2 VARCHAR(200)<br>
     */
    private String buyerOrgannizationCode2;

    /**
     * buyer_organnization_code3 VARCHAR(200)<br>
     */
    private String buyerOrgannizationCode3;

    /**
     * buyer_aliase VARCHAR(200)<br>
     */
    private String buyerAliase;

    /**
     * buyer_sourcing_office VARCHAR(200)<br>
     */
    private String buyerSourcingOffice;

    /**
     * country_of_origin VARCHAR(200)<br>
     */
    private String countryOfOrigin;

    /**
     * country_of_destination VARCHAR(500)<br>
     */
    private String countryOfDestination;

    /**
     * dff_form_id VARCHAR(36)<br>
     */
    private String dffFormId;

    /**
     * supplier VARCHAR(250)<br>
     */
    private String supplier;

    /**
     * supplier_no VARCHAR(500)<br>
     */
    private String supplierNo;

    /**
     * factory_id VARCHAR(200)<br>
     */
    private String factoryId;

    /**
     * factory_name VARCHAR(250)<br>
     */
    private String factoryName;

    /**
     * first_fpu_no VARCHAR(200)<br>
     */
    private String firstFpuNo;

    /**
     * first_pass_fpu_no VARCHAR(200)<br>
     */
    private String firstPassFpuNo;

    /**
     * first_time_application_flag VARCHAR(50)<br>
     */
    private String firstTimeApplicationFlag;

    /**
     * fpu_no VARCHAR(200)<br>
     */
    private String fpuNo;

    /**
     * fpu_report_no VARCHAR(200)<br>
     */
    private String fpuReportNo;

    /**
     * gpu_no VARCHAR(200)<br>
     */
    private String gpuNo;

    /**
     * lot_no VARCHAR(1000)<br>
     */
    private String lotNo;

    /**
     * no_of_sample INTEGER(10)<br>
     */
    private Integer noOfSample;

    /**
     * peformance_code VARCHAR(500)<br>
     */
    private String peformanceCode;

    /**
     * po_no VARCHAR(1500)<br>
     */
    private String poNo;

    /**
     * previous_report_no VARCHAR(200)<br>
     */
    private String previousReportNo;

    /**
     * trim_report_no VARCHAR(1000)<br>
     */
    private String trimReportNo;

    /**
     * fabric_report VARCHAR(1000)<br>
     */
    private String fabricReport;

    /**
     * product_category1 VARCHAR(500)<br>
     */
    private String productCategory1;

    /**
     * product_category2 VARCHAR(500)<br>
     */
    private String productCategory2;

    /**
     * style_no VARCHAR(1500)<br>
     */
    private String styleNo;

    /**
     * product_color VARCHAR(500)<br>
     */
    private String productColor;

    /**
     * production_stage VARCHAR(500)<br>
     */
    private String productionStage;

    /**
     * sample_id VARCHAR(36)<br>
     */
    private String sampleId;

    /**
     * sample_received_date TIMESTAMP(19)<br>
     */
    private Date sampleReceivedDate;

    /**
     * age_group VARCHAR(200)<br>
     */
    private String ageGroup;

    /**
     * end_use1 VARCHAR(500)<br>
     */
    private String endUse1;

    /**
     * construction VARCHAR(500)<br>
     */
    private String construction;

    /**
     * yarn_count VARCHAR(500)<br>
     */
    private String yarnCount;

    /**
     * thread_count VARCHAR(500)<br>
     */
    private String threadCount;

    /**
     * fiber_composition VARCHAR(500)<br>
     */
    private String fiberComposition;

    /**
     * fiber_weight VARCHAR(500)<br>
     */
    private String fiberWeight;

    /**
     * fabric_width VARCHAR(500)<br>
     */
    private String fabricWidth;

    /**
     * season VARCHAR(500)<br>
     */
    private String season;

    /**
     * size VARCHAR(200)<br>
     */
    private String size;

    /**
     * special_finishing VARCHAR(500)<br>
     */
    private String specialFinishing;

    /**
     * collection VARCHAR(500)<br>
     */
    private String collection;

    /**
     * care_label_flag VARCHAR(50)<br>
     */
    private String careLabelFlag;

    /**
     * care_label VARCHAR(30)<br>
     */
    private String careLabel;

    /**
     * care_label_wording VARCHAR(500)<br>
     */
    private String careLabelWording;

    /**
     * header_id VARCHAR(50)<br>
     */
    private String headerId;

    /**
     * product_type VARCHAR(500)<br>
     */
    private String productType;

    /**
     * product_item_no VARCHAR(30)<br>
     */
    private String productItemNo;

    /**
     * cancel_flag BIT(1)<br>
     */
    private Boolean cancelFlag;

    /**
     * ref_sample_id VARCHAR(36)<br>
     * record sampleID,when have multilanguage,same sample should have the same RefSampleID
     */
    private String refSampleId;

    /**
     * language_id INTEGER(10) 默认值[1]<br>
     */
    private Integer languageId;

    /**
     * created_date TIMESTAMP(19)<br>
     * CreatedDate
     */
    private Date createdDate;

    /**
     * modified_by VARCHAR(50)<br>
     * ModifiedBy
     */
    private String modifiedBy;

    /**
     * active_indicator INTEGER(10) 默认值[1] 必填<br>
     * 0: inactive, 1: active
     */
    private Integer activeIndicator;

    /**
     * created_by VARCHAR(50)<br>
     * CreatedBy
     */
    private String createdBy;

    /**
     * modified_date TIMESTAMP(19)<br>
     * ModifitedDate
     */
    private Date modifiedDate;

    /**
     * Vendor_No VARCHAR(500)<br>
     */
    private String vendorNo;

    /**
     * other_sample_information LONGVARCHAR(65535)<br>
     */
    private String otherSampleInformation;

    /**
     * ref_code1 LONGVARCHAR(65535)<br>
     */
    private String refCode1;

    /**
     * ref_code2 LONGVARCHAR(65535)<br>
     */
    private String refCode2;

    /**
     * ref_code3 LONGVARCHAR(65535)<br>
     */
    private String refCode3;

    /**
     * ref_code4 LONGVARCHAR(65535)<br>
     */
    private String refCode4;

    /**
     * ref_code5 LONGVARCHAR(65535)<br>
     */
    private String refCode5;

    /**
     * ref_code6 LONGVARCHAR(65535)<br>
     */
    private String refCode6;

    /**
     * ref_code7 LONGVARCHAR(65535)<br>
     */
    private String refCode7;

    /**
     * ref_code8 LONGVARCHAR(65535)<br>
     */
    private String refCode8;

    /**
     * ref_code9 LONGVARCHAR(65535)<br>
     */
    private String refCode9;

    /**
     * ref_code10 LONGVARCHAR(65535)<br>
     */
    private String refCode10;

    /**
     * product_description LONGVARCHAR(65535)<br>
     */
    private String productDescription;

    /**
     * special_customer_attribute1 LONGVARCHAR(65535)<br>
     */
    private String specialCustomerAttribute1;

    /**
     * special_customer_attribute2 LONGVARCHAR(65535)<br>
     */
    private String specialCustomerAttribute2;

    /**
     * special_customer_attribute3 LONGVARCHAR(65535)<br>
     */
    private String specialCustomerAttribute3;

    /**
     * special_customer_attribute4 LONGVARCHAR(65535)<br>
     */
    private String specialCustomerAttribute4;

    /**
     * special_customer_attribute5 LONGVARCHAR(65535)<br>
     */
    private String specialCustomerAttribute5;

    /**
     * special_customer_attribute6 LONGVARCHAR(65535)<br>
     */
    private String specialCustomerAttribute6;

    /**
     * special_customer_attribute7 LONGVARCHAR(65535)<br>
     */
    private String specialCustomerAttribute7;

    /**
     * special_customer_attribute8 LONGVARCHAR(65535)<br>
     */
    private String specialCustomerAttribute8;

    /**
     * special_customer_attribute9 LONGVARCHAR(65535)<br>
     */
    private String specialCustomerAttribute9;

    /**
     * special_customer_attribute10 LONGVARCHAR(65535)<br>
     */
    private String specialCustomerAttribute10;

    /**
     * special_customer_attribute11 LONGVARCHAR(65535)<br>
     */
    private String specialCustomerAttribute11;

    /**
     * special_customer_attribute12 LONGVARCHAR(65535)<br>
     */
    private String specialCustomerAttribute12;

    /**
     * special_customer_attribute13 LONGVARCHAR(65535)<br>
     */
    private String specialCustomerAttribute13;

    /**
     * special_customer_attribute14 LONGVARCHAR(65535)<br>
     */
    private String specialCustomerAttribute14;

    /**
     * special_customer_attribute15 LONGVARCHAR(65535)<br>
     */
    private String specialCustomerAttribute15;

    /**
     * special_customer_attribute16 LONGVARCHAR(65535)<br>
     */
    private String specialCustomerAttribute16;

    /**
     * special_customer_attribute17 LONGVARCHAR(65535)<br>
     */
    private String specialCustomerAttribute17;

    /**
     * special_customer_attribute18 LONGVARCHAR(65535)<br>
     */
    private String specialCustomerAttribute18;

    /**
     * special_product_attribute1 LONGVARCHAR(65535)<br>
     */
    private String specialProductAttribute1;

    /**
     * special_product_attribute2 LONGVARCHAR(65535)<br>
     */
    private String specialProductAttribute2;

    /**
     * special_product_attribute3 LONGVARCHAR(65535)<br>
     */
    private String specialProductAttribute3;

    /**
     * special_product_attribute4 LONGVARCHAR(65535)<br>
     */
    private String specialProductAttribute4;

    /**
     * special_product_attribute5 LONGVARCHAR(65535)<br>
     */
    private String specialProductAttribute5;

    /**
     * special_product_attribute6 LONGVARCHAR(65535)<br>
     */
    private String specialProductAttribute6;

    /**
     * special_product_attribute7 LONGVARCHAR(65535)<br>
     */
    private String specialProductAttribute7;

    /**
     * special_product_attribute8 LONGVARCHAR(65535)<br>
     */
    private String specialProductAttribute8;

    /**
     * special_product_attribute9 LONGVARCHAR(65535)<br>
     */
    private String specialProductAttribute9;

    /**
     * special_product_attribute10 LONGVARCHAR(65535)<br>
     */
    private String specialProductAttribute10;

    /**
     * Item_No LONGVARCHAR(65535)<br>
     */
    private String itemNo;

}
