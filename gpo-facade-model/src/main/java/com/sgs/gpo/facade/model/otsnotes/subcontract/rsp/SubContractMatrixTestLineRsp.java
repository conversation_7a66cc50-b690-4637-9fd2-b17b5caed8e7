package com.sgs.gpo.facade.model.otsnotes.subcontract.rsp;

import com.sgs.framework.model.test.testline.v2.TestLineBO;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixBO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SubContractMatrixTestLineRsp implements Serializable {
    private String orderId;
    List<TestMatrixBO> matrixList;
    List<TestLineBO> testLineList;
}
