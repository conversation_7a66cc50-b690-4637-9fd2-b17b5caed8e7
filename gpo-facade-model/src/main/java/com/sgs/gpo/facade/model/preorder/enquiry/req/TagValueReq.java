package com.sgs.gpo.facade.model.preorder.enquiry.req;

import com.sgs.framework.core.base.BaseProductLine;
import lombok.Data;

import java.util.List;

@Data
public class TagValueReq extends BaseProductLine {
    private String tagId;
    private String tagName;
    private Boolean isHand;
    private Boolean isCheck;
    private Boolean isSave;
    private Boolean isRequired;
    private List<TagValueItemReq> tagValues;
    private List<String> selectedValue;
}
