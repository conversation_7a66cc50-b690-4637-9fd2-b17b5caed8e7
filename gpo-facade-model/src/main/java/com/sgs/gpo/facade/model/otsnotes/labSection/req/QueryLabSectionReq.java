package com.sgs.gpo.facade.model.otsnotes.labSection.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @title: LabSectionQueryReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/9/11 14:02
 */
@Data
public class QueryLabSectionReq extends BaseRequest {
    private Set<String> orderNoList;
    private Set<String> reportNoList;
    private Set<String> reportIdList;
    private Set<String> testLineInstanceIdList;
}
