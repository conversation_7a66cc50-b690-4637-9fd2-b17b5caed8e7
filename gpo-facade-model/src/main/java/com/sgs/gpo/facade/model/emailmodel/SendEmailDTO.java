package com.sgs.gpo.facade.model.emailmodel;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
public class SendEmailDTO implements Serializable {

	private String sgsToken;
	private String systemId;
	private String from;
	private String lab;
	private String to;
	private String cc;
	private String subject;
	private String bcc;
	private String body;
	private List<Map<String,String>> attachmentList;
}
