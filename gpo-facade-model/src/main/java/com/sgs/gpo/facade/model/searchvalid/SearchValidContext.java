package com.sgs.gpo.facade.model.searchvalid;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.common.object.busetting.attribute.ObjectTemplateAttributeBO;

/**
 * <AUTHOR>
 * @title: SearchValidContext
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/4 16:00
 */
public class SearchValidContext<Input extends BaseRequest> extends BaseContext<Input, ObjectTemplateAttributeBO> {

}
