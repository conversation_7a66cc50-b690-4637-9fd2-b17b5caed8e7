package com.sgs.gpo.facade.model.otsnotes.testline.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @title: QueryTestConditionReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/1 20:24
 */
@Data
public class QueryTestConditionGroupReq extends BaseRequest {
    private Set<String> testLineInstanceIdList;
    private Set<String> TestConditionGroupIdList;
    private String orderId;
    private String testLineInstanceId;
    private String testSampleId;
}
