package com.sgs.gpo.facade.model.subcontract.req;

import com.sgs.framework.core.base.BaseQueryReq;
import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractIdBO;
import lombok.Data;

import java.util.Set;

@Data
public class SubcontractQueryReq extends BaseQueryReq<SubcontractIdBO> {

    //TODO 逐步去掉仅保留orderNoList
    private String orderNo;

    //TODO 逐步去掉仅保留subcontractIdList
    private String subcontractId;

    private Set<String> orderNoList;

    private Set<String> subcontractIdList;

    private Set<String> subcontractNoList;

    private String blockTops;

    /**Subcontract 关联的测试项集合*/
    private Set<String> testLineInstanceIdList;

}
