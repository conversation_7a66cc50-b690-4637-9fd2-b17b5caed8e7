package com.sgs.gpo.facade.model.notifyconfig.req;

import com.sgs.framework.core.base.BaseQueryReq;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
public class NotifyConfigQueryReq extends BaseQueryReq {
    private Set<String> objectNoList;
    private String objectType;
    private String eventType;
    private List<String> eventTypeList;
    private Date notifyDateStart;
    private Date notifyDateEnd;
    private Integer activeIndicator;
    private String buCode;

}
