package com.sgs.gpo.facade.model.preorder.enquiry.req;

import com.sgs.framework.core.base.BaseQueryReq;
import com.sgs.framework.model.annotation.SearchValidate;
import lombok.Data;

import java.util.List;

@Data
public class EnquiryQueryReq extends BaseQueryReq {
    /**
     * 询价单号
     */
    @SearchValidate(buSettingCode = "enquiryNo")
    private String enquiryNo;
    @SearchValidate(buSettingCode = "enquiryNoBatch")
    private List<String> enquiryNoList;
    private List<String> enquiryIdList;
    @SearchValidate(buSettingCode = "enquiryNoBatch")
    private String  enquiryNoBatch;
    /**
     * 询价单状态
     */
    @SearchValidate(buSettingCode = "enquiryStatus")
    private Integer enquiryStatus;
    /**
     * 申请人
     */
    @SearchValidate(buSettingCode = "applicant")
    private CustomerReq applicant;
    /**
     * 买方
     */
    @SearchValidate(buSettingCode = "buyer")
    private CustomerReq buyer;

    /**
     * 付款人
     */
    @SearchValidate(buSettingCode = "payer")
    private CustomerReq payer;

    @SearchValidate(buSettingCode = "trfNo")
    private String trfNo;

    @SearchValidate(buSettingCode = "serviceItem")
    private List<String> serviceItem;
    private List<ServiceItemReq> serviceItemList;

    @SearchValidate(buSettingCode = "sampleInfo")
    private String sampleInfo;

    @SearchValidate(buSettingCode = "createdDate")
    private String createdDateStart;
    @SearchValidate(buSettingCode = "createdDate")
    private String createdDateEnd;

    @SearchValidate(buSettingCode = "createdBy")
    private String createdBy;
    @SearchValidate(buSettingCode = "cSName")
    private String cSName;
    @SearchValidate(buSettingCode = "csa")
    private String csa;
    /**
     * 参考单号
     */
    @SearchValidate(buSettingCode = "referenceEnquiryNo")
    private String referenceEnquiryNo;
    @SearchValidate(buSettingCode = "salesName")
    private String salesName;
    @SearchValidate(buSettingCode = "salesNameList")
    private List<String> salesNameList;
    @SearchValidate(buSettingCode = "enquiryType")
    private String enquiryType;
    @SearchValidate(buSettingCode = "productCategory")
    private String productCategory;
    private String productSubCategory;
    @SearchValidate(buSettingCode = "remark")
    private String remark;

    @SearchValidate(buSettingCode = "subcontractOrderNo")
    private String subcontractOrderNo;
    private Integer cancelStatus;
    // 用于json查询结果
    private List<TagValueReq> tagValueSelectDTOS;
    @SearchValidate(buSettingCode = "labCodeList")
    private List<String> labCodeList;
    private String regionAccount;
    private String[] productCategories;
    private boolean salesEnquiry = false;

    private Integer templateFlag;


}
