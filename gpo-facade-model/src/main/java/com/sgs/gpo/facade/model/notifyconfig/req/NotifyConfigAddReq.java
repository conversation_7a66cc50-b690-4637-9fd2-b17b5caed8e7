package com.sgs.gpo.facade.model.notifyconfig.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.Date;

/**
 * 添加通知配置
 */
@Data
public class NotifyConfigAddReq extends BaseRequest {
    private String objectId;
    private String objectNo;
    private String objectType;
    private String eventType;
    private Integer labId;
    private String labCode;
    private Integer buId;
    private String buCode;
    private Integer locationId;
    private String locationCode;
    private Date nextNotifyDate;
    private Integer notifyCount;
    private Integer maxNotifyCount;
    private Integer retryCount;
    private Integer activeIndicator;
    private String notifyParams;
    private String remark;
    private Boolean isCalculateNotifyDate=false;

}
