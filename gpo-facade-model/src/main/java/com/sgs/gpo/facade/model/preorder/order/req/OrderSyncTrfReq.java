package com.sgs.gpo.facade.model.preorder.order.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.Set;

@Data
public class OrderSyncTrfReq extends BaseRequest {
    private Set<String> orderNoList;
    private Set<String> reportNoList;
    private String syncType;
    private boolean cpToSgsMart = false;
    private Set<String> buyerGroupCodeList;
    private Set<Long> bossNumberList;
    private String eventTypeName;
    private boolean checkExistRD = true;
}
