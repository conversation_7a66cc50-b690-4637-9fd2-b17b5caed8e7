package com.sgs.gpo.facade.model.otsnotes.testline.req;

import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: ConditionSubItemUpdateReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/28 15:57
 */
@Data
@ApiModel(value = "ConditionSubItemUpdateReq",description = "conditionValue数据")
public class ConditionSubItemUpdateReq extends BaseRequest {
    @ApiModelProperty(notes = "conditionBaseId")
    private Long conditionBaseId;
    @ApiModelProperty(notes = "testConditionId")
    private Integer testConditionId;
    @ApiModelProperty(notes = "testConditionName")
    private String testConditionName;
    @ApiModelProperty(notes = "testConditionDesc")
    private String testConditionDesc;
    @ApiModelProperty(notes = "testConditionValue")
    private String testConditionValue;

    @ApiModelProperty(notes = "ClientSpecified default 0, 1为手动录入")
    private Integer clientSpecified;
    @ApiModelProperty(notes = "testConditionSeq")
    private Integer testConditionSeq;
    @ApiModelProperty(notes = "defaultCondition")
    private Boolean defaultCondition;
    @ApiModelProperty(notes = "DefaultBy,判断 默认值来源   1:From TestLine;2:From Pp")
    private Integer defaultBy;
    @ApiModelProperty(notes = "productCondition")
    private Integer productCondition;
}
