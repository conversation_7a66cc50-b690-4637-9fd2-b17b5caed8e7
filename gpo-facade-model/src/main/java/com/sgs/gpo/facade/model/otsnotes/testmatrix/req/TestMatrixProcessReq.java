package com.sgs.gpo.facade.model.otsnotes.testmatrix.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @title: TestMatrixProcessReq
 * @projectName gpo-micro-service
 * @description:
 * @date 2023/8/30 15:56
 */
@Data
public class TestMatrixProcessReq extends BaseRequest {
    private Set<String> testMatrixIdList;
    private String action;
    private Integer currentMatrixStatus;
    private Integer newMatrixStatus;
    private Set<String> testLineInstanceIdList;
    private String entryMode;
    private boolean needStatusControl = true;
    /**
     * Matrix和TestLine的状态变化会相互联动，此参数用来控制是否相互影响
     * */
    private boolean triggerTestLine = true;
}

