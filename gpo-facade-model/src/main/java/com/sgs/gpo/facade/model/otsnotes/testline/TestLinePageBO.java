package com.sgs.gpo.facade.model.otsnotes.testline;

import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.rsp.TestMatrixSampleRsp;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @title: TestLinePageBO
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/10 14:47
 */
@Data
public class TestLinePageBO extends TestLineBO {
    private String csName;
    private String amountText;
    private BigDecimal amount;
    private String subcontractLabCode;
    private Boolean havingAttachment = false;
    private String displayCitationName;
    private String displayLabSectionName;
    private String displaySubcontractLabCode;
    private String displayPpNo;
    private String sample;
    private Integer orderStatus;
    private List<TestMatrixSampleRsp> testMatrixSampleList;
}
