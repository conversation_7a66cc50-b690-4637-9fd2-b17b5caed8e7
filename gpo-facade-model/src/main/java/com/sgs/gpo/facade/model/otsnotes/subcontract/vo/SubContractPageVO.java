package com.sgs.gpo.facade.model.otsnotes.subcontract.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sgs.gpo.facade.model.otsnotes.subreport.vo.SubReportVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @title: SubContractListVO
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/7 17:39
 */
@Data
public class SubContractPageVO implements Serializable {
    private String id;
    private String subContractNo;
    private String orderNo;
    private String generalOrderInstanceId;
    private String orderId;
    private String externalOrderNo;
    private String subContractLabCode;
    private String subContractLabName;
    private Integer subContractLabId;
    private String referenceNo;
    private String referenceCs;
    private BigDecimal referenceAmount;
    private String referenceCurrency;
    private String formatReferenceAmount;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", locale = "zh", timezone = "GMT+8")
    private String referenceDueDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", locale = "zh", timezone = "GMT+8")
    private String referenceConfirmDate;
    private String responsibleCs;
    private Integer subContractStatus;
    private Integer syncStatus;
    private Integer operationModel;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", locale = "zh", timezone = "GMT+8")
    private Date subContractExpectDueDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", locale = "zh", timezone = "GMT+8")
    private Date startDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", locale = "zh", timezone = "GMT+8")
    private Date completedDate;
    private String remark;
    private BigDecimal subContractFee;
    private String subContractFeeCurrency;
    private String formatSubContractFee;
    private List<SubReportVO> subReportList;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", locale = "zh", timezone = "GMT+8")
    private Date createdDate;
    private BigDecimal tlTotalAmount;
    private String tlTotalAmountDisplay = "Unable to calculate";
    // 订单上的reportLanguage
    private Integer reportLanguage;
    private Integer SubContractOrder;
    private Integer stylesQty;
    private Integer piecesQty;
    private Integer partsQty;

}
