package com.sgs.gpo.facade.model.preorder.enquiry.rsp;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Data
public class EnquiryListRsp implements Serializable {
    /**
     * 主键
     */
    private String id;
    /**
     * 询价单号
     */
    private String enquiryNo;
    /**
     * 询价单状态
     */
    private Integer enquiryStatus;
    /**
     * 询价单对应订单号
     */
    private String orderNo;
    /**
     * 申请人
     */
    private String applicantCn;
    private String applicantEn;
    /**
     * 买方
     */
    private String buyerCn;
    private String buyerEn;
    /**
     * 付款人
     */
    private String payerCn;
    private String payerEn;


    private  String  trfNo;
    private String subcontractOrderNo;
    /**
     * 创建时间
     */
    private Date createdDateShow;
    private String createdDateStart;
    private String createdDateEnd;
    /**
     * 创建人
     */
    private String createdBy;
    private List<EnquiryOrderRsp> orderDTOS;
    private String orderNoShow;

    /**
     * 参考单号
     */
    private String referenceEnquiryNo;
    private String salesName;
    private List<String> salesNameList;
    private String enquiryType;
    private String productCategory;
    private String remark;
    private String cSName;
    private String csa;
    /**
     *  Bu Code
     */
    private String buCode;

    private String labCode;
    private List<String> labCodeList;
    private Integer page;
    private Integer rows;


    // 动态生成的tag条件
    private String tagValues;
    private String currencyCode;
    private BigDecimal netAmount;
    private BigDecimal afterTaxAmount;
    private BigDecimal finalAmount;

    // 用于前端table动态显示
    private HashMap<String,String> tagMaps;

    private List<QuotationHeadRsp> quotationHeadDTOS;
    //不可随意修改该属性名称
    private EnquiryProductInstanceRsp dff;
    private EnquiryProductInstanceRsp dffCn;
    private Integer templateFlag;


}
