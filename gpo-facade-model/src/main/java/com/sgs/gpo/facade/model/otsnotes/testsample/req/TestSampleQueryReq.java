package com.sgs.gpo.facade.model.otsnotes.testsample.req;

import com.sgs.framework.core.base.BaseQueryReq;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.model.test.testsample.v2.TestSampleIdBO;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/7/3 15:44
 */
@Data
public class TestSampleQueryReq extends BaseQueryReq<TestSampleIdBO> {

    private String orderId;
    private String orderNo;
    private Set<String> testSampleInstanceIdList;
    private Set<String> testLineInstanceId;
    // Sample对应的categoryList
    private List<String> categoryList;
    private Set<String> orderNoList;

}
