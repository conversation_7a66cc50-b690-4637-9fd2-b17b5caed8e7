package com.sgs.gpo.facade.model.preorder.enquiry.req;

import com.sgs.gpo.facade.model.preorder.annotation.ObjectSetting;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class EnquiryTestRequestDTO {

    private String id;

    /**
     * EnquiryID VARCHAR(36)<br>
     * FK TB_Order
     */
    private String enquiryId;

    /**
     * EvaluationBasis VARCHAR(250)<br>
     * Judgement Principle
     */
    private String evaluationBasis;

    /**
     * OtherRequirements VARCHAR(500)<br>
     * Other Requirements
     */
    private String otherRequirements;

    /**
     * Qualification VARCHAR(250)<br>
     * Qualification flags CMA/CMAF flag:  1-Non; 2-CNAS;3-CMA Or CMAF; 4-CANS,CMA Or CMAF,   relate to the TB_SystemConstants  SysCode
     */
    private String qualification;

    /**
     * ReoprtDate TIMESTAMP(19)<br>
     * the expect Report receive Date
     */
    private Date reportDate;

    /**
     * ReportLanguage VARCHAR(50)<br>
     * Report Language:1-English report (be sure to complete information in English);2-Chinese report ( charge 200 yuan), these two types can be selected both.,   relate to the TB_SystemConstants  SysCode
     */
    @ObjectSetting(code = "reportLanguage")
    private String reportLanguage;

    /**
     * ReportManner INTEGER(10)<br>
     * Ways of reporting:1-A Report Containing One Sample; 2-A Report Containing Multiple Samples; 3-Test Report Template(Need Product Standards ),   relate to the TB_SystemConstants  SysCode
     */
    private Integer reportManner;

    /**
     * ReportType VARCHAR(50)<br>
     * Report Type: 1-Electronic Report; 2-Paper Reports, these two types can be selected both.,   relate to the TB_SystemConstants  SysCode
     */
    private String reportType;

    /**
     * ResultJudgingFlag TINYINT(3)<br>
     * Result Judging0-no; 1-yes
     */
    private Integer resultJudgingFlag;

    /**
     * ServiceType INTEGER(10)<br>
     * Service Type:1-Conventional tests (default service);2-Urgent Service,   relate to the TB_SystemConstants  SysCode
     */
    private Integer serviceType;

    /**
     * DisplaySupplierFlag TINYINT(3)<br>
     * Display supplier or not:0-no, 1-yes
     */
    private Integer displaySupplierFlag;

    /**
     * CommentFlag TINYINT(3)<br>
     */
    @ObjectSetting(code = "commentFlag")
    private Integer commentFlag;

    /**
     * HardCopyFlag TINYINT(3)<br>
     */
    @ObjectSetting(code = "hardCopyFlag")
    private Integer hardCopyFlag;

    /**
     * ChineseReportFlag TINYINT(3)<br>
     */
    private Integer chineseReportFlag;

    /**
     * TakePhotoFlag TINYINT(3)<br>
     */
    @ObjectSetting(code = "takePhotoFlag")
    private Integer takePhotoFlag;

    /**
     * ConfirmCoverPageFlag TINYINT(3)<br>
     */
    @ObjectSetting(code = "confirmCoverPageFlag")
    private Integer confirmCoverPageFlag;

    /**
     * PackageIndicator VARCHAR(10)<br>
     * full package / partial package / incomplete (single choice),
     * F- full package
     * P- partial package
     * I-  incomplete
     * This code & display value  should be configed in TB_SystemConstans.
     */
    private String packageIndicator;

    /**
     * TakePhotoRemark VARCHAR(500)<br>
     */
    @ObjectSetting(code = "takePhotoRemark")
    private String takePhotoRemark;

    /**
     * ReturnResidueSampleFlag TINYINT(3)<br>
     * need Return Residue Sample ,  0-no; 1-yes
     */
    private Integer returnResidueSampleFlag;

    /**
     * ReturnTestedSampleFlag TINYINT(3)<br>
     * need Return Tested Sample ,  0-no; 1-yes
     */
    private Integer returnTestedSampleFlag;

    /**
     * ReturnResidueSampleRemark VARCHAR(500)<br>
     * remark of Return Residue Sample
     */
    private String returnResidueSampleRemark;

    /**
     * ReturnTestedSampleRemark VARCHAR(500)<br>
     * remark of Return Tested Sample
     */
    private String returnTestedSampleRemark;

    /**
     * ReportAccreditationNeededFlag TINYINT(3)<br>
     * need Accreditation info on report  ,  0-no; 1-yes
     */
    private Integer reportAccreditationNeededFlag;

    /**
     * HardCopyToApplicantFlag TINYINT(3)<br>
     * the flag to indicate the report hard copy need send to order applicant, 0 - N, 1 - Yes
     */
    private Integer hardCopyToApplicantFlag;

    /**
     * HardCopyToPayertFlag TINYINT(3)<br>
     * the flag to indicate the report hard copy need send to order payer, 0 - N, 1 - Yes
     */
    private Integer hardCopyToPayertFlag;

    /**
     * HardCopyToOther VARCHAR(500)<br>
     * other address which will send report hard copy
     */
    private String hardCopyToOther;

    /**
     * SoftCopyToApplicantFlag TINYINT(3)<br>
     * the flag to indicate the report soft  copy need send to order applicnt, 0 - N, 1 - Yes
     */
    private Integer softCopyToApplicantFlag;

    /**
     * SoftCopyToPayerFlag TINYINT(3)<br>
     * the flag to indicate the report soft  copy need send to order payer, 0 - N, 1 - Yes
     */
    private Integer softCopyToPayerFlag;

    /**
     * SoftCopyToOther VARCHAR(500)<br>
     * other address which will send report soft copy
     */
    private String softCopyToOther;

    /**
     * HtmlString VARCHAR(10000)<br>
     */
    private String htmlString;

    /**
     * PdfReportSecurity TINYINT(3) 默认值[0]<br>
     */
    private Integer pdfReportSecurity;

    /**
     * PaymentRemark VARCHAR(500)<br>
     */
    @ObjectSetting(code = "paymentRemark")
    private String paymentRemark;
    /**
     *
     */
    private String receiverId;
    /**
     * 字段在表 tb_order_report_receiver
     */
    private String reportHeader;
    /**
     * 字段在表 tb_order_report_receiver 对应页面reportAddress
     */
    private String reportDeliveredTo;

    /**
     * qualification_type VARCHAR(50)<br>
     */
    private String qualificationType;

    /**
     * draft_report_required TINYINT(3)<br>
     */
    @ObjectSetting(code = "isDraftReportRequired")
    private Integer draftReportRequired;

    /**
     * proforma_invoice TINYINT(3)<br>
     */
    @ObjectSetting(code = "isProformaInvoice")
    private Integer isProformaInvoice;

    /**
     * liquid_test_sample TINYINT(3)<br>
     */
    @ObjectSetting(code = "liquidTestSample")
    private Integer liquidTestSample;

    /**
     * vat_type TINYINT(3)<br>
     */
    @ObjectSetting(code = "vatType")
    private Integer vatType;

    /**
     * PhotoRequest VARCHAR(250)<br>
     * Photo Request
     */
    @ObjectSetting(code = "photoRequest")
    private String photoRequest;

    /**
     * ReportRequirement VARCHAR(250)<br>
     * Report Requirement
     */
    @ObjectSetting(code = "reportRequirement")
    private String reportRequirement;

    /**
     * NeedConclusion TINYINT(3)<br>
     * Need Conclusion， 0-no; 1-yes
     */
    @ObjectSetting(code = "needConclusion")
    private Integer needConclusion;

    /**
     * HardCopyReportDeliverWay VARCHAR(500)<br>
     * HardCopy Report Deliver Way
     */
    @ObjectSetting(code = "hardCopyReportDeliverWay")
    private String hardCopyReportDeliverWay;

    /**
     * InvoiceDeliverWay VARCHAR(500)<br>
     * Invoice Deliver Way
     */
    @ObjectSetting(code = "invoiceDeliverWay")
    private String invoiceDeliverWay;

    /**
     * QrcodeFlag VARCHAR(500)<br>
     * QrcodeFlag
     */
    @ObjectSetting(code = "qrcodeFlag")
    private String qrcodeFlag;

    /**
     * SealFlag Integer<br>
     * 是否打印骑缝章
     */
    @ObjectSetting(code = "sealFlag")
    private Integer sealFlag;

    /**
     * SealCode VARCHAR(50)<br>
     */
    private String sealCode;

    /**
     * reportInfo锁 0-未锁定 1-锁定
     */
    private Integer reportLock;

    /**
     * SampleSaveDuration INTEGER(10)<br>
     * 样品保存天数
     */
    private Integer sampleSaveDuration;

    /**
     * General GB
     *
     * @return
     */
    private String businessProjectType;

    private List<EnquiryTestRequestContactDTO> testRequestContacts;

}
