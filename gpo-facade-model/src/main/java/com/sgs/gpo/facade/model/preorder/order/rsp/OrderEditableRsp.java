package com.sgs.gpo.facade.model.preorder.order.rsp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @title: OrderEditableRsp
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/26 12:59
 */
@NoArgsConstructor
@Data
public class OrderEditableRsp {
    @JsonProperty("gpoOrderNo")
    private String gpoOrderNo;
    @JsonProperty("externalOrderNo")
    private String externalOrderNo;
    @JsonProperty("externalSubOrderNo")
    private String externalSubOrderNo;
    @JsonProperty("reportInfo")
    private Boolean reportInfo;
    @JsonProperty("productSample")
    private ProductSample productSample;

    @NoArgsConstructor
    @Data
    public static class ProductSample {
        @JsonProperty("productInfo")
        private Boolean productInfo;
        @JsonProperty("sampleList")
        private List<Sample> sampleList;

        @NoArgsConstructor
        @Data
        public static class Sample {
            @JsonProperty("externalSampleId")
            private String externalSampleId;
            @JsonProperty("gpoSampleNo")
            private String gpoSampleNo;
            @JsonProperty("edit")
            private Boolean edit;
        }
    }
}
