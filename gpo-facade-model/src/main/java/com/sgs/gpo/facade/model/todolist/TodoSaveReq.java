package com.sgs.gpo.facade.model.todolist;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

@Data
public class TodoSaveReq extends BaseRequest {

    /**
     * ID
     */
    private String id;

    /**
     * BU 编码
     */
    private String productLineCode;

    /**
     * Location 编码
     */
    private String locationCode;

    /**
     * 实验室编码
     */
    private String labCode;

    /**
     * 待办类型
     */
    private String type;

    /**
     * 对象ID
     */
    private String objectId;

    /**
     * 对象No
     */
    private String objectNo;

    /**
     * assignee VARCHAR(50)<br>
     * 责任人
     */
    private String assignee;

    /**
     * status VARCHAR(2) 必填<br>
     * 10:TODO;20:Complate;30:Cancel;
     */
    private String status;

    /**
     * data OTHER<br>
     * 待办数据
     */
    private Object data;


}
