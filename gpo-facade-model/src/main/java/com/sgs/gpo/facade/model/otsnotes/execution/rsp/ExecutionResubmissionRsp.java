package com.sgs.gpo.facade.model.otsnotes.execution.rsp;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ExecutionResubmissionRsp implements Serializable {
    private String resubmissionId;
    private String orderNo;
    private String executionNo;
    private String executionType;
    private String reportNo;
    private String csName;
    private String csEmail;
    private String applicantEnName;
    private String buyerEnName;
    private String customerContactMobilePhone;
    private String customerContactTelPhone;
    private String contactPersonName;
    private Date createdDate;
    private String createdBy;
    private Date resubmitDate;
    private Date expectDate;
    private Integer status;
    private String remark;
    private String detail;

    private String itemNo = "";
    private String styleNo = "";
    private String customerRefNo = "";
    private String ppName = "";
}
