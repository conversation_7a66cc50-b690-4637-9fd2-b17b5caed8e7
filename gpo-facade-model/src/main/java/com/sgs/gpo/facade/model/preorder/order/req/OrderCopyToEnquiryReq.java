package com.sgs.gpo.facade.model.preorder.order.req;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.model.order.v2.OrderIdBO;
import com.sgs.gpo.facade.model.preorder.enquiry.req.UserLabDTO;
import lombok.Data;

import java.util.List;

@Data
public class OrderCopyToEnquiryReq extends BaseRequest {
    /**对象信息*/
    private OrderIdBO object;
    private String orderId;
    private String enquiryId;
    private Integer num;
    private Integer copyFcmFlag;
    List<String> sectionTypeList;

    private String regular = "1";

    private Integer copyFcm;

    private String salesPerson;

    UserLabDTO saleUserLabDTO;

}
