package com.sgs.gpo.facade.model.workflow.req;

import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WorkflowRuleReq extends BaseRequest {

    /** 实验室标识*/
    @ApiModelProperty(value = "实验室编码")
    private String labCode;

    /** 业务流程编码*/
    @ApiModelProperty(value = "业务流程编码")
    private String processCode;

    /** 当前节点 */
    @ApiModelProperty(value = "当前节点")
    private String currentNode;

    /** 下一节点 */
    @ApiModelProperty(value = "下一节点")
    private String nextNode;

    /** 触发事件 */
    @ApiModelProperty(value = "操作事件")
    private String action;


}
