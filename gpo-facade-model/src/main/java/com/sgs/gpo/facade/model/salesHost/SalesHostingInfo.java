package com.sgs.gpo.facade.model.salesHost;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
@Data
public class SalesHostingInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    private String id;
    private String regionAccount;
    private String salesPerson;
    private String hostingId;
    private String hostingCustomerNo;
    private String hostingCustomerNameEn;
    private String hostingCustomerNameCn;
    private String createdBy;
    private Date createdDate;
    private String modifiedBy;
    private Date modifiedDate;
    private String buCode;
    private String locationCode;
    private String deleted;
    private Boolean isMainHost;
}
