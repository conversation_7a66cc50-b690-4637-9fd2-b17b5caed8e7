package com.sgs.gpo.facade.model.preorder.enquiry.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

@Data
public class UserLabDTO extends BaseRequest {

    private Long labId;

    private String labCode;

    private String otherCode;

    private String labName;

    private String labAddress;

    private Integer locationId;

    private String locationCode;

    private String locationName;

    private Integer productLineId;

    private String productLineName;

    private Long organizationID;

    private String organizationName;

    private String legalEntityCode;

    private String legalEntityName;

    private String productTypeCode;

    private String[] postfix;

    private String countryCode;

    private String teamCode;

    private String teamName;

    private String shortCode;

}
