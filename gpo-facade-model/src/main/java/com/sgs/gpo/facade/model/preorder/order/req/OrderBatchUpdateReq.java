package com.sgs.gpo.facade.model.preorder.order.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

@NoArgsConstructor
@Data
public class OrderBatchUpdateReq extends BaseRequest {
    private Set<String> orderNoList;
    private String orgOrderNo;
    private List<BatchUpdateSectionReq> updateSection;
    private boolean sameAsApplicantFlag = false;
    private boolean sameAsApplicantShow = false;
    @Data
    public static class BatchUpdateSectionReq {
        private String attributeCode;
        private String attributeName;
        private Boolean checkAll;
        private Boolean isIndeterminate;
        private List<BatchUpdateSubSectionReq> subSectionReqList;
    }
    @Data
    public static class BatchUpdateSubSectionReq {
        private String attributeCode;
        private String attributeName;
        private String displayInReport;
    }
}
