package com.sgs.gpo.facade.model.preorder.order.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.Date;

@Data
public class OrderLockReq extends BaseRequest {

    private String orderNo;
    private String subcontractNo;
    private String operator;//操作人
    //操作类型 1-新建分包单 2 编辑分包单
    private Integer operationType;
    //操作开始时间
    private Date createTime;
}
