package com.sgs.gpo.facade.model.otsnotes.subreport.req;

import com.sgs.framework.core.base.BaseQueryReq;
import com.sgs.framework.model.report.subreport.SubReportIdBO;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @title: SubReportQueryReq
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/8 17:30
 */
@Data
public class SubReportQueryReq extends BaseQueryReq<SubReportIdBO> {
    private Set<String> objectNoList;
    private Set<String> subContractIdList;
    private String orderNo;
    private String reportNo;
    private Integer languageId;
}
