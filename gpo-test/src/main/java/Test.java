import com.google.common.collect.Lists;
import com.sgs.gpo.integration.framework.rsp.trf.TrfUpdateConfigRsp;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/5 16:40
 */
public class Test {
    public static void main(String [] args){
        List<TrfUpdateConfigRsp.RuleDTO> ruleDTOList =  new ArrayList<>();
        TrfUpdateConfigRsp.RuleDTO ruleDTO1 = new TrfUpdateConfigRsp.RuleDTO();
        TrfUpdateConfigRsp.RuleDTO ruleDTO2 = new TrfUpdateConfigRsp.RuleDTO();
        TrfUpdateConfigRsp.RuleDTO ruleDTO3 = new TrfUpdateConfigRsp.RuleDTO();
        ruleDTO1.setOperation("update");
        ruleDTO1.setReportStatus(Lists.newArrayList(201));
        ruleDTO2.setOperation("confirm");
        ruleDTO2.setReportStatus(Lists.newArrayList(203,204));
        ruleDTO3.setOperation("alert");
        ruleDTO3.setReportStatus(Lists.newArrayList(208));
        ruleDTOList.add(ruleDTO1);
        ruleDTOList.add(ruleDTO2);
        ruleDTOList.add(ruleDTO3);
        List<Integer> dbList1 = new ArrayList<>();
        dbList1.add(204);
        dbList1.add(203);
        dbList1.add(208);
        TrfUpdateConfigRsp.RuleDTO ruleDTO = ruleDTOList.stream().filter(item -> containsAllElements(item.getReportStatus(), dbList1)).findAny().orElse(null);
        System.out.println(ruleDTO);
    }
    private static <T> boolean containsAllElements(List<T> list1, List<T> list2) {
        return list2.stream().allMatch(list1::contains);
    }
}
