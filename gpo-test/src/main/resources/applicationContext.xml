<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:task="http://www.springframework.org/schema/task"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-4.2.xsd
        http://www.springframework.org/schema/aop
        http://www.springframework.org/schema/aop/spring-aop-4.2.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-4.2.xsd
        http://code.alibabatech.com/schema/dubbo
        http://code.alibabatech.com/schema/dubbo/dubbo.xsd
        http://www.springframework.org/schema/task
        http://www.springframework.org/schema/task/spring-task.xsd">

    <!--启动注解-->
    <context:annotation-config/>
    <!--开启spring对AspectJ的支持,也可通过注解：@EnableAspectJAutoProxy开启-->
    <aop:aspectj-autoproxy proxy-target-class="true"/>
    <!-- 配置dubbo注解识别处理器，不指定包名的话会在spring bean中查找对应实例的类配置了dubbo注解的 -->

    <!--spring自动扫描的包下@Service、@Component、@repository和@Controller的类，并注册为Bean-->
    <context:component-scan base-package="com.sgs.gpo"/>

    <!--任务扫描注解 start-->
    <task:executor id="executor" pool-size="5"/>
    <task:scheduler id="scheduler" pool-size="5"/>
    <task:annotation-driven executor="executor" scheduler="scheduler"/>
    <!--任务扫描注解 end-->

    <!--<import resource="classpath:spring/sqlserver-persistence.xml" />-->
    <context:property-placeholder location="classpath:application.properties"/>

    <!-- 提供方应用信息，用于计算依赖关系 -->
    <dubbo:application name="cnapp-dev.sgs.net"/>
    <dubbo:registry protocol="zookeeper" address="${zookeeper.address}" timeout="30000"/>

    <!-- 协议注册 -->
    <dubbo:protocol name="dubbo" port="${dubbo.port}"/>
    <dubbo:protocol name="rest" server="tomcat" port="${tomcat.port}" extension="com.sgs.preorder.core.serialize.JacksonConfig"/>

    <dubbo:annotation package="com.sgs.preorder.facade.impl"/>

    <!-- 注册dubbo服务 -->
    <dubbo:service group="SODA" ref="orderFacade" protocol="dubbo" interface="com.sgs.preorder.facade.OrderFacade"/>

    <!-- 注册Rest服务 -->
    <dubbo:service ref="systemRest" protocol="dubbo" interface="com.sgs.preorder.facade.rest.SystemRest"/>
    <dubbo:service ref="orderRest" protocol="dubbo" interface="com.sgs.preorder.facade.rest.OrderRest"/>


</beans>