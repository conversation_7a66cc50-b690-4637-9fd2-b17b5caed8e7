spring.profiles.active=dev
logging.config=classpath:logback.xml
spring.mvc.favicon.enabled=false

jdbc.datasource.read=get,select,count,list,query,find,search,sum
jdbc.datasource.write=add,create,update,delete,remove,insert

##\u6570\u636e\u5e93\u914d\u7f6e
validationQuery=SELECT 'x'

# Ö÷¿â
jdbc.driverClassName=com.mysql.jdbc.Driver
jdbc.url=jdbc:mysql://**************:3306/preorder?useUnicode=true&amp;characterEncoding=UTF-8&amp;allowMultiQueries=true&amp;tinyInt1isBit=false
jdbc.username=mycat
jdbc.password=Sgs123456!@#

# ´Ó¿â
jdbc.read.url=****************************************************************************************************************************************
jdbc.read.username=mycat
jdbc.read.password=Sgs123456!@#


# redis config
redis.cluster.nodes=**************:6379
redis.cluster.password=
redis.cluster.maxRedirects=6
redis.cluster.timeout=10000

redis.pool.maxIdle=20
redis.pool.minIdle=5
redis.pool.maxTotal=20
redis.pool.maxWait=1000
redis.pool.testOnBorrow=true


# ¿ª·¢»·¾³ÓÃÕâ¸ö
tomcat.protocol=org.apache.coyote.http11.Http11Nio2Protocol
# ·¢²¼ºóÓÃÕâ¸ö£¬ÐÔÄÜ¸üºÃ£¬ÐèÒª°²×°
# tomcat.protocol=org.apache.coyote.http11.Http11AprProtocol
tomcat.connectionTimeout=20000
tomcat.maxConnections=2000
tomcat.maxThreads=700
tomcat.uriEncoding=UTF-8
tomcat.acceptCount=2000
# webEnvironment=false
tomcat.port=8085
# ÓÃdubboÐ­ÒéÔÚ20880¶Ë¿Ú±©Â¶·þÎñ
dubbo.port=20880
zookeeper.address=**************:2181,**************:2181
