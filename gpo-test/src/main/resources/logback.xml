<?xml version="1.0" encoding="UTF-8" ?>
<!-- Logback configuration. See http://logback.qos.ch/manual/index.html -->
<configuration scan="true" scanPeriod="10 seconds">
	<property name="queueSize" value="1024"/>
	<property name="LOG_PATH" value="/usr/local/applogs/new-workflow-service/"/>
	<property name="level" value="info" />
	<include resource="org/springframework/boot/logging/logback/base.xml" />
	<!--<include resource="org/springframework/boot/logging/logback/defaults.xml" />-->

	<appender name="DEFAULT_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<File>${LOG_PATH}/new-workflow-service.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_PATH}/new-workflow-service-%d{yyyyMMdd}.log.%i</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>500MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<maxHistory>15</maxHistory>
		</rollingPolicy>
		<layout class="ch.qos.logback.classic.PatternLayout">
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
		</layout>
	</appender>

	<appender name="ERROR_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>ERROR</level>
		</filter>
		<File>${LOG_PATH}/new-workflow-service_error.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_PATH}/new-workflow-service_error-%d{yyyyMMdd}.log.%i
			</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>500MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<maxHistory>15</maxHistory>
		</rollingPolicy>
		<layout class="ch.qos.logback.classic.PatternLayout">
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
		</layout>
	</appender>

	<appender name="ASYNC-DEFAULT-APPENDER" class="ch.qos.logback.classic.AsyncAppender">
		<queueSize>${queueSize}</queueSize>
		<appender-ref ref="DEFAULT_APPENDER"/>
	</appender>

	<appender name="ASYNC-ERROR-APPENDER" class="ch.qos.logback.classic.AsyncAppender">
		<queueSize>${queueSize}</queueSize>
		<appender-ref ref="ERROR_APPENDER"/>
	</appender>

	<!--  <logger name="" additivity="false">
         <level value="${level}" />
         <appender-ref ref="ASYNC-BIZ-APPENDER" />
     </logger> -->

	<root level="${level}">
		<appender-ref ref="ASYNC-DEFAULT-APPENDER" />
		<appender-ref ref="ASYNC-ERROR-APPENDER" />
	</root>
</configuration>