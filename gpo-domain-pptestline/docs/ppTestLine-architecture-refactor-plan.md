# gpo-domain-pptestline 领域重构与架构治理方案

> 版本：v1.0  
> 日期：2025-08-08  
> 适用范围：gpo-domain-pptestline 模块（并与 uni-otsnotes 的分层范式对齐）

---

## 目标与范围

- 目标
  - 让 gpo-domain-pptestline 遵循 DDD/COLA 分层：Domain 纯净、Infra 落地、App/Adapter 解耦。
  - 引入 ArchUnit 架构规则，纳入持续治理。
  - 与 uni-otsnotes 架构对齐：大框架（uni-otsnotes 的分层范式）包含小框架（ppTestLine 领域实现）的模式。

- 范围
  - 结构对比、差异定位、目标蓝图、迁移路径、包/代码迁移映射、POM 调整、ArchUnit 规则建议、里程碑与风险。

---

## 现状与差异对比

- 模块与分层
  - uni-otsnotes：标准 COLA/DDD 多模块（client/adapter/app/domain/infrastructure/start），Domain 纯净。
  - gpo-domain-pptestline：单一“domain”模块内混入 Infra（dbstorages/entity/mapper）、App（command/context/service/impl）、DTO（model/req/bo）。

- 依赖与构建
  - uni-otsnotes：父 POM 统一管控、MapStruct 编译期处理、ArchUnit 测试依赖。
  - gpo-domain-pptestline：Domain 模块直接依赖 MyBatis-Plus 与存储框架（不利于领域纯净），缺少 ArchUnit。

- 包结构与命名
  - ppTestLine 包路径含“otsnotes”片段（`com.sgs.gpo.domain.otsnotes.pptestline`），与 gpo 命名不一致。
  - command/context/req/bo 位于“domain”模块，不符合分层角色定位。

- 架构治理
  - uni-otsnotes 提供 ArchUnit 文档与规则实践。
  - gpo-domain-pptestline 暂未纳入 ArchUnit 规则。

---

## 目标架构蓝图（与 uni-otsnotes 对齐）

- Domain（目标：纯净，无框架依赖）
  - `com.sgs.gpo.domain.pptestline.domain.model`（实体/聚合根/值对象）
  - `com.sgs.gpo.domain.pptestline.domain.service`（领域服务，纯函数/无状态）
  - `com.sgs.gpo.domain.pptestline.domain.repository`（仓储接口）
  - `com.sgs.gpo.domain.pptestline.domain.specification` / `...domain.event`（可选）
  - 不依赖 Spring/MyBatis/Mapper/DO/DTO

- Application（位于 gpo-facade-impl 或新建 gpo-app-pptestline）
  - 用例编排、事务边界、调用 Domain + Repository 接口
  - 命令/查询对象、DTO 映射（MapStruct）
  - 依赖 Domain / Facade-Model，不依赖 Infra

- Infrastructure（建议承载在 gpo-dbstorages 或新建 ppTestLine 专属 infra 包）
  - DO/Mapper/XML、RepositoryImpl（实现 domain.repository）
  - MapStruct 映射 DO ↔ Domain
  - 依赖 MyBatis-Plus、数据源等

- Adapter（已有 gpo-web / 其他网关模块）
  - Controller/Facade 层对接

- 治理（全模块）
  - ArchUnit 测试：分层依赖、命名规范、包边界

---

## 迁移原则

- 渐进式、低风险：先建接口/骨架，再迁实现；先加“软规则”，逐步收紧。
- 领域纯净优先：Domain 禁框架、禁 Mapper/DO/DTO。
- 包与命名一次性纠偏：统一到 `com.sgs.gpo.domain.pptestline`。
- 维持对外 API 稳定性：对外 Facade/DTO 不破坏，内部逐步改造。

---

## 分阶段改造计划

### 阶段 0：基线与盘点（不改行为）
- 扫描 gpo-domain-pptestline 包清单，标注：
  - 纯领域类（可直接迁入 domain.model/service）
  - 应用/编排类（迁至 App）
  - 基础设施类（迁至 Infra）
- 在该模块新增 ArchUnit 基础用例（仅报警/TODO，不阻断）。

### 阶段 1：引入 ArchUnit 基础治理（非破坏）
- 在 gpo-domain-pptestline 引入 test-scope ArchUnit 依赖。
- 新增 ArchitectureTest（禁止 domain 引用 spring/mybatis/…mapper/…entity；禁止 DTO/Controller 进入 domain）。
- 提交架构 README（约定分层与边界）。

### 阶段 2：目标包骨架与命名整改
- 新建目标包：
  - `com.sgs.gpo.domain.pptestline.domain.{model,service,repository}`
- 将“otsnotes”路径逐步剔除；建立临时转发（deprecated）以降低改动面。

### 阶段 3：抽取基础设施
- 在 gpo-dbstorages 增设 `com.sgs.gpo.dbstorages.pptestline.{entity,mapper,repository.impl}`
- 在 Domain 定义仓储接口（`domain.repository.*`）
- 在 Infra 实现仓储，替换原直接 Mapper 调用
- 引入 MapStruct 于 Infra 映射 DO ↔ Domain

### 阶段 4：应用层收敛
- 将 `command`/`context`/`model/req` 中的“用例编排/事务/聚合操作”迁至 App（gpo-facade-impl 或新建 gpo-app-pptestline）
- Domain 服务去除 `@Service` 等注解，保持纯净
- DTO 归口 gpo-facade-model；App 层做 DTO ↔ Domain 映射

### 阶段 5：POM 清理与依赖收口
- gpo-domain-pptestline：移除 MyBatis-Plus/存储依赖，仅保留 lombok、domain-common、ArchUnit（test）
- gpo-dbstorages：承载 MyBatis-Plus、数据源、MapStruct 处理器
- gpo-facade-impl（或 app 模块）：承载 Spring、事务、MapStruct 处理器、DTO 依赖

### 阶段 6：收紧 ArchUnit 规则并接入 CI
- 打开强规则：分层依赖、命名规范、包边界
- 在 CI Pipeline 执行 `mvn test` 校验，违规阻断

---

## 包与代码迁移映射（示例）

- 原：`.../dbstorages/{entity,mapper}`
  - 迁 → `gpo-dbstorages/src/main/java/com/sgs/gpo/dbstorages/pptestline/{entity,mapper,repository/impl}`
- 原：`.../service/impl`（若调用 Mapper/持久化）
  - 迁 → Infra 的 `repository.impl`（或拆分：持久化实现进 Infra；纯领域逻辑进 domain.service）
- 原：`.../service/*.java`
  - 纯领域服务 → `domain.service`（去除 Spring 注解）
  - 应用服务/编排 → App（`gpo-facade-impl` 或新建 app 模块）
- 原：`.../model/req`、`.../command`、`.../context`
  - 用例入口/命令模型 → App 层
  - 对外 DTO → `gpo-facade-model`
- 原：`.../model/bo`
  - 领域模型 → `domain.model`（命名为 `XxxAggregate`/`XxxEntity`/`XxxVO`）
  - 仅用于传输的 BO → DTO 模块

- 包名纠偏
  - `com.sgs.gpo.domain.otsnotes.pptestline`
  - → `com.sgs.gpo.domain.pptestline`（保留 `@Deprecated` 适配期 1-2 个版本）

---

## POM 与依赖调整建议

- `gpo-domain-pptestline/pom.xml`
  - 移除：`mybatis-plus-boot-starter`、框架存储类库
  - 增加（test）：`com.tngtech.archunit:archunit`
  - 保留：lombok、gpo-core、gpo-domain-common、必要基础依赖

- `gpo-dbstorages/pom.xml`
  - 增补 MapStruct（processor）与 MyBatis-Plus、数据源相关依赖

- `gpo-facade-impl` 或新建 app 模块
  - Spring 事务、MapStruct（processor）、DTO 依赖

---

## ArchUnit 规则建议（示例片段）

```java
@AnalyzeClasses(packages = "com.sgs.gpo.domain.pptestline")
public class DomainArchitectureTest {

  @ArchTest
  static final ArchRule domain_should_be_framework_free =
      noClasses().that().resideInAPackage("..domain..")
        .should().dependOnClassesThat().resideInAnyPackage(
           "..springframework..", "..mybatis..", "..mapper..", "..entity..");

  @ArchTest
  static final ArchRule no_dto_or_controller_in_domain =
      noClasses().that().resideInAPackage("..domain..")
        .should().haveSimpleNameEndingWith("DTO")
        .orShould().beAnnotatedWith("org.springframework.stereotype.Controller")
        .orShould().beAnnotatedWith("org.springframework.web.bind.annotation.RestController");

  @ArchTest
  static final ArchRule repositories_should_be_interfaces =
      classes().that().resideInAPackage("..domain.repository..")
        .should().beInterfaces();
}
```

> 建议先以“软规则”（允许失败但输出报告）落地，随着迁移进度逐步升级为“强规则”（违规即失败）。

---

## 里程碑与验收

- M1（约 1 周）：建立目标包骨架、引入 ArchUnit（软规则）、架构 README
  - 验收：构建通过、ArchUnit 可运行且产出报告

- M2（约 2-3 周）：抽取 Infra、引入 repository 接口、MapStruct 映射、替换直接 Mapper 调用
  - 验收：所有用例改走仓储接口，Domain 无框架依赖

- M3（约 1-2 周）：App 层收敛（command/context/req/bo 分流）、POM 清理
  - 验收：Domain POM 无存储依赖，App/Infra 编译通过

- M4（约 1 周）：收紧 ArchUnit、CI 集成、命名/边界规则强制
  - 验收：CI 违规阻断；新增迭代自动校验

---

## 风险与缓解

- 交叉调用广，包名调整影响面大
  - 缓解：先引入新包并双轨运行，旧包 `@Deprecated` 保留两版

- 直接 Mapper 调用遍布
  - 缓解：先抽 repository 接口并适配，统一入口后再迁实现

- 构建依赖耦合
  - 缓解：先加 ArchUnit 软规则与 skeleton，不做行为变更

---

## 与 uni-otsnotes 的“大带小”关系落地

- 逻辑对齐（推荐，保持物理解耦）：ppTestLine 作为“遵循 uni-otsnotes 分层范式”的领域实现，结构/规则完全同构，CI 共用 ArchUnit 规则模板。
- 物理合并（可选）：在 `uni-otsnotes-domain` 下增设 `pptestline` 包作为子域；但考虑到 gpo 微服务划分，建议仅做逻辑对齐与规则共用，不做跨仓/跨聚合的物理合并。

---

## 最小可行落地（建议启动项）

- 在 gpo-domain-pptestline 新增：
  - domain 目标包骨架（`model`/`service`/`repository`）
  - ArchUnit 基础用例（软规则）
  - 架构 README（分层、边界、命名规范）

- 提交基线后，开始仓储接口与 Infra 抽取的第一轮重构。

---

## 需求覆盖

- 差异分析：已提供
- 修改方案与计划：分阶段、映射、POM/规则建议已提供
- 领域编程对齐：目标蓝图、迁移原则、包与命名规范已明确
- ArchUnit 架构管控：规则建议、接入与收紧方案已覆盖

---

> 维护建议：任何跨层依赖、新增持久化访问、新增命名不合规的代码，应在提交前通过 ArchUnit 用例验证。建议在 CI 中强制执行，防止架构回退。
