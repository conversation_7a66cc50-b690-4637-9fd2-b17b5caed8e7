package com.sgs.gpo.domain.otsnotes.pptestline.service.impl;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.model.test.testscheme.TestSchemeBO;
import com.sgs.framework.model.test.testscheme.TestSchemeIdBO;
import com.sgs.framework.open.platform.base.service.impl.AbstractDomainService;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;

import com.sgs.gpo.domain.otsnotes.pptestline.command.*;
import com.sgs.gpo.domain.otsnotes.pptestline.context.TestSchemeHandleContext;
import com.sgs.gpo.domain.otsnotes.pptestline.context.TestSchemeQueryContext;
import com.sgs.gpo.domain.otsnotes.pptestline.model.req.*;
import com.sgs.gpo.domain.otsnotes.pptestline.model.rsp.TestSchemeVO;
import com.sgs.gpo.domain.otsnotes.pptestline.subdomain.ITestSchemeService;
import com.sgs.gpo.domain.otsnotes.pptestline.service.ITestSchemeDomainService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TestSchemeDomainServiceImpl
        extends AbstractDomainService<TestSchemeBO, TestSchemeIdBO, TestSchemeQueryReq, ITestSchemeService>
        implements ITestSchemeDomainService {

    @Override
    public BaseResponse<List<TestSchemeBO>> query(TestSchemeQueryReq  testSchemeQueryReq) {
        TestSchemeQueryContext testSchemeQueryContext = initContext(testSchemeQueryReq);
        return BaseExecutor.start(TestSchemeBOQueryCMD.class,testSchemeQueryContext);
    }

    @Override
    public BaseResponse<Page<TestSchemeBO>> queryTestSchemePage(TestSchemeQueryReq testSchemeQueryReq, Integer page, Integer rows) {
        TestSchemeQueryContext testSchemeQueryContext = initContext(testSchemeQueryReq, page, rows);
        return BaseExecutor.start(TestSchemeBOQueryCMD.class,testSchemeQueryContext);
    }

    @Override
    public BaseResponse<Page<TestSchemeVO>> queryTestSchemeVOPage(TestSchemeQueryReq testSchemeQueryReq, Integer page, Integer rows) {

        TestSchemeQueryContext testSchemeQueryContext = initContext(testSchemeQueryReq, page, rows);
        return BaseExecutor.start(TestSchemeVOQueryCMD.class,testSchemeQueryContext);
    }

    @Override
    public BaseResponse<Boolean> testSchemeLabOut(TestSchemeLabOutReq testLineLabOutReq) {
        TestSchemeHandleContext<TestSchemeLabOutReq, TestSchemeVO>
                context = new TestSchemeHandleContext<>();
        context.setParam(testLineLabOutReq);
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        context.setToken(SecurityContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(TestSchemeLabOutCMD.class,context);
    }

    @Override
    public BaseResponse<Boolean> testSchemeLabIn(TestSchemeLabInReq testLineLabInReq) {
        TestSchemeHandleContext<TestSchemeLabInReq, TestSchemeVO>
                context = new TestSchemeHandleContext<>();
        context.setParam(testLineLabInReq);
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        context.setToken(SecurityContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(TestSchemeLabInCMD.class,context);
    }

    @Override
    public BaseResponse<Boolean> testSchemePending(TestSchemePendingReq testSchemePendingReq) {
        TestSchemeHandleContext<TestSchemePendingReq, TestSchemeVO>
                context = new TestSchemeHandleContext<>();
        context.setParam(testSchemePendingReq);
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        context.setToken(SecurityContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(TestSchemePendingCMD.class,context);
    }

    @Override
    public BaseResponse<Boolean> testSchemeUnPending(TestSchemeUnPendingReq testSchemeUnPendingReq) {
        TestSchemeHandleContext<TestSchemeUnPendingReq, TestSchemeVO>
                context = new TestSchemeHandleContext<>();
        context.setParam(testSchemeUnPendingReq);
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        context.setToken(SecurityContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(TestSchemeUnPendingCMD.class,context);
    }


    @Override
    public BaseResponse<Boolean> testSchemeSetDueDate(TestSchemeDueDateReq testSchemeDueDateReq) {
        TestSchemeHandleContext<TestSchemeDueDateReq, TestSchemeVO>
                context = new TestSchemeHandleContext<>();
        context.setParam(testSchemeDueDateReq);
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        context.setToken(SecurityContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(TestSchemeDueDateCMD.class,context);
    }

    @Override
    public BaseResponse<Boolean> testSchemeAssignEngineer(TestSchemeAssignEngineerReq testSchemeAssignEngineerReq) {
        TestSchemeHandleContext<TestSchemeAssignEngineerReq, TestSchemeVO>
                context = new TestSchemeHandleContext<>();
        context.setParam(testSchemeAssignEngineerReq);
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        context.setToken(SecurityContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(TestSchemeAssignEngineerCMD.class,context);
    }



    private <Domain> TestSchemeQueryContext<TestSchemeQueryReq,Domain> initContext(TestSchemeQueryReq testSchemeListReq){
        return initContext(testSchemeListReq,null,null);
    }


    private <Domain> TestSchemeQueryContext<TestSchemeQueryReq,Domain> initContext(TestSchemeQueryReq testSchemeListReq, Integer page, Integer rows){
        TestSchemeQueryContext<TestSchemeQueryReq,Domain> testSchemeQueryContext = new TestSchemeQueryContext<>();
        testSchemeQueryContext.setParam(testSchemeListReq);
        testSchemeQueryContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        testSchemeQueryContext.setToken(SecurityContextHolder.getSgsToken());
        testSchemeQueryContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        if(Func.isNotEmpty(page) && Func.isNotEmpty(rows)) {
            testSchemeQueryContext.setPage(page);
            testSchemeQueryContext.setRows(rows);
        }
        return testSchemeQueryContext;
    }


}
