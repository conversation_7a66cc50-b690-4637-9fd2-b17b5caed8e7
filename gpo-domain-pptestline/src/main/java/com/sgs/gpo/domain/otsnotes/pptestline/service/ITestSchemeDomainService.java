package com.sgs.gpo.domain.otsnotes.pptestline.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.test.testscheme.TestSchemeBO;
import com.sgs.framework.open.platform.base.service.IDomainService;
import com.sgs.framework.model.test.testscheme.TestSchemeIdBO;
import com.sgs.gpo.domain.otsnotes.pptestline.model.req.*;
import com.sgs.gpo.domain.otsnotes.pptestline.model.rsp.TestSchemeVO;


import java.util.List;

public interface ITestSchemeDomainService extends IDomainService<TestSchemeBO, TestSchemeIdBO, TestSchemeQueryReq> {
    /**
     * 查询测试方案列表
     * @param testSchemeQueryReq
     * @return
     */
    BaseResponse<List<TestSchemeBO>> query(TestSchemeQueryReq testSchemeQueryReq);


    /**
     * 查询测试方案列表
     * @param testSchemeQueryReq
     * @param page
     * @param rows
     * @return
     */
    BaseResponse<Page<TestSchemeBO>> queryTestSchemePage(TestSchemeQueryReq testSchemeQueryReq, Integer page, Integer rows);

    /**
     * 实验开始
     * @param testSchemeQueryReq
     * @param page
     * @param rows
     * @return
     */
    BaseResponse<Page<TestSchemeVO>> queryTestSchemeVOPage(TestSchemeQueryReq testSchemeQueryReq, Integer page, Integer rows);
    /**
     * 实验结束
     * @param testLineLabOutReq
     * @return
     */
    BaseResponse<Boolean> testSchemeLabOut(TestSchemeLabOutReq testLineLabOutReq);

    /**
     * 记录开始测试时间
     * @param testLineLabInReq
     * @return
     */
    BaseResponse<Boolean> testSchemeLabIn(TestSchemeLabInReq testLineLabInReq);

    BaseResponse<Boolean> testSchemePending(TestSchemePendingReq testSchemePendingReq);

    BaseResponse<Boolean> testSchemeUnPending(TestSchemeUnPendingReq testSchemeUnPendingReq);

    /**
     * 设置测试方案截止时间
     * @param testSchemeDueDateReq
     * @return
     */
    BaseResponse<Boolean> testSchemeSetDueDate(TestSchemeDueDateReq testSchemeDueDateReq);
    /**
     * 分配工程师
     * @param testSchemeAssignEngineerReq
     * @return
     */
    BaseResponse<Boolean> testSchemeAssignEngineer(TestSchemeAssignEngineerReq testSchemeAssignEngineerReq);
}

