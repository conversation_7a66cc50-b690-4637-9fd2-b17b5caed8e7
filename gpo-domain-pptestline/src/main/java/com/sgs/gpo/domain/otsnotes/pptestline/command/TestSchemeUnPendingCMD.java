package com.sgs.gpo.domain.otsnotes.pptestline.command;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.domain.otsnotes.pptestline.context.TestSchemeHandleContext;
import com.sgs.gpo.domain.otsnotes.pptestline.model.req.TestSchemeQueryReq;
import com.sgs.gpo.domain.otsnotes.pptestline.model.req.TestSchemeUnPendingReq;
import com.sgs.gpo.domain.otsnotes.pptestline.model.rsp.TestSchemeVO;
import com.sgs.gpo.domain.otsnotes.pptestline.subdomain.ITestSchemeService;
import lombok.experimental.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class TestSchemeUnPendingCMD extends BaseCommand<TestSchemeHandleContext<TestSchemeUnPendingReq, TestSchemeVO>> {
    @Autowired
    private ITestSchemeService testSchemeService;
    @Override
    public BaseResponse validateParam(TestSchemeHandleContext<TestSchemeUnPendingReq, TestSchemeVO> context) {
        if(Func.isEmpty(context) || Func.isEmpty(context.getParam())){
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(TestSchemeHandleContext<TestSchemeUnPendingReq, TestSchemeVO> context) {
        context.setLab(SystemContextHolder.getLab());
        TestSchemeQueryReq testSchemeQueryReq = new TestSchemeQueryReq();
        testSchemeQueryReq.setBaseQuery(true);
        testSchemeQueryReq.setLab(context.getLab());
        testSchemeQueryReq.setLabId(SystemContextHolder.getLab().getLabId());
        testSchemeQueryReq.setLabCode(SystemContextHolder.getLab().getLabCode());
        testSchemeQueryReq.setIdList(context.getParam().getIdList());
        testSchemeQueryReq.setSchemeIds(context.getParam().getTestSchemeIds());
        // 根据 schemeIds 查询多个 scheme
        var testSchemePOs = testSchemeService.query(testSchemeQueryReq);
        context.setTestSchemePOList(testSchemePOs);

        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(TestSchemeHandleContext<TestSchemeUnPendingReq, TestSchemeVO> context) {
        try {
//            //需要事务提交
//            var testSchemePOs = context.getTestSchemePOList();
//            var testLineIds = testSchemePOs.stream().map(TestSchemePO::getTestLineInstanceId).distinct().collect(Collectors.toList());
//            TestLineLabInReq testLineLabInReq = new TestLineLabInReq();
//            testLineLabInReq.setTestLineInstanceIdList(new HashSet<>(testLineIds));
//            var labInRsp = testLineDomainService.labIn(testLineLabInReq);
//            if(labInRsp.isFail()) return BaseResponse.newFailInstance(labInRsp.getMessage());
//            testSchemePOs.forEach(testSchemePO -> testSchemePO.setStatus(LabStatusEnum.ENTERED.getDisplayName()));
//            boolean result = testSchemeService.updateBatchById(testSchemePOs);
//            if(!result) return BaseResponse.newFailInstance("testScheme labin failed");
            return BaseResponse.newSuccessInstance(true);
        } catch (Exception e) {
            return BaseResponse.newFailInstance("unpending failed");
        }
    }
}
