package com.sgs.gpo.domain.otsnotes.pptestline.service;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.domain.otsnotes.pptestline.config.PPTestLineConfig;
import com.sgs.gpo.domain.otsnotes.pptestline.model.req.PPTestLineConfigReq;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineDomainService;

import java.util.List;

public interface IPPTestLineDomainService extends ITestLineDomainService {

    /**
     * 查询PPTestline配置信息
     * @return
     */
    BaseResponse<List<PPTestLineConfig>> getConfig(PPTestLineConfigReq configReq);

}
