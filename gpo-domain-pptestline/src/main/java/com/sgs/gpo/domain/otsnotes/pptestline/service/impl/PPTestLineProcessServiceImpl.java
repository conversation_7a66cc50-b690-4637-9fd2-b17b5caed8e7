package com.sgs.gpo.domain.otsnotes.pptestline.service.impl;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.common.object.StatusControlBO;
import com.sgs.framework.model.common.object.process.ObjectProcessBO;
import com.sgs.framework.model.test.testline.v2.TestLineBO;
import com.sgs.framework.model.test.testline.v2.TestLineIdBO;
import com.sgs.framework.open.platform.model.req.ObjectStatusVerifyReq;
import com.sgs.framework.open.platform.model.req.StatusControlReq;
import com.sgs.gpo.domain.otsnotes.pptestline.service.IPPTestLineProcessService;

import java.util.List;

public class PPTestLineProcessServiceImpl implements IPPTestLineProcessService {

    @Override
    public BaseResponse<List<StatusControlBO<TestLineBO>>> statusControl(StatusControlReq<TestLineIdBO> statusControlReq) {
        return null;
    }

    @Override
    public BaseResponse<List<TestLineBO>> statusVerify(ObjectStatusVerifyReq<TestLineBO, TestLineIdBO> orderStatusVerifyReq) {
        return null;
    }

    @Override
    public BaseResponse<List<ObjectProcessBO>> getProcess() {
        return null;
    }

    @Override
    public BaseResponse<List<TestLineBO>> queryStatus(List<TestLineIdBO> testLineIdBOS) {
        return null;
    }
}
