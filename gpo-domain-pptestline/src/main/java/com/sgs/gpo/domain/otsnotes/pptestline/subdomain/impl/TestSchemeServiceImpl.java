package com.sgs.gpo.domain.otsnotes.pptestline.subdomain.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.common.tracking.TrackingBO;
import com.sgs.framework.model.order.v2.OrderIdBO;
import com.sgs.framework.model.test.testline.v2.TestLineIdBO;
import com.sgs.framework.model.test.testscheme.*;
import com.sgs.framework.open.platform.base.service.impl.AbstractBaseService;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.domain.otsnotes.pptestline.dbstorages.mapper.TestSchemeMapper;
import com.sgs.gpo.domain.otsnotes.pptestline.entity.TestSchemePO;
import com.sgs.gpo.domain.otsnotes.pptestline.model.req.TestSchemeQueryReq;
import com.sgs.gpo.domain.otsnotes.pptestline.subdomain.ITestSchemeService;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class TestSchemeServiceImpl
        extends AbstractBaseService<TestSchemeBO, TestSchemePO, TestSchemeIdBO, TestSchemeMapper, TestSchemeQueryReq>
        implements ITestSchemeService {

    @Override
    public List<TestSchemeBO> convertToBO(Collection<TestSchemePO> poList) {
        if(Func.isEmpty(poList)){
            return Collections.emptyList();
        }
        List<TestSchemeBO> testSchemeBOs = poList.stream().map(po->{
            TestSchemeBO testSchemeBO = new TestSchemeBO();

            //ID 信息
            TestSchemeIdBO testSchemeIdBO = new TestSchemeIdBO();
            testSchemeIdBO.setParentTestSchemeId(po.getParentId());
            testSchemeIdBO.setTestSchemeNo(po.getTestSchemeNo());
            testSchemeIdBO.setTestSchemeId(po.getId());
            testSchemeIdBO.setId(po.getId());
            testSchemeBO.setId(testSchemeIdBO);

            //Releationship 信息
            TestSchemeRelationshipBO testSchemeRelationshipBO = new TestSchemeRelationshipBO();
            TestSchemeParentBO testSchemeParentBO = new TestSchemeParentBO();
            TestLineIdBO testLineIdBO = new TestLineIdBO();
            testLineIdBO.setTestLineInstanceId(po.getTestLineInstanceId());
            testSchemeParentBO.setTestLine(testLineIdBO);
            OrderIdBO orderIdBO = new OrderIdBO();
            orderIdBO.setOrderNo(po.getOrderNo());
            testSchemeParentBO.setOrder(orderIdBO);
            testSchemeRelationshipBO.setParent(testSchemeParentBO);
            testSchemeBO.setRelationship(testSchemeRelationshipBO);

            //Header 信息
            TestSchemeHeaderBO headerBO = new TestSchemeHeaderBO();
            headerBO.setTestSchemeName(po.getTestSchemeName());
            headerBO.setTestSeq(po.getTestSeq());
            // 多语言信息
            if(Func.isNotEmpty(po.getLanguage())){
                List<TestSchemeLanguageBO> languageBOs = JSON.parseObject(
                        po.getLanguage(),
                        new TypeReference<List<TestSchemeLanguageBO>>() {}
                );

                headerBO.setLanguageList(languageBOs);
            }
            testSchemeBO.setHeader(headerBO);

            //Other 信息
            TestSchemeOthersBO othersBO = new TestSchemeOthersBO();
            othersBO.setRemark(po.getRemark());
            testSchemeBO.setOthers(othersBO);


            //Tracking信息
            TrackingBO<String> trackingBO = new TrackingBO<>();
            trackingBO.setOwner(po.getOwner());
            trackingBO.setRemark(po.getRemark());
            trackingBO.setExpectDueDate(po.getExpectDueDate());
            trackingBO.setStatus(po.getStatus());
            trackingBO.setPendingFlag(po.getPendingFlag());
            trackingBO.setStartDate(po.getStartDate());
            trackingBO.setEndDate(po.getEndDate());
            testSchemeBO.setTracking(trackingBO);

            testSchemeBO.setCreateDate(po.getCreatedDate());
            testSchemeBO.setCreatedBy(po.getCreatedBy());
            testSchemeBO.setModifiedBy(po.getModifiedBy());
            testSchemeBO.setModifiedDate(po.getModifiedDate());


            return testSchemeBO;
        }).collect(Collectors.toList());
        return testSchemeBOs;
    }

    @Override
    public List<TestSchemePO> convertToPO(Collection<TestSchemeBO> boList) {
        if (Func.isEmpty(boList)) {
            return Collections.emptyList();
        }
        UserInfo userInfo = SystemContextHolder.getUserInfo();
        List<TestSchemePO> testSchemePOs = boList.stream().map(bo->{
            TestSchemePO  testSchemePO = new TestSchemePO();
            //ID
            if(Func.isNotEmpty(bo.getId())&&Func.isNotEmpty(bo.getId().getTestSchemeId())){
                testSchemePO.setId(bo.getId().getTestSchemeId());
                testSchemePO.setParentId(bo.getId().getParentTestSchemeId());
                testSchemePO.setTestSchemeNo(bo.getId().getTestSchemeNo());
                testSchemePO.setModifiedBy(userInfo.getRegionAccount());
                testSchemePO.setModifiedDate(new Date());
            }else{
                testSchemePO.setId(IdUtil.uuId());
                //TODO 生成TestSchemeNo
                testSchemePO.setTestSchemeNo("Todo");
                testSchemePO.setCreatedBy(userInfo.getRegionAccount());
                testSchemePO.setCreatedDate(new Date());
            }
            //Relationship
            TestSchemeRelationshipBO testSchemeRelationshipBO = bo.getRelationship();
            if(Func.isNotEmpty(testSchemeRelationshipBO) && Func.isNotEmpty(testSchemeRelationshipBO.getParent())) {
                TestSchemeParentBO testSchemeParentBO = testSchemeRelationshipBO.getParent();
                Assert.notNull(testSchemeParentBO.getTestLine());
                testSchemePO.setTestLineInstanceId(testSchemeParentBO.getTestLine().getTestLineInstanceId());

                Assert.notNull(testSchemeParentBO.getOrder());
                testSchemePO.setOrderNo(testSchemeParentBO.getOrder().getOrderNo());
            }

            //Header
            TestSchemeHeaderBO testSchemeHeaderBO = bo.getHeader();
            if(Func.isNotEmpty(testSchemeHeaderBO)){
                testSchemePO.setTestSchemeName(testSchemeHeaderBO.getTestSchemeName());
                testSchemePO.setTestSeq(testSchemeHeaderBO.getTestSeq());

                //Language
                testSchemePO.setLanguage(Func.toJson(testSchemeHeaderBO.getLanguageList()));
            }

            //Others
            TestSchemeOthersBO testSchemeOthersBO = bo.getOthers();
            if(Func.isNotEmpty(testSchemeOthersBO)){
                testSchemePO.setRemark(testSchemeOthersBO.getRemark());
            }

            //Tracking
            TrackingBO<String> trackingBO = bo.getTracking();
            if(Func.isNotEmpty(trackingBO)) {
                testSchemePO.setOwner(trackingBO.getOwner());
                testSchemePO.setStatus(trackingBO.getStatus());
                testSchemePO.setExpectDueDate(trackingBO.getExpectDueDate());
                testSchemePO.setStartDate(trackingBO.getStartDate());
                testSchemePO.setEndDate(trackingBO.getEndDate());
                testSchemePO.setPendingFlag(trackingBO.getPendingFlag());
            }

            testSchemePO.setCreatedDate(bo.getCreateDate());
            testSchemePO.setCreatedBy(bo.getCreatedBy());
            testSchemePO.setModifiedDate(bo.getModifiedDate());
            testSchemePO.setModifiedBy(bo.getModifiedBy());

            return testSchemePO;
        }).collect(Collectors.toList());
        return testSchemePOs;
    }

    @Override
    public LambdaQueryWrapper<TestSchemePO> createWrapper(TestSchemeQueryReq queryReq) {
        Assert.isTrue(Func.isNotEmpty(queryReq),"common.param.miss",new Object[]{Constants.TERM.REQUEST.getCode()});
        // 按严谨逻辑规则，仅支持部分能力，如有需要逐步放开，防止全表操作
        Assert.isTrue(!(Func.isEmpty(queryReq.getIdList())
                && Func.isEmpty(queryReq.getOrderNoList())
                && Func.isEmpty(queryReq.getTestLineInstanceIdList())),
                ResponseCode.PARAM_MISS);
        LambdaQueryWrapper<TestSchemePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        // ID 查询
        if(Func.isNotEmpty(queryReq.getIdList())) {
            lambdaQueryWrapper.in(TestSchemePO::getId, queryReq.getIdList().stream().map(TestSchemeIdBO::getTestSchemeId).collect(Collectors.toSet()));
        }
        // Order 查询
        if(Func.isNotEmpty(queryReq.getOrderNoList())) {
            lambdaQueryWrapper.in(TestSchemePO::getOrderNo,queryReq.getOrderNoList());
        }
        // TestLineInstance 查询
        if(Func.isNotEmpty(queryReq.getTestLineInstanceIdList())){
            lambdaQueryWrapper.in(TestSchemePO::getTestLineInstanceId,queryReq.getTestLineInstanceIdList());
        }
        return lambdaQueryWrapper;
    }
}
