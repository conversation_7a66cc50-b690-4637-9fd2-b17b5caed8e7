package com.sgs.gpo.domain.otsnotes.pptestline.context;

import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.test.testline.v2.TestLineBO;
import com.sgs.framework.model.test.testscheme.TestSchemeIdBO;
import com.sgs.gpo.domain.otsnotes.pptestline.entity.TestSchemePO;
import com.sgs.gpo.domain.otsnotes.pptestline.model.rsp.TestSchemeVO;
import lombok.Data;

import java.util.List;

@Data
public class TestSchemeHandleContext<Input, Domain> extends BaseContext<Input,Domain> {
    protected List<Domain> domainList;
    protected List<Domain> inPutList;

    protected List<TestLineBO> testLineBOList;
    protected List<TestSchemePO> testSchemePOList;
    protected List<TestSchemePO> testSchemePOListByTestLineId;
    protected List<TestSchemeIdBO> testSchemeIdBOList;
    protected List<TestSchemeVO> testSchemeVOList;
}
