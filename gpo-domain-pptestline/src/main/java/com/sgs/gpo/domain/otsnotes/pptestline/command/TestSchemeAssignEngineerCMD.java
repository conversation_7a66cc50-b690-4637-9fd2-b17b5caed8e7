package com.sgs.gpo.domain.otsnotes.pptestline.command;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.gpo.domain.otsnotes.pptestline.context.TestSchemeHandleContext;
import com.sgs.gpo.domain.otsnotes.pptestline.model.req.TestSchemeAssignEngineerReq;
import com.sgs.gpo.domain.otsnotes.pptestline.model.rsp.TestSchemeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@Scope
public class TestSchemeAssignEngineerCMD extends BaseCommand<TestSchemeHandleContext<TestSchemeAssignEngineerReq, TestSchemeVO>> {

    @Override
    public BaseResponse validateParam(TestSchemeHandleContext<TestSchemeAssignEngineerReq, TestSchemeVO> context) {
        return null;
    }

    @Override
    public BaseResponse execute(TestSchemeHandleContext<TestSchemeAssignEngineerReq, TestSchemeVO> context) {
        return null;
    }
}
