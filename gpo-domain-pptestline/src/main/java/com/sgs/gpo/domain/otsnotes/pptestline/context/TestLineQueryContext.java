package com.sgs.gpo.domain.otsnotes.pptestline.context;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.model.test.condition.v2.ConditionBO;
import com.sgs.framework.model.test.testline.TestLineExecutionBO;
import com.sgs.framework.model.test.testline.v2.TestLineBO;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixBO;
import com.sgs.framework.model.test.testsample.v2.TestSampleBO;
import com.sgs.framework.model.test.testscheme.TestSchemeBO;
import com.sgs.framework.model.trims.labsection.LabSectionBO;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.analyte.AnalytePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.condition.*;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.attachment.OrderAttachmentPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.ordercitation.OrderCitationPO;
import com.sgs.gpo.facade.model.otsnotes.testline.TestLinePageBO;
import com.sgs.priceengine.facade.model.DTO.TLAmountDTO;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestLineAnalyteRsp;
import com.sgs.trimslocal.facade.model.condition.rsp.TestConditionRsp;
import com.sgs.trimslocal.facade.model.pp.rsp.GetPpBaseInfoRsp;
import com.sgs.trimslocal.facade.model.testline.rsp.GetCitationBaseInfoRsp;
import com.sgs.trimslocal.facade.model.testline.rsp.TestLineSimplifyInfoRsp;
import com.sgs.trimslocal.facade.model.workinginstruction.rsp.WorkingInstructionRsp;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/6/28 23:01
 */
@Data
public class TestLineQueryContext<Input> extends BaseContext<Input,TestLineBO> {
    /**订单号*/
    private String orderId;
    /**测试项列表*/
    protected List<TestLineBO> testLineList;
    protected List<com.sgs.framework.model.test.testline.TestLineBO> testLineV1List;

    protected String testLineGroupModel = Constants.OBJECT.JOB.TESTLINE_GROUP_MODE.LAB_SECTION;
    protected List<TestLineExecutionBO> testExecutionList;
    protected Set<String> orderIdList;
    protected Set<String> testLineInstanceIdList;
    protected List<OrderCitationPO> orderCitationList;
    protected List<LabSectionBO> labSectionList;
    protected List<AnalytePO> analyteList;
    protected List<TestConditionGroupLanguagePO> testConditionGroupLanguageList;
    protected List<TestConditionGroupPO> testConditionGroupList;
    protected List<TestPpConditionGroupPO> testPpConditionGroupList;

    protected List<TestLineSimplifyInfoRsp> testLineSimplifyInfoRspList;
    protected List<TestLineAnalyteRsp> testLineAnalyteRspList;
    protected List<TestConditionRsp> testConditionRspList;
    protected List<WorkingInstructionRsp> workingInstructionRspList;
    protected List<GetCitationBaseInfoRsp> citationBaseInfoRspList;
    protected List<GetPpBaseInfoRsp> ppBaseInfoRspList;

    protected boolean chineseFlag = false;


    private Page<TestLineBO> testLineBOPage;
    private List<OrderBO> orderBOList;
    private Page<TestLinePageBO> testLinePageBOPage;
    private List<TLAmountDTO> tlAmountList;
    private List<OrderAttachmentPO> orderAttachmentPOList;
    private List<TestMatrixBO> testMatrixBOList;
    private List<TestSampleBO> testSampleBOList;
    private List<TestSchemeBO> testSchemeList;
    private List<ConditionBO> conditionList;

}
