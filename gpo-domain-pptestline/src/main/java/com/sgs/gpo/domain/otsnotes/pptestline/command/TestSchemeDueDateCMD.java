package com.sgs.gpo.domain.otsnotes.pptestline.command;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.domain.otsnotes.pptestline.context.TestSchemeHandleContext;
import com.sgs.gpo.domain.otsnotes.pptestline.model.req.TestSchemeDueDateReq;
import com.sgs.gpo.domain.otsnotes.pptestline.model.req.TestSchemeQueryReq;
import com.sgs.gpo.domain.otsnotes.pptestline.model.rsp.TestSchemeVO;
import com.sgs.gpo.domain.otsnotes.pptestline.subdomain.ITestSchemeService;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineDomainService;
import lombok.experimental.var;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
@Service
@Scope
public class TestSchemeDueDateCMD extends BaseCommand<TestSchemeHandleContext<TestSchemeDueDateReq, TestSchemeVO>> {

    @Autowired
    private ITestSchemeService testSchemeService;
    @Autowired
    private ITestLineDomainService testLineDomainService;
    @Override
    public BaseResponse validateParam(TestSchemeHandleContext<TestSchemeDueDateReq, TestSchemeVO> context) {
        if(Func.isEmpty(context) || Func.isEmpty(context.getParam())){
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        return BaseResponse.newSuccessInstance(true);
    }
    @Override
    public BaseResponse before(TestSchemeHandleContext<TestSchemeDueDateReq, TestSchemeVO> context){
        context.setLab(SystemContextHolder.getLab());
        TestSchemeQueryReq testSchemeQueryReq = new TestSchemeQueryReq();
        testSchemeQueryReq.setBaseQuery(true);
        testSchemeQueryReq.setLab(context.getLab());
        testSchemeQueryReq.setLabId(SystemContextHolder.getLab().getLabId());
        testSchemeQueryReq.setLabCode(SystemContextHolder.getLab().getLabCode());
        testSchemeQueryReq.setIdList(context.getParam().getIdList());
        testSchemeQueryReq.setSchemeIds(context.getParam().getTestSchemeIds());
        // 根据 schemeIds 查询多个 scheme
        var testSchemePOs = testSchemeService.query(testSchemeQueryReq);
        context.setTestSchemePOList(testSchemePOs);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(TestSchemeHandleContext<TestSchemeDueDateReq, TestSchemeVO> context) {
        var testSchemePOs = context.getTestSchemePOList();
        Date dueDate;
        try {
            dueDate = new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(context.getParam().getDueDate()); // 从上下文中获取并转换过期时间
        } catch (Exception e) {
            log.error("Failed to convert due date", e);
            return BaseResponse.newFailInstance("common.date.convert.error", null);
        }
        testSchemePOs.forEach(testSchemePO -> testSchemePO.setExpectDueDate(dueDate)); // 设置预期到期日期
        boolean result = testSchemeService.updateBatchById(testSchemePOs);
        if(!result) return BaseResponse.newFailInstance("testScheme Lab-out failed");
        return BaseResponse.newSuccessInstance(true);
    }
}
