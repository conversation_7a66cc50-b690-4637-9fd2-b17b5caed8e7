package com.sgs.gpo.domain.otsnotes.pptestline.dbstorages.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.gpo.domain.otsnotes.pptestline.entity.TestSchemePO;
import com.sgs.gpo.domain.otsnotes.pptestline.model.req.TestSchemeQueryReq;
import org.apache.ibatis.annotations.Mapper;

/**
 * TestSchemeMapper 接口定义了与测试方案相关的数据库操作。
 * 继承自BaseMapper，提供基本的CRUD操作，并定义了额外的自定义方法。
 */
@Mapper
public interface TestSchemeMapper extends BaseMapper<TestSchemePO> {
    /**
     * 根据查询条件获取测试方案分页总数。
     * @param testSchemeQueryReq 查询条件封装对象
     * @return 符合条件的测试方案分页总数
     */
    Long selectTestSchemePageCount(TestSchemeQueryReq testSchemeQueryReq);

}

