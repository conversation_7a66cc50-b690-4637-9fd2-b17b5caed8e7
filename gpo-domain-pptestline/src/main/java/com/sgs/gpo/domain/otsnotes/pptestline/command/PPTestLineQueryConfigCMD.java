package com.sgs.gpo.domain.otsnotes.pptestline.command;

import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.test.citation.CitationBO;
import com.sgs.framework.model.test.citation.CitationLanguageBO;
import com.sgs.framework.model.test.testline.v2.TestLineBO;
import com.sgs.framework.model.test.testline.v2.TestLineIdBO;
import com.sgs.framework.model.trims.labsection.LabSectionBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testline.TestLineLabTeamDefaultRelationshipPO;
import com.sgs.gpo.domain.otsnotes.pptestline.context.TestLineQueryContext;
import com.sgs.gpo.domain.otsnotes.pptestline.config.PPTestLineConfig;
import com.sgs.gpo.domain.otsnotes.pptestline.model.req.PPTestLineConfigReq;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.ITestLineLabTeamDefaultRelService;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.ITestLineService;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineLabTeamSearchReq;
import com.sgs.gpo.integration.trims.TrimsClient;
import com.sgs.gpo.integration.usermanagement.UserManagementClient;
import com.sgs.otsnotes.facade.model.info.labteam.LabTeam;
import com.sgs.trimslocal.facade.model.citation.req.GetCitationListByTestLineVersionIdReq;
import com.sgs.trimslocal.facade.model.citation.rsp.CitationListRsp;
import com.sgs.trimslocal.facade.model.labsection.req.GetLabSectionListByTestLineVersionIdsReq;
import com.sgs.trimslocal.facade.model.labsection.rsp.GetLabSectionBaseInfoRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PPTestLineQueryConfigCMD extends BaseCommand<TestLineQueryContext<PPTestLineConfigReq>> {

    @Autowired
    private TrimsClient trimsClient;
    @Autowired
    private UserManagementClient userManagementClient;
    @Autowired
    private ITestLineService testLineService;
    @Autowired
    private ITestLineLabTeamDefaultRelService testLineLabTeamDefaultRelService;


    @Override
    public BaseResponse validateParam(TestLineQueryContext<PPTestLineConfigReq> context) {
        PPTestLineConfigReq ppTestLineConfigReq = context.getParam();
        Assert.notNull(ppTestLineConfigReq);
        Assert.notNull(ppTestLineConfigReq.getTestLineInstanceList());

        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(TestLineQueryContext<PPTestLineConfigReq> context) {
        PPTestLineConfigReq ppTestLineConfigReq = context.getParam();
        List<PPTestLineConfig> ppTestLineConfigList = getConfig(ppTestLineConfigReq);
        return BaseResponse.newSuccessInstance(ppTestLineConfigList);
    }

    public List<PPTestLineConfig> getConfig(PPTestLineConfigReq configReq) {

        List<PPTestLineConfig> result = Lists.newArrayList();
        OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
        List<TestLineIdBO> testLineIdBOList = configReq.getTestLineInstanceList()
                .stream()
                .map(testLineInstanceId -> {
                    TestLineIdBO testLineIdBO = new TestLineIdBO();
                    testLineIdBO.setTestLineInstanceId(testLineInstanceId);
                    return testLineIdBO;
                })
                .collect(Collectors.toList());
        orderTestLineReq.setIdList(testLineIdBOList);
        List<TestLineBO> testLineBOList = testLineService.queryBO(orderTestLineReq);
        Assert.notNull(testLineBOList);
        Assert.isTrue(testLineBOList.size()==configReq.getTestLineInstanceList().size(), ResponseCode.NOT_FOUND);
        Set<Long> testLineBaseIdList = testLineBOList.stream().map(TestLineBO::getId).map(TestLineIdBO::getTestLineBaseId).collect(Collectors.toSet());
        Set<Integer> testLineVersionIdList = testLineBOList.stream().map(TestLineBO::getId).map(TestLineIdBO::getTestLineVersionId).collect(Collectors.toSet());
        Set<Integer> testLineIdList = testLineBOList.stream().map(TestLineBO::getId).map(TestLineIdBO::getTestLineId).collect(Collectors.toSet());
        // 设置Citation Config
        List<PPTestLineConfig> citationConfigList = getCitationConfig(testLineBaseIdList);
        // 设置LabSection Config
        List<PPTestLineConfig> labSectionConfigList =getLabSectionConfig(testLineVersionIdList);
        // 设置LabTeam 配置
        //TODO 修改为取订单的
        List<PPTestLineConfig> labTeamConfigListList =getLabTeamConfigList(testLineIdList, SystemContextHolder.getLabCode());

        testLineBOList.forEach(testLineBO->{
            PPTestLineConfig ppTestLineConfig = new PPTestLineConfig();
            TestLineIdBO testLineIdBO = testLineBO.getId();
            ppTestLineConfig.setTestLineInstanceId(testLineIdBO.getTestLineInstanceId());
            ppTestLineConfig.setTestLineBaseId(testLineIdBO.getTestLineBaseId());
            ppTestLineConfig.setTestLineVersionId(testLineIdBO.getTestLineVersionId());
            ppTestLineConfig.setTestLineId(testLineIdBO.getTestLineId());

            PPTestLineConfig ppTestLineCitationConfig = citationConfigList.stream().filter(item-> Func.equalsSafe(item.getTestLineBaseId(),testLineBO.getId().getTestLineBaseId())).findFirst().orElse(null);
            ppTestLineConfig.setCitationList(ppTestLineCitationConfig==null?Lists.newArrayList():ppTestLineCitationConfig.getCitationList());

            PPTestLineConfig ppTestLineLabSectionConfig = labSectionConfigList.stream().filter(item->Func.equalsSafe(item.getTestLineVersionId(),testLineBO.getId().getTestLineVersionId())).findFirst().orElse(null);
            ppTestLineConfig.setLabSectionList(ppTestLineLabSectionConfig==null?Lists.newArrayList():ppTestLineLabSectionConfig.getLabSectionList());

            PPTestLineConfig ppTestLineLabTeamConfig = labTeamConfigListList.stream().filter(item->Func.equalsSafe(item.getTestLineId(),testLineBO.getId().getTestLineId())).findFirst().orElse(null);
            ppTestLineConfig.setLabTeamList(ppTestLineLabTeamConfig==null?Lists.newArrayList():ppTestLineLabTeamConfig.getLabTeamList());
            result.add(ppTestLineConfig);
        });

        return result;
    }

    public List<PPTestLineConfig> getCitationConfig(Set<Long> testLineBaseIdList){
        List<PPTestLineConfig> result = Lists.newArrayList();
        //TODO 确认Trims是否有批量接口
        testLineBaseIdList.forEach(testLineBaseId -> {
            PPTestLineConfig ppTestLineConfig = new PPTestLineConfig();
            ppTestLineConfig.setTestLineBaseId(testLineBaseId);
            List<CitationBO> citationBOList = Lists.newArrayList();
            GetCitationListByTestLineVersionIdReq getCitationListByTestLineVersionIdReq = new GetCitationListByTestLineVersionIdReq();
            getCitationListByTestLineVersionIdReq.setTestLineBaseId(testLineBaseId);
            getCitationListByTestLineVersionIdReq.setCallerBU(SystemContextHolder.getBuCode());
            // 查询TL的Citation集合
            BaseResponse<List<CitationListRsp>> testLineCitationRsp = trimsClient.getTestLineCitationList(getCitationListByTestLineVersionIdReq);

            if(testLineCitationRsp.isSuccess()&&Func.isNotEmpty(testLineCitationRsp.getData())) {
                testLineCitationRsp.getData().forEach(item->{
                    CitationBO citationBO = new CitationBO();
                    citationBO.setCitationType(item.getCitationType());
                    citationBO.setCitationBaseId(item.getCitationBaseId());
                    citationBO.setCitationVersionId(item.getCitationVersionId());
                    citationBO.setCitationId(item.getCitationId());
                    citationBO.setCitationFullName(item.getCitationFullName());
                    citationBO.setCitationSectionId(item.getCitationSectionId());
                    citationBO.setCitationSectionName(item.getCitationSectionName());

                    if(Func.isNotEmpty(item.getOtherLanguageItems())){
                        List<CitationLanguageBO> citationLanguageList = Lists.newArrayList();
                        item.getOtherLanguageItems().forEach(lang->{
                            CitationLanguageBO citationLanguageBO = new CitationLanguageBO();
                            citationLanguageBO.setLanguageId(lang.getLanguageId());
                            citationLanguageBO.setCitationFullName(lang.getCitationFullName());
                            citationLanguageList.add(citationLanguageBO);
                        });
                        citationBO.setLanguageList(citationLanguageList);
                    }

                    citationBOList.add(citationBO);
                });
            }
            ppTestLineConfig.setCitationList(citationBOList);
            result.add(ppTestLineConfig);
        });
        return result;
    }

    public List<PPTestLineConfig> getLabSectionConfig(Set<Integer> testLineVersionIdList){

        GetLabSectionListByTestLineVersionIdsReq getLabSectionBaseInfoReq = new GetLabSectionListByTestLineVersionIdsReq();
        getLabSectionBaseInfoReq.setCallerBU(SystemContextHolder.getBuCode());
        getLabSectionBaseInfoReq.setTestLineVersionIds(Lists.newArrayList(testLineVersionIdList));
        BaseResponse<List<GetLabSectionBaseInfoRsp>> getLabSectionRsp =trimsClient.getLabSectionListByTestLineVersionIds(getLabSectionBaseInfoReq);

        Map<Integer,PPTestLineConfig> testLineVersionConfigMap= new HashMap<>();
        if(getLabSectionRsp.isSuccess() && Func.isNotEmpty(getLabSectionRsp.getData())){
            getLabSectionRsp.getData().forEach(item->{
                PPTestLineConfig ppTestLineConfig ;
                Integer testLineVersionId = item.getTestLineVersionId();
                if(testLineVersionConfigMap.containsKey(testLineVersionId)){
                    ppTestLineConfig = testLineVersionConfigMap.get(testLineVersionId);
                }else{
                    ppTestLineConfig = new PPTestLineConfig();
                    ppTestLineConfig.setLabSectionList(Lists.newArrayList());
                    ppTestLineConfig.setTestLineVersionId(item.getTestLineVersionId());
                }
                LabSectionBO labSectionBO = new LabSectionBO();
                labSectionBO.setLabSectionBaseId(item.getLabSectionBaseId());
                labSectionBO.setLabSectionId(item.getLabSectionId());
                labSectionBO.setLabSectionCode(item.getLabSectionCode());
                labSectionBO.setLabSectionName(item.getLabSectionName());
                labSectionBO.setLabSectionSeq(item.getLabSectionSeq());
                ppTestLineConfig.getLabSectionList().add(labSectionBO);
                testLineVersionConfigMap.put(testLineVersionId,ppTestLineConfig);
            });
        }
        List<PPTestLineConfig> result = Lists.newArrayList(testLineVersionConfigMap.values());

        return result;
    }

    public List<PPTestLineConfig> getLabTeamConfigList(Set<Integer> testLineIdList,String labCode){
        List<PPTestLineConfig> result = new ArrayList<>();
        TestLineLabTeamSearchReq req = new TestLineLabTeamSearchReq();
        req.setTestLineIdList(testLineIdList);
        req.setLabCode(labCode);
        // 查询TL的LabTeam集合；
        BaseResponse<List<TestLineLabTeamDefaultRelationshipPO>> response = testLineLabTeamDefaultRelService.select(req);
        if(response.isSuccess() && Func.isNotEmpty(response.getData())){
            Map<Integer,PPTestLineConfig> testLineConfigMap= new HashMap<>();
            response.getData().forEach(item->{
                Integer testLineId = item.getTestLineId();
                PPTestLineConfig ppTestLineConfig;
                if(testLineConfigMap.containsKey(testLineId)){
                    ppTestLineConfig = testLineConfigMap.get(testLineId);
                }else{
                    ppTestLineConfig = new PPTestLineConfig();
                    ppTestLineConfig.setTestLineId(testLineId);
                    ppTestLineConfig.setLabTeamList(new ArrayList<>());
                }
                LabTeam labTeam = new LabTeam();
                labTeam.setLabTeamCode(item.getLabTeamCode());
                labTeam.setLabTeamName(item.getLabTeamCode());
                ppTestLineConfig.getLabTeamList().add(labTeam);
                testLineConfigMap.put(testLineId,ppTestLineConfig);
            });
            result = Lists.newArrayList(testLineConfigMap.values());
        }

        return result;
    }
}
