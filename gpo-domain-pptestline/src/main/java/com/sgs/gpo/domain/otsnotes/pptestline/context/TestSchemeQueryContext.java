package com.sgs.gpo.domain.otsnotes.pptestline.context;

import com.sgs.framework.core.base.PageBO;
import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.test.testline.v2.TestLineBO;

import com.sgs.framework.model.test.testscheme.TestSchemeBO;
import com.sgs.gpo.domain.otsnotes.pptestline.entity.TestLinePretreatmentRelationshipPO;
import com.sgs.gpo.domain.otsnotes.pptestline.entity.TestSchemePO;

import lombok.Data;

import java.util.List;

@Data
public class TestSchemeQueryContext<Input, Domain> extends BaseContext<Input,Domain> {
    protected PageBO<Domain> testSchemePage;
    protected PageBO<TestSchemeBO> testSchemeBOPage;


    protected List<TestSchemePO> testSchemePOList;
    protected List<TestLinePretreatmentRelationshipPO> testGroupPOList;
    protected List<TestLineBO> testLineBOList;
    protected boolean chineseFlag = false;
}
