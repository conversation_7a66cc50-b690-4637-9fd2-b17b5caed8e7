package com.sgs.gpo.domain.otsnotes.pptestline.subdomain;

import com.sgs.framework.model.test.testscheme.TestSchemeBO;
import com.sgs.framework.model.test.testscheme.TestSchemeIdBO;
import com.sgs.framework.open.platform.base.service.IBaseService;
import com.sgs.gpo.domain.otsnotes.pptestline.entity.TestSchemePO;
import com.sgs.gpo.domain.otsnotes.pptestline.model.req.TestSchemeQueryReq;

public interface ITestSchemeService extends IBaseService<TestSchemeBO, TestSchemePO, TestSchemeIdBO, TestSchemeQueryReq> {

}
