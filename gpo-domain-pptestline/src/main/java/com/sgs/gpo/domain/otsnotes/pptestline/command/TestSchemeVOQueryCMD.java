package com.sgs.gpo.domain.otsnotes.pptestline.command;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.base.MoreObjects;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.PageBO;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.model.test.condition.conditiongroup.ConditionGroupBO;
import com.sgs.framework.model.test.testline.v2.TestLineSampleBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.domain.otsnotes.pptestline.context.TestSchemeQueryContext;
import com.sgs.gpo.domain.otsnotes.pptestline.model.req.TestSchemeQueryReq;
import com.sgs.gpo.domain.otsnotes.pptestline.model.rsp.TestSchemeVO;
import com.sgs.gpo.domain.otsnotes.pptestline.subdomain.ITestSchemeGroupService;
import com.sgs.gpo.domain.otsnotes.pptestline.subdomain.ITestSchemeService;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineDomainService;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import lombok.experimental.var;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@Scope
public class TestSchemeVOQueryCMD extends BaseCommand<TestSchemeQueryContext<TestSchemeQueryReq, TestSchemeVO>> {

    @Autowired
    private ITestSchemeGroupService testSchemeGroupService;
    @Autowired
    private ITestLineDomainService testLineDomainService;
    @Autowired
    private ITestSchemeService testSchemeService;

    @Override
    public BaseResponse validateParam(TestSchemeQueryContext<TestSchemeQueryReq, TestSchemeVO> context) {
        // 验证参数
        if(Func.isEmpty(context) || Func.isEmpty(context.getParam())){
            return BaseResponse.newFailInstance("common.param.miss", null);
        }

        Integer rows = context.getRows();
        Integer page = context.getPage();
        if (Func.isEmpty(page) || Func.isEmpty(rows)) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"rows/page"});
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(TestSchemeQueryContext<TestSchemeQueryReq, TestSchemeVO> context) {
        // 执行查询
        BaseResponse buildResponse = this.buildDomain(context);
        if (buildResponse.isFail()) {
            return buildResponse;
        }
        return BaseResponse.newSuccessInstance(context.getTestSchemePage());
    }

    @Override
    public BaseResponse before(TestSchemeQueryContext<TestSchemeQueryReq, TestSchemeVO> context) {
        // 执行前操作
        TestSchemeQueryReq testSchemeQueryReq = context.getParam();

        if(Func.isEmpty(SystemContextHolder.getLab())){
            return BaseResponse.newFailInstance("common.miss", new Object[]{"lab"});
        }

        // 设置实验室上下文
        testSchemeQueryReq.setLab(SystemContextHolder.getLab());
        testSchemeQueryReq.setLabId(SystemContextHolder.getLab().getLabId());
        testSchemeQueryReq.setLabCode(SystemContextHolder.getLab().getLabCode());
        testSchemeQueryReq.setOrderNo(testSchemeQueryReq.getOrderNo());
        testSchemeQueryReq.setDueDate(testSchemeQueryReq.getDueDate());
        testSchemeQueryReq.setStartDate(testSchemeQueryReq.getStartDate());
        testSchemeQueryReq.setEndDate(testSchemeQueryReq.getEndDate());

        var testSchemePOs = testSchemeService.query(testSchemeQueryReq);
        context.setTestSchemePOList(testSchemePOs);

        if(testSchemePOs.isEmpty()){
            return BaseResponse.newSuccessInstance(true);
        }

        var queryTestGroup = testSchemeGroupService.queryTestSchemeGroupPage(testSchemeQueryReq, context.getPage(),context.getRows());
        context.setTestGroupPOList(queryTestGroup.getRecords());
        //必须有入参,不允许全表查询
        OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
        orderTestLineReq.setOrderNo(testSchemeQueryReq.getOrderNo());
        orderTestLineReq.setLabId(testSchemeQueryReq.getLabId());
        orderTestLineReq.setLabCode(testSchemeQueryReq.getLabCode());
        var testLineBOList = testLineDomainService.queryBO(orderTestLineReq);
        context.setTestLineBOList(testLineBOList.getData());

        super.before(context);

        return BaseResponse.newSuccessInstance(true);
    }



    @Override
    public BaseResponse<IPage<TestSchemeVO>> buildDomain(TestSchemeQueryContext<TestSchemeQueryReq, TestSchemeVO> context) {
        // 构建领域模型
        super.buildDomain(context);
        // return result
        var testSchemeVOList = new ArrayList<TestSchemeVO>();
        try {
            // 获取上下文中的数据
            var testSchemePOList = context.getTestSchemePOList();
            var testGroupPOList = context.getTestGroupPOList();
            var testLineBOList = context.getTestLineBOList();
            // 需要用查询到Scheme和Group的关系 组合一个
            Map<String, TestSchemeVO> groupSchemeMap = new HashMap<>();
            for (var g : testGroupPOList) {
                var schemeId = g.getTestSchemeId();
                var testSchemePO = testSchemePOList.stream().filter(f -> f.getId().equalsIgnoreCase(schemeId)).findFirst().orElse(null);
                if (testSchemePO == null) continue;
                var testLineBO = testLineBOList.stream().filter(
                        f -> f.getId().getTestLineInstanceId().equals(testSchemePO.getTestLineInstanceId())
                ).findFirst().orElse(null);
                if (testLineBO == null || g.getTestGroupName() == null || g.getId() == null)
                    continue;

                // 检查是否已经存在该group
                TestSchemeVO parentGroupScheme = groupSchemeMap.get(g.getTestGroupName());
                if (parentGroupScheme == null) {
                    // 创建新的父级groupscheme
                    parentGroupScheme = new TestSchemeVO();
                    parentGroupScheme.setSchemeId(g.getId().toString());
                    parentGroupScheme.setGroupSeq(g.getTestSeq());
                    parentGroupScheme.setGroupName(g.getTestGroupName());
                    parentGroupScheme.setGroupId(g.getId().toString());
                    parentGroupScheme.setGroupSeq(g.getTestSeq());
                    parentGroupScheme.setChildren(new ArrayList<>());
                    groupSchemeMap.put(g.getTestGroupName(), parentGroupScheme);
                    testSchemeVOList.add(parentGroupScheme);
                }

                // 创建schemevo并添加到父级children中
                var testSchemeVO = new TestSchemeVO();
                testSchemeVO.setSchemeId(testSchemePO.getId());
                testSchemeVO.setTestLineId(testLineBO.getId().getTestLineInstanceId());
                testSchemeVO.setTestLine(MoreObjects.firstNonNull(testLineBO.getHeader().getEvaluationName(), ""));
                testSchemeVO.setGroupSeq(testSchemePO.getTestSeq());
                testSchemeVO.setGroupId(MoreObjects.firstNonNull(g.getId().toString(), null));
                testSchemeVO.setGroupName(g.getTestGroupName());
                testSchemeVO.setStandard(MoreObjects.firstNonNull(testLineBO.getCitation().getCitationName(),""));
                testSchemeVO.setOrderNo(testSchemePO.getOrderNo());
                var ppNo = testLineBO.getPpTestLineRelList().stream().map(
                        m -> m.getId().getPpNo() == null ? "" : m.getId().getPpNo().toString())
                        .filter(f -> !Func.isEmpty(f))
                        .collect(Collectors.joining(", "));
                testSchemeVO.setPpNo(ppNo);
                var samples = MoreObjects.firstNonNull(testLineBO.getRelationship().getParallel().getTestSampleList(),null);
                testSchemeVO.setSamples(samples == null ? "": samples.stream().map(
                        TestLineSampleBO::getTestSampleNo).collect(Collectors.joining(", ")));
                testSchemeVO.setEndDate(testSchemePO.getEndDate());
                testSchemeVO.setPendingFlag(testSchemePO.getPendingFlag());
                testSchemeVO.setStartDate(testSchemePO.getStartDate());
                testSchemeVO.setOwner(testSchemePO.getOwner());
                testSchemeVO.setLabSection(MoreObjects.firstNonNull(testLineBO.getHeader().getLabTeam(),""));
                testSchemeVO.setRemark(testSchemePO.getRemark());
                testSchemeVO.setExpectDueDate(testSchemePO.getExpectDueDate());
                testSchemeVO.setStatus(testSchemePO.getStatus());
                testSchemeVO.setWiForSample("");//TODO:获取什么?
                testSchemeVO.setWiForCs(testSchemePO.getCreatedBy());
                testSchemeVO.setAnalyte(testLineBO.getAnalyteList().stream()
                        .map(analyte -> analyte.getHeader().getAnalyteName())
                        .collect(Collectors.joining(", ")));
                testSchemeVO.setCondition(testSchemePO.getTestSchemeName());
//                var conditionGroupList = testLineBO.getConditionGroupList();
//                if (conditionGroupList != null && !conditionGroupList.isEmpty()) {
//                    testSchemeVO.setCondition(conditionGroupList.stream()
//                            .map(ConditionGroupBO::getCombinedConditionDescription)
//                            .collect(Collectors.joining(", ")));
//                }

//                testSchemeVO.setTestLineJobs();

                parentGroupScheme.getChildren().add(testSchemeVO);
            }
            PageBO<TestSchemeVO> testSchemePage = new PageBO<>();
            testSchemePage.setRecords(testSchemeVOList);
            testSchemePage.setTotal(testSchemeVOList.size());
            testSchemePage.setSize(context.getRows());
            context.setTestSchemePage(testSchemePage);
        } catch (Exception e) {
            log.error("TestSchemeQueryCMD.buildDomain error", e);
            throw new RuntimeException(e);
        }

        return BaseResponse.newSuccessInstance(true);
    }
}
