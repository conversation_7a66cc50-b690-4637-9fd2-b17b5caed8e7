package com.sgs.gpo.domain.otsnotes.pptestline.subdomain;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.gpo.domain.otsnotes.pptestline.entity.TestLinePretreatmentRelationshipPO;
import com.sgs.gpo.domain.otsnotes.pptestline.model.req.TestSchemeQueryReq;

import java.util.List;

public interface ITestSchemeGroupService extends IService<TestLinePretreatmentRelationshipPO> {
    /**
     * 根据测试方案组的请求获取测试方案组
     * @param testSchemeQueryReq 测试方案查询请求
     * @return 测试方案组
     */
    Page<TestLinePretreatmentRelationshipPO> queryTestSchemeGroupPage(TestSchemeQueryReq testSchemeQueryReq, Integer page, Integer rows);

    List<TestLinePretreatmentRelationshipPO> queryTestSchemeGroup(TestSchemeQueryReq testSchemeQueryReq);
}
