package com.sgs.gpo.domain.otsnotes.pptestline.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.gpo.domain.otsnotes.pptestline.dbstorages.mapper.TestLinePretreatmentRelationshipMapper;
import com.sgs.gpo.domain.otsnotes.pptestline.entity.TestLinePretreatmentRelationshipPO;
import com.sgs.gpo.domain.otsnotes.pptestline.model.req.TestSchemeQueryReq;
import com.sgs.gpo.domain.otsnotes.pptestline.subdomain.ITestSchemeGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TestSchemeGroupServiceImpl
        extends ServiceImpl<TestLinePretreatmentRelationshipMapper, TestLinePretreatmentRelationshipPO>
        implements ITestSchemeGroupService {

    @Autowired
    private TestLinePretreatmentRelationshipMapper testLineGroupMapper;
    @Override
    public Page<TestLinePretreatmentRelationshipPO> queryTestSchemeGroupPage(TestSchemeQueryReq testSchemeQueryReq, Integer page, Integer rows) {
        QueryWrapper<TestLinePretreatmentRelationshipPO> queryWrapper = new QueryWrapper<>();
        //queryWrapper.in("GeneralOrderInstanceId", testSchemeQueryReq.getOrderNo());
        Page<TestLinePretreatmentRelationshipPO> queryPage = new Page<TestLinePretreatmentRelationshipPO>(page, rows);
        queryPage.setPages(page);
        queryPage.setSize(rows);
        return testLineGroupMapper.selectPage(queryPage,queryWrapper);
    }

    @Override
    public List<TestLinePretreatmentRelationshipPO> queryTestSchemeGroup(TestSchemeQueryReq testSchemeQueryReq) {
        return testLineGroupMapper.selectList(null);
    }
}
