package com.sgs.gpo.domain.otsnotes.pptestline.service.impl;

import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.PageBO;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.test.testline.v2.TestLineBO;
import com.sgs.framework.model.test.testline.v2.TestLineIdBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.domain.otsnotes.pptestline.command.PPTestLinePageCMD;
import com.sgs.gpo.domain.otsnotes.pptestline.command.PPTestLineQueryCMD;
import com.sgs.gpo.domain.otsnotes.pptestline.command.PPTestLineQueryConfigCMD;
import com.sgs.gpo.domain.otsnotes.pptestline.context.TestLineQueryContext;
import com.sgs.gpo.domain.otsnotes.pptestline.config.PPTestLineConfig;
import com.sgs.gpo.domain.otsnotes.pptestline.model.req.PPTestLineConfigReq;
import com.sgs.gpo.domain.service.otsnotes.testline.impl.TestLineDomainServiceImpl;
import com.sgs.gpo.domain.otsnotes.pptestline.service.IPPTestLineDomainService;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.*;


@Slf4j
@Service
@Primary
public class PPTestLineDomainServiceImpl extends TestLineDomainServiceImpl implements IPPTestLineDomainService {


    @Override
    public BaseResponse<List<PPTestLineConfig>> getConfig(PPTestLineConfigReq configReq) {

        TestLineQueryContext<PPTestLineConfigReq> context = new TestLineQueryContext<>();
        context.setParam(configReq);
        context.setLab(SystemContextHolder.getLab());
        context.setUserInfo(SystemContextHolder.getUserInfo());
        return BaseExecutor.start(PPTestLineQueryConfigCMD.class,context);
    }

    @Override
    public BaseResponse<List<TestLineBO>> queryBO(OrderTestLineReq orderTestLineReq) {
        TestLineQueryContext<OrderTestLineReq> context = new TestLineQueryContext<>();
        context.setParam(orderTestLineReq);
        context.setLab(SystemContextHolder.getLab());
        context.setUserInfo(SystemContextHolder.getUserInfo());
        return BaseExecutor.start(PPTestLineQueryCMD.class,context);
    }

    @Override
    public BaseResponse<PageBO<TestLineBO>> pageBO(OrderTestLineReq queryReq, Integer page, Integer rows) {
        TestLineQueryContext<OrderTestLineReq> context = new TestLineQueryContext<>();
        context.setParam(queryReq);
        context.setLab(SystemContextHolder.getLab());
        context.setUserInfo(SystemContextHolder.getUserInfo());
        context.setPage(page);
        context.setRows(rows);
        return BaseExecutor.start(PPTestLinePageCMD.class,context);
    }

    @Override
    public BaseResponse<TestLineBO> getDetail(TestLineIdBO testLineIdBO) {
        // 入参不允许为空
        Assert.isTrue(Func.isNotEmpty(testLineIdBO),"common.param.miss",new Object[]{Constants.TERM.REQUEST});
        OrderTestLineReq orderTestLineReq = new OrderTestLineReq();

        orderTestLineReq.setTestLineInstanceIdList(Sets.newHashSet(testLineIdBO.getTestLineInstanceId()));
        TestLineQueryContext<OrderTestLineReq> testLineQueryContext = new TestLineQueryContext<>();
        testLineQueryContext.setParam(orderTestLineReq);
        testLineQueryContext.setToken(SystemContextHolder.getSgsToken());
        testLineQueryContext.setLab(SystemContextHolder.getLab());

        BaseResponse<List<TestLineBO>> response =  BaseExecutor.start(PPTestLineQueryCMD.class, testLineQueryContext);
        TestLineBO testLineBO = null;
        if(response.isFail()){
            return BaseResponse.newFailInstance(response.getMessage());
        }
        if(Func.isNotEmpty(response.getData())&&response.getData().size()==1){
            testLineBO = response.getData().stream().findFirst().orElse(null);
        }else{
            return BaseResponse.newFailInstance(ResponseCode.NOT_FOUND);
        }
        return BaseResponse.newSuccessInstance(testLineBO);
    }
}
