<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <parent>
        <artifactId>gpo-micro-service</artifactId>
        <groupId>com.sgs.gpo</groupId>
        <version>0.1.89-af</version>
    </parent>
    <version>${gpo-micro-service.version}</version>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>gpo-domain-pptestline</artifactId>
    <name>Archetype - gpo-domain-pptestline</name>
    <url>http://maven.apache.org</url>

    <dependencies>
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>gpo-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>gpo-facade-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sgs.framework</groupId>
            <artifactId>sgs-framework-mybatisplus</artifactId>
            <version>${sgs.framework.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sgs.framework</groupId>
                    <artifactId>sgs-framework-tool</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis.plus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sgs.framework</groupId>
            <artifactId>sgs-framework-clickhouse</artifactId>
            <version>${sgs.framework.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>gpo-integration</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>gpo-dbstorages</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>gpo-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>gpo-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>gpo-domain-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>gpo-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sgs.framework</groupId>
            <artifactId>sgs-framework-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sgs.framework</groupId>
            <artifactId>sgs-framework-open-platform</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>preorder-facade-model</artifactId>
        </dependency>
    </dependencies>
</project>
