package com.sgs.gpo.integration.rd;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.log.SystemLogHelper;
import com.sgs.framework.log.enums.SystemLogType;
import com.sgs.framework.log.model.SystemLog;
import com.sgs.framework.tool.jackson.JsonUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.HttpClientUtil;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.config.InterfaceConfig;
import com.sgs.otsnotes.facade.model.enums.ObjectType;
import com.sgs.testdatabiz.facade.model.base.BaseModel;
import com.sgs.testdatabiz.facade.model.req.ConclusionInfoReq;
import com.sgs.testdatabiz.facade.model.req.rd.BatchExistReportDataReq;
import com.sgs.testdatabiz.facade.model.req.rd.CancelReportDataReq;
import com.sgs.testdatabiz.facade.model.req.rd.ImportReportDataReq;
import com.sgs.testdatabiz.facade.model.rsp.ConclusionInfoRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class RdClient {

    @Resource
    private InterfaceConfig interfaceConfig;
    @Autowired
    private SystemLogHelper systemLogHelper;

    /**
     * 调用RD import方法
     *
     * @param importReportDataReq
     * @return
     */
    public BaseResponse importReportData(ImportReportDataReq importReportDataReq) {
        BaseResponse response = new BaseResponse();
        if (Func.isEmpty(importReportDataReq)) {
            return BaseResponse.newFailInstance(ResponseCode.PARAM_MISS);
        }
        String url = String.format("%s/testdatabiz/api/report-data/import", interfaceConfig.getBaseUrl());
        try {
            Map<String, String> headerMaps = this.buildHeaderMap(importReportDataReq);
            String result = HttpClientUtil.postJsonHeader(url, importReportDataReq, headerMaps);
            if (Func.isEmpty(result)) {
                return BaseResponse.newFailInstance("rd import 返回为空");
            }
            return JsonUtil.parse(result, BaseResponse.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("dm import 异常：{}", e);
            response.setMessage(e.getMessage());
            response.setStatus(ResponseCode.FAIL.getCode());
        }
        return response;
    }

    /**
     * 调用RD cancel方法
     *
     * @param cancelReportDataReq
     * @return
     */
    public BaseResponse cancelReportData(CancelReportDataReq cancelReportDataReq) {
        BaseResponse response = new BaseResponse();
        if (Func.isEmpty(cancelReportDataReq)) {
            return BaseResponse.newFailInstance(ResponseCode.PARAM_MISS);
        }
        String url = String.format("%s/testdatabiz/api/report-data/cancel", interfaceConfig.getBaseUrl());
        String result = "";
        try {
            Map<String, String> headerMaps = this.buildHeaderMap(cancelReportDataReq);
            log.info("dm cancel 入参：{} {}", cancelReportDataReq, headerMaps);
            result = HttpClientUtil.postJsonHeader(url, cancelReportDataReq, headerMaps);
            log.info("dm cancel 返回：{}", result);
            if (Func.isEmpty(result)) {
                return BaseResponse.newFailInstance("rd cancel 返回为空");
            }
            return JsonUtil.parse(result, BaseResponse.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("dm cancel 异常：{}", e);
            response.setMessage(e.getMessage());
            response.setStatus(ResponseCode.FAIL.getCode());
        } finally {
            try {
                SystemLog resultLog = new SystemLog();
                resultLog.setObjectType(ObjectType.Report.getCode());
                resultLog.setObjectNo(cancelReportDataReq.getReportNo());
                resultLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                resultLog.setType(SystemLogType.API.getType());
                resultLog.setRemark("Call RD Cancel Report");
                resultLog.setResponse(JSON.toJSONString(result));
                resultLog.setRequest(JSON.toJSONString(cancelReportDataReq));
                resultLog.setOperationType("Call RD Cancel Report");
                resultLog.setLocationCode(SystemContextHolder.getLabCode());
                systemLogHelper.save(resultLog);
            } catch (Exception e) {
                log.error("Call RD Cancel Report error:{}", e);
            }
        }
        return response;
    }


    /**
     * 调用RD batchExist
     *
     * @param batchExistReportDataReq
     * @return
     */
    public BaseResponse batchExist(BatchExistReportDataReq batchExistReportDataReq) {
        BaseResponse response = new BaseResponse();
        if (Func.isEmpty(batchExistReportDataReq) || Func.isEmpty(batchExistReportDataReq.getReportNos())) {
            return BaseResponse.newFailInstance(ResponseCode.PARAM_MISS);
        }
        String url = String.format("%s/testdatabiz/api/report-data/batch-exist", interfaceConfig.getBaseUrl());
        String result = "";
        try {
            Map<String, String> headerMaps = this.buildHeaderMap(batchExistReportDataReq);
            log.info("RD batchExist 入参：{} {}", batchExistReportDataReq, headerMaps);
            result = HttpClientUtil.postJsonHeader(url, batchExistReportDataReq, headerMaps);
            log.info("RD batchExist 返回：{}", result);
            if (Func.isEmpty(result)) {
                return BaseResponse.newFailInstance("rd batchExist 返回为空");
            }
            return JsonUtil.parse(result, BaseResponse.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("RD batchExist 异常：{}", e);
            response.setMessage(e.getMessage());
            response.setStatus(ResponseCode.FAIL.getCode());
        } finally {
            try {
                SystemLog resultLog = new SystemLog();
                resultLog.setObjectType(ObjectType.Report.getCode());
                resultLog.setObjectNo(Func.join(batchExistReportDataReq.getReportNos()));
                resultLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                resultLog.setType(SystemLogType.API.getType());
                resultLog.setRemark("Call RD BatchExist");
                resultLog.setResponse(result);
                resultLog.setRequest(JSON.toJSONString(batchExistReportDataReq));
                resultLog.setOperationType("Call RD BatchExist");
                resultLog.setLocationCode(SystemContextHolder.getLabCode());
                systemLogHelper.save(resultLog);
            } catch (Exception e) {
                log.error("Call RD BatchExist error:{}", e);
            }
        }
        return response;
    }

    private Map<String, String> buildHeaderMap(BaseModel baseModel) {
        Map<String, String> headerMaps = Maps.newHashMap();
        headerMaps.put("productLineCode", Func.toStr(baseModel.getProductLineCode()));
        headerMaps.put("requestId", Func.toStr(baseModel.getRequestId()));
        headerMaps.put("labCode", Func.toStr(baseModel.getLabCode()));
        headerMaps.put("systemId", Func.toStr(baseModel.getSystemId()));
        return headerMaps;
    }

    /**
     * 调用RD updateNonReportInfo方法
     *
     * @param
     * @return
     */
    public BaseResponse updateReportData(ImportReportDataReq importReportDataReq) {
        BaseResponse response = new BaseResponse();
        if (Func.isEmpty(importReportDataReq)) {
            return BaseResponse.newFailInstance(ResponseCode.PARAM_MISS);
        }
        String url = String.format("%s/testdatabiz/api/report-data/updateNonReportInfo", interfaceConfig.getBaseUrl());
        try {
            Map<String, String> headerMaps = this.buildHeaderMap(importReportDataReq);
            String result = HttpClientUtil.postJsonHeader(url, importReportDataReq, headerMaps);
            if (Func.isEmpty(result)) {
                return BaseResponse.newFailInstance("rd update 返回为空");
            }
            return JsonUtil.parse(result, BaseResponse.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("dm import 异常：{}", e);
            response.setMessage(e.getMessage());
            response.setStatus(ResponseCode.FAIL.getCode());
        }
        return response;
    }

    /**
     * 查询RD ConclusionList
     */

    public BaseResponse<List<ConclusionInfoRsp>> queryAllConclusionList(ConclusionInfoReq conclusionInfoReq){
        if(Func.isEmpty(conclusionInfoReq)){
            return BaseResponse.fail(500, "参数缺少");
        }
        if(Func.isEmpty(conclusionInfoReq.getSourceTypes())){
            return BaseResponse.fail(500, "参数[sourceTypes]缺少");
        }
        if(Func.isEmpty(conclusionInfoReq.getLabCode())){
            return BaseResponse.fail(500, "参数[labCode]缺少");
        }
        if(Func.isEmpty(conclusionInfoReq.getOrderNo())){
            return BaseResponse.fail(500, "参数[orderNo]缺少");
        }
        if (Func.isEmpty(conclusionInfoReq.getProductLineCode())) {
            return BaseResponse.fail(500, "参数[productLineCode]缺少");
        }
        if (Func.isEmpty(conclusionInfoReq.getSystemId())) {
            return BaseResponse.fail(500, "参数[systemId]缺少");
        }
        String url = String.format("%s/testdatabiz/api/testdata/queryAllConclusionList", interfaceConfig.getBaseUrl());
        BaseResponse response = new BaseResponse();
        try {
            String result = HttpClientUtil.postJson(url, JsonUtil.toJson(conclusionInfoReq));
            if(Func.isEmpty(result)){
                return BaseResponse.newFailInstance("queryAllConclusionList 返回为空");
            }
            return JsonUtil.parse(result, new TypeReference<BaseResponse<List<ConclusionInfoRsp>>>(){});
        } catch (Exception e) {
            e.printStackTrace();
            log.error("queryAllConclusionList 异常：{}", e);
            response.setMessage(e.getMessage());
            response.setStatus(ResponseCode.FAIL.getCode());
        }
        return response;
    }




}
