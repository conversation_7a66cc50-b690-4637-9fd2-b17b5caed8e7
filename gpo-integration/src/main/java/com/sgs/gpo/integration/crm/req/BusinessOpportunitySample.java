package com.sgs.gpo.integration.crm.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import java.util.List;

@Data
public class BusinessOpportunitySample {
    //TB_QuotationSample的自增ID（最大长度：300）
    private String sampleID;
    //样品名称（最大长度：300）
    private String sampleName;
    //样品大类（最大长度：300）
    private String productTaxonomy;
    //生产商地址（最大长度：300）
    private String manufacturer;
    //样品批号（最大长度：300）
    private String batch;
    //生产日期（最大长度：300）
    private String productionDate;
    //其他（最大长度：300）
    private String others;
    //买家名称（最大长度：300）
    private String buyer;
    //目的国（最大长度：300）
    private String destinationCountry;
    //代理商（最大长度：300）
    private String distributor;
    //供应商（最大长度：300）
    private String supplier;
    //规格/等级（最大长度：300）
    private String specifications;
    //生产商地址（最大长度：300）
    private String manufacturerAddress;

    private List<BusinessOpportunityServiceItem> serviceItems;

}
