package com.sgs.gpo.domain.service.preorder.order.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.model.order.v2.OrderIdBO;
import com.sgs.framework.open.platform.base.service.impl.AbstractBaseService;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.enums.OrderStatus;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.GeneralOrderMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.quotation.QuotationHeaderPO;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IGeneralOrderService;
import com.sgs.gpo.facade.model.payment.costlist.req.ActualFeeSaveReq;
import com.sgs.gpo.facade.model.preorder.order.dto.OrderQuotationDTO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.preorder.productsample.req.QuotationQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/7 12:00
 */
@Service
@Slf4j
public class GeneralOrderServiceImpl
        extends AbstractBaseService<OrderBO,GeneralOrderPO, OrderIdBO,GeneralOrderMapper, OrderQueryReq>
        implements IGeneralOrderService {

    @Override
    public BaseResponse<List<GeneralOrderPO>> query2(OrderQueryReq orderQueryReq) {
        if(Func.isEmpty(orderQueryReq) ||
                (Func.isAllEmpty(orderQueryReq.getOrderIdList(),orderQueryReq.getOrderNoList(),orderQueryReq.getEnquiryIdList(),orderQueryReq.getIntervalHours()))){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        if(Func.isNotEmpty(orderQueryReq.getOrderIdList())) {
            queryWrapper.in(GeneralOrderPO.COLUMN.ID, orderQueryReq.getOrderIdList());
        }
        if(Func.isNotEmpty(orderQueryReq.getOrderNoList())) {
            queryWrapper.in(GeneralOrderPO.COLUMN.ORDER_NO, orderQueryReq.getOrderNoList());
        }
        if(Func.isNotEmpty(orderQueryReq.getEnquiryIdList())) {
            queryWrapper.in(GeneralOrderPO.COLUMN.ENQUIRY_ID, orderQueryReq.getEnquiryIdList());
        }
        if(Func.isNotEmpty(orderQueryReq.getProductLineCode())) {
            queryWrapper.eq(GeneralOrderPO.COLUMN.BU_CODE, orderQueryReq.getProductLineCode());
        }

        if(Func.isNotEmpty(orderQueryReq.getIntervalHours())) {
            queryWrapper.eq(GeneralOrderPO.COLUMN.ORDER_STATUS, OrderStatus.Confirmed.getStatus());
            queryWrapper.apply("NOW() > DATE_ADD("+GeneralOrderPO.COLUMN.ORDER_CONFIRM_DATE+", INTERVAL {0} HOUR)", orderQueryReq.getIntervalHours());
        }

        return BaseResponse.newSuccessInstance(baseMapper.selectList(queryWrapper));
    }

    @Override
    public BaseResponse<List<GeneralOrderPO>> queryByRootOrderNo(OrderQueryReq orderQueryReq) {
        if(Func.isEmpty(orderQueryReq) || Func.isEmpty(orderQueryReq.getOrderNoList())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        if(Func.isNotEmpty(orderQueryReq.getOrderNoList())) {
            queryWrapper.in(GeneralOrderPO.COLUMN.ROOT_ORDER_NO, orderQueryReq.getOrderNoList());
        }
        return BaseResponse.newSuccessInstance(baseMapper.selectList(queryWrapper));
    }

    @Override
    public BigDecimal getAllTotalAmountByPaidUp(String orderNo) {
        return baseMapper.getAllTotalAmountByPaidUp(orderNo);
    }

    @Override
    public List<OrderQuotationDTO> getQuotationOrderForOrderId(String orderId){
        return baseMapper.getQuotationOrderForOrderId(orderId);
    }

    @Override
    public List<String> getOrderNoByBossOrder(List<String> bossOrderNoList) {
        return baseMapper.getOrderByBossOrderNos(bossOrderNoList);
    }

    @Override
    public List<String> getOrderNoListByInvoiceNo(List<String> invoiceNos){
        return baseMapper.getOrderNoListByInvoiceNo(invoiceNos);
    }

    @Override
    public List<QuotationHeaderPO> selectQuotationList(QuotationQueryReq quotationQueryReq) {
        return baseMapper.selectQuotationList(quotationQueryReq);
    }

    @Override
    public void updateActualFeeByOrderNo(List<ActualFeeSaveReq> request) {
        baseMapper.updateActualFeeByOrderNo(request);
    }

    @Override
    public List<OrderBO> convertToBO(Collection<GeneralOrderPO> poList) {
        return null;
    }

    @Override
    public List<GeneralOrderPO> convertToPO(Collection<OrderBO> boList) {
        return null;
    }

    @Override
    public LambdaQueryWrapper<GeneralOrderPO> createWrapper(OrderQueryReq queryReq) {
        return null;
    }
}
