package com.sgs.gpo.domain.service.otsnotes.subreport;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.facade.model.otsnotes.subreport.req.UpdateCombineFlagReq;

/**
 * SubReport相关的服务
 */
public interface ISubReportDomainService  {

    /**
     * 更新 combineFlag
     * @param updateCombineFlagReq
     * @return
     */
    BaseResponse updateCombineFlag(UpdateCombineFlagReq updateCombineFlagReq);
}
