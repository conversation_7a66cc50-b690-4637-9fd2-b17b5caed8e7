package com.sgs.gpo.domain.service.otsnotes.report.impl;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Maps;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.PageBO;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.facade.domain.req.BuParamReq;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.ReportLanguage;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.framework.model.report.report.v2.ReportIdBO;
import com.sgs.framework.model.test.testsample.v2.TestSampleBO;
import com.sgs.framework.open.platform.base.service.impl.AbstractDomainService;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.framework.tool.utils.StringPool;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.GenerateStatusEnums;
import com.sgs.gpo.core.enums.PrelimResultStatusEnum;
import com.sgs.gpo.core.enums.ReportActionType;
import com.sgs.gpo.core.enums.ReportFileStatus;
import com.sgs.gpo.core.enums.ReportFileType;
import com.sgs.gpo.core.enums.ReportFlagEnums;
import com.sgs.gpo.core.enums.ReportStatus;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.attachment.ObjectAttachmentPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.PrelimResultPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportFilePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportTemplatePO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.report.GpoReportPO;
import com.sgs.gpo.domain.service.otsnotes.attachment.IObjectAttachmentService;
import com.sgs.gpo.domain.service.otsnotes.report.IReportDomainService;
import com.sgs.gpo.domain.service.otsnotes.report.command.ReportAutoDeliverPageCMD;
import com.sgs.gpo.domain.service.otsnotes.report.command.ReportExtUpdateCMD;
import com.sgs.gpo.domain.service.otsnotes.report.command.ReportFileHistoryQueryCMD;
import com.sgs.gpo.domain.service.otsnotes.report.command.ReportMatrixQueryCMD;
import com.sgs.gpo.domain.service.otsnotes.report.command.ReportQueryCMD;
import com.sgs.gpo.domain.service.otsnotes.report.context.ReportAutoDeliverContext;
import com.sgs.gpo.domain.service.otsnotes.report.context.ReportContext;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IPrelimReportService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportFileHistoryService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportFileService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportTemplateService;
import com.sgs.gpo.domain.service.otsnotes.testsample.ITestSampleDomainServe;
import com.sgs.gpo.domain.service.preorder.order.IOrderAttachmentDomainService;
import com.sgs.gpo.domain.service.preorder.report.IGpoReportService;
import com.sgs.gpo.facade.model.attachment.ObjectAttachmentQueryReq;
import com.sgs.gpo.facade.model.digital.dto.GenerateReportTemplateDTO;
import com.sgs.gpo.facade.model.otsnotes.report.req.ReportTestMatrixModelGetReq;
import com.sgs.gpo.facade.model.otsnotes.report.rsp.ReportScanFileRsp;
import com.sgs.gpo.facade.model.otsnotes.report.vo.ReportVO;
import com.sgs.gpo.facade.model.otsnotes.reportmatrix.vo.ReportMatrixVO;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import com.sgs.gpo.facade.model.report.bo.ReportAutoDeliverBO;
import com.sgs.gpo.facade.model.report.req.PrelimReportGenerateReq;
import com.sgs.gpo.facade.model.report.req.ReportAccreditationUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportAutoDeliverQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportBatchUpdateTestingTypeReq;
import com.sgs.gpo.facade.model.report.req.ReportExtForTLUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportFileQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportGenerateReq;
import com.sgs.gpo.facade.model.report.req.ReportIdReq;
import com.sgs.gpo.facade.model.report.req.ReportMatrixQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportTemplateReq;
import com.sgs.gpo.facade.model.report.req.ReportUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportUpdateTestingTypeReq;
import com.sgs.gpo.facade.model.report.req.TestResultGenerateReq;
import com.sgs.gpo.facade.model.scantool.rsp.ScanToolFile;
import com.sgs.gpo.integration.framework.FrameworkClient;
import com.sgs.gpo.integration.framework.req.DownloadFileReq;
import com.sgs.gpo.integration.framework.req.UserSignatureReq;
import com.sgs.gpo.integration.framework.rsp.DownLoadFileRsp;
import com.sgs.gpo.integration.framework.rsp.UserSignatureRsp;
import com.sgs.gpo.integration.ilayer.LocalILayerClient;
import com.sgs.gpo.integration.ilayer.req.SyncGetInfoBody;
import com.sgs.gpo.integration.ilayer.req.SyncGetInfoRequest;
import com.sgs.gpo.integration.ilayer.req.SysDataDto;
import com.sgs.gpo.integration.ilayer.rsp.PreOrderRsp;
import com.sgs.gpo.integration.sci.SciClient;
import com.sgs.grus.async.AsyncCall;
import com.sgs.grus.async.AsyncResult;
import com.sgs.grus.async.AsyncUtils;
import com.sgs.otsnotes.facade.model.enums.FileType;
import com.sgs.otsnotes.facade.model.enums.SignTypeEnums;
import com.sgs.preorder.core.order.dto.OrderMatrixDto;
import com.sgs.preorder.core.order.dto.SysHeaderDto;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class ReportDomainServiceImpl
        extends AbstractDomainService<ReportBO, ReportIdBO, ReportQueryReq, IReportService>
        implements IReportDomainService {

    private static final Logger log = LoggerFactory.getLogger(ReportDomainServiceImpl.class);
    private IReportService reportService;
    private IGpoReportService gpoReportService;
    private IReportFileService reportFileService;
    private FrameworkClient frameworkClient;
    private LocalILayerClient localILayerClient;
    private IPrelimReportService prelimReportService;
    private IObjectAttachmentService objectAttachmentService;
    private IOrderAttachmentDomainService orderAttachmentDomainService;
    private ITestSampleDomainServe testSampleDomainService;
    private IReportTemplateService reportTemplateService;
    private IReportFileHistoryService reportFileHistoryService;
    private SciClient sciClient;


    @Override
    public BaseResponse<Boolean> updateReportExtForTL(ReportExtForTLUpdateReq reportExtForTLUpdateReq) {
        ReportContext<ReportExtForTLUpdateReq> reportContext = new ReportContext();
        reportContext.setParam(reportExtForTLUpdateReq);
        reportContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        reportContext.setToken(SecurityContextHolder.getSgsToken());
        if (Func.isNotEmpty(reportExtForTLUpdateReq.getToken())) {
            reportContext.setToken(reportExtForTLUpdateReq.getToken());
        }
        reportContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        if (Func.isNotEmpty(reportExtForTLUpdateReq.getProductLineCode())) {
            reportContext.setProductLineCode(reportExtForTLUpdateReq.getProductLineCode());
        }
        //使用新线程
        ExecutorService executorService = ThreadUtil.newExecutor(1);
        executorService.execute(() -> {
            // 执行任务
            try {
                BaseExecutor.start(ReportExtUpdateCMD.class, reportContext);
                executorService.shutdown();
                if (!executorService.awaitTermination(90, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
            }
        });
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse<List<ReportMatrixVO>> queryReportMatrix(ReportMatrixQueryReq reportMatrixQueryReq) {
        ReportContext<ReportMatrixQueryReq> reportContext = new ReportContext();
        reportContext.setParam(reportMatrixQueryReq);
        reportContext.setUserInfo(SecurityContextHolder.getUserInfo());
        reportContext.setToken(SecurityContextHolder.getSgsToken());
        if (Func.isNotEmpty(reportMatrixQueryReq.getToken())) {
            reportContext.setToken(reportMatrixQueryReq.getToken());
        }
        reportContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        if (Func.isNotEmpty(reportMatrixQueryReq.getProductLineCode())) {
            reportContext.setProductLineCode(reportMatrixQueryReq.getProductLineCode());
        }
        return BaseExecutor.start(ReportMatrixQueryCMD.class, reportContext);
    }

    @Override
    public BaseResponse<ReportScanFileRsp> queryReportScanFile(ReportIdReq reportIdReq) {
        ReportScanFileRsp reportScanFileRsp = new ReportScanFileRsp();
        Assert.isTrue(Func.isNotEmpty(reportIdReq), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(reportIdReq.getReportNo()), "common.param.miss", new Object[]{"ReportNo"});
        ReportMatrixQueryReq reportMatrixQueryReq = new ReportMatrixQueryReq();
        reportMatrixQueryReq.setReportNoList(Sets.newHashSet(reportIdReq.getReportNo()));
        List<ReportMatrixVO> reportMatrixVOList = this.queryReportMatrix(reportMatrixQueryReq).getData();
        reportScanFileRsp.setReportMatrixVOList(reportMatrixVOList);
        ReportQueryReq reportQueryReq = new ReportQueryReq();
        reportQueryReq.setReportNo(reportIdReq.getReportNo());
        List<ReportPO> reportPOList = reportService.select(reportQueryReq).getData();
        if (Func.isNotEmpty(reportPOList)) {
            String orderNo = reportPOList.get(0).getOrderNo();
            String reportNo = reportPOList.get(0).getOrderNo();
            reportScanFileRsp.setReportNo(reportNo);
            reportScanFileRsp.setOrderNo(orderNo);
            List<ScanToolFile> allScanToolFileList = orderAttachmentDomainService.queryScanFiles(orderNo).getData();
            //返回ScanCode与OrderNo,TestItemNo匹配的PDF File
            if (Func.isNotEmpty(allScanToolFileList) && Func.isNotEmpty(reportMatrixVOList)) {
                Set<String> testItemNoList = reportMatrixVOList.stream().map(ReportMatrixVO::getTestItemNo).collect(Collectors.toSet());
                List<ScanToolFile> scanToolFileList = allScanToolFileList.stream().filter(item -> StringUtils.equalsIgnoreCase(Constants.FILE_TYPE.PDF, item.getFileTypeName()) && Func.isNotEmpty(testItemNoList) && testItemNoList.contains(item.getScanCode()) || (Func.equalsSafe(orderNo, item.getScanCode()))).collect(Collectors.toList());
                if (Func.isNotEmpty(scanToolFileList)) {
                    Set<String> scanFileCloudIdList = scanToolFileList.stream().map(ScanToolFile::getFileName).filter(Func::isNotEmpty).distinct().collect(Collectors.toSet());
                    if (Func.isNotEmpty(scanFileCloudIdList)) {
                        DownloadFileReq downloadFileReq = new DownloadFileReq();
                        downloadFileReq.setSystemId(45);
                        downloadFileReq.setCloudIdList(scanFileCloudIdList);
                        BaseResponse<List<DownLoadFileRsp>> downFileRsp = frameworkClient.downloadByCloudIDList(downloadFileReq);
                        if (downFileRsp.isSuccess() && Func.isNotEmpty(downFileRsp.getData())) {
                            List<DownLoadFileRsp> downLoadFileRspList = downFileRsp.getData();
                            for (ScanToolFile scanToolFile : scanToolFileList) {
                                DownLoadFileRsp downLoadFileRsp = downLoadFileRspList.stream().filter(item -> Func.equalsSafe(item.getCloudId(), scanToolFile.getFileName())).findAny().orElse(null);
                                if (Func.isNotEmpty(downLoadFileRsp)) {
                                    String url = downLoadFileRsp.getUrl();
                                    scanToolFile.setFileUrl(url);
                                }
                            }
                        }
                    }

                }

                reportScanFileRsp.setScanToolFileList(scanToolFileList);
            }
        }
        return BaseResponse.newSuccessInstance(reportScanFileRsp);
    }

    @Override
    public BaseResponse<List<ReportBO>> queryBO(ReportQueryReq request) {
        // 入参不允许为空
        Assert.isTrue(Func.isNotEmpty(request), "common.param.miss", new Object[]{Constants.TERM.REQUEST});
        // 参数必须是ReportQueryReq
        ReportContext<ReportQueryReq> reportContext = new ReportContext<>(request);
        if(Func.isNotEmpty(request.getProductLineCode())){
            reportContext.setProductLineCode(request.getProductLineCode());
        }
        if (Func.isNotEmpty(request.getLab())) {
            reportContext.setLab(request.getLab());
            if(Func.isNotEmpty(request.getLab().getProductLineCode())){
                reportContext.setProductLineCode(request.getLab().getProductLineCode());
            }
        }
        return BaseExecutor.start(ReportQueryCMD.class, reportContext);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse generateReport(ReportGenerateReq reportGenerateReq) {
        if (ReportActionType.check(reportGenerateReq.getReportActionType(), ReportActionType.GENERATE_REPORT) || ReportActionType.check(reportGenerateReq.getReportActionType(), ReportActionType.GENERATE_REPORT_BY_TL_REPORT)) {
            // Report处理
            // 1 更新报告状态，报告Flag,workFlow
            // 如果signatures,editorsSign,reviewersSign,csSign为空，需要更新数据到DB
            ReportPO reportEntity = new ReportPO();
            reportEntity.setId(reportGenerateReq.getReportId());
            reportEntity.setReportStatus(ReportStatus.Draft.getCode());
            reportEntity.setReportFlag(ReportFlagEnums.REPORT.getCode());
            reportEntity.setSubReportReviseFlag(0);
            reportEntity.setWorkFlow(reportGenerateReq.getWorkFlow());
            // 如果signatures,editorsSign,reviewersSign,csSign 重新从Framework获取了就触发更新
            if (Func.isNotEmpty(reportGenerateReq.getEditorBy())) {
                reportEntity.setEditorBy(reportGenerateReq.getEditorBy());
            }
            if (Func.isNotEmpty(reportGenerateReq.getEditor())) {
                reportEntity.setEditor(reportGenerateReq.getEditor());
            }
            if (Func.isNotEmpty(reportGenerateReq.getReviewerBy())) {
                reportEntity.setReviewerBy(reportGenerateReq.getReviewerBy());
            }
            if (Func.isNotEmpty(reportGenerateReq.getReviewer())) {
                reportEntity.setReviewer(reportGenerateReq.getReviewer());
            }
            try {
                ReportTestMatrixModelGetReq reportTestMatrixModelGetReq = new ReportTestMatrixModelGetReq();
                reportTestMatrixModelGetReq.setReportId(reportGenerateReq.getReportId());
                BaseResponse<String> reportTestMatrixModeRes = reportService.getReportTestMatrixMode(reportTestMatrixModelGetReq);
                if (reportTestMatrixModeRes.isSuccess()) {
                    reportEntity.setTestMatrixMergeMode(reportTestMatrixModeRes.getData());
                }
            } catch (Exception e) {
                log.error("Generate Report BuildReportTestMatrixMode error:{}", e.getMessage());
            }

            reportService.updateById(reportEntity);
            // GPO 处理
            // 3 同步更新GPO的报告状态
            this.updateGpoReport(reportGenerateReq.getReportId());
        }
        //  处理ReportFile
        this.resetReportFile(reportGenerateReq);

        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse updateReportSigner(ReportUpdateReq reportUpdateReq) {
        ReportPO reportPO = reportService.getById(reportUpdateReq.getReportId());
        if (Func.isEmpty(reportPO)) {
            return BaseResponse.newFailInstance("【UpdateReportSigner】报告不存在");
        }
        Boolean needUpdate = false;
        String userName = reportUpdateReq.getUserName();
        // 查询签名信息
        UserSignatureReq userSignReq = new UserSignatureReq();
        userSignReq.setLanguageCodes(Lists.newArrayList(LanguageType.English.getCode(), LanguageType.Chinese.getCode()));
        userSignReq.setLabCode(reportUpdateReq.getLabCode());
        userSignReq.setAuthSignTypes(Lists.newArrayList(SignTypeEnums.EDITOR.getCode(), SignTypeEnums.REVIEWER.getCode()));
        List<UserSignatureRsp> userSignatureList = frameworkClient.queryUserSignature(userSignReq);
        if (Func.isEmpty(userSignatureList)) {
            return BaseResponse.newSuccessInstance(true);
        }
        // 过滤中英文
        List<UserSignatureRsp> enSignatures = userSignatureList.stream().filter(userSignature -> Func.equalsSafe(LanguageType.English.getCode(), userSignature.getLanguageCode())).collect(Collectors.toCollection(ArrayList::new));
        List<UserSignatureRsp> cnSignatures = userSignatureList.stream().filter(userSignature -> Func.equalsSafe(LanguageType.Chinese.getCode(), userSignature.getLanguageCode())).collect(Collectors.toCollection(ArrayList::new));
        // 当前报告上签名信息为空才更新
        if (Func.isEmpty(reportPO.getEditorBy())) {
            Integer signTypeCode = SignTypeEnums.EDITOR.getCode();
            Map<String, Object> editorFilePathMap = new HashMap<>();
            List<UserSignatureRsp> editorSignEnList = enSignatures.stream().filter(i -> StringUtils.equalsIgnoreCase(userName, i.getRegionAccount()) && Func.isNotEmpty(i.getAuthSignTypeKey()) && i.getAuthSignTypeKey().stream().anyMatch(k -> Func.equals(k, Func.toStr(signTypeCode)))).collect(Collectors.toList());
            List<UserSignatureRsp> editorSignCnList = cnSignatures.stream().filter(i -> StringUtils.equalsIgnoreCase(userName, i.getRegionAccount()) && Func.isNotEmpty(i.getAuthSignTypeKey()) && i.getAuthSignTypeKey().stream().anyMatch(k -> Func.equals(k, Func.toStr(signTypeCode)))).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(editorSignEnList)) {
                editorFilePathMap.put(LanguageType.English.getCode(), editorSignEnList);
            }
            if (CollectionUtils.isNotEmpty(editorSignCnList)) {
                editorFilePathMap.put(LanguageType.Chinese.getCode(), editorSignCnList);
            }

            if (Func.isNotEmpty(editorSignEnList)) {
                editorFilePathMap.put(LanguageType.English.getCode(), editorSignEnList);
            }
            if (Func.isNotEmpty(editorSignCnList)) {
                editorFilePathMap.put(LanguageType.Chinese.getCode(), editorSignCnList);
            }
            if (Func.isNotEmpty(editorFilePathMap)) {
                reportPO.setEditorBy(userName);
                reportPO.setEditor(JSON.toJSONString(editorFilePathMap));
                needUpdate = true;
            }
        }
        if (Func.isEmpty(reportPO.getReviewerBy())) {
            Integer signTypeCode = SignTypeEnums.REVIEWER.getCode();
            Map<String, Object> reviewerFilePathMap = new HashMap<>();
            String reviewer = null;
            //取bu配置，获取reviewer
            BuParamReq buParamReq = new BuParamReq();
            buParamReq.setLocationCode(reportUpdateReq.getLocationCode());
            buParamReq.setProductLineCode(reportUpdateReq.getProductLineCode());
            buParamReq.setParamCode("ReviewerSign");
            buParamReq.setGroupCode("Report");
            BuParamValueRsp buParamValueRsp = frameworkClient.getBuParam(buParamReq);
            if (Func.isNotEmpty(buParamValueRsp) && Func.isNotEmpty(buParamValueRsp.getParamValue())) {
                //判断取值逻辑  TS/lab out by
                if (Func.equals("TS", buParamValueRsp.getParamValue())) {
                    //查询订单信息
                    if (Func.isNotEmpty(reportUpdateReq.getTsPerson())) {
                        reviewer = reportUpdateReq.getTsPerson();
                    }
                } else if (Func.equals("LAB_OUT_BY", buParamValueRsp.getParamValue())) {
                    //todo lab out 取值
                    reviewer = reportService.queryJobValidateByReportId(reportUpdateReq.getReportId());
                }
            }
            if (Func.isNotEmpty(reviewer)) {
                String finalReviewer = reviewer;
                List<UserSignatureRsp> reviewerSignEnList = enSignatures.stream().filter(i -> StringUtils.equalsIgnoreCase(finalReviewer, i.getRegionAccount()) && Func.isNotEmpty(i.getAuthSignTypeKey()) && i.getAuthSignTypeKey().stream().anyMatch(k -> Func.equals(k, Func.toStr(signTypeCode)))).collect(Collectors.toList());
                List<UserSignatureRsp> reviewerSignCnList = cnSignatures.stream().filter(i -> StringUtils.equalsIgnoreCase(finalReviewer, i.getRegionAccount()) && Func.isNotEmpty(i.getAuthSignTypeKey()) && i.getAuthSignTypeKey().stream().anyMatch(k -> Func.equals(k, Func.toStr(signTypeCode)))).collect(Collectors.toList());

                if (Func.isNotEmpty(reviewerSignEnList)) {
                    reviewerFilePathMap.put(LanguageType.English.getCode(), reviewerSignEnList);
                }
                if (Func.isNotEmpty(reviewerSignCnList)) {
                    reviewerFilePathMap.put(LanguageType.Chinese.getCode(), reviewerSignCnList);
                }
            }
            if (Func.isNotEmpty(reviewerFilePathMap)) {
                reportPO.setReviewerBy(reviewer);
                reportPO.setReviewer(JSON.toJSONString(reviewerFilePathMap));
                needUpdate = true;
            }
        }
        // 更新签名信息
        if (needUpdate) {
            reportService.updateById(reportPO);
        }
        return BaseResponse.newSuccessInstance(true);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse<String> generatePrelimReport(PrelimReportGenerateReq req) {
        // 如果已经存在word报告需要覆盖
        ObjectAttachmentQueryReq attachmentQueryReq = new ObjectAttachmentQueryReq();
        attachmentQueryReq.setObjectNo(req.getPrelimResultNo());
        attachmentQueryReq.setFileType(FileType.WORD.getCode());
        List<ObjectAttachmentPO> prelimFileList = objectAttachmentService.query(attachmentQueryReq);
        String attachmentId;
        if (Func.isNotEmpty(prelimFileList)) {
            // 设置已经存在的Prelim附件为空
            ObjectAttachmentPO prelimWordAttachment = prelimFileList.get(0);
            prelimWordAttachment.setCloudId("");
            prelimWordAttachment.setGenerateStatus(GenerateStatusEnums.GENERATING.getCode());
            prelimWordAttachment.setModifiedBy(SystemContextHolder.getRegionAccount());
            prelimWordAttachment.setModifiedDate(DateUtil.now());
            objectAttachmentService.updateById(prelimWordAttachment);
            attachmentId = prelimWordAttachment.getId();
        } else {
            ObjectAttachmentPO newPrelimWordAttachment = new ObjectAttachmentPO();
            newPrelimWordAttachment.setId(IdUtil.uuId());
            newPrelimWordAttachment.setCreatedDate(DateUtil.now());
            newPrelimWordAttachment.setCreatedBy(SystemContextHolder.getRegionAccount());
            newPrelimWordAttachment.setObjectNo(req.getPrelimResultNo());
            newPrelimWordAttachment.setObjectType("prelim");
            newPrelimWordAttachment.setCloudId("");
            newPrelimWordAttachment.setFileName(req.getPrelimResultNo().concat(Constants.GENERATE_REPORT_FILE_SUFFIXES));
            newPrelimWordAttachment.setFileType(FileType.WORD.getCode());
            newPrelimWordAttachment.setActiveIndicator(ActiveType.Enable.getStatus());
            newPrelimWordAttachment.setGenerateStatus(GenerateStatusEnums.GENERATING.getCode());
            objectAttachmentService.save(newPrelimWordAttachment);
            attachmentId = newPrelimWordAttachment.getId();
        }
        // 更新prelim result 信息
        PrelimResultPO prelimResultUpdate = new PrelimResultPO();
        prelimResultUpdate.setId(req.getPrelimResultId());
        prelimResultUpdate.setGenerateBy(SystemContextHolder.getUserInfo().getRegionAccount());
        prelimResultUpdate.setGenerateDate(DateUtil.now());
        prelimResultUpdate.setPrelimResultStatus(PrelimResultStatusEnum.GENERATE.getCode());
        prelimReportService.updateById(prelimResultUpdate);
        return BaseResponse.newSuccessInstance(attachmentId);
    }


    @Override
    public BaseResponse updateExternalReportNo(ReportUpdateReq reportUpdateReq) {
        if (Func.isEmpty(reportUpdateReq.getTestSampleIdList())) {
            return BaseResponse.newSuccessInstance(true);
        }
        // 查询报告对应的样品信息
        TestSampleQueryReq testSampleQueryReq = new TestSampleQueryReq();
        testSampleQueryReq.setTestSampleInstanceIdList(reportUpdateReq.getTestSampleIdList());
        BaseResponse<List<TestSampleBO>> testSampleRes = testSampleDomainService.queryBO(testSampleQueryReq);
        if (testSampleRes.isFail()) {
            return testSampleRes;
        }
        List<TestSampleBO> testSampleBOList = testSampleRes.getData();
        if (Func.isEmpty(testSampleBOList)) {
            return BaseResponse.newSuccessInstance(true);
        }
        Set<String> externalSamples = testSampleBOList.stream().map(item->item.getId().getExternal().getExternalSampleId()).collect(Collectors.toSet());
        if(Func.isEmpty(externalSamples)){
            return BaseResponse.newSuccessInstance(true);
        }
        // 查询DAL 样品-Report对照关系
        Map<String, Set<String>> sampleReportMaps = this.convertSampleMap(reportUpdateReq);
        Set<String> externalReports = Sets.newHashSet();
        externalSamples.stream().forEach(sampleNo->{
            if(sampleReportMaps.containsKey(sampleNo)){
                externalReports.addAll(sampleReportMaps.get(sampleNo));
            }
        });
        // 如果全部样品都指向一个报告，需要更新报告号到当前报告
        if(externalReports.size() == 1){
            ReportPO reportPO = new ReportPO();
            reportPO.setId(reportUpdateReq.getReportId());
            reportPO.setExternalReportNo(externalReports.stream().collect(Collectors.toList()).get(0));
            reportService.updateById(reportPO);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    @Transactional
    public BaseResponse updateReportTemplate(List<TestResultGenerateReq> generateTestResultReqs) {
        if(Func.isEmpty(generateTestResultReqs)){
            return BaseResponse.newFailInstance("common.param.miss",new Object[]{Constants.TERM.REQUEST});
        }
        Set<String> reportIds = generateTestResultReqs.stream().map(TestResultGenerateReq::getReportId).collect(Collectors.toSet());
        // 查询Report Template
        ReportTemplateReq reportTemplateReq = new ReportTemplateReq();
        reportTemplateReq.setReportIds(reportIds);
        BaseResponse<List<ReportTemplatePO>> reportTemplateRes = reportTemplateService.queryReportTemplateList(reportTemplateReq);
        if(reportTemplateRes.isFail()){
            return reportTemplateRes;
        }
        List<ReportTemplatePO> reportTemplateList = reportTemplateRes.getData();
        for(TestResultGenerateReq testResultGenerateReq:generateTestResultReqs){
            // EN
            if(Func.isNotEmpty(testResultGenerateReq.getGenerateTemplateDtoEN())){
                GenerateReportTemplateDTO generateTemplateDtoEN = testResultGenerateReq.getGenerateTemplateDtoEN();
                ReportTemplatePO reportTemplateEN = reportTemplateList.stream().filter(e -> e.getReportId().equals(testResultGenerateReq.getReportId()) && LanguageType.check(e.getLanguageId(),LanguageType.English))
                        .findFirst().orElse(null);

                if(Func.isNotEmpty(reportTemplateEN)){
                    // 更新DB中现存的模板数据
                    reportTemplateEN.setModifiedDate(new Date());
                    reportTemplateEN.setModifiedBy(SystemContextHolder.getRegionAccount());
                    reportTemplateEN.setTemplateName(generateTemplateDtoEN.getTemplateName());
                    reportTemplateEN.setContainer(Func.isNotEmpty(generateTemplateDtoEN.getContainer()) ? Integer.valueOf(generateTemplateDtoEN.getContainer()) : null);
                    reportTemplateEN.setContainerTemplateFilepath(generateTemplateDtoEN.getContainerTemplateFilePath());
                    reportTemplateEN.setDataObject(generateTemplateDtoEN.getDataObject());
                    reportTemplateEN.setTemplateSettingId(generateTemplateDtoEN.getTemplateSettingID());
                    reportTemplateEN.setTemplateTypeId(Func.isNotEmpty(generateTemplateDtoEN.getTemplateTypeId()) ? Integer.valueOf(generateTemplateDtoEN.getTemplateTypeId()) : null);
                    reportTemplateService.updateById(reportTemplateEN);
                }else {
                    // 新增Template数据
                    reportTemplateEN = new ReportTemplatePO();
                    reportTemplateEN.setId(IdUtil.uuId());
                    reportTemplateEN.setReportId(testResultGenerateReq.getReportId());
                    reportTemplateEN.setCreatedDate(DateUtil.now());
                    reportTemplateEN.setCreatedBy(SystemContextHolder.getRegionAccount());
                    reportTemplateEN.setTemplateName(generateTemplateDtoEN.getTemplateName());
                    reportTemplateEN.setActiveIndicator(true);
                    reportTemplateEN.setContainer(Func.isNotEmpty(generateTemplateDtoEN.getContainer()) ? Integer.valueOf(generateTemplateDtoEN.getContainer()) : null);
                    reportTemplateEN.setContainerTemplateFilepath(generateTemplateDtoEN.getContainerTemplateFilePath());
                    reportTemplateEN.setDataObject(generateTemplateDtoEN.getDataObject());
                    reportTemplateEN.setLanguageId(LanguageType.English.getLanguageId());
                    reportTemplateEN.setTemplateSettingId(generateTemplateDtoEN.getTemplateSettingID());
                    reportTemplateEN.setTemplateTypeId(Func.isNotEmpty(generateTemplateDtoEN.getTemplateTypeId()) ? Integer.valueOf(generateTemplateDtoEN.getTemplateTypeId()) : null);
                    reportTemplateService.save(reportTemplateEN);
                }
                // 更新Report上存储的模板数据
                ReportPO reportPO = new ReportPO();
                reportPO.setId(testResultGenerateReq.getReportId());
                reportPO.setCoverPageTemplateName(generateTemplateDtoEN.getTemplateName());
                reportService.updateById(reportPO);
            }
            // CN
            if(Func.isNotEmpty(testResultGenerateReq.getGenerateTemplateDtoCN())){
                GenerateReportTemplateDTO generateTemplateDtoCN = testResultGenerateReq.getGenerateTemplateDtoCN();
                ReportTemplatePO reportTemplateCN = reportTemplateList.stream().filter(e -> e.getReportId().equals(testResultGenerateReq.getReportId()) && LanguageType.check(e.getLanguageId(),LanguageType.Chinese))
                        .findFirst().orElse(null);

                if(Func.isNotEmpty(reportTemplateCN)){
                    // 更新DB中现存的模板数据
                    reportTemplateCN.setModifiedDate(new Date());
                    reportTemplateCN.setModifiedBy(SystemContextHolder.getRegionAccount());
                    reportTemplateCN.setTemplateName(generateTemplateDtoCN.getTemplateName());
                    reportTemplateCN.setContainer(Func.isNotEmpty(generateTemplateDtoCN.getContainer()) ? Integer.valueOf(generateTemplateDtoCN.getContainer()) : null);
                    reportTemplateCN.setContainerTemplateFilepath(generateTemplateDtoCN.getContainerTemplateFilePath());
                    reportTemplateCN.setDataObject(generateTemplateDtoCN.getDataObject());
                    reportTemplateCN.setTemplateSettingId(generateTemplateDtoCN.getTemplateSettingID());
                    reportTemplateCN.setTemplateTypeId(Func.isNotEmpty(generateTemplateDtoCN.getTemplateTypeId()) ? Integer.valueOf(generateTemplateDtoCN.getTemplateTypeId()) : null);
                    reportTemplateService.updateById(reportTemplateCN);
                }else {
                    // 新增Template数据
                    reportTemplateCN = new ReportTemplatePO();
                    reportTemplateCN.setId(IdUtil.uuId());
                    reportTemplateCN.setReportId(testResultGenerateReq.getReportId());
                    reportTemplateCN.setCreatedDate(DateUtil.now());
                    reportTemplateCN.setCreatedBy(SystemContextHolder.getRegionAccount());
                    reportTemplateCN.setTemplateName(generateTemplateDtoCN.getTemplateName());
                    reportTemplateCN.setActiveIndicator(true);
                    reportTemplateCN.setContainer(Func.isNotEmpty(generateTemplateDtoCN.getContainer()) ? Integer.valueOf(generateTemplateDtoCN.getContainer()) : null);
                    reportTemplateCN.setContainerTemplateFilepath(generateTemplateDtoCN.getContainerTemplateFilePath());
                    reportTemplateCN.setDataObject(generateTemplateDtoCN.getDataObject());
                    reportTemplateCN.setLanguageId(LanguageType.Chinese.getLanguageId());
                    reportTemplateCN.setTemplateSettingId(generateTemplateDtoCN.getTemplateSettingID());
                    reportTemplateCN.setTemplateTypeId(Func.isNotEmpty(generateTemplateDtoCN.getTemplateTypeId()) ? Integer.valueOf(generateTemplateDtoCN.getTemplateTypeId()) : null);
                    reportTemplateService.save(reportTemplateCN);
                }
            }
        }
        return new BaseResponse();
    }


    /**
     * 组装DAL数据Map
     */
    private Map<String, Set<String>> convertSampleMap(ReportUpdateReq reportUpdateReq) {
        Map<String, Set<String>>  sampleReportMaps = Maps.newHashMap();
        // 组装查询DAL信息的入参
        SyncGetInfoBody body = new SyncGetInfoBody();
        SysHeaderDto header = new SysHeaderDto();
        header.setAction("1");
        header.setTimestamp(System.currentTimeMillis());
        header.setCaller(String.valueOf(SgsSystem.GPO.getSgsSystemId()));
        header.setRequestId(Func.randomUUID());
        header.setVersion("1.0");
        body.setHeader(header);

        SysDataDto data = new SysDataDto();
        data.setSubOrderNo(reportUpdateReq.getTrf().getTrfNo());
        data.setOrderNo(reportUpdateReq.getTrf().getExternalOrderNo());
        data.setRefSystemId(reportUpdateReq.getTrf().getRefSystemId().toString());
        body.setData(data);
        body.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        body.setToken(SystemContextHolder.getSgsToken());

        SyncGetInfoRequest syncGetInfoRequest = new SyncGetInfoRequest();
        syncGetInfoRequest.setAction("GetReportCoverPage");
        syncGetInfoRequest.setRefSystemId(reportUpdateReq.getTrf().getRefSystemId());
        syncGetInfoRequest.setBody(body);

        BaseResponse syncRes = localILayerClient.syncGetInfo(syncGetInfoRequest);
        if (syncRes.isFail() || Func.isEmpty(syncRes.getData())) {
            return sampleReportMaps;
        }
        Object orderJson = JSONObject.parseObject(syncRes.getData().toString()).get("order");
        if(Func.isEmpty(orderJson)){
            return sampleReportMaps;
        }
        PreOrderRsp preOrderRsp = JSON.parseObject(orderJson.toString(), PreOrderRsp.class);
        if (Func.isEmpty(preOrderRsp) || Func.isEmpty(preOrderRsp.getReportList()) || Func.isEmpty(preOrderRsp.getTestMatrixList())) {
            return sampleReportMaps;
        }
        List<OrderMatrixDto> testMatrixList = preOrderRsp.getTestMatrixList();
        preOrderRsp.getReportList().stream().forEach(report -> {
            if (Func.isEmpty(report.getReportMatrix())) {
                return;
            }
            report.getReportMatrix().stream().forEach(matrix -> {
                OrderMatrixDto orderMatrixDto = testMatrixList.stream().filter(item->Func.equalsSafe(item.getExternalMatrixInstanceId(),matrix.getExternalMatrixInstanceId()))
                        .findAny().orElse(null);
                if(Func.isEmpty(orderMatrixDto)){
                    return;
                }
                if (sampleReportMaps.containsKey(orderMatrixDto.getExternalSampleInstanceId())) {
                    sampleReportMaps.get(orderMatrixDto.getExternalSampleInstanceId()).add(report.getExternalReportNo());
                    return;
                }
                Set<String> reportList =Sets.newHashSet();
                reportList.add(report.getExternalReportNo());
                sampleReportMaps.put(orderMatrixDto.getExternalSampleInstanceId(),reportList);
            });
        });
        return sampleReportMaps;
    }

    /**
     * 更新Gpo库中的report状态
     *
     * @param reportId
     */
    private void updateGpoReport(String reportId) {
        GpoReportPO reportEntity = new GpoReportPO();
        reportEntity.setId(reportId);
        reportEntity.setReportStatus(ReportStatus.Draft.getCode());
        reportEntity.setModifiedBy(SecurityContextHolder.getUserInfoFillSystem().getRegionAccount());
        reportEntity.setModifiedDate(DateUtil.now());
        gpoReportService.updateById(reportEntity);
    }

    /**
     * 生成报告时更新报告附件
     * 1.更新Word的CloudId为空
     */
    private void resetReportFile(ReportGenerateReq req) {
        ReportFileQueryReq reportFileQueryReq = new ReportFileQueryReq();
        reportFileQueryReq.setReportIdList(Sets.newHashSet(req.getReportId()));
        BaseResponse<List<ReportFilePO>> reportFileRes = reportFileService.query(reportFileQueryReq);
        List<ReportFilePO> reportFileList = reportFileRes.getData();
        String reportNo = req.getReportNo();
        String actualReportNo = req.getActualReportNo();
        String reportLanguage = req.getReportLanguage();
        List<ReportFilePO> newReportFileList = Lists.newArrayList();
        String suffixes = req.isHasFileCn() && req.isHasFileEn() ? "_CN" : "";
        if (ReportActionType.check(req.getReportActionType(), ReportActionType.GENERATE_REPORT) || ReportActionType.check(req.getReportActionType(), ReportActionType.GENERATE_REPORT_BY_TL_REPORT)) {
            // 判断统计无用的报告语言
            // 如果不存在对应语言的报告需要新增
            ReportFilePO engReportFile = null;
            ReportFilePO cnReportFile = null;
            ReportFilePO mulReportFile = null;
            if (Func.isNotEmpty(reportFileList)) {
                engReportFile = reportFileList.stream().filter(file ->
                        LanguageType.check(file.getLanguageID(), LanguageType.English) && ReportFileType.check(file.getReportFileType(), ReportFileType.Word))
                        .findAny().orElse(null);
                cnReportFile = reportFileList.stream().filter(file ->
                        LanguageType.check(file.getLanguageID(), LanguageType.Chinese) && ReportFileType.check(file.getReportFileType(), ReportFileType.Word))
                        .findAny().orElse(null);
                mulReportFile = reportFileList.stream().filter(file ->
                        LanguageType.check(file.getLanguageID(), LanguageType.EnglishAndChinese) && ReportFileType.check(file.getReportFileType(), ReportFileType.Word))
                        .findAny().orElse(null);
            }

            List<Integer> delReportFileLanguageIdList = new ArrayList<>();
            // 如果不存在对应语言模板也需要删除报告文件
            if (!req.isHasFileCn()) {
                delReportFileLanguageIdList.add(LanguageType.Chinese.getLanguageId());
            }
            if (!req.isHasFileEn()) {
                delReportFileLanguageIdList.add(LanguageType.English.getLanguageId());
            }
            if (ReportLanguage.checkLanguage(reportLanguage, ReportLanguage.EnglishReportOnly)) {
                delReportFileLanguageIdList.add(LanguageType.Chinese.getLanguageId());
                delReportFileLanguageIdList.add(LanguageType.EnglishAndChinese.getLanguageId());
                if (Func.isEmpty(engReportFile) && req.isHasFileEn()) {
                    newReportFileList.add(assembleReportFile(LanguageType.English, req.getReportId(), reportNo, ReportFileType.Word, "", actualReportNo));
                }
            } else if (ReportLanguage.checkLanguage(reportLanguage, ReportLanguage.ChineseReportOnly)) {
                delReportFileLanguageIdList.add(LanguageType.English.getLanguageId());
                delReportFileLanguageIdList.add(LanguageType.EnglishAndChinese.getLanguageId());
                if (Func.isEmpty(cnReportFile) && req.isHasFileCn()) {
                    newReportFileList.add(assembleReportFile(LanguageType.Chinese, req.getReportId(), reportNo, ReportFileType.Word, suffixes, actualReportNo));
                }
            } else if (ReportLanguage.checkLanguage(reportLanguage, ReportLanguage.EnglishAndChineseReport)) {
                delReportFileLanguageIdList.add(LanguageType.EnglishAndChinese.getLanguageId());
                if (Func.isEmpty(engReportFile) && req.isHasFileEn()) {
                    newReportFileList.add(assembleReportFile(LanguageType.English, req.getReportId(), reportNo, ReportFileType.Word, "", actualReportNo));
                }
                if (Func.isEmpty(cnReportFile) && req.isHasFileCn()) {
                    newReportFileList.add(assembleReportFile(LanguageType.Chinese, req.getReportId(), reportNo, ReportFileType.Word, suffixes, actualReportNo));
                }
            } else if (ReportLanguage.checkLanguage(reportLanguage, ReportLanguage.MultilingualReport)) {
                delReportFileLanguageIdList.add(LanguageType.Chinese.getLanguageId());
                delReportFileLanguageIdList.add(LanguageType.English.getLanguageId());
                if (Func.isEmpty(mulReportFile)) {
                    newReportFileList.add(assembleReportFile(LanguageType.EnglishAndChinese, req.getReportId(), reportNo, ReportFileType.Word, "", actualReportNo));
                }
            }
            // 现有数据处理
            reportFileList.stream().forEach(report -> {
                if (ReportFileType.check(report.getReportFileType(), ReportFileType.TestResult)) {
                    return;
                }
                // 1 清空原有的CloudId
                // 1.1 如果不存在任何记录是否需要生成一个空的记录，还是在报告异步回传的时候生成
                report.setCloudID("");
                report.setStatus(ReportFileStatus.NEW.getCode());
                report.setGenerateStatus(GenerateStatusEnums.GENERATING.getCode());
                if (LanguageType.check(report.getLanguageID(), LanguageType.Chinese)) {
                    report.setFilename(buildReportFileName(report.getReportFileType(), actualReportNo, report.getLanguageID(), suffixes));
                } else {
                    report.setFilename(buildReportFileName(report.getReportFileType(), actualReportNo, report.getLanguageID(), ""));
                }
                reportFileService.updateById(report);
                // 2 报告语言更新后需要删除无用的报告
                if (Func.isNotEmpty(delReportFileLanguageIdList) && delReportFileLanguageIdList.contains(report.getLanguageID())) {
                    reportFileService.removeById(report);
                }
            });
            if (Func.isNotEmpty(newReportFileList)) {
                reportFileService.saveBatch(newReportFileList);
            }
        }
        if (ReportActionType.check(req.getReportActionType(), ReportActionType.GENERATE_TEST_RESULT)) {
            // 删除TestResult
            reportFileList.stream().forEach(report -> {
                if (ReportFileType.check(report.getReportFileType(), ReportFileType.TestResult)) {
                    reportFileService.removeById(report);
                }
            });
            // 基于报告语言初始化新的TestResult.SaveFile
            List<ReportFilePO> newTestResultFile = Lists.newArrayList();
            if (ReportLanguage.checkLanguage(reportLanguage, ReportLanguage.EnglishReportOnly, ReportLanguage.EnglishAndChineseReport)) {
                newTestResultFile.add(assembleReportFile(LanguageType.English, req.getReportId(), reportNo, ReportFileType.TestResult, suffixes, actualReportNo));
            }
            if (ReportLanguage.checkLanguage(reportLanguage, ReportLanguage.ChineseReportOnly, ReportLanguage.EnglishAndChineseReport)) {
                newTestResultFile.add(assembleReportFile(LanguageType.Chinese, req.getReportId(), reportNo, ReportFileType.TestResult, suffixes, actualReportNo));
            }
            if (ReportLanguage.checkLanguage(reportLanguage, ReportLanguage.MultilingualReport)) {
                newTestResultFile.add(assembleReportFile(LanguageType.English, req.getReportId(), reportNo, ReportFileType.TestResult, suffixes, actualReportNo));
            }
            if (Func.isNotEmpty(newTestResultFile)) {
                reportFileService.saveBatch(newTestResultFile);
            }
        }
    }


    private ReportFilePO assembleReportFile(LanguageType languageType, String reportId, String reportNo,
                                            ReportFileType reportFileType, String suffixes, String actualReportNo) {
        ReportFilePO reportFile = new ReportFilePO();
        reportFile.setID(IdUtil.uuId());
        reportFile.setReportID(reportId);
        reportFile.setReportNo(reportNo);
        reportFile.setReportFileType(reportFileType.getCode());
        reportFile.setSuffixes(Constants.FILE_TYPE.WORD);
        reportFile.setCreatedBy(SystemContextHolder.getUserInfo().getRegionAccount());
        reportFile.setStatus(ReportFileStatus.NEW.getCode());
        reportFile.setGenerateStatus(GenerateStatusEnums.GENERATING.getCode());
        reportFile.setLanguageID(languageType.getLanguageId());
        reportFile.setFilename(buildReportFileName(reportFileType.getCode(), actualReportNo, languageType.getLanguageId(), suffixes));
        reportFile.setModifiedBy(SystemContextHolder.getUserInfo().getRegionAccount());
        reportFile.setModifiedDate(DateUtil.now());
        reportFile.setCreatedDate(DateUtil.now());
        reportFile.setActiveIndicator(true);
        return reportFile;
    }

    private String buildReportFileName(Integer reportFileType, String actualReportNo, Integer languageId, String suffixes) {
        String fileName = null;
        if (ReportFileType.check(reportFileType, ReportFileType.TestResult)) {
            fileName = String.format("%s%s", actualReportNo, "_TestResult.docx");
            if (LanguageType.check(languageId, LanguageType.Chinese)) {
                fileName = String.format("%s%s", actualReportNo, "_CN_TestResult.docx");
            }
        }
        if (ReportFileType.check(reportFileType, ReportFileType.Word)) {
            fileName = String.format("%s%s%s", actualReportNo, suffixes, ".docx");
        }
        return fileName;
    }

    /**
     * 查询签名信息
     * @param regionAccount
     * @param labCode
     * @param signTypeCode
     * @return
     */
    @Override
    public String getSignatureInfo(String regionAccount, String labCode, Integer signTypeCode){
        // 查询签名信息
        UserSignatureReq userSignReq = new UserSignatureReq();
        userSignReq.setIsSignature("1");
        userSignReq.setLanguageCodes(Lists.newArrayList(LanguageType.English.getCode(), LanguageType.Chinese.getCode()));
        userSignReq.setLabCode(labCode);
        userSignReq.setAuthSignTypes(Lists.newArrayList(signTypeCode));
        List<UserSignatureRsp> userSignatureList = frameworkClient.queryUserSignature(userSignReq);
        if (Func.isEmpty(userSignatureList)) {
            return null;
        }
        Map<String, Object> signFilePathMap = new HashMap<>();
        // 过滤中英文
        List<UserSignatureRsp> enSignatures = userSignatureList.stream().filter(userSignature -> Func.equalsSafe(LanguageType.English.getCode(), userSignature.getLanguageCode())).collect(Collectors.toCollection(ArrayList::new));
        List<UserSignatureRsp> cnSignatures = userSignatureList.stream().filter(userSignature -> Func.equalsSafe(LanguageType.Chinese.getCode(), userSignature.getLanguageCode())).collect(Collectors.toCollection(ArrayList::new));
        List<UserSignatureRsp> signEnList = enSignatures.stream().filter(i -> StringUtils.equalsIgnoreCase(regionAccount, i.getRegionAccount()) && Func.isNotEmpty(i.getAuthSignTypeKey()) && i.getAuthSignTypeKey().stream().anyMatch(k -> Func.equals(k, Func.toStr(signTypeCode)))).collect(Collectors.toList());
        List<UserSignatureRsp> signCnList = cnSignatures.stream().filter(i -> StringUtils.equalsIgnoreCase(regionAccount, i.getRegionAccount()) && Func.isNotEmpty(i.getAuthSignTypeKey()) && i.getAuthSignTypeKey().stream().anyMatch(k -> Func.equals(k, Func.toStr(signTypeCode)))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(signEnList)) {
            signFilePathMap.put(LanguageType.English.getCode(), signEnList);
        }
        if (CollectionUtils.isNotEmpty(signCnList)) {
            signFilePathMap.put(LanguageType.Chinese.getCode(), signCnList);
        }
        if (Func.isNotEmpty(signFilePathMap)) {
            return JSON.toJSONString(signFilePathMap);
        }
        return null;
    }

    @Override
    public BaseResponse<List<ReportVO>> listByOrderNo(String orderNo) {
        if(Func.isEmpty(orderNo)){
            return BaseResponse.newFailInstance("common.param.miss",new Object[]{"orderNo"});
        }
        //1 基于订单查询报告列表
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderNoList(Sets.newHashSet(orderNo));
        BaseResponse<List<ReportPO>> reportListRes = reportService.queryReportByOrderNo(orderIdReq);
        if(Func.isEmpty(reportListRes) || reportListRes.isFail() || Func.isEmpty(reportListRes.getData())){
            return BaseResponse.newFailInstance("common.param.invalid",new Object[]{Constants.TERM.REQUEST.getCode()});
        }
        // 2 过滤订单下的有效状态的报告
        List<ReportPO> activeReportList = reportListRes.getData().stream()
                .filter(item->!ReportStatus.checkCategory(item.getReportStatus(), Constants.OBJECT.REPORT.STATUS_CATEGORY.INACTIVE))
                .collect(Collectors.toList());
        if(Func.isEmpty(activeReportList)){
            return BaseResponse.newSuccessInstance(Lists.newArrayList());
        }
        return BaseResponse.newSuccessInstance(activeReportList.stream().map(item-> Func.deepCopy(item,ReportVO.class)).collect(Collectors.toList()));
    }

    @Override
    public BaseResponse queryReportByReportNo(String reportNo) {
        String result = null;
        Assert.isTrue(Func.isNotEmpty(reportNo),"common.param.miss",new Object[]{"reportNo"});
        ReportQueryReq reportQueryReq = new ReportQueryReq();
        reportQueryReq.setReportNo(reportNo);
        List<ReportPO> reportPOList = reportService.select(reportQueryReq).getData();
        if(Func.isNotEmpty(reportPOList)){
            ReportPO reportPO = reportPOList.get(0);
            result = reportPO.getOrderNo() + StringPool.PIPE + reportPO.getReportNo();
        }
        return BaseResponse.newSuccessInstance(result);
    }

    @Override
    public BaseResponse batchUpdateTestingType(ReportBatchUpdateTestingTypeReq reportBatchUpdateTestingTypeReq) {
        Assert.isTrue(Func.isNotEmpty(reportBatchUpdateTestingTypeReq),"common.param.miss",new Object[]{"report"});
        Assert.isTrue(Func.isNotEmpty(reportBatchUpdateTestingTypeReq.getList()),"common.param.miss",new Object[]{"report"});

        List<Map> maps = Lists.newArrayList();
        String productLineCode = ProductLineContextHolder.getProductLineCode();
        UserInfo user = SecurityContextHolder.getUserInfoFillSystem();
        AsyncCall asyncCall = new AsyncCall();

        for (ReportUpdateTestingTypeReq reportUpdateTestingTypeReq : reportBatchUpdateTestingTypeReq.getList()) {
            reportUpdateTestingTypeReq.setUpdateBy(user.getRegionAccount());
            reportUpdateTestingTypeReq.setProductLineCode(productLineCode);
            asyncCall.put(reportUpdateTestingTypeReq.getReportNo(), () -> this.updateTestingType(reportUpdateTestingTypeReq));
        }

        List<AsyncResult> asyncResults = AsyncUtils.awaitResult(asyncCall);
        for (AsyncResult asyncResult : asyncResults) {
            String asyncType = asyncResult.getTaskKey();
            Map objMap = new HashMap();
            objMap.put("reportNo", asyncType);
            BaseResponse baseResponse = asyncResult.getData();
            if(Func.isEmpty(baseResponse)){
                objMap.put("message", asyncResult.getResultMsg());
            }
            else {
                objMap.put("message", baseResponse.getMessage());
            }
            maps.add(objMap);
        }
        return BaseResponse.newSuccessInstance(maps);
    }

    @Override
    public BaseResponse updateTestingType(ReportUpdateTestingTypeReq reportUpdateTestingTypeReq) {
        Assert.isTrue(Func.isNotEmpty(reportUpdateTestingTypeReq),"common.param.miss",new Object[]{"report"});
        String reportId = reportUpdateTestingTypeReq.getReportId();
        String testingType = Func.isEmpty(reportUpdateTestingTypeReq.getTestingType()) ? "" : reportUpdateTestingTypeReq.getTestingType();
        String updateBy = reportUpdateTestingTypeReq.getUpdateBy();
        Assert.isTrue(Func.isNotEmpty(reportId),"common.param.miss",new Object[]{"reportId"});
        //Assert.isTrue(Func.isNotEmpty(testingType),"common.param.miss",new Object[]{"testingType"});
        if(Func.isEmpty(updateBy)){
            updateBy = SecurityContextHolder.getUserInfoFillSystem().getRegionAccount();
        }
        ReportPO dbReport = reportService.getById(reportId);
        Assert.isTrue(Func.isNotEmpty(dbReport),"report.not.exist",new Object[]{reportUpdateTestingTypeReq.getReportNo()});
        if(ReportStatus.check(dbReport.getReportStatus(),ReportStatus.Cancelled)){
            return BaseResponse.newFailInstance("report.status.not.allowed.update",new Object[]{ReportStatus.Cancelled.getMessage()});
        }
        ReportPO reportPO = new ReportPO();
        reportPO.setId(reportId);
        reportPO.setTestingType(testingType);
        reportPO.setModifiedBy(updateBy);
        reportPO.setModifiedDate(DateUtil.now());
        reportService.updateById(reportPO);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse queryReportFileHistory(ReportFileQueryReq reportFileQueryReq) {
        ReportContext<ReportFileQueryReq> reportContext = new ReportContext();
        reportContext.setParam(reportFileQueryReq);
        reportContext.setUserInfo(SystemContextHolder.getUserInfoFillSystem());
        reportContext.setToken(SecurityContextHolder.getSgsToken());
        reportContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(ReportFileHistoryQueryCMD.class, reportContext);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse<Boolean> updateReportAccreditation(ReportAccreditationUpdateReq reportAccreditationUpdateReq) {
        Boolean b = reportService.updateReportAccreditation(reportAccreditationUpdateReq);
        return BaseResponse.newSuccessInstance(b);
    }

    @Override
    public BaseResponse<PageBO<ReportAutoDeliverBO>> autoDeliverPage(ReportAutoDeliverQueryReq queryReq, Integer page, Integer rows) {
        ReportAutoDeliverContext<ReportAutoDeliverQueryReq> context = new ReportAutoDeliverContext<>();
        context.setProductLineCode(SystemContextHolder.getBuCode());
        context.setLab(SystemContextHolder.getLab());
        context.setParam(queryReq);
        context.setPage(page);
        context.setRows(rows);
        return BaseExecutor.start(ReportAutoDeliverPageCMD.class,context);
    }


    @Override
    public BaseResponse<ReportBO> getDetail(ReportIdBO reportIdBO) {
        // 校验入参不能为空
        Assert.isTrue(Func.isNotEmpty(reportIdBO),"common.param.miss",new Object[]{Constants.TERM.REQUEST.getCode()});
        // 设置请求参数
        ReportQueryReq reportQueryReq = new ReportQueryReq();
        reportQueryReq.setProductLineCode(SystemContextHolder.getBuCode());
        reportQueryReq.setLab(SystemContextHolder.getLab());
        if(Func.isNotEmpty(reportIdBO.getReportId())) {
            reportQueryReq.setReportIdList(Sets.newHashSet(reportIdBO.getReportId()));
        }else if(Func.isNotEmpty(reportIdBO.getReportNo())) {
            reportQueryReq.setReportNoList(Sets.newHashSet(reportIdBO.getReportNo()));
        }else{
            Assert.isTrue(true,"common.param.miss",new Object[]{"ReportId/ReportNo"});
        }
        // 查询ReportBO
        log.info("查询ReportBO Detail入参：{}",Func.toJson(reportQueryReq));
        BaseResponse<List<ReportBO>> baseResponse = queryBO(reportQueryReq);
        // 返回查询错误
        Assert.isTrue(baseResponse.isSuccess(),baseResponse.getMessage(),null);
        // 发现多条数据记录
        Assert.isTrue(baseResponse.getData().size()==1,"common.detail.found.multiple.items",null);
        return BaseResponse.newSuccessInstance(baseResponse.getData().get(0));
    }


}
