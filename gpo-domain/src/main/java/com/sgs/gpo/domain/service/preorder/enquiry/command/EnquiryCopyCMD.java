package com.sgs.gpo.domain.service.preorder.enquiry.command;


import com.beust.jcommander.internal.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.common.object.ObjectIdBO;
import com.sgs.framework.model.common.object.busetting.attribute.ObjectTemplateAttributeBO;
import com.sgs.framework.model.common.object.busetting.attribute.ObjectTemplateAttributeHeaderBO;
import com.sgs.framework.model.common.object.busetting.template.ObjectTemplateBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementReportBO;
import com.sgs.framework.model.order.enquiry.EnquiryBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryReportReceiverPO;
import com.sgs.gpo.domain.service.preorder.enquiry.context.EnquiryContext;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryService;
import com.sgs.gpo.domain.service.setting.object.IObjectTemplateDomainService;
import com.sgs.gpo.facade.model.enums.EnquirySectionTypeEnum;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryCopyReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryQueryReq;
import com.sgs.gpo.facade.model.setting.object.req.ObjectBuSettingReq;
import com.sgs.gpo.integration.TokenClient;
import com.sgs.gpo.integration.framework.rsp.UserLabRsp;
import com.sgs.gpo.integration.usermanagement.UserManagementClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

@Service
@Slf4j
public class EnquiryCopyCMD extends BaseCommand<EnquiryContext<EnquiryCopyReq>> {

    @Autowired
    private IObjectTemplateDomainService objectTemplateDomainService;

    @Autowired
    private IEnquiryService enquiryService;

    @Autowired
    private UserManagementClient userManagementClient;

    @Autowired
    private TokenClient tokenClient;


    @Override
    public BaseResponse validateParam(EnquiryContext<EnquiryCopyReq> context) {
        EnquiryCopyReq copyReq = context.getParam();
        Assert.isTrue(Func.isNotEmpty(copyReq), "common.param.miss", new String[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(context.getLab()), "common.param.miss", new String[]{Constants.TERM.LAB.getCode()});
        // enquiryId必填
        Assert.isTrue(Func.isNotEmpty(copyReq.getEnquiryId()), "common.param.miss", new String[]{"enquiryId"});
        // copyNum必须是数字，且大于等于1
        Assert.isTrue(Func.isNotEmpty(copyReq.getNum()), "common.param.miss", new String[]{"copyNum"});
        Assert.isTrue(Func.isNotEmpty(copyReq.getSectionTypeList()), "common.param.miss", new String[]{"sectionTypeList"});
        int copyNum = copyReq.getNum();
        if (copyNum < 1 || copyNum > 100) {
            Assert.isTrue(copyNum >= 1 &&  copyNum <= 100, "common.param.invalid", new String[]{"copyNum"});
        }
        // 查询旧单的Enquiry详情
        EnquiryBO originalEnquiry = this.queryEnquiry(copyReq.getEnquiryId());
        if (Func.isEmpty(originalEnquiry)) {
            return BaseResponse.newFailInstance("common.param.invalid", new String[]{"enquiryId"});
        }
        context.setCopyNum(copyNum);
        context.setOriginalEnquiry(originalEnquiry);
        // lab切换校验
        //判断当前用户是否切换了BU
        // 当前登陆用户的Lab信息
        Integer userLocationId = context.getLab().getLocationId();
        Integer userBuId = context.getLab().getBuId();
        // 当前单据的Lab信息
        Integer enquiryLocationId = originalEnquiry.getLab().getLocationId();
        Integer enquiryBuId = originalEnquiry.getLab().getProductLineId();
//        if (!Func.equalsSafe(userLocationId, enquiryLocationId) || !Func.equalsSafe(userBuId, enquiryBuId)) {
//            return BaseResponse.newFailInstance("BU已经切换，请刷新页面后切换到正确BU下再操作！");
//        }
        // 校验黑名单,HL要求黑名单不允许开单
        // 统一设计查询Object Setting中配置的必填字段
        return BaseResponse.newSuccessInstance(true);

    }

    @Override
    public BaseResponse before(EnquiryContext<EnquiryCopyReq> context) {
        // 获取前端传来的item列表
        List<String> sectionTypeList = Optional.ofNullable(context.getParam().getSectionTypeList()).orElse(Lists.newArrayList());

        // 获取enquiry对象下配置的列表
        ObjectBuSettingReq objectBuSettingReq = new ObjectBuSettingReq();
        ObjectIdBO objectId = new ObjectIdBO();
        objectId.setObjectCode(Constants.OBJECT.ENQUIRY);
        objectBuSettingReq.setObject(objectId);
        objectBuSettingReq.setProductLineCode(context.getLab().getBuCode());
        objectBuSettingReq.setLabCode(context.getLab().getLabCode());

        BaseResponse<ObjectTemplateBO> objectTemplateRes = objectTemplateDomainService.getBuSetting(objectBuSettingReq);


        if (objectTemplateRes == null || objectTemplateRes.getData() == null || Func.isEmpty(objectTemplateRes.getData().getAttributeList())) {
            return BaseResponse.newSuccessInstance(true);
        }
        List<ObjectTemplateAttributeBO> attributeList = objectTemplateRes.getData().getAttributeList();
        // 因为attachment不是配置出来的，手动添加一下
        ObjectTemplateAttributeBO objectTemplateAttributeBO = new ObjectTemplateAttributeBO();
        ObjectTemplateAttributeHeaderBO objectTemplateAttributeHeaderBO = new ObjectTemplateAttributeHeaderBO();
        objectTemplateAttributeHeaderBO.setAttributeCode(EnquirySectionTypeEnum.ATTACHMENT.getCode());
        objectTemplateAttributeBO.setHeader(objectTemplateAttributeHeaderBO);
        attributeList.add(objectTemplateAttributeBO);

        // 提取配置的section类型列表
        List<String> configSectionTypeEnumList = extractSectionTypeEnumList(attributeList);

        // 设置是否copy quotation
        setCopyQuotationFlag(sectionTypeList, context);

        // 比较并找出需要情况的选项
        Set<String> onlyInSectionTypeList = findSectionTypeDifference(sectionTypeList, configSectionTypeEnumList);

        // 根据差异项清空对应的字段
        clearEnquiryFields(context.getOriginalEnquiry(), onlyInSectionTypeList, context);

        return BaseResponse.newSuccessInstance(true);
    }

    private void setCopyQuotationFlag(List<String> sectionTypeList, EnquiryContext<EnquiryCopyReq> context) {
        if (Func.isNotEmpty(sectionTypeList) && !sectionTypeList.contains(EnquirySectionTypeEnum.SERVICE_ITEMS.getCode())) {
            context.setCopyQuotationFlag(false);
        }
        context.setCopyQuotationFlag(true);
    }

/**
 * 提取属性列表中的section类型枚举
 */
private List<String> extractSectionTypeEnumList(List<ObjectTemplateAttributeBO> attributeList) {
    List<String> result = Lists.newArrayList();
    for (ObjectTemplateAttributeBO attribute : attributeList) {
        if (attribute != null && attribute.getHeader() != null && Func.isNotEmpty(attribute.getHeader().getAttributeCode())) {
            EnquirySectionTypeEnum sectionTypeEnum = EnquirySectionTypeEnum.findCode(attribute.getHeader().getAttributeCode());
            if (Func.isNotEmpty(sectionTypeEnum)) {
                result.add(sectionTypeEnum.getCode());
            }
        }
    }
    return result;
}

/**
 * 找出两个集合的差异项
 * 如果list1集合为空或者是传了serviceItem选项，则返回空，默认全部的都要copy
 */
private Set<String> findSectionTypeDifference(List<String> list1, List<String> list2) {
    Set<String> set1 = new HashSet<>(list1);
    Set<String> set2 = new HashSet<>(list2);

    // 检查 set1 是否包含 SERVICE_ITEM
//    if (set1.isEmpty() || set1.contains(EnquirySectionTypeEnum.SERVICE_ITEMS.getCode())) {
//        return Collections.emptySet();
//    }

    // 检查set1中是否包含head，不包含则新增。默认head必须要copy
    if (!set1.contains(EnquirySectionTypeEnum.HEAD.getCode())) {
        set1.add(EnquirySectionTypeEnum.HEAD.getCode());
    }


    // 找出 set1 中有但 set2 中没有的元素
    Set<String> onlyInSet1 = new HashSet<>(set1);
    onlyInSet1.removeAll(set2);

    // 找出 set2 中有但 set1 中没有的元素
    Set<String> onlyInSet2 = new HashSet<>(set2);
    onlyInSet2.removeAll(set1);

    // 合并两个集合
    Set<String> result = new HashSet<>(onlyInSet1);
    result.addAll(onlyInSet2);

    return result;
}


/**
 * 根据差异项清空对应的字段，传入的值是需要清空的数据
 */
private static final Runnable NO_OP = () -> {};

/**
 * 根据差异项清空对应的字段，传入的值是需要清空的数据
 */
private void clearEnquiryFields(EnquiryBO enquiryBo, Set<String> sectionTypesToClear, EnquiryContext<EnquiryCopyReq> context) {
    if (enquiryBo == null || Func.isEmpty(sectionTypesToClear)) {
        return;
    }

    ServiceRequirementBO serviceRequirementBo = enquiryBo.getServiceRequirement();
    ServiceRequirementReportBO serviceRequirementReportBo;

    EnquiryReportReceiverPO reportReceiverPO = new EnquiryReportReceiverPO();
    if (Func.isNotEmpty(serviceRequirementBo)) {
        serviceRequirementReportBo = serviceRequirementBo.getReport();
        reportReceiverPO.setId(IdUtil.uuId());
        reportReceiverPO.setReportLanguage(serviceRequirementReportBo != null ? serviceRequirementReportBo.getReportLanguage() : null);
        reportReceiverPO.setReportHeader(serviceRequirementReportBo != null ? serviceRequirementReportBo.getReportHeader() : null);
        reportReceiverPO.setReportDeliveredTo(serviceRequirementReportBo != null ? serviceRequirementReportBo.getReportAddress() : null);
        reportReceiverPO.setReceiverType(serviceRequirementReportBo != null ? serviceRequirementReportBo.getReportType() : null);
    } else {
        serviceRequirementReportBo = null;
    }
    context.setEnquiryReportReceiver(reportReceiverPO);

    Map<String, Runnable> fieldClearMap = new HashMap<>();

    // 如果Service Requirement 不copy， report copy的话，因为report的内容是放在serviceRequirement中，所以要单独处理
    fieldClearMap.put(EnquirySectionTypeEnum.SERVICE_REQUIREMENT.getCode(), () -> {
        enquiryBo.setServiceRequirement(null);
        if (!sectionTypesToClear.contains(EnquirySectionTypeEnum.REPORT_INFO.getCode())) {
            ServiceRequirementBO tempServiceRequirementBo = new ServiceRequirementBO();
            ServiceRequirementReportBO newReport = new ServiceRequirementReportBO();
            newReport.setReportLanguage(reportReceiverPO.getReportLanguage());
            tempServiceRequirementBo.setReport(newReport);
            enquiryBo.setServiceRequirement(tempServiceRequirementBo);
        }
    });

    fieldClearMap.put(EnquirySectionTypeEnum.PRODUCT_SAMPLE.getCode(), () -> {
        enquiryBo.setSampleList(null);
        enquiryBo.setProduct(null);
        enquiryBo.setDffAttrList(null);
    });

    fieldClearMap.put(EnquirySectionTypeEnum.ATTACHMENT.getCode(), () -> enquiryBo.setAttachmentList(null));

    fieldClearMap.put(EnquirySectionTypeEnum.REPORT_INFO.getCode(), () -> {
        if (!sectionTypesToClear.contains(EnquirySectionTypeEnum.PRODUCT_SAMPLE.getCode())) {
            if (Func.isNotEmpty(reportReceiverPO)) {
                reportReceiverPO.setReportHeader(null);
                reportReceiverPO.setReportDeliveredTo(null);
                reportReceiverPO.setReceiverType(null);
                return;
            }
        }
        if (serviceRequirementBo != null && serviceRequirementBo.getReport() != null) {
            serviceRequirementBo.setReport(null);
        }
        context.setEnquiryReportReceiver(null);
    });

    fieldClearMap.put(EnquirySectionTypeEnum.CUSTOMER.getCode(), () -> {
        // 客户不copy
        enquiryBo.setCustomerList(null);

        // 不copy商机
        Optional.ofNullable(enquiryBo.getRelationship())
                .map(item -> item.getParent())
                .ifPresent(parent -> {
                    List<?> trfList = parent.getTrfList();
                    if (Func.isNotEmpty(trfList)) {
                        trfList.clear();
                    }
                });

        // service requirement中的不开票信息清空
        if (serviceRequirementBo != null) {
            serviceRequirementBo.setIssueInvoiceFlag("1");
        }

        // 报告的抬头不copy
        if (Func.isNotEmpty(serviceRequirementReportBo)) {
            serviceRequirementReportBo.setReportHeader(null);
            serviceRequirementReportBo.setReportAddress(null);
        }
    });

    // serviceItems 是通过context.setCopyQuotationFlag来决定是否需要copy的
    for (String sectionTypeCode : sectionTypesToClear) {
        fieldClearMap.getOrDefault(sectionTypeCode, NO_OP).run();
    }
}



    @Override
    public BaseResponse execute(EnquiryContext<EnquiryCopyReq> context) {
        // groupId更新
        EnquiryContext<EnquiryBO> createContext = new EnquiryContext<>();
        createContext.setParam(context.getOriginalEnquiry());
        createContext.setLab(context.getLab());
        createContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        createContext.setIsNew(context.getIsNew());
        createContext.setUserInfo(SystemContextHolder.getUserInfo());
        createContext.setCopyNum(context.getCopyNum());
        createContext.setRegular(context.getParam().getRegular());
        createContext.setCopyFcm(context.getParam().getCopyFcm());
        createContext.setSalesPerson(context.getParam().getSalesPerson());
        createContext.setSaleUserLabDTO(context.getParam().getSaleUserLabDTO());
        createContext.setEnquiryReportReceiver(context.getEnquiryReportReceiver());
        createContext.setCopyQuotationFlag(context.isCopyQuotationFlag());
        createContext.setOrderIdBO(null);
        EnquiryPO newEnquiry = null;
        for(int i = 0; i < context.getCopyNum(); i++) {
            EnquiryPO resEnquiry = (EnquiryPO) BaseExecutor.start(EnquiryCreateCMD.class, createContext).getData();
            if (Func.isEmpty(newEnquiry)) {
                newEnquiry = resEnquiry;
            }
        }
        return BaseResponse.newSuccessInstance(newEnquiry);
    }

    private EnquiryBO queryEnquiry(String enquiryId) {
        EnquiryContext<EnquiryQueryReq> context = new EnquiryContext<>();
        EnquiryQueryReq enquiryQueryReq = new EnquiryQueryReq();
        enquiryQueryReq.setEnquiryIdList(Lists.newArrayList(enquiryId));
        context.setParam(enquiryQueryReq);
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        UserLabRsp userLabRsp = userManagementClient.getUserLabBuInfo(tokenClient.getToken());
        context.setLab(SystemContextHolder.getLab());
        if (Func.isNotEmpty(userLabRsp) || Func.isNotEmpty(userLabRsp.getLabId())) {
            Lab lab = context.getLab();
            lab.setLocationId(userLabRsp.getLocationId());
            lab.setLocationCode(userLabRsp.getLocationCode());
        }
        BaseResponse<List<EnquiryBO>> enquiryListRes = BaseExecutor.start(EnquiryQueryCMD.class, context);
        if (Func.isNotEmpty(enquiryListRes.getData())) {
            return enquiryListRes.getData().get(0);
        }
        return null;
    }
}
