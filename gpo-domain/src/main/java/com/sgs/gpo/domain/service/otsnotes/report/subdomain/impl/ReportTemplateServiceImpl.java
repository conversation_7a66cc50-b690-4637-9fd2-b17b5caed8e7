package com.sgs.gpo.domain.service.otsnotes.report.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.report.ReportTemplateMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportTemplatePO;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportTemplateService;
import com.sgs.gpo.facade.model.report.req.ReportTemplateReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class ReportTemplateServiceImpl extends ServiceImpl<ReportTemplateMapper, ReportTemplatePO> implements IReportTemplateService {

    /**
     * 基于语言查询报告的模板列表
     */
    @Override
    public BaseResponse<List<ReportTemplatePO>> queryReportTemplateList(ReportTemplateReq reportTemplateReq) {
        BaseResponse<List<ReportTemplatePO>>  response = new BaseResponse<>();
        if(Func.isEmpty(reportTemplateReq) || (Func.isEmpty(reportTemplateReq.getReportId()) && Func.isEmpty(reportTemplateReq.getReportIds()))){
            return response;
        }
        LambdaQueryWrapper<ReportTemplatePO> wrapper= Wrappers.lambdaQuery();
        if(Func.isNotEmpty(reportTemplateReq.getLanguageId())){
            wrapper.eq(ReportTemplatePO::getLanguageId,reportTemplateReq.getLanguageId());
        }
        if(Func.isNotEmpty(reportTemplateReq.getReportId())){
            wrapper.eq(ReportTemplatePO::getReportId,reportTemplateReq.getReportId());
        }
        if(Func.isNotEmpty(reportTemplateReq.getReportIds())){
            wrapper.in(ReportTemplatePO::getReportId,reportTemplateReq.getReportIds());
        }
        response.setData(this.getBaseMapper().selectList(wrapper));
        return response;
    }

}
