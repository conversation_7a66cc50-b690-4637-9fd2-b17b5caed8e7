package com.sgs.gpo.domain.service.preorder.material.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.jaxrs.json.annotation.JSONP;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.StringUtil;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.ObjectType;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryService;
import com.sgs.gpo.domain.service.preorder.material.IMaterialDomainService;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IGeneralOrderService;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;
import com.sgs.gpo.facade.model.preorder.material.MaterialPageReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.integration.sgsmart.SGSMartClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class MaterialDomainServiceImpl implements IMaterialDomainService {
    @Autowired
    private IGeneralOrderService generalOrderService;
    @Autowired
    private IEnquiryService enquiryService;
    @Autowired
    private SGSMartClient sgsMartClient;
    @Override
    public BaseResponse queryMaterialPage(MaterialPageReq materialPageReq) {
        BaseResponse baseResponse = sgsMartClient.queryMaterialList(materialPageReq);
        if(StringUtil.equalsIgnoreCase(materialPageReq.getQueryType(),"ORDER") && Func.isNotEmpty(baseResponse.getData())){
            JSONObject jsonObject = JSONObject.parseObject(Func.toJson(baseResponse.getData()));
            if(Func.isNotEmpty(jsonObject) && jsonObject.containsKey("records")){
                Object recordsList = jsonObject.get("records");
                if(Func.isNotEmpty(recordsList)){
                    JSONArray records = JSONArray.parseArray(Func.toStr(recordsList));
                    JSONArray recordsArrayNew = new JSONArray();
                    Set<String> orderNoList = new HashSet<>();
                    for (Object o : records) {
                        JSONObject record = JSONObject.parseObject(Func.toJson(o));
                        if(Func.isNotEmpty(record) && record.containsKey("relationships")){
                            JSONArray relationships = JSONArray.parseArray(Func.toStr(record.get("relationships")));
                            Object orderRelationship = relationships.stream().filter(item -> Func.isNotEmpty(item) && Func.equalsSafe(Func.toInt(JSONObject.parseObject(Func.toStr(item)).getOrDefault("relType", 0)), 2)).findAny().orElse(null);
                            if(Func.isNotEmpty(orderRelationship)){
                                JSONObject relatioshipJO = JSONObject.parseObject(Func.toJson(orderRelationship));
                                if(Func.isNotEmpty(relatioshipJO)){
                                    String orderNo = Func.toStr(relatioshipJO.getOrDefault("relNo", ""));
                                    record.put("orderNo",orderNo);
                                    if(Func.isNotEmpty(orderRelationship)){
                                        orderNoList.add(orderNo);
                                    }
                                }
                            }
                            Object trfRelationship = relationships.stream().filter(item -> Func.isNotEmpty(item) && Func.equalsSafe(Func.toInt(JSONObject.parseObject(Func.toStr(item)).getOrDefault("relType", 0)), 3)).findAny().orElse(null);
                            if(Func.isNotEmpty(trfRelationship)){
                                JSONObject relatioshipJO = JSONObject.parseObject(Func.toJson(trfRelationship));
                                if(Func.isNotEmpty(relatioshipJO)){
                                    String trfNo = Func.toStr(relatioshipJO.getOrDefault("relNo", ""));
                                    record.put("trfNo",trfNo);
                                }
                            }
                        }
                        recordsArrayNew.add(record);
                    }
                    jsonObject.put("records",recordsArrayNew);
                    if(Func.isNotEmpty(orderNoList)){
                        OrderQueryReq orderQueryReq = new OrderQueryReq();
                        orderQueryReq.setOrderNoList(orderNoList);
                        List<GeneralOrderPO> generalOrderPOList = generalOrderService.query2(orderQueryReq).getData();
                        EnquiryIdReq enquiryIdReq = new EnquiryIdReq();
                        enquiryIdReq.setEnquiryNoList(orderNoList);
                        List<EnquiryPO> enquiryPOList = enquiryService.select(enquiryIdReq).getData();
                        JSONArray recordsArrayFinal = new JSONArray();
                        for (Object o : recordsArrayNew) {
                            JSONObject record = JSONObject.parseObject(Func.toJson(o));
                            if(Func.isNotEmpty(record)){
                                record.put("gpoOrderType","");
                                record.put("gpoOrderId","");
                                String orderNo = Func.toStr(record.getOrDefault("orderNo", ""));
                                if(Func.isNotEmpty(orderNo)){
                                    GeneralOrderPO generalOrderPO = null;
                                    if(Func.isNotEmpty(generalOrderPOList)){
                                        generalOrderPO = generalOrderPOList.stream().filter(item -> Func.equalsSafe(item.getOrderNo(), orderNo)).findAny().orElse(null);
                                    }
                                    EnquiryPO enquiryPO = null;
                                    if(Func.isNotEmpty(enquiryPOList)){
                                        enquiryPO = enquiryPOList.stream().filter(item -> Func.equalsSafe(item.getEnquiryNo(), orderNo)).findAny().orElse(null);
                                    }
                                    if(Func.isNotEmpty(generalOrderPO)){
                                        record.put("gpoOrderId",generalOrderPO.getId());
                                        record.put("gpoOrderType",Constants.OBJECT.ORDER.OBJECT_CODE.toUpperCase());
                                    }else if(Func.isNotEmpty(enquiryPO)){
                                        record.put("gpoOrderId",enquiryPO.getId());
                                        record.put("gpoOrderType", Constants.OBJECT.ENQUIRY.toUpperCase());
                                    }
                                }
                                recordsArrayFinal.add(record);
                            }
                        }
                        if(Func.isNotEmpty(recordsArrayFinal)){
                            jsonObject.put("records",recordsArrayFinal);
                        }
                    }
                }

            }
            baseResponse.setData(jsonObject);
        }
        return baseResponse;
    }
}
