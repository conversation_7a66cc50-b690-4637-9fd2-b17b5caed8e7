package com.sgs.gpo.domain.service.otsnotes.report.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportExtPO;
import com.sgs.gpo.facade.model.report.req.ReportIdReq;
import com.sgs.gpo.facade.model.report.req.ReportExtForTLUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportTLCompletedFlagUpdateReq;

import java.util.List;

/**
 * <AUTHOR>
 * @title: IReportExtService
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2024/2/29 8:49
 */
public interface IReportExtService extends IService<ReportExtPO> {
    BaseResponse<List<ReportExtPO>> query(ReportQueryReq reportQueryReq);

    BaseResponse delete(ReportQueryReq reportQueryReq);
//    BaseResponse<Boolean> clearReportLabSection(ReportIdReq reportIdReq);
//    BaseResponse<Boolean> updateReportLabSection(ReportExtForTLUpdateReq reportExtForTLUpdateReq);

}
