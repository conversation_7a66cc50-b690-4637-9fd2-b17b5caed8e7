package com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testsample.ProductAttributeInstanceMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.ProductAttributeInstancePO;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.IProductAttributeService;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.ProductAttributeReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class ProductAttributeImpl extends ServiceImpl<ProductAttributeInstanceMapper, ProductAttributeInstancePO>
        implements IProductAttributeService {


    @Override
    public BaseResponse<List<ProductAttributeInstancePO>> query(ProductAttributeReq productAttributeReq) {
        if(Func.isEmpty(productAttributeReq) || Func.isEmpty(productAttributeReq.getTestMatrixIdList())){
            return BaseResponse.newFailInstance("param miss");
        }
        LambdaQueryWrapper<ProductAttributeInstancePO> wrapper= Wrappers.<ProductAttributeInstancePO>lambdaQuery()
                .in(ProductAttributeInstancePO::getTestMatrixId,productAttributeReq.getTestMatrixIdList());
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }
}
