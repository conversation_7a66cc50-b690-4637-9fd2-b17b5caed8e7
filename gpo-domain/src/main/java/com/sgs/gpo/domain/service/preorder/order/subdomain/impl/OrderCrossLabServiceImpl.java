package com.sgs.gpo.domain.service.preorder.order.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.OrderCrossLabMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderCrossLabPO;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IOrderCrossLabService;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class OrderCrossLabServiceImpl extends ServiceImpl<OrderCrossLabMapper, OrderCrossLabPO>
        implements IOrderCrossLabService {


    @Override
    public BaseResponse<List<OrderCrossLabPO>> select(OrderIdReq orderIdReq) {
        if(Func.isEmpty(orderIdReq)||Func.isEmpty(orderIdReq.getOrderNoList())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        LambdaQueryWrapper<OrderCrossLabPO> wrapper= Wrappers.<OrderCrossLabPO>lambdaQuery()
                .in(OrderCrossLabPO::getOrderNo,orderIdReq.getOrderNoList());
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }
}
