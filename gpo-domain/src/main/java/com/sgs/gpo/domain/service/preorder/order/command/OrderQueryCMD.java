package com.sgs.gpo.domain.service.preorder.order.command;

import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.model.common.attachment.AttachmentBO;
import com.sgs.framework.model.common.contact.ContactPersonBO;
import com.sgs.framework.model.common.customer.CustomerBO;
import com.sgs.framework.model.common.customer.CustomerContactBO;
import com.sgs.framework.model.common.customer.CustomerLanguageBO;
import com.sgs.framework.model.common.lab.LabBO;
import com.sgs.framework.model.common.productsample.CareLabelBO;
import com.sgs.framework.model.common.servicerequirement.ReportLanguageBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementReportBO;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.ReportLanguage;
import com.sgs.framework.model.order.order.*;
import com.sgs.framework.model.order.v2.OrderCrossLabBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.domain.service.preorder.externalno.IExternalNoDomainService;
import com.sgs.gpo.domain.service.preorder.order.context.OrderContext;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.attachment.OrderAttachmentPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.customer.CustomerPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.externalno.ExternalNoRelPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.*;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.testrequest.TestRequestPO;
import com.sgs.gpo.domain.service.preorder.attachment.IOrderAttachmentService;
import com.sgs.gpo.domain.service.preorder.customer.subdomain.ICustomerService;
import com.sgs.gpo.domain.service.preorder.externalno.subdomain.IExternalNoRelService;
import com.sgs.gpo.domain.service.preorder.order.subdomain.*;
import com.sgs.gpo.domain.service.preorder.testrequest.ITestRequestService;
import com.sgs.gpo.facade.model.preorder.externalno.bo.ExternalNoBO;
import com.sgs.gpo.facade.model.preorder.externalno.req.ExternalNoQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.CareLabelReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderAttachmentQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.integration.usermanagement.UserManagementClient;
import com.sgs.gpo.integration.usermanagement.req.EmpInfoReq;
import com.sgs.gpo.integration.usermanagement.rsp.EmpInfoExtRsp;
import com.sgs.otsnotes.facade.model.enums.GpoCustomerType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/5 15:33
 */
@Service
@Scope(value = "prototype")
public class OrderQueryCMD extends BaseCommand<OrderContext<OrderQueryReq>> {

    @Autowired
    private IGeneralOrderService generalOrderService;
    @Autowired
    private ILabInstanceService labInstanceService;
    @Autowired
    private ISLOrderService slOrderService;
    @Autowired
    private IOrderPersonService orderPersonService;
    @Autowired
    private ICustomerService customerService;
    @Autowired
    private ITestRequestService testRequestService;
    @Autowired
    private IOrderReportReceiverService orderReportReceiverService;
    @Autowired
    private IOrderAttachmentService orderAttachmentService;
    @Autowired
    private IOrderCrossLabService orderCrossLabService;
    @Autowired
    private IExternalNoDomainService externalNoDomainService;
    @Autowired
    private ICareLabelService careLabelService;
    @Autowired
    private UserManagementClient userManagementClient;


    @Override
    public BaseResponse validateParam(OrderContext<OrderQueryReq> context) {
        if (Func.isEmpty(context) || Func.isEmpty(context.getParam())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse<List<OrderBO>> buildDomain(OrderContext<OrderQueryReq> context) {
        List<OrderBO> orderList = Lists.newArrayList();
        List<LabInstancePO> labList = context.getLabList();
        if (Func.isEmpty(labList)) {
            labList = Lists.newArrayList();
        }
        List<GeneralOrderPO> generalOrderList = context.getGeneralOrderList();
        if (Func.isEmpty(generalOrderList)) {
            return BaseResponse.newSuccessInstance(Lists.newArrayList());
        }
        List<ExternalNoBO> externalNoBOList = context.getExternalNoBOList();
        for (GeneralOrderPO generalOrderPO : generalOrderList) {
            OrderBO order = new OrderBO();
            OrderHeaderBO orderHeader = Func.copy(generalOrderPO, OrderHeaderBO.class);
            orderHeader.setOrderId(generalOrderPO.getId());
            orderHeader.setCreateBy(generalOrderPO.getCreatedBy());
            orderHeader.setCreateDate(generalOrderPO.getCreatedDate());
            orderHeader.setOrderExpectDueDate(generalOrderPO.getExpectedOrderDueDate());
            orderHeader.setReportExpectDueDate(generalOrderPO.getReportExpectDueDate());
            orderHeader.setExternalOrderNo(generalOrderPO.getOrderNo());
            orderHeader.setServiceType(generalOrderPO.getServiceLevel());
            orderHeader.setOrderChannelType(generalOrderPO.getOrderChannelType());
            orderHeader.setOrderConfirmDate(generalOrderPO.getOrderConfirmDate());
            orderHeader.setEnquiryNo(generalOrderPO.getEnquiryNo());
            orderHeader.setEnquiryId(generalOrderPO.getEnquiryId());

            if(Func.isNotEmpty(externalNoBOList)){
                ExternalNoBO externalNoBO = externalNoBOList.parallelStream().filter(item -> Func.equalsSafe(generalOrderPO.getId(), item.getObjectId())).findAny().orElse(null);
                if(Func.isNotEmpty(externalNoBO)){
                    String externalOrderNo = externalNoBO.getExternalNo();
                    orderHeader.setExternalOrderNo(externalOrderNo);
                }
            }
            order.setHeader(orderHeader);

            OrderFlagBO orderFlagBO = Func.copy(generalOrderPO, OrderFlagBO.class);
            order.setFlags(orderFlagBO);

            OrderPaymentBO orderPaymentBO = Func.copy(generalOrderPO, OrderPaymentBO.class);
            orderPaymentBO.setCurrency(generalOrderPO.getCurrencyID());
            orderPaymentBO.setTotalAmount(generalOrderPO.getTotalPrice());
            orderPaymentBO.setPaymentStatus(generalOrderPO.getPayStatus());
            orderPaymentBO.setMainCurrencyTotalAmount(generalOrderPO.getMainCurrencyTotalPrice());
            order.setPayment(orderPaymentBO);

            LabInstancePO labInstancePO = labList.stream().filter(lab -> orderHeader.getOrderId().equals(lab.getOrderId())).findFirst().orElse(null);
            if (Func.isNotEmpty(labInstancePO)) {
                LabBO labBO = Func.copy(labInstancePO, LabBO.class);
                labBO.setBuCode(labInstancePO.getProductLineCode());
                order.setLab(labBO);
            }
            buildSlOrderInfo(order, context.getSlOrderList());
            buildContactPerson(order, context.getSlOrderList(), context.getOrderPersonList());
            buildCustomerInfo(order, context.getCustomerList());
            buildOrderServiceRequirement(order, context.getTestRequestList(), context.getOrderReportReceiverList());
            buildOrderAttachment(order, context.getOrderAttachmentList());
            buildOrderCrossLab(order, context.getOrderCrossLabList());
            buildCareLabels(order,context.getCareLabelList());

            // others
            OrderOthersBO others = new OrderOthersBO();
            others.setOrderRemark(generalOrderPO.getRemark());
            order.setOthers(others);

            orderList.add(order);

        }
        context.setOrderList(orderList);
        return BaseResponse.newSuccessInstance(orderList);
    }

    private void buildOrderAttachment(OrderBO order, List<OrderAttachmentPO> orderAttachmentList) {
        if (Func.isNotEmpty(orderAttachmentList)) {
            List<AttachmentBO> attachmentList = Lists.newArrayList();
            orderAttachmentList.stream().filter(attachment -> Func.equalsSafe(attachment.getGeneralOrderID(), order.getHeader().getOrderId()))
                    .forEach(attachment -> {
                        AttachmentBO rdAttachment = new AttachmentBO();
                        rdAttachment.setCloudId(attachment.getCloudID());
                        rdAttachment.setFileName(attachment.getAttachmentName());
                        rdAttachment.setFileType(attachment.getFileType());
                        attachmentList.add(rdAttachment);
                    });
            order.setAttachmentList(attachmentList);
        }
    }

    private void buildOrderCrossLab(OrderBO order, List<OrderCrossLabPO> orderCrossLabPOList) {
        if (Func.isNotEmpty(orderCrossLabPOList)) {
            orderCrossLabPOList = orderCrossLabPOList.stream().filter(item -> Func.equalsSafe(item.getOrderNo(), order.getHeader().getOrderNo())).collect(Collectors.toList());
            if (Func.isNotEmpty(orderCrossLabPOList)) {
                order.setTops(Func.copy(orderCrossLabPOList, OrderCrossLabPO.class, OrderCrossLabBO.class));
            }
        }

    }

    private void buildCareLabels(OrderBO order,List<CareLabelBO> careLabelList){
        if(Func.isEmpty(careLabelList)){
            return;
        }
        careLabelList = careLabelList.stream().filter(item->Func.equalsSafe(item.getGeneralOrderId(),order.getHeader().getOrderId()))
                .collect(Collectors.toList());
        if(Func.isEmpty(careLabelList)){
            return;
        }
        order.setCareLabels(careLabelList);
    }

    private void buildOrderServiceRequirement(OrderBO order, List<TestRequestPO> testRequestList, List<OrderReportReceiverPO> orderReportReceiverList) {
        ServiceRequirementBO serviceRequirement = new ServiceRequirementBO();
        ServiceRequirementReportBO orderReportRequirement = new ServiceRequirementReportBO();
        List<ReportLanguageBO> languageList = Lists.newArrayList();
        String reportLanguage = null;
        if (Func.isNotEmpty(testRequestList)) {
            TestRequestPO orderTestRequest = testRequestList.stream().filter(item -> Func.equals(item.getGeneralOrderId(), order.getHeader().getOrderId())).findAny().orElse(null);
            if (Func.isNotEmpty(orderTestRequest)) {
                Func.copy(orderTestRequest,orderReportRequirement);
                Func.copy(orderTestRequest,serviceRequirement);
                // 名称不一致的字段手动Copy
                orderReportRequirement.setNeedAccreditation(orderTestRequest.getReportAccreditationNeededFlag());
                orderReportRequirement.setNeedDraft(orderTestRequest.getDraftReportRequired());
                orderReportRequirement.setNeedPhoto(orderTestRequest.getTakePhotoFlag());
                orderReportRequirement.setReportLanguage(Integer.valueOf(orderTestRequest.getReportLanguage()));
                orderReportRequirement.setHardcopyRequired(orderTestRequest.getHardCopyFlag());
                serviceRequirement.setOtherRequestRemark(orderTestRequest.getOtherRequirements());
                serviceRequirement.setReturnTestSampleFlag(orderTestRequest.getReturnTestedSampleFlag());
                serviceRequirement.setReturnTestSampleRemark(orderTestRequest.getReturnTestedSampleRemark());
                reportLanguage = orderTestRequest.getReportLanguage();
            }
        }
        if (Func.isNotEmpty(orderReportReceiverList)) {

            OrderReportReceiverPO reportReceiver = orderReportReceiverList.stream().filter(item -> Func.equals(item.getGeneralOrderId(), order.getHeader().getOrderId())).findAny().orElse(null);
            if (Func.isNotEmpty(reportReceiver)) {
                String reportHeader = reportReceiver.getReportHeader();
                String reportDeliveredTo = reportReceiver.getReportDeliveredTo();
                if (Func.equalsSafe(reportLanguage, ReportLanguage.EnglishReportOnly.getCode())) {
                    orderReportRequirement.setReportHeader(reportHeader);
                    orderReportRequirement.setReportAddress(reportDeliveredTo);
                    ReportLanguageBO reportLanguageBO = new ReportLanguageBO();
                    reportLanguageBO.setLanguageId(LanguageType.English.getLanguageId());
                    reportLanguageBO.setReportAddress(reportHeader);
                    reportLanguageBO.setReportHeader(reportDeliveredTo);
                    languageList.add(reportLanguageBO);
                }
                if (Func.equalsSafe(reportLanguage, ReportLanguage.ChineseReportOnly.getCode())) {
                    orderReportRequirement.setReportHeader(reportHeader);
                    orderReportRequirement.setReportAddress(reportDeliveredTo);
                    ReportLanguageBO reportLanguageBO = new ReportLanguageBO();
                    reportLanguageBO.setLanguageId(LanguageType.Chinese.getLanguageId());
                    reportLanguageBO.setReportAddress(reportHeader);
                    reportLanguageBO.setReportHeader(reportDeliveredTo);
                    languageList.add(reportLanguageBO);
                }
                if (Func.equalsSafe(reportLanguage, ReportLanguage.EnglishAndChineseReport.getCode())) {
                    List<String> reportHeaderList;
                    List<String> reportDeliveredToList;
                    ReportLanguageBO reportLanguageCN = new ReportLanguageBO();
                    reportLanguageCN.setLanguageId(LanguageType.Chinese.getLanguageId());
                    ReportLanguageBO reportLanguageEN = new ReportLanguageBO();
                    reportLanguageEN.setLanguageId(LanguageType.English.getLanguageId());
                    if (Func.isNotEmpty(reportHeader)) {
                        reportHeaderList = Arrays.stream(reportHeader.split("\\|\\|\\|")).collect(Collectors.toList());
                        if(Func.isNotEmpty(reportHeaderList)){
                            orderReportRequirement.setReportHeader(reportHeaderList.get(0));
                            reportLanguageCN.setReportHeader(reportHeaderList.get(0));
                        }
                        if (reportHeaderList.size() > 1) {
                            reportLanguageEN.setReportHeader(reportHeaderList.get(1));
                        }
                    }
                    if (Func.isNotEmpty(reportDeliveredTo)) {
                        reportDeliveredToList = Arrays.stream(reportDeliveredTo.split("\\|\\|\\|")).collect(Collectors.toList());
                        if(Func.isNotEmpty(reportDeliveredToList)){
                            orderReportRequirement.setReportAddress(reportDeliveredToList.get(0));
                            reportLanguageCN.setReportHeader(reportDeliveredToList.get(0));
                        }
                        if (reportDeliveredToList.size() > 1) {
                            reportLanguageEN.setReportHeader(reportDeliveredToList.get(1));
                        }
                    }
                    languageList.add(reportLanguageCN);
                    languageList.add(reportLanguageEN);
                    orderReportRequirement.setLanguageList(languageList);
                }
            }
        }
        serviceRequirement.setReport(orderReportRequirement);
        order.setServiceRequirement(serviceRequirement);
    }

    private void buildCustomerInfo(OrderBO order, List<CustomerPO> customerList) {
        if (Func.isNotEmpty(customerList)) {
            List<CustomerBO> customerBOList = Lists.newArrayList();
            customerList = customerList.stream().filter(e -> Func.equalsSafe(e.getGeneralOrderId(),order.getHeader().getOrderId())).collect(Collectors.toList());
            customerList.stream().forEach(customer -> {
                customerBOList.add(convertCustomer(customer));
            });
            order.setCustomerList(customerBOList);
        }
    }

    private CustomerBO convertCustomer(CustomerPO customerInstance) {
        CustomerBO rdCustomer = new CustomerBO();
        rdCustomer.setCustomerUsage(GpoCustomerType.enumOf(customerInstance.getCustomerUsage()).getStatus());
        rdCustomer.setBossNo(customerInstance.getBossNumber());
        rdCustomer.setCustomerGroupCode(customerInstance.getBuyerGroup());
        rdCustomer.setCustomerName(customerInstance.getCustomerNameEn());
        if(Func.isEmpty(customerInstance.getCustomerNameEn())){
            rdCustomer.setCustomerName(customerInstance.getCustomerNameCn());
        }
        rdCustomer.setCustomerAddress(customerInstance.getCustomerAddressEn());
        rdCustomer.setCustomerId(customerInstance.getCustomerId());
        rdCustomer.setPaymentTerm(customerInstance.getPaymentTermName());
        List<CustomerLanguageBO> languageList = Lists.newArrayList();
        CustomerLanguageBO rdCustomerLanguageDTO = new CustomerLanguageBO();
        rdCustomerLanguageDTO.setLanguageId(LanguageType.Chinese.getLanguageId());
        rdCustomerLanguageDTO.setCustomerAddress(customerInstance.getCustomerAddressCn());
        rdCustomerLanguageDTO.setCustomerName(customerInstance.getCustomerNameCn());
        languageList.add(rdCustomerLanguageDTO);
        rdCustomer.setLanguageList(languageList);
        List<CustomerContactBO> customerContactList = Lists.newArrayList();
        CustomerContactBO applicantContact = new CustomerContactBO();
        applicantContact.setContactEmail(customerInstance.getContactPersonEmail());
        applicantContact.setContactName(customerInstance.getContactPersonName());
        applicantContact.setBossContactId(customerInstance.getBossContactId());
        applicantContact.setContactFAX(customerInstance.getContactPersonFax());
        applicantContact.setContactTelephone(customerInstance.getContactPersonPhone1());
        applicantContact.setContactMobile(customerInstance.getContactPersonPhone2());
        applicantContact.setBossSiteUseId(customerInstance.getBossSiteUseId());
        applicantContact.setContactSgsMartAccount(customerInstance.getSgsMartAccount());
        applicantContact.setContactSgsMartUserId(customerInstance.getSgsMartUserId());
        customerContactList.add(applicantContact);
        rdCustomer.setCustomerContactList(customerContactList);
        return rdCustomer;
    }

    private void buildSlOrderInfo(OrderBO order, List<SLOrderPO> slOrderList) {
        if (Func.isNotEmpty(order) && Func.isNotEmpty(order.getHeader()) && Func.isNotEmpty(slOrderList)) {
            SLOrderPO slOrderPO = slOrderList.stream().filter(item -> Func.equals(item.getGeneralOrderID(), order.getHeader().getOrderId())).findAny().orElse(null);
            if (Func.isNotEmpty(slOrderPO)) {
                Func.copy(slOrderPO,order.getHeader());
                order.getHeader().setCsName(slOrderPO.getCSName());
                order.getHeader().setCsEmail(slOrderPO.getCSEmail());
                order.getHeader().setCsContact(slOrderPO.getCSContact());
                order.getHeader().setCuttingExpectDueDate(slOrderPO.getCuttingExpectDueDate());
                order.getHeader().setJobExpectDueDate(slOrderPO.getJobExpectDueDate());
            }
        }
    }

    // 组装联系人信息
    private void buildContactPerson(OrderBO order, List<SLOrderPO> slOrderList, List<OrderPersonPO> orderPersonList) {
        if (Func.isNotEmpty(slOrderList)) {
            SLOrderPO slOrderPO = slOrderList.stream().filter(item -> Func.equals(item.getGeneralOrderID(), order.getHeader().getOrderId())).findAny().orElse(null);
            List<ContactPersonBO> contactPersonList = Lists.newArrayList();
            // CS
            if (Func.isNotEmpty(slOrderPO.getCSName())) {
                ContactPersonBO csPerson = new ContactPersonBO();
                csPerson.setContactUsage("cs");
                csPerson.setContactName(slOrderPO.getCSContact());
                csPerson.setContactEmail(slOrderPO.getCSEmail());
                csPerson.setRegionAccount(slOrderPO.getCSName());
                csPerson.setResponsibleTeamCode(slOrderPO.getResponsibleTeamCode());
                csPerson.setContactTelephone(slOrderPO.getCSContact());
                contactPersonList.add(csPerson);
            }
            if(Func.isNotEmpty(slOrderPO.getOr())){
                ContactPersonBO csPerson = new ContactPersonBO();
                csPerson.setContactUsage("OR");
                csPerson.setContactName(slOrderPO.getOr());
                csPerson.setRegionAccount(slOrderPO.getOr());
                EmpInfoReq empInfoReq = new EmpInfoReq();
                empInfoReq.setLabCode(order.getLab().getLabCode());
                empInfoReq.setRegionAccount(slOrderPO.getOr());
                BaseResponse<EmpInfoExtRsp> empInfoExtRsp = userManagementClient.getEmpInfo(empInfoReq);
                if(empInfoExtRsp.isSuccess() && Func.isNotEmpty(empInfoExtRsp.getData())){
                    csPerson.setContactEmail(empInfoExtRsp.getData().getEmail());
                    csPerson.setContactTelephone(empInfoExtRsp.getData().getTelephone());
                }
                contactPersonList.add(csPerson);
            }
            if(Func.isNotEmpty(slOrderPO.getCr())){
                ContactPersonBO csPerson = new ContactPersonBO();
                csPerson.setContactUsage("CR");
                csPerson.setContactName(slOrderPO.getCr());
                csPerson.setRegionAccount(slOrderPO.getCr());
                EmpInfoReq empInfoReq = new EmpInfoReq();
                empInfoReq.setLabCode(order.getLab().getLabCode());
                empInfoReq.setRegionAccount(slOrderPO.getCr());
                BaseResponse<EmpInfoExtRsp> empInfoExtRsp = userManagementClient.getEmpInfo(empInfoReq);
                if(empInfoExtRsp.isSuccess() && Func.isNotEmpty(empInfoExtRsp.getData())){
                    csPerson.setContactEmail(empInfoExtRsp.getData().getEmail());
                    csPerson.setContactTelephone(empInfoExtRsp.getData().getTelephone());
                }
                contactPersonList.add(csPerson);
            }
            //
            if (Func.isNotEmpty(orderPersonList)) {
                orderPersonList = orderPersonList.stream().filter(e -> Func.equalsSafe(e.getGeneralOrderId(),order.getHeader().getOrderId())).collect(Collectors.toList());
                orderPersonList.stream().forEach(orderPerson -> {
                    ContactPersonBO contactPerson = new ContactPersonBO();
                    contactPerson.setContactEmail(orderPerson.getEmail());
                    contactPerson.setContactUsage(orderPerson.getPersonType());
                    contactPerson.setRegionAccount(orderPerson.getRegionAccount());
                    contactPersonList.add(contactPerson);
                });
            }
            order.setContactPersonList(contactPersonList);
        }
    }

    @Override
    public BaseResponse before(OrderContext<OrderQueryReq> context) {
        OrderQueryReq orderQueryReq = context.getParam();
        orderQueryReq.setProductLineCode(context.getProductLineCode());
        BaseResponse<List<GeneralOrderPO>> generalOrderResponse = generalOrderService.query2(orderQueryReq);
        List<GeneralOrderPO> generalOrderList = generalOrderResponse.getData();
        if (Func.isNotEmpty(generalOrderList)) {
            context.setGeneralOrderList(generalOrderList);
            OrderIdReq orderIdReq = new OrderIdReq();
            orderIdReq.setOrderIdList(generalOrderList.stream().map(GeneralOrderPO::getId).collect(Collectors.toSet()));
            orderIdReq.setOrderNoList(generalOrderList.stream().map(GeneralOrderPO::getOrderNo).collect(Collectors.toSet()));
            BaseResponse<List<LabInstancePO>> labResponse = labInstanceService.select(orderIdReq);
            if (labResponse.isSuccess()) {
                context.setLabList(labResponse.getData());
            }
            BaseResponse<List<SLOrderPO>> slOrderResponse = slOrderService.select(orderIdReq);
            if (slOrderResponse.isSuccess()) {
                context.setSlOrderList(slOrderResponse.getData());
            }
            BaseResponse<List<OrderPersonPO>> orderPersonResponse = orderPersonService.select(orderIdReq);
            if (orderPersonResponse.isSuccess()) {
                context.setOrderPersonList(orderPersonResponse.getData());
            }
            BaseResponse<List<CustomerPO>> customerResponse = customerService.select(orderIdReq);
            if (customerResponse.isSuccess()) {
                context.setCustomerList(customerResponse.getData());
            }
            BaseResponse<List<TestRequestPO>> testRequestResponse = testRequestService.select(orderIdReq);
            if (testRequestResponse.isSuccess()) {
                context.setTestRequestList(testRequestResponse.getData());
            }
            BaseResponse<List<OrderReportReceiverPO>> reportReceiverResponse = orderReportReceiverService.select(orderIdReq);
            if (reportReceiverResponse.isSuccess()) {
                context.setOrderReportReceiverList(reportReceiverResponse.getData());
            }
            OrderAttachmentQueryReq orderAttachmentQueryReq = new OrderAttachmentQueryReq();
            orderAttachmentQueryReq.setOrderIdList(generalOrderList.stream().map(GeneralOrderPO::getId).collect(Collectors.toSet()));
            BaseResponse<List<OrderAttachmentPO>> attachmentResponse = orderAttachmentService.select(orderAttachmentQueryReq);
            if (attachmentResponse.isSuccess()) {
                context.setOrderAttachmentList(attachmentResponse.getData());
            }
            BaseResponse<List<OrderCrossLabPO>> orderCrossLabResponse = orderCrossLabService.select(orderIdReq);
            if (orderCrossLabResponse.isSuccess()) {
                context.setOrderCrossLabList(orderCrossLabResponse.getData());
            }

            ExternalNoQueryReq externalNoQueryReq = new ExternalNoQueryReq();
            externalNoQueryReq.setObjectType(Constants.OBJECT.ORDER.OBJECT_CODE);
            externalNoQueryReq.setObjectIdList(generalOrderList.stream().map(GeneralOrderPO::getId).collect(Collectors.toSet()));
            BaseResponse<List<ExternalNoBO>> externalNoBOListRsp = externalNoDomainService.queryBO(externalNoQueryReq);
            if(externalNoBOListRsp.isSuccess() && Func.isNotEmpty(externalNoBOListRsp.getData())){
                context.setExternalNoBOList(externalNoBOListRsp.getData());
            }
            CareLabelReq careLabelReq = new CareLabelReq();
            careLabelReq.setOrderIds(orderIdReq.getOrderIdList());
            BaseResponse<List<CareLabelBO>> careLabelRes = careLabelService.queryCareLabelList(careLabelReq);
            if (careLabelRes.isSuccess()) {
                context.setCareLabelList(careLabelRes.getData());
            }
        }
        return super.before(context);
    }

    @Override
    public BaseResponse<List<OrderBO>> execute(OrderContext<OrderQueryReq> context) {
        return buildDomain(context);
    }

    @Override
    public BaseResponse after(OrderContext<OrderQueryReq> context) {
        return BaseResponse.newSuccessInstance(context.getOrderList());
    }
}
