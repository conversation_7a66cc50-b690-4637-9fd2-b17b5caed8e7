package com.sgs.gpo.domain.service.otsnotes.job.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.job.JobTestLinePO;
import com.sgs.gpo.facade.model.job.req.JobIdReq;
import com.sgs.gpo.facade.model.otsnotes.job.dto.JobTestLineAndStandardDTO;
import com.sgs.gpo.facade.model.otsnotes.job.dto.JobTestLineDTO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.QueryJobTestLineReq;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/7/3 09:44
 */
public interface IJobTestLineService extends IService<JobTestLinePO> {
    /**
     * 查询关联的Job TestLine信息
     * @param jobIdReq
     * @return
     */
    BaseResponse<List<JobTestLinePO>> query(JobIdReq jobIdReq);

    BaseResponse<List<JobTestLinePO>> query(QueryJobTestLineReq jobTestLineReq);
    /**
     * 删除Job关联的测试项
     * @param jobId
     * @param testLineInstanceIdList
     * @return
     */
    BaseResponse<Integer> removeByJobAndTL(String jobId, Set<String> testLineInstanceIdList);
    BaseResponse<List<JobTestLineDTO>> queryJobTestLineDTO(QueryJobTestLineReq jobTestLineReq);

    List<JobTestLineAndStandardDTO> getTestLineAndStandardByOrderNo(Set<String> orderNoList);
}
