package com.sgs.gpo.domain.service.otsnotes.subcontract.context;

import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractReportMatrixQueryReq;
import com.sgs.gpo.facade.model.subcontract.rsp.SubcontractReportMatrixQueryRsp;
import lombok.Data;

@Data
public class SubcontractReportMatrixContext extends BaseContext<SubcontractReportMatrixQueryReq, SubcontractReportMatrixQueryRsp> {

    String orderNo;

    String orderId;

}
