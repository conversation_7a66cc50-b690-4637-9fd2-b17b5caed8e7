package com.sgs.gpo.domain.service.otsnotes.testline.command;

import com.alibaba.fastjson.JSON;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.model.enums.JobStatus;
import com.sgs.framework.model.enums.TestExecutionType;
import com.sgs.framework.model.enums.TestLineStatus;
import com.sgs.framework.model.order.order.OrderBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.test.testline.TestLineExecutionBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.BizLogConstant;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.ObjectOperationTypeEnum;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.job.JobPO;
import com.sgs.gpo.domain.service.otsnotes.job.command.JobLabOutCMD;
import com.sgs.gpo.domain.service.otsnotes.job.context.JobContext;
import com.sgs.gpo.domain.service.otsnotes.job.subdomain.IJobService;
import com.sgs.gpo.domain.service.otsnotes.job.subdomain.IJobTestLineService;
import com.sgs.gpo.domain.service.otsnotes.report.IReportDomainService;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineDomainService;
import com.sgs.gpo.domain.service.otsnotes.testline.context.TestLineLabOutContext;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.ITestLineService;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.facade.model.job.req.JobQueryReq;
import com.sgs.gpo.facade.model.otsnotes.job.dto.JobTestLineDTO;
import com.sgs.gpo.facade.model.otsnotes.job.req.JobLabOutReq;
import com.sgs.gpo.facade.model.otsnotes.testline.dto.TestLineDTO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.QueryJobTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineLabOutReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportExtForTLUpdateReq;
import com.sgs.gpo.integration.framework.FrameworkClient;
import com.sgs.gpo.integration.framework.req.BUReq;
import com.sgs.gpo.integration.framework.rsp.BURsp;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.info.BizLogInfo;
import com.sgs.otsnotes.facade.OrderFacade;
import com.sgs.otsnotes.facade.TestLineFacade;
import com.sgs.otsnotes.facade.model.req.testLine.TestLineStatusUpdateReq;
import com.sgs.preorder.facade.model.req.OrderIdReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: TestLineLabOutCMD
 * @projectName gpo-micro-service
 * @date 2023/8/21 11:06
 */
@Service
@Slf4j
@Scope(value = "prototype")
@Primary
public class TestLineLabOutCMD extends BaseCommand<TestLineLabOutContext> {
    @Autowired
    private ITestLineDomainService testLineDomainService;
    @Autowired
    private ITestLineService testLineService;
    @Autowired
    private IJobTestLineService jobTestLineService;
    @Autowired
    private IJobService jobService;
    @Autowired
    private TestLineFacade testLineFacade;
    @Autowired
    private IOrderDomainService orderDomainService;
    @Autowired
    private BizLogClient bizLogClient;
    @Autowired
    private OrderFacade notesOrderFacade;
    @Autowired
    private IReportDomainService reportDomainService;
    @Autowired
    private FrameworkClient frameworkClient;


    @Override
    public BaseResponse validateParam(TestLineLabOutContext context) {
        TestLineLabOutReq testLineLabOutReq = context.getParam();
        if (Func.isEmpty(testLineLabOutReq)) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        if (Func.isEmpty(testLineLabOutReq.getTestItemNoList()) && Func.isEmpty(testLineLabOutReq.getTestLineInstanceIdList())) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"TestItemNoList or TestLineInstanceIdList"});
        }
        if(Func.isNotEmpty(testLineLabOutReq.getProductLineCode())){
            ProductLineContextHolder.setProductLineCode(testLineLabOutReq.getProductLineCode());
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(TestLineLabOutContext context) {
        TestLineLabOutReq testLineLabOutReq = context.getParam();

        //查询TL
        OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
        orderTestLineReq.setTestItemNoList(testLineLabOutReq.getTestItemNoList());
        orderTestLineReq.setProductLineCode(testLineLabOutReq.getProductLineCode());
        orderTestLineReq.setTestLineInstanceIdList(testLineLabOutReq.getTestLineInstanceIdList());
        if(Func.isNotEmpty(orderTestLineReq.getProductLineCode())){
            BUReq buReq = new BUReq();
            buReq.setProductLineAbbr(orderTestLineReq.getProductLineCode());
            BaseResponse<List<BURsp>> buRes = frameworkClient.searchBUList(buReq);
            if(buRes.isSuccess() && Func.isNotEmpty(buRes.getData())){
                orderTestLineReq.setBuId(buRes.getData().get(0).getProductLineID());
            }
        }
        List<TestLineBO> testLineBOList = testLineDomainService.queryTestLine(orderTestLineReq).getData();

        List<TestLineExecutionBO> testLineExecutionJobBOList = null;
        List<JobPO> jobPOList = null;
        List<OrderBO> orderBOList = null;
        //查询JobTestLineRel
        if (Func.isNotEmpty(testLineBOList)) {
            testLineExecutionJobBOList = testLineBOList.stream().filter(tl -> Func.isNotEmpty(tl.getTestExecutionList())).flatMap(tl -> tl.getTestExecutionList().stream()).filter(testLineExecution -> TestExecutionType.check(testLineExecution.getTestExecutionType(), TestExecutionType.JOB)).collect(Collectors.toList());
            Set<String> testLineInstanceIdList = testLineBOList.stream().map(TestLineBO::getTestLineInstanceId).collect(Collectors.toSet());
            if (Func.isNotEmpty(testLineExecutionJobBOList)) {
                //查询Job
                Set<String> jobIdList = testLineExecutionJobBOList.stream().map(TestLineExecutionBO::getTestExecutionId).collect(Collectors.toSet());
                JobQueryReq jobQueryReq = new JobQueryReq();
                jobQueryReq.setJobIdList(jobIdList);
                jobPOList = jobService.query(jobQueryReq);
                if (Func.isNotEmpty(jobPOList)) {
                    Set<String> orderNoList = jobPOList.stream().map(JobPO::getOrderNo).collect(Collectors.toSet());
                    OrderQueryReq orderQueryReq = new OrderQueryReq();
                    orderQueryReq.setOrderNoList(orderNoList);
                    orderBOList = orderDomainService.queryV1(orderQueryReq).getData();
                }
            }
            if(Func.isEmpty(testLineLabOutReq.getTestItemNoList())){
                context.getParam().setTestItemNoList(testLineBOList.stream().map(e -> e.getTestItemNo()).collect(Collectors.toSet()));
            }
        }
        context.setTestLineBOList(testLineBOList);
        context.setJobPOList(jobPOList);
        // 2024/4/11 ScanTool入参没有token,无法获取到lab,此处先取Order的labCode，后面考虑让ScanTool传入
        if(Func.isNotEmpty(orderBOList) && Func.isNotEmpty(orderBOList.get(0).getLab()) && Func.isNotEmpty(orderBOList.get(0).getLab().getLabCode())){
            SystemContextHolder.resetLab(orderBOList.get(0).getLab().getLabCode());
        }
        context.setOrderBOList(orderBOList);
        context.setTestLineExecutionJobBOList(testLineExecutionJobBOList);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(TestLineLabOutContext context) {
        List<TestLineBO> testLineBOList = context.getTestLineBOList();
        List<JobPO> jobPOList = context.getJobPOList();
        List<TestLineExecutionBO> testLineExecutionJobBOList = context.getTestLineExecutionJobBOList();
        List<OrderBO> orderBOList = context.getOrderBOList();
        if (Func.isEmpty(testLineBOList)) {
            return BaseResponse.newFailInstance("test.line.not.find", null);
        }
        TestLineLabOutReq testLineLabOutReq = context.getParam();
        String userName = SystemContextHolder.getUserInfoFillSystem().getRegionAccount();
        if(Func.isNotEmpty(testLineLabOutReq.getOperatedBy())){
            userName = testLineLabOutReq.getOperatedBy();
        }
        Map<String, String> checkResult = new HashMap<>();
        Set<String> labOutTestLineInstanceIdList = new HashSet<>();
        //TestLine是否存在
        if (Func.isNotEmpty(testLineLabOutReq.getTestItemNoList())) {
            for (String testItemNo : testLineLabOutReq.getTestItemNoList()) {
                TestLineBO matchTestLine = testLineBOList.stream().filter(item -> Func.equalsSafe(item.getTestItemNo(), testItemNo)).findAny().orElse(null);
                if (Func.isEmpty(matchTestLine)) {
                    checkResult.put(testItemNo, "Not Find TestLine!");
                    continue;
                }
                if (Func.isNotEmpty(matchTestLine.getPendingFlag()) && matchTestLine.getPendingFlag()) {
                    checkResult.put(testItemNo, "Test Line is Pending");
                    continue;
                }
//            GPO2-14015 TestItem Status=Typing or Submitted，StartDate <>""，Engineer<>""
                if (!TestLineStatus.check(matchTestLine.getTestLineStatus(), TestLineStatus.Typing,TestLineStatus.Submit,TestLineStatus.Entered)) {
                    checkResult.put(testItemNo, "TestLine Status is not Typing or Submitted or Entered");
                    continue;
                }

                if (Func.isEmpty(matchTestLine.getTestStartDate())) {
                    checkResult.put(testItemNo, "Test Line Not LabIn");
                    continue;
                }

                if (Func.isEmpty(matchTestLine.getEngineer()) && Func.isEmpty(testLineLabOutReq.getEngineer())) {
                    checkResult.put(testItemNo, "TestLine Engineer is Empty");
                    continue;
                }

                //根据TestLine查询job，没有Job不允许操作labOut
                if (Func.isEmpty(jobPOList) || Func.isEmpty(testLineExecutionJobBOList)) {
                    checkResult.put(testItemNo, "Not Find Job Info");
                    continue;
                } else {
                    List<TestLineExecutionBO> matchExecutionJobList = testLineExecutionJobBOList.stream().filter(item -> Func.equalsSafe(item.getTestLineInstanceId(), matchTestLine.getTestLineInstanceId()) && !JobStatus.check(item.getTestExecutionStatus(), JobStatus.Cancelled)).collect(Collectors.toList());
                    List<String> matchJobNoList = matchExecutionJobList.stream().map(TestLineExecutionBO::getTestExecutionNo).collect(Collectors.toList());
                    List<JobPO> matchJobBOList = jobPOList.stream().filter(item -> matchJobNoList.contains(item.getJobNo())).collect(Collectors.toList());
                    if (Func.isEmpty(matchJobBOList)) {
                        checkResult.put(testItemNo, "Not Find Job Info");
                        continue;
                    } else if (matchJobBOList.stream().filter(item -> !JobStatus.check(item.getJobStatus(), JobStatus.Testing)).count() > 0 && testLineLabOutReq.getCheckJobStatus()) {
                        checkResult.put(testItemNo, "Job Status is Not Testing");
                        continue;
                    }
                    if(matchJobBOList.stream().filter(item -> !JobStatus.check(item.getJobStatus(), JobStatus.Closed)).count() > 0 && !testLineLabOutReq.getCheckJobStatus()){
                        checkResult.put(testItemNo, "Job Status is Not All Closed");
                    }
                }
                labOutTestLineInstanceIdList.add(matchTestLine.getTestLineInstanceId());
            }
        } else {
            //TL StartDate是否为空， After LabIn Can Operate Lab Out
            String testStartDateEmptyTestLines = testLineBOList.stream().filter(item -> Func.isEmpty(item.getTestStartDate())).map(TestLineBO::getTestItemNo).collect(Collectors.joining(","));
            if (Func.isNotEmpty(testStartDateEmptyTestLines)) {
                return BaseResponse.newFailInstance("test.line.labOut.check.labIn", new Object[]{testStartDateEmptyTestLines});
            }
            //校验TL是否都有Engineer,没有且传入的Engineer为空，提示Please Select Engineer!
            if (testLineLabOutReq.getCheckEngineer()) {
                String engineerEmptyTestLines = testLineBOList.stream().filter(item -> Func.isEmpty(item.getEngineer())).map(TestLineBO::getTestItemNo).collect(Collectors.joining(","));
                if (Func.isNotEmpty(engineerEmptyTestLines) && Func.isEmpty(testLineLabOutReq.getEngineer())) {
                    return BaseResponse.newFailInstance("test.line.engineer.empty", new Object[]{engineerEmptyTestLines});
                }
            }
            //校验TL状态是否Pending
            String pendingTestLines = testLineBOList.stream().filter(item -> Func.isEmpty(item.getPendingFlag())).map(TestLineBO::getTestItemNo).collect(Collectors.joining(","));
            if (Func.isNotEmpty(pendingTestLines)) {
                return BaseResponse.newFailInstance("test.line.pending", new Object[]{pendingTestLines});
            }
            //根据TestLine查询job，没有Job不允许操作labOut
            if (Func.isEmpty(jobPOList)) {
                return BaseResponse.newFailInstance("job.not.find", null);
            }
            //校验关联的Job的状态是否是Testing
            List<JobPO> noTestingJobList = jobPOList.stream().filter(i -> !Func.equals(JobStatus.Testing.getStatus(), i.getJobStatus())).collect(Collectors.toList());
            if (Func.isNotEmpty(noTestingJobList)) {
                String noTestingJobNos = noTestingJobList.stream().map(JobPO::getJobNo).collect(Collectors.joining(","));
                return BaseResponse.newFailInstance("job.status.not.match", new Object[]{noTestingJobNos, "Testing"});
            }
        }

        if (Func.isNotEmpty(labOutTestLineInstanceIdList)) {
            TestLineLabOutReq testLineLabOutReqNew = new TestLineLabOutReq();
            testLineLabOutReqNew.setTestLineInstanceIdList(labOutTestLineInstanceIdList);
            testLineLabOutReqNew.setEngineer(testLineLabOutReq.getEngineer());
            BaseResponse<Boolean> tlLabOutResponse = testLineService.updateTestLineForLabOut(testLineLabOutReqNew);
            log.info("updateTestLineForLabOut rsp:{}", JSON.toJSONString(tlLabOutResponse));

            QueryJobTestLineReq queryJobTestLineReq = new QueryJobTestLineReq();
            queryJobTestLineReq.setTestLineInstanceIdList(labOutTestLineInstanceIdList);
            queryJobTestLineReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            List<JobTestLineDTO> jobTestLineDTOListByTl = jobTestLineService.queryJobTestLineDTO(queryJobTestLineReq).getData();
            Map<String, List<JobTestLineDTO>> orderJobMaps = jobTestLineDTOListByTl.stream().collect(Collectors.groupingBy(JobTestLineDTO::getOrderNo));
            String finalUserName = userName;
            // TODO-Trevor.Yuan 2023/8/22 记录操作Bizlog和TL的状态变化
            orderJobMaps.forEach((orderNo, jobList) -> {
                if (Func.isNotEmpty(jobList)) {
                    List<TestLineDTO> testLineDTOList = jobList.stream().filter(job -> Func.isNotEmpty(job.getTestLineDTOList())).flatMap(tl -> tl.getTestLineDTOList().stream()).collect(Collectors.toList());
                    List<String> jobTestLineInstanceIdList = testLineDTOList.stream().map(TestLineDTO::getTestLineInstanceId).collect(Collectors.toList());
                    if (Func.isNotEmpty(jobTestLineInstanceIdList)) {

                        if(Func.isNotEmpty(orderBOList)){
                            OrderBO orderBO = orderBOList.stream().filter(item -> Func.isNotEmpty(item.getHeader()) && Func.equalsSafe(item.getHeader().getOrderNo(), orderNo)).findFirst().orElse(null);
                            if (Func.isNotEmpty(orderBO)) {
                                BizLogInfo bizLog = new BizLogInfo();
                                bizLog.setBizId(orderNo);
                                bizLog.setLab(orderBO.getLab().getLocationCode());
                                bizLog.setBu(orderBO.getLab().getProductLineCode());
                                bizLog.setBizOpType(BizLogConstant.TEST_HISTORY);
                                bizLog.setOpType(ObjectOperationTypeEnum.TEST_LINE_LABOUT.getType());
                                bizLog.setOpUser(finalUserName);
                                String testItemNos = testLineDTOList.stream().filter(i -> Func.isNotEmpty(i.getTestItemNo())).map(tl -> tl.getTestItemNo()).collect(Collectors.joining(","));
                                String bizNewVal = "TestItemNo[" + testItemNos + "]";
                                bizLog.setNewVal(bizNewVal);
                                bizLogClient.doSend(bizLog);
                            }
                        }
                    }
                }
            });


        }
        //判断对应Job下的TL是否全部LabOut，全部TL LabOut后再执行JobLabOut
        //根据Job查询TL
        if (Func.isNotEmpty(jobPOList)) {
            Set<String> jobNoList = jobPOList.stream().map(JobPO::getJobNo).collect(Collectors.toSet());
            QueryJobTestLineReq queryJobTestLineReq = new QueryJobTestLineReq();
            queryJobTestLineReq.setJobNoList(jobNoList);
            queryJobTestLineReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            List<JobTestLineDTO> jobTestLineDTOListByJob = jobTestLineService.queryJobTestLineDTO(queryJobTestLineReq).getData();
            Set<JobPO> labOutJobList = new HashSet<>();
            for (JobPO jobPO : jobPOList) {
                if (Func.isNotEmpty(jobTestLineDTOListByJob)) {
                    JobTestLineDTO jobTestLineDTO = jobTestLineDTOListByJob.stream().filter(item -> Func.equalsSafe(item.getJobNo(), jobPO.getJobNo())).findFirst().orElse(null);
                    if (Func.isNotEmpty(jobTestLineDTO) && Func.isNotEmpty(jobTestLineDTO.getTestLineDTOList())) {
                        List<TestLineDTO> testLineDTOList = jobTestLineDTO.getTestLineDTOList();
                        Long testEndDateEmptyCount = testLineDTOList.stream().filter(item -> Func.isEmpty(item.getTestEndDate())).count();
                        if (testEndDateEmptyCount == 0) {
                            labOutJobList.add(jobPO);
                        }
                    }
                }
            }
            if (Func.isNotEmpty(labOutJobList)) {
                Set<String> labOutJobNoList = labOutJobList.stream().map(JobPO::getJobNo).collect(Collectors.toSet());
                ProductLineContextHolder.setProductLineCode(testLineLabOutReq.getProductLineCode());

                JobLabOutReq jobLabOutReq = new JobLabOutReq();
                jobLabOutReq.setJobNoList(labOutJobNoList);
                jobLabOutReq.setTriggerFrom(Constants.OBJECT.TEST_LINE.OBJECT_CODE);
                jobLabOutReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                jobLabOutReq.setOperatedBy(userName);
                JobContext<JobLabOutReq> jobLabOutContext = new JobContext<>(jobLabOutReq);
                jobLabOutContext.setOrderBOList(context.getOrderBOList());
                if(testLineLabOutReq.getCheckJobStatus()){
                    BaseResponse jobLabOutRsp = BaseExecutor.start(JobLabOutCMD.class, jobLabOutContext);
                    log.info("job LabOut Result :{}",jobLabOutRsp);
                }
                //再次查询TL,判断job下所有的TL是否都Completed，调用closeOrderForTestOnly, // 如果是 testing only，cancel report, order close
                for (JobPO jobPO : labOutJobList) {
                    // closeOrderForTestOnly
                    OrderIdReq orderIdReq = new OrderIdReq();
                    orderIdReq.setOrderNo(jobPO.getOrderNo());
                    orderIdReq.setProductLineCode(testLineLabOutReq.getProductLineCode());
                    orderIdReq.setToken(context.getToken());
                    BaseResponse<String> closeOrderForTestOnlyRsp = notesOrderFacade.closeOrderForTestOnly(orderIdReq);
                    log.info("call closeOrderForTestOnly rsp:{}", JSON.toJSONString(closeOrderForTestOnlyRsp));
                }
            }
        }
        if(Func.isNotEmpty(context.getOrderBOList())){
            for (OrderBO orderBO : context.getOrderBOList()) {
                TestLineStatusUpdateReq testLineStatusUpdateReq = new TestLineStatusUpdateReq();
                testLineStatusUpdateReq.setOrderNo(orderBO.getHeader().getOrderNo());
                testLineStatusUpdateReq.setProductLineCode(testLineLabOutReq.getProductLineCode());
                testLineStatusUpdateReq.setToken(context.getToken());
                testLineStatusUpdateReq.setTrigger(testLineLabOutReq.getTrigger());
                BaseResponse testLineChangeResponse = testLineFacade.onChange(testLineStatusUpdateReq);
                log.info("call testLineFacade onChange rsp:{}", JSON.toJSONString(testLineChangeResponse));
            }
        }


        return BaseResponse.newSuccessInstance(checkResult);
    }

    @Override
    public BaseResponse after(TestLineLabOutContext context) {
        TestLineLabOutReq testLineLabOutReq = context.getParam();
        List<TestLineBO> testLineBOList = context.getTestLineBOList();
        try {
            //更新TestLine相关的Report的testCompletedFlag
            if(Func.isNotEmpty(testLineBOList)){
                Set<String> testLineInstanceIdList = testLineBOList.stream().map(TestLineBO::getTestLineInstanceId).collect(Collectors.toSet());
                ReportExtForTLUpdateReq reportExtForTLUpdateReq = new ReportExtForTLUpdateReq();
                reportExtForTLUpdateReq.setTestLineInstanceIdList(testLineInstanceIdList);
                BaseResponse<Boolean> updateReportExtRes = reportDomainService.updateReportExtForTL(reportExtForTLUpdateReq);
                log.info("TL LabOut updateReportExt res:{}", Func.toJson(updateReportExtRes));
            }
        } catch (Exception e) {
            log.error("TL LabOut updateReportExt Error:{}",e);
        }
        return super.after(context);
    }
}
