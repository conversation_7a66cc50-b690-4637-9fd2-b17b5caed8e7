package com.sgs.gpo.domain.service.preorder.externalno.context;

import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.externalno.ExternalNoRelPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.facade.model.preorder.externalno.bo.ExternalNoBO;
import lombok.Data;

import java.util.List;

@Data
public class ExternalNoContext<Input> extends BaseContext<Input, ExternalNoBO> {

    private boolean showExternalNo;
    private List<ExternalNoBO> externalNoBOList;
    private List<ExternalNoRelPO> externalNoRelPOList;
    private List<ReportPO> reportPOList;
    List<GeneralOrderPO> generalOrderPOList;

}
