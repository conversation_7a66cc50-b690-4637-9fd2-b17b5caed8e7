package com.sgs.gpo.domain.service.otsnotes.subcontract.context;

import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractBO;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractRequirementPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractTestLinePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subreport.SubReportPO;
import lombok.Data;

import java.util.List;

@Data
public class SubcontractContext<Input> extends BaseContext<Input, SubcontractBO> {

    private Lab lab;

    private List<SubcontractBO> subcontractBOList;
    private List<SubcontractPO> subcontractPOList;
    private List<SubcontractRequirementPO> subcontractRequirementList;
    private List<SubcontractTestLinePO> subcontractTestLineList;
    private List<SubReportPO> subReportList;

    public SubcontractContext(Input input){
        this.setLab(SystemContextHolder.getLab());
        this.setToken(SecurityContextHolder.getSgsToken());
        this.setUserInfo(SecurityContextHolder.getUserInfo());
        this.setParam(input);
    }
}
