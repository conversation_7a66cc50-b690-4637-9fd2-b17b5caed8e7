package com.sgs.gpo.domain.service.otsnotes.testmatrix.command;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.model.common.productsample.ProductSampleBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.test.testmatrix.TestLineWithSampleMatrixBO;
import com.sgs.framework.model.test.testmatrix.TestMatrixBO;
import com.sgs.framework.model.test.testmatrix.TestSampleMatrixBO;
import com.sgs.framework.model.test.testsample.TestSampleBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineDomainService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.context.TestMatrixContext;
import com.sgs.gpo.domain.service.preorder.productsample.IProductSampleDomainService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixService;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.ITestSampleService;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;
import com.sgs.gpo.facade.model.preorder.productsample.req.ProductSampleQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/3 14:25
 */
@Service
@Slf4j
@Scope(value = "prototype")
public class TestLineWithSampleMatrixQueryCMD extends BaseCommand<TestMatrixContext<TestMatrixQueryReq,TestLineWithSampleMatrixBO>> {

    @Autowired
    private ITestMatrixService testMatrixService;
    @Autowired
    private ITestLineDomainService testLineDomainService;
    @Autowired
    private ITestSampleService testSampleService;
    @Autowired
    private IProductSampleDomainService productSampleDomainService;


    @Override
    public BaseResponse validateParam(TestMatrixContext<TestMatrixQueryReq,TestLineWithSampleMatrixBO> context) {
        // 校验入参不能为空
        if(Func.isEmpty(context)||Func.isEmpty(context.getParam())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(TestMatrixContext<TestMatrixQueryReq,TestLineWithSampleMatrixBO> context) {
        //1、查询TestMatrix列表
        BaseResponse<List<TestMatrixBO>> testMatrixResponse = testMatrixService.queryV1(context.getParam());
        List<TestMatrixBO> testMatrixList = testMatrixResponse.getData();
        if(Func.isEmpty(testMatrixList)){
            return BaseResponse.newSuccessInstance(testMatrixList);
        }
        //2、查询TestLine列表；
        Set<String> testLineInstanceIdList = testMatrixList.stream().map(TestMatrixBO::getTestLineInstanceId).collect(Collectors.toSet());
        OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
        orderTestLineReq.setTestLineInstanceIdList(testLineInstanceIdList);
        BaseResponse<List<TestLineBO>> testLineResponse = testLineDomainService.queryTestLine(orderTestLineReq);
        List<TestLineBO> testLineList = testLineResponse.getData();
        if(Func.isEmpty(testLineList)){
            return BaseResponse.newSuccessInstance(testMatrixList);
        }
        //3、查询TestSample列表；
        String orderId = testLineResponse.getData().get(0).getOrderId();

        TestSampleQueryReq testSampleQueryReq = new TestSampleQueryReq();
        testSampleQueryReq.setOrderId(orderId);
        BaseResponse<List<TestSampleBO>> testSampleResponse = testSampleService.queryV1(testSampleQueryReq);
        List<TestSampleBO>testSampleList = testSampleResponse.getData();

        ProductSampleQueryReq productSampleQueryReq = new ProductSampleQueryReq();
        productSampleQueryReq.setOrderIdList(Sets.newHashSet(orderId));
        productSampleQueryReq.setRefSampleIdList(testSampleList.stream().map(TestSampleBO::getTestSampleInstanceId).collect(Collectors.toSet()));
        BaseResponse<List<ProductSampleBO>> productSampleResponse = productSampleDomainService.queryOrderProductSample(productSampleQueryReq);
        List<ProductSampleBO> productSampleList = productSampleResponse.getData();

        //4、TestLine 追加Sample信息
        List<TestLineWithSampleMatrixBO> testLineWithSampleMatrixList = Lists.newArrayList();
        for(TestLineBO testLine : testLineResponse.getData()){
            TestLineWithSampleMatrixBO testLineAndSampleList = new TestLineWithSampleMatrixBO();
            testLineAndSampleList.setTestLine(testLine);

            Set<String> testLineSampleIdList = testMatrixList.stream().filter(testMatrix -> {
                return testMatrix.getTestLineInstanceId().equals(testLine.getTestLineInstanceId());
            }).map(TestMatrixBO::getTestSampleInstanceId).collect(Collectors.toSet());

            List<TestSampleBO> testLineSampleList = testSampleList.stream().filter(testSample->{
                return testLineSampleIdList.contains(testSample.getTestSampleInstanceId());
            }).collect(Collectors.toList());

            List<TestSampleMatrixBO> testSampleMatrixBOList = Lists.newArrayList();
            if(Func.isNotEmpty(testLineSampleList)) {
                for (TestSampleBO testSampleBO : testLineSampleList) {
                    TestSampleMatrixBO testSampleMatrixBO = Func.copy(testSampleBO, TestSampleMatrixBO.class);
                    TestMatrixBO testMatrixBO = testMatrixList.stream().filter(item -> {
                        return item.getTestLineInstanceId().equals(testLine.getTestLineInstanceId()) &&
                                item.getTestSampleInstanceId().equals(testSampleBO.getTestSampleInstanceId());}).findFirst().orElse(null);
                    if(Func.isNotEmpty(testMatrixBO)) {
                        testSampleMatrixBO.setTestMatrixId(testMatrixBO.getTestMatrixId());
                        // todo
//                    testSampleMatrixBO.setTestMatrixStatus();
                    }
                    testSampleMatrixBOList.add(testSampleMatrixBO);
                }
            }
            testLineAndSampleList.setTestSampleMatrixList(testSampleMatrixBOList);
            testLineWithSampleMatrixList.add(testLineAndSampleList);
        }
        return BaseResponse.newSuccessInstance(testLineWithSampleMatrixList);
    }

}
