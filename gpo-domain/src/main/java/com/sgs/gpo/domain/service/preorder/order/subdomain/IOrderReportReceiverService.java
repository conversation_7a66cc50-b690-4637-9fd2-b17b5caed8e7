package com.sgs.gpo.domain.service.preorder.order.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderReportReceiverPO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;

import java.util.List;

public interface IOrderReportReceiverService extends IService<OrderReportReceiverPO> {

    BaseResponse<List<OrderReportReceiverPO>> select(OrderIdReq orderIdReq);

}
