package com.sgs.gpo.domain.service.otsnotes.attachment.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.attachment.ObjectAttachmentMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.attachment.ObjectAttachmentPO;
import com.sgs.gpo.domain.service.otsnotes.attachment.IObjectAttachmentService;
import com.sgs.gpo.facade.model.attachment.ObjectAttachmentQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class ObjectAttachmentServiceImpl extends ServiceImpl<ObjectAttachmentMapper, ObjectAttachmentPO> implements IObjectAttachmentService {
    @Override
    public List<ObjectAttachmentPO> query(ObjectAttachmentQueryReq queryReq) {
        if(Func.isEmpty(queryReq) || Func.isAllEmpty(queryReq.getObjectNo(),queryReq.getObjectIdList(),queryReq.getObjectType(),queryReq.getObjectDataList())){
            return null;
        }
        LambdaQueryWrapper<ObjectAttachmentPO> wrapper = Wrappers.lambdaQuery();
        if(Func.isNotEmpty(queryReq.getObjectNo())){
            wrapper.eq(ObjectAttachmentPO::getObjectNo,queryReq.getObjectNo());
        }
        if(Func.isNotEmpty(queryReq.getFileType())){
            wrapper.eq(ObjectAttachmentPO::getFileType,queryReq.getFileType());
        }
        if(Func.isNotEmpty(queryReq.getObjectType())){
            wrapper.eq(ObjectAttachmentPO::getObjectType,queryReq.getObjectType());
        }
        if(Func.isNotEmpty(queryReq.getObjectIdList())){
            wrapper.in(ObjectAttachmentPO::getObjectId,queryReq.getObjectIdList());
        }
        if(Func.isNotEmpty(queryReq.getObjectDataList())){
            wrapper.in(ObjectAttachmentPO::getObjectData,queryReq.getObjectDataList());
        }
        return this.baseMapper.selectList(wrapper);
    }
}
