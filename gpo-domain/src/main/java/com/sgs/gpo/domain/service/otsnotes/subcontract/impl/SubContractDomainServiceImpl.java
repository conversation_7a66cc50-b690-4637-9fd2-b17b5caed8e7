package com.sgs.gpo.domain.service.otsnotes.subcontract.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractBO;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractIdBO;
import com.sgs.framework.open.platform.base.service.impl.AbstractDomainService;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.jackson.JsonUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.domain.service.otsnotes.subcontract.ISubContractDomainService;
import com.sgs.gpo.domain.service.otsnotes.subcontract.command.SubContractPageQueryCMD;
import com.sgs.gpo.domain.service.otsnotes.subcontract.command.SubContractSendEmailCMD;
import com.sgs.gpo.domain.service.otsnotes.subcontract.command.SubcontractQueryCMD;
import com.sgs.gpo.domain.service.otsnotes.subcontract.command.SubcontractReportMatrixCMD;
import com.sgs.gpo.domain.service.otsnotes.subcontract.context.SubContractPageContext;
import com.sgs.gpo.domain.service.otsnotes.subcontract.context.SubcontractContext;
import com.sgs.gpo.domain.service.otsnotes.subcontract.context.SubcontractReportMatrixContext;
import com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.ISubcontractService;
import com.sgs.gpo.facade.model.otsnotes.subcontract.req.SubContractPageReq;
import com.sgs.gpo.facade.model.otsnotes.subcontract.req.SubcontractBatchToStarLimsReq;
import com.sgs.gpo.facade.model.otsnotes.subcontract.req.SubcontractToStarLimsReq;
import com.sgs.gpo.facade.model.otsnotes.subcontract.rsp.SubcontractToStarLimsRsp;
import com.sgs.gpo.facade.model.otsnotes.subcontract.vo.SubContractPageVO;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractQueryReq;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractReportMatrixQueryReq;
import com.sgs.gpo.integration.sodanotes.SodaNotesClient;
import com.sgs.gpo.integration.starLims.StarLimsClient;
import com.sgs.gpo.integration.starLims.rsp.Folder;
import com.sgs.gpo.integration.starLims.rsp.Sample;
import com.sgs.gpo.integration.starLims.rsp.Site;
import com.sgs.gpo.integration.starLims.rsp.TestLine;
import com.sgs.grus.async.AsyncCall;
import com.sgs.grus.async.AsyncResult;
import com.sgs.grus.async.AsyncUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @title: SubContractBizServiceImpl
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/7 17:52
 */
@Service
@Slf4j
public class SubContractDomainServiceImpl
        extends AbstractDomainService<SubcontractBO, SubcontractIdBO,SubcontractQueryReq, ISubcontractService>
        implements ISubContractDomainService {

    @Autowired
    StarLimsClient starLimsClient;
    @Autowired
    SodaNotesClient sodaNotesClient;

    private static final Integer MAX_TO_STARLIMS_COUNT = 30;

    @Override
    public BaseResponse<IPage<SubContractPageVO>> querySubContractPage(SubContractPageReq subContractPageReq, Integer page, Integer rows) {
        SubContractPageContext subContractPageContext = new SubContractPageContext<>();
        subContractPageContext.setParam(subContractPageReq);
        subContractPageContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        subContractPageContext.setToken(SecurityContextHolder.getSgsToken());
        subContractPageContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        subContractPageContext.setPage(page);
        subContractPageContext.setRows(rows);
        return BaseExecutor.start(SubContractPageQueryCMD.class,subContractPageContext);
    }

    @Override
    public BaseResponse getReportMatrixBySubcontract(SubcontractReportMatrixQueryReq subcontractReportMatrixQueryReq) {
        SubcontractReportMatrixContext context = new SubcontractReportMatrixContext();
        context.setParam(subcontractReportMatrixQueryReq);
        context.setLab(SystemContextHolder.getLab());
        context.setUserInfo(SecurityContextHolder.getUserInfo());
        context.setToken(SecurityContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(SubcontractReportMatrixCMD.class,context);
    }

    @Override
    public BaseResponse getSendEmailModel(SubContractPageReq subContractPageReq) {
        SubcontractContext context = new SubcontractContext(subContractPageReq);
        context.setParam(subContractPageReq);
        context.setUserInfo(SecurityContextHolder.getUserInfo());
        context.setToken(SecurityContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(SubContractSendEmailCMD.class,context);
    }


    @Override
    public BaseResponse<List<SubcontractBO>> queryBO(SubcontractQueryReq request) {
        // 入参不允许为空
        Assert.isTrue(Func.isNotEmpty(request),"common.param.miss",new Object[]{Constants.TERM.REQUEST});
        // 参数必须是SubcontractQueryReq
        SubcontractContext<SubcontractQueryReq> subcontractContext = new SubcontractContext<>(request);
        return BaseExecutor.start(SubcontractQueryCMD.class, subcontractContext);
    }


    @Override
    public BaseResponse getStarLimsFolders(SubContractPageReq subContractPageReq) {
            // 入参不允许为空
        Assert.isTrue(Func.isNotEmpty(subContractPageReq),"common.param.miss",new Object[]{Constants.TERM.REQUEST});

        List<Map> params=new ArrayList<>();
//        获取subcontractNo、folderNo
        subContractPageReq.setPageType("getStarLimsFolders");
        BaseResponse<IPage<SubContractPageVO>> sub=querySubContractPage(subContractPageReq,1,10);
        if(sub.getStatus()!=200){
            return BaseResponse.newFailInstance(sub.getMessage());
        }
        BaseResponse starLimsFoldersRsp = null;
        List<SubContractPageVO> subList=sub.getData().getRecords();
        if (Func.isNotEmpty(subList)) {
//            若增加订单调用此处应遍历subList
            SubContractPageVO subContract=subList.get(0);
            if(Func.isNotEmpty(subContract.getReferenceNo())) {
                Map<String,Object> param=new HashMap();
                param.put("folderNo",subContract.getReferenceNo());
                param.put("externalId",subContract.getSubContractNo());
                params.add(param);
            }
            log.info("getStarLimsFolders param:{}", params);
            starLimsFoldersRsp = starLimsClient.getStarLimsFolders(params);
            log.info("getStarLimsFolders result:{}", starLimsFoldersRsp);

            //        starlims默认返回sample维度信息，手动需转换为Testline维度
            //         默认 folderlist - sampleList - testLineList
            //         转换 folderlist - testLineList - siteList - sampleList
            if (starLimsFoldersRsp.getStatus() == 200) {
                List<Folder> folders = JsonUtil.parse((String) starLimsFoldersRsp.getData(), new TypeReference<List<Folder>>() {
                });
                Set<TestLine> tlList = new LinkedHashSet<>();
                Site site = new Site();
                //            Folder folder=new Folder();
                folders.stream().forEach(
                        item -> {
                            Folder folder = item;
        //                   遍历Sample获取所有TestLine到tlList
                            item.getSampleList().stream().forEach(
                                    smp -> {
                                        tlList.addAll(smp.getTestLineList());
                                    });
        //                  遍历tlList将相同sample放到tl下
                            tlList.stream().forEach(
                                    tl -> {
                                        getTestLineBySample(item.getSampleList(), tl);
                                    }
                            );
        //                  遍历tlList - tl 组装site --交由前端处理
                            item.setTestLineList(new ArrayList<>(tlList));
                            //                        item.setSampleList(null);
                        }
                );
                starLimsFoldersRsp.setData(folders);
            }
        }else{
            return BaseResponse.newFailInstance("未查询到 SubContract");
        }
        return starLimsFoldersRsp;
    }

    @Override
    public BaseResponse checkStarLimsFolders(SubContractPageReq subContractPageReq) {
        BaseResponse<IPage<SubContractPageVO>> sub=querySubContractPage(subContractPageReq,1,10);
        if(sub.getStatus()!=200){
            return BaseResponse.newFailInstance(sub.getMessage());
        }
        List <SubContractPageVO> list=sub.getData().getRecords();
        return BaseResponse.newSuccessInstance(list);
    }

    @Override
    public BaseResponse batchToStarLims(SubcontractBatchToStarLimsReq subcontractBatchToStarLimsReq) {
        // 入参校验
        if(Func.isEmpty(subcontractBatchToStarLimsReq) || Func.isEmpty(subcontractBatchToStarLimsReq.getToStarLimsList())){
            return BaseResponse.newFailInstance("common.param.miss", new Object[]{"ToStarLims"});
        }
        List<SubcontractToStarLimsReq> toStarLimsList = subcontractBatchToStarLimsReq.getToStarLimsList();
        if(toStarLimsList.size() > MAX_TO_STARLIMS_COUNT){
            return BaseResponse.newFailInstance("subcontract.to.starlims.over.maxSize", new Object[]{MAX_TO_STARLIMS_COUNT});
        }
        // 执行生成报告
        AsyncCall asyncCall = new AsyncCall();
        Lab lab = SystemContextHolder.getLab();
        String productLineCode = SystemContextHolder.getBuCode();
        String token = SystemContextHolder.getSgsToken();
        toStarLimsList.stream().forEach(subcontract->{
            String subcontractId = subcontract.getSubContractId();
            String orderNo = subcontract.getOrderNo();
            subcontract.setLabCode(lab.getLabCode());
            subcontract.setToken(token);
            subcontract.setProductLineCode(productLineCode);
            if(Func.isEmpty(subcontractId) || Func.isEmpty(orderNo)){
                return;
            }
            asyncCall.put(subcontract.getSubContractNo(),()-> sodaNotesClient.toStarLims(subcontract));
        });
        List<AsyncResult> asyncResults = AsyncUtils.awaitResult(asyncCall);
        List<SubcontractToStarLimsRsp> toStarLimsRspList = new ArrayList<>();
        for (AsyncResult asyncResult : asyncResults) {
            SubcontractToStarLimsRsp toStarLimsRsp = new SubcontractToStarLimsRsp();
            toStarLimsRsp.setSubcontractNo(asyncResult.getTaskKey());
            toStarLimsRsp.setResult("To StarLims 成功");
            BaseResponse result = asyncResult.getData();
            if(Func.isEmpty(result)){
                toStarLimsRsp.setResult("To StarLims 失败:" + Func.toStr(asyncResult.getResultMsg()));
                toStarLimsRspList.add(toStarLimsRsp);
            }else if(result.isFail()){
                //转成JSON Array
                try {
                    Object data = result.getData();
                    JSONArray jsonArray = null;

                    if (data instanceof JSONArray) {
                        jsonArray = (JSONArray) data;
                    } else if (data instanceof String) {
                        // 如果是JSON字符串，尝试解析
                        jsonArray = JSONArray.parseArray((String) data);
                    } else if (data instanceof List) {
                        // 如果是List，转换为JSONArray
                        jsonArray = new JSONArray((List<Object>) data);
                    } else if (data != null) {
                        // 如果是其他对象，先转为JSON字符串再转为JSONArray
                        String jsonString = JsonUtil.toJson(data);
                        jsonArray = JSONArray.parseArray(jsonString);
                    }
                    // 循环处理JSONArray
                    if (jsonArray != null && !jsonArray.isEmpty()) {
                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject jsonObject = jsonArray.getJSONObject(i);
                            SubcontractToStarLimsRsp toStarLimsRspNew = new SubcontractToStarLimsRsp();
                            toStarLimsRspNew.setPpName(jsonObject.get("ppName").toString());
                            toStarLimsRspNew.setResult(jsonObject.get("errorMsg").toString());
                            toStarLimsRspNew.setSubcontractNo(asyncResult.getTaskKey());
                            toStarLimsRspNew.setTestLineId(jsonObject.get("testLineId").toString());
                            toStarLimsRspNew.setPpNo(jsonObject.get("errorObject").toString());
                            toStarLimsRspList.add(toStarLimsRspNew);
                        }
                    }else{
                        toStarLimsRsp.setResult("To StarLims 失败,错误原因: " + Func.toStr(result.getMessage()));
                        toStarLimsRspList.add(toStarLimsRsp);
                    }
                } catch (Exception e) {
                    log.error("解析JSONArray失败", e);
                    toStarLimsRsp.setResult("To StarLims 失败,错误原因: " + Func.toStr(result.getMessage()));
                    toStarLimsRspList.add(toStarLimsRsp);
                }
            }else{//成功
                toStarLimsRspList.add(toStarLimsRsp);
            }

        }
        return BaseResponse.newSuccessInstance(toStarLimsRspList);
    }

    private TestLine getTestLineBySample(List<Sample> smpList,TestLine testLine){
//        List<TestLine> tlList=new ArrayList<>();
        Set <Sample> sampleList=new LinkedHashSet<>();
        Sample sample=new Sample();
        smpList.stream().forEach(
            smp -> {
                smp.getTestLineList().stream().forEach(
                    tl ->{
//                        if(testLine.getTestLineId().equals(tl.getTestLineId())){
                        if(Func.equals(tl.getTestLineId(), testLine.getTestLineId())
                                &&Func.equals(tl.getCondition(), testLine.getCondition())
                                &&Func.equals(tl.getPpNo(), testLine.getPpNo())){
                            //sample=smp;//-------------------------------------
                            sample.setSampleId(smp.getSampleId());
                            sample.setSampleType(smp.getSampleType());
                            sample.setMaterialNo(smp.getMaterialNo());
                            sample.setSgsDescription(smp.getSgsDescription());
                            sample.setStatus(smp.getStatus());
                            sampleList.add(sample);
                        }
                    }
                );
            });
        testLine.setSampleList(new ArrayList<>(sampleList));
        return testLine;
    }
}
