package com.sgs.gpo.domain.service.preorder.order.subdomain.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.BossOrderMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.BossOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.payment.BossOrderInvoiceDTO;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IBossOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
public class BossOrderServiceImpl extends ServiceImpl<BossOrderMapper, BossOrderPO> implements IBossOrderService {

    @Override
    public Boolean getBossOrderByTrueOrderNo(String trueOrderNo){
        return baseMapper.getBossOrderByTrueOrderNo(trueOrderNo);
    }

    @Override
    public GeneralOrderPO getOrderByBossOrderNo(String bossOrderNo) {
        return baseMapper.getOrderByBossOrderNo(bossOrderNo);
    }

    @Override
    public List<BossOrderInvoiceDTO> getBossOrderNoByOrderNo(String orderNo) {
        if(Func.isEmpty(orderNo)){
            return null;
        }
        return baseMapper.getBossOrderNoByOrderNo(orderNo);
    }

}
