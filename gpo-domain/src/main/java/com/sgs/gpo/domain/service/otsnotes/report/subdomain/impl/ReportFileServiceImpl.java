package com.sgs.gpo.domain.service.otsnotes.report.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.report.ReportFileMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportFilePO;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportFileService;
import com.sgs.gpo.facade.model.report.req.ReportFileQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class ReportFileServiceImpl extends ServiceImpl<ReportFileMapper,ReportFilePO> implements IReportFileService {


    @Override
    public BaseResponse<List<ReportFilePO>> query(ReportFileQueryReq reportFileQueryReq) {
        if(Func.isEmpty(reportFileQueryReq) || (Func.isEmpty(reportFileQueryReq.getReportIdList())
                && Func.isEmpty(reportFileQueryReq.getReportNoList()))){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        LambdaQueryWrapper<ReportFilePO> wrapper = Wrappers.lambdaQuery();
        if(Func.isNotEmpty(reportFileQueryReq.getReportIdList())){
            wrapper.in(ReportFilePO::getReportID,reportFileQueryReq.getReportIdList());
        }
        if(Func.isNotEmpty(reportFileQueryReq.getReportNoList())){
            wrapper.in(ReportFilePO::getReportNo,reportFileQueryReq.getReportNoList());
        }
        if(Func.isNotEmpty(reportFileQueryReq.getReportFileType())){
            wrapper.eq(ReportFilePO::getReportFileType,reportFileQueryReq.getReportFileType());
        }
        if(Func.isNotEmpty(reportFileQueryReq.getReportFileStatus())){
            wrapper.in(ReportFilePO::getStatus,reportFileQueryReq.getReportFileStatus());
        }
        return BaseResponse.newSuccessInstance(this.list(wrapper));

    }


}
