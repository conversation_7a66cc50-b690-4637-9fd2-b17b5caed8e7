package com.sgs.gpo.domain.service.preorder.order.command;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.preorder.order.context.OrderTrfContext;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.ReportStatus;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.TestSampleReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.status.GpnStatusPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.productinstance.ProductInstancePO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.status.GpoStatusPO;
import com.sgs.gpo.domain.service.otsnotes.status.IGpnStatusService;
import com.sgs.gpo.domain.service.preorder.ordertrfrel.IOrderTrfRelationshipService;
import com.sgs.gpo.domain.service.preorder.productinstance.IProductInstanceService;
import com.sgs.gpo.domain.service.preorder.status.IGpoStatusService;
import com.sgs.gpo.facade.model.report.req.QuerySampleReportReq;
import com.sgs.gpo.facade.model.preorder.order.bo.OrderTrfBO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderEditableReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderTrfReq;
import com.sgs.gpo.facade.model.preorder.order.rsp.OrderEditableRsp;
import com.sgs.gpo.facade.model.preorder.order.rsp.OrderEditableRsp.ProductSample;
import com.sgs.gpo.facade.model.preorder.productsample.req.ProductSampleQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportIdReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: OrderEditableCMD
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/26 14:42
 */
@Service
@Slf4j
@Scope(value = "prototype")
public class OrderEditableCMD extends BaseCommand<OrderTrfContext> {

    @Autowired
    private IOrderTrfRelationshipService orderTrfRelationshipService;
    @Autowired
    private IProductInstanceService productInstanceService;
    @Autowired
    private IReportService reportService;
    @Autowired
    private IGpnStatusService gpnStatusService;
    @Autowired
    private IGpoStatusService gpoStatusService;

    @Override
    public BaseResponse validateParam(OrderTrfContext context) {
        OrderEditableReq orderEditableReq = context.getParam();
        if (Func.isEmpty(orderEditableReq) || (Func.isEmpty(orderEditableReq.getGpoOrderNo()) && Func.isEmpty(orderEditableReq.getSubcontractNo()) && Func.isEmpty(orderEditableReq.getSubcontractOrderNo()))) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        if (Func.isEmpty(orderEditableReq.getLabCode())) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"labCode"});
        }
        if (Func.isEmpty(orderEditableReq.getGpoOrderNo())) {
            if (Func.isEmpty(orderEditableReq.getSubcontractNo())) {
                return BaseResponse.newFailInstance("common.miss", new Object[]{"subcontractNo"});
            }
            if (Func.isEmpty(orderEditableReq.getSubcontractOrderNo())) {
                return BaseResponse.newFailInstance("common.miss", new Object[]{"subcontractOrderNo"});
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(OrderTrfContext context) {
        return this.buildDomain(context);
    }

    @Override
    public BaseResponse<List<OrderEditableRsp>> buildDomain(OrderTrfContext context) {
        List<OrderEditableRsp> orderEditableRspList = new ArrayList<>();
        List<OrderTrfBO> orderTrfBOList = context.getOrderTrfBOList();
        List<ReportPO> reportPOList = context.getReportPOList();
        List<GpnStatusPO> gpnStatusPOList = context.getGpnStatusPOList();
        List<ProductInstancePO> productInstancePOList = context.getProductInstancePOList();
        List<TestSampleReportPO> testSampleReportPOList = context.getTestSampleReportPOList();
        if (Func.isNotEmpty(orderTrfBOList)) {
            for (OrderTrfBO orderTrfBO : orderTrfBOList) {
                OrderEditableRsp orderEditableRsp = new OrderEditableRsp();
                orderEditableRsp.setGpoOrderNo(orderTrfBO.getOrderNo());
                orderEditableRsp.setExternalOrderNo(orderTrfBO.getExternalOrderNo());
                orderEditableRsp.setExternalSubOrderNo(orderTrfBO.getRefNo());
                ProductSample productSample = new ProductSample();
                productSample.setProductInfo(false);
                orderEditableRsp.setReportInfo(false);
//                Order下有效报告都是Status = [ New || (Pending 前是New) ]时,（reportInfo，productinfo） Edit = True
                if (Func.isEmpty(reportPOList)) {
                    productSample.setProductInfo(true);
                    orderEditableRsp.setReportInfo(true);
                } else {
                    List<ReportPO> machReportPOList = reportPOList.stream().filter(item -> Func.equalsSafe(item.getOrderNo(), orderTrfBO.getOrderNo())).collect(Collectors.toList());
                    if (Func.isEmpty(machReportPOList)) {
                        productSample.setProductInfo(true);
                        orderEditableRsp.setReportInfo(true);
                    } else {
                        //是否存在非New、Pending的报告
                        long count = machReportPOList.parallelStream().filter(item -> !ReportStatus.check(item.getReportStatus(), ReportStatus.New, ReportStatus.Pending)).count();
                        if (count == 0) {
                            if (Func.isEmpty(gpnStatusPOList)) {
                                productSample.setProductInfo(true);
                                orderEditableRsp.setReportInfo(true);
                            } else {
                                //报告Pending前是否是new状态
                                List<ReportPO> pendingReportList = machReportPOList.parallelStream().filter(item -> ReportStatus.check(item.getReportStatus(), ReportStatus.Pending)).collect(Collectors.toList());
                                if(Func.isEmpty(pendingReportList)){
                                    productSample.setProductInfo(true);
                                    orderEditableRsp.setReportInfo(true);
                                }else{
                                    Set<String> pendingReportNoList = pendingReportList.parallelStream().map(ReportPO::getReportNo).collect(Collectors.toSet());
                                    Map<String, GpnStatusPO> latestReportStatusMap = gpnStatusPOList.stream().filter(item -> Func.isNotEmpty(item.getCreatedDate()) && pendingReportNoList.contains(item.getObjectNo())).collect(
                                            Collectors.groupingBy(GpnStatusPO::getObjectNo,
                                                    Collectors.collectingAndThen(Collectors.reducing((c1, c2) -> DateUtil.compare(c1.getCreatedDate(), c2.getCreatedDate()) > 0 ? c1 : c2),
                                                            Optional::get)));
                                    List<GpnStatusPO> latestReportStatusList = new ArrayList<>(latestReportStatusMap.values());
                                    long beforePendingNotNewCount = latestReportStatusList.stream().filter(item -> !ReportStatus.check(item.getOldStatus(), ReportStatus.New)).count();
                                    if(beforePendingNotNewCount==0){
                                        productSample.setProductInfo(true);
                                        orderEditableRsp.setReportInfo(true);
                                    }
                                }
                            }
                        }
                    }
                }

                if (Func.isNotEmpty(productInstancePOList)) {
                    List<ProductSample.Sample> sampleList = new ArrayList<>();
                    for (ProductInstancePO productInstancePO : productInstancePOList) {
                        ProductSample.Sample sample = new ProductSample.Sample();
                        sample.setExternalSampleId(productInstancePO.getExternalSampleId());
                        sample.setGpoSampleNo(productInstancePO.getSampleID());
//                        Sample关联的有效报告都是Status = [ New || (Pending 前是New) ]时
                        sample.setEdit(false);
                        if (Func.isNotEmpty(testSampleReportPOList)) {
                            List<TestSampleReportPO> sampleReportPOList = testSampleReportPOList.stream().filter(item -> Func.equalsSafe(item.getTestSampleId(), productInstancePO.getRefSampleID())).collect(Collectors.toList());
                            if (Func.isNotEmpty(sampleReportPOList)) {
                                //是否存在非New、Pending的报告
                                long count = sampleReportPOList.parallelStream().filter(item -> !ReportStatus.check(item.getReportStatus(), ReportStatus.New, ReportStatus.Pending)).count();
                                if (count == 0) {
                                    if (Func.isEmpty(gpnStatusPOList)) {
                                        sample.setEdit(true);
                                    } else {
                                        //报告Pending前是否是new状态
                                        List<TestSampleReportPO> pendingReportList = sampleReportPOList.parallelStream().filter(item -> ReportStatus.check(item.getReportStatus(), ReportStatus.Pending)).collect(Collectors.toList());
                                        if(Func.isEmpty(pendingReportList)){
                                            sample.setEdit(true);
                                        }else{
                                            Set<String> pendingReportNoList = pendingReportList.parallelStream().map(TestSampleReportPO::getReportNo).collect(Collectors.toSet());
                                            Map<String, GpnStatusPO> latestReportStatusMap = gpnStatusPOList.stream().filter(item -> Func.isNotEmpty(item.getCreatedDate()) && pendingReportNoList.contains(item.getObjectNo())).collect(
                                                    Collectors.groupingBy(GpnStatusPO::getObjectNo,
                                                            Collectors.collectingAndThen(Collectors.reducing((c1, c2) -> DateUtil.compare(c1.getCreatedDate(), c2.getCreatedDate()) > 0 ? c1 : c2),
                                                                    Optional::get)));
                                            List<GpnStatusPO> latestReportStatusList = new ArrayList<>(latestReportStatusMap.values());
                                            long beforePendingNotNewCount = latestReportStatusList.stream().filter(item -> !ReportStatus.check(item.getOldStatus(), ReportStatus.New)).count();
                                            if(beforePendingNotNewCount==0){
                                                sample.setEdit(true);
                                            }
                                        }
                                    }
                                }
                            }else{
                                sample.setEdit(true);
                            }
                        }else{
                            sample.setEdit(true);
                        }

                        sampleList.add(sample);
                    }
                    productSample.setSampleList(sampleList);
                }
                orderEditableRsp.setProductSample(productSample);
                orderEditableRspList.add(orderEditableRsp);
            }
        }
        return BaseResponse.newSuccessInstance(orderEditableRspList);
    }

    @Override
    public BaseResponse before(OrderTrfContext context) {
        List<OrderTrfBO> orderTrfBOList = Lists.newArrayList();
        List<ProductInstancePO> productInstancePOList;
        List<TestSampleReportPO> testSampleReportPOList;
        List<ReportPO> reportPOList;
        List<GpoStatusPO> gpoStatusPOList;
        List<GpnStatusPO> gpnStatusPOList;
        OrderEditableReq orderEditableReq = context.getParam();
        //入参没有ProductLineCode,此处截取LabCode使用
        ProductLineContextHolder.setProductLineCode(orderEditableReq.getLabCode().split(" ")[1]);
        OrderTrfReq orderTrfReq = new OrderTrfReq();
        orderTrfReq.setLabCode(orderEditableReq.getLabCode());
        orderTrfReq.setRefSystemIdList(Sets.newHashSet(RefSystemIdEnum.RSTS.getRefSystemId()));
        if (Func.isNotEmpty(orderEditableReq.getGpoOrderNo())) {
            orderTrfReq.setOrderNoList(Sets.newHashSet(orderEditableReq.getGpoOrderNo()));
        } else {
            //根据externalOrderNo和externalOrderNo查询
            orderTrfReq.setExternalOrderNo(orderEditableReq.getSubcontractOrderNo());
            orderTrfReq.setRefNo(orderEditableReq.getSubcontractNo());
        }
        BaseResponse<List<OrderTrfBO>> orderTrfBoRes = orderTrfRelationshipService.queryOrderTrf(orderTrfReq);
        if (orderTrfBoRes.isSuccess() && Func.isNotEmpty(orderTrfBoRes.getData())) {
            orderTrfBOList = orderTrfBoRes.getData();
            context.setOrderTrfBOList(orderTrfBOList);

        }
        if (Func.isNotEmpty(orderTrfBOList)) {
            Set<String> orderIdList = orderTrfBOList.parallelStream().map(OrderTrfBO::getId).filter(Func::isNotEmpty).collect(Collectors.toSet());
            Set<String> orderNoList = orderTrfBOList.parallelStream().map(OrderTrfBO::getOrderNo).filter(Func::isNotEmpty).collect(Collectors.toSet());
            //查询ProductInstance
            ProductSampleQueryReq productSampleQueryReq = new ProductSampleQueryReq();
            productSampleQueryReq.setOrderIdList(orderIdList);
            BaseResponse<List<ProductInstancePO>> orderProductSampleRes = productInstanceService.queryOrderProductSample(productSampleQueryReq);
            if (orderProductSampleRes.isSuccess()) {
                productInstancePOList = orderProductSampleRes.getData().stream().filter(item -> Func.isNotEmpty(item.getHeaderID()) && (Func.isEmpty(item.getCancelFlag()) || NumberUtil.equals(item.getCancelFlag().intValue(), 0))).collect(Collectors.toList());
                productInstancePOList = productInstancePOList.stream()
                        .collect(Collectors.toMap(ProductInstancePO::getSampleID, p -> p, (p1, p2) -> p1))
                        .values()
                        .stream()
                        .collect(Collectors.toList());
                context.setProductInstancePOList(productInstancePOList);
                Set<String> refSampleIdList = productInstancePOList.parallelStream().map(ProductInstancePO::getRefSampleID).collect(Collectors.toSet());
                //查询Sample关联的Report
                QuerySampleReportReq querySampleReportReq = new QuerySampleReportReq();
                querySampleReportReq.setTestSampleIdList(refSampleIdList);
                BaseResponse<List<TestSampleReportPO>> testSampleReportRes = reportService.queryReportByTestSample(querySampleReportReq);
                if (testSampleReportRes.isSuccess()) {
                    testSampleReportPOList = testSampleReportRes.getData();
                    if (Func.isNotEmpty(testSampleReportPOList)) {
                        //只过滤有效的Report
                        testSampleReportPOList = testSampleReportPOList.stream().filter(item -> !ReportStatus.checkCategory(item.getReportStatus(), Constants.OBJECT.REPORT.STATUS_CATEGORY.INACTIVE)).collect(Collectors.toList());
                    }
                    context.setTestSampleReportPOList(testSampleReportPOList);
                }
            }
            //查询Order关联的Report
            OrderIdReq orderNosReq = new OrderIdReq();
            orderNosReq.setOrderNoList(orderNoList);
            BaseResponse<List<ReportPO>> reportListRsp = reportService.queryReportByOrderNo(orderNosReq);
            if (reportListRsp.isSuccess()) {
                reportPOList = reportListRsp.getData();
                if (Func.isNotEmpty(reportPOList)) {
                    //只过滤有效的Report
                    reportPOList = reportPOList.stream().filter(item->!ReportStatus.checkCategory(item.getReportStatus(), Constants.OBJECT.REPORT.STATUS_CATEGORY.INACTIVE)).collect(Collectors.toList());
                    context.setReportPOList(reportPOList);
                    Set<String> reportNoList = reportPOList.parallelStream().map(ReportPO::getReportNo).collect(Collectors.toSet());
                    //查询Report的Status变化
                    ReportIdReq reportIdReq = new ReportIdReq();
                    reportIdReq.setReportNoList(reportNoList);
                    BaseResponse<List<GpnStatusPO>> gpnStatusRes = gpnStatusService.queryReportStatus(reportIdReq);
                    if (gpnStatusRes.isSuccess()) {
                        gpnStatusPOList = gpnStatusRes.getData();
                        if(Func.isNotEmpty(gpnStatusPOList)){
                            gpnStatusPOList = gpnStatusPOList.stream().filter(item->Func.isNotEmpty(item.getOldStatus())).sorted(Comparator.comparing(GpnStatusPO::getCreatedDate).reversed()).collect(Collectors.toList());
                            context.setGpnStatusPOList(gpnStatusPOList);
                        }
                    }
                }
            }
            //查询Order的Status变化
            OrderIdReq orderIdReq = new OrderIdReq();
            orderIdReq.setOrderNoList(orderNoList);
            BaseResponse<List<GpoStatusPO>> gpoStatusRes = gpoStatusService.queryOrderStatusByOrderNo(orderIdReq);
            if (gpoStatusRes.isSuccess()) {
                gpoStatusPOList = gpoStatusRes.getData();
                context.setGpoStatusPOList(gpoStatusPOList);
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }
}
