package com.sgs.gpo.domain.service.preorder.productinstance;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.productinstance.ProductInstancePO;
import com.sgs.gpo.facade.model.preorder.productsample.req.ProductSampleDeleteReq;
import com.sgs.gpo.facade.model.preorder.productsample.req.ProductSampleQueryReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/4 08:59
 */
public interface IProductInstanceService extends IService<ProductInstancePO> {
    /**
     * 查询Product Sample相关信息
     * @param productSampleQueryReq
     * @return
     */
    BaseResponse<List<ProductInstancePO>> queryOrderProductSample(ProductSampleQueryReq productSampleQueryReq);
    /**
     * 基于订单ID，语言删除对应的DFF数据
     */
    Integer delete(ProductSampleDeleteReq deleteReq);
}
