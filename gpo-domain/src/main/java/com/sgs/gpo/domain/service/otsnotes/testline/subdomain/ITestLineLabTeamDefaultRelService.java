package com.sgs.gpo.domain.service.otsnotes.testline.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testline.TestLineLabTeamDefaultRelationshipPO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineLabTeamSearchReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/30 10:56
 */
public interface ITestLineLabTeamDefaultRelService extends IService<TestLineLabTeamDefaultRelationshipPO> {

    BaseResponse<List<TestLineLabTeamDefaultRelationshipPO>> select(TestLineLabTeamSearchReq testLineLabTeamReq);
}
