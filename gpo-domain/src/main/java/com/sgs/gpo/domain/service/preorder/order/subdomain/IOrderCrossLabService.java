package com.sgs.gpo.domain.service.preorder.order.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderCrossLabPO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;

import java.util.List;

public interface IOrderCrossLabService extends IService<OrderCrossLabPO> {

    BaseResponse<List<OrderCrossLabPO>> select(OrderIdReq orderIdReq);
}
