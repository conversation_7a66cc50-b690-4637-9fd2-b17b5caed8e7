package com.sgs.gpo.domain.service.preorder.order.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.SLOrderMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.SLOrderPO;
import com.sgs.gpo.domain.service.preorder.order.subdomain.ISLOrderService;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @title: SlOrderServiceImpl
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/10 17:02
 */
@Service
public class SLOrderServiceImpl extends ServiceImpl<SLOrderMapper, SLOrderPO> implements ISLOrderService {
    @Override
    public BaseResponse<List<SLOrderPO>> select(OrderIdReq orderIdReq) {
        if (Func.isEmpty(orderIdReq) || (Func.isEmpty(orderIdReq.getOrderIdList()) && Func.isEmpty(orderIdReq.getOrderNoList()))) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"OrderId"});
        }
        return BaseResponse.newSuccessInstance(baseMapper.select(orderIdReq));
    }
}
