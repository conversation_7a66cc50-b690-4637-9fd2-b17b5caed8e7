package com.sgs.gpo.domain.service.otsnotes.job;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.test.execution.v2.job.JobBO;
import com.sgs.framework.model.test.execution.v2.job.JobIdBO;
import com.sgs.framework.open.platform.base.service.IDomainService;
import com.sgs.gpo.facade.model.job.req.*;
import com.sgs.gpo.facade.model.job.rsp.JobRsp;
import com.sgs.gpo.facade.model.otsnotes.job.req.JobLabInReq;
import com.sgs.gpo.facade.model.otsnotes.job.req.JobLabOutReq;
import com.sgs.gpo.facade.model.otsnotes.job.req.JobAssignOwnerReq;
import com.sgs.gpo.facade.model.otsnotes.job.rsp.JobLabInRsp;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/3 17:51
 */
public interface IJobDomainService extends IDomainService<JobBO, JobIdBO,JobQueryReq> {

    /**
     * 创建Job TODO 未来用标准结构做入参
     * @param jobCreateReq * 订单信息
     * @return
     */
    BaseResponse create(JobCreateReq jobCreateReq);

    /**
     * 更新Job TODO 未来用标准结构做入参
     * @param jobEditReq
     * @return
     */
    BaseResponse update(JobEditReq jobEditReq);

    /**
     * 查询Job详情 TODO 未来用标准结构做出参
     * @param jobIdReq
     * @return
     */
    @Deprecated
    BaseResponse<JobRsp> detail(JobIdReq jobIdReq);

    /**
     * 记录开始测试 (批量操作，自动忽略失败，并提醒失败部分的原因)
     * @param jobLabInReq
     * @return
     */
    BaseResponse<List<JobLabInRsp>> labIn(JobLabInReq jobLabInReq);

    /**
     * 记录完成测试
     * @param jobLabOutReq
     * @return
     */
    BaseResponse labOut(JobLabOutReq jobLabOutReq);

    /**
     * Assign Owner
     * @param jobAssignOwnerReq
     * @return
     */
    BaseResponse<Boolean> assignOwner(JobAssignOwnerReq jobAssignOwnerReq);

    BaseResponse<Long> queryJobOwnerEmptyCount(JobQueryReq jobQueryReq);

}
