package com.sgs.gpo.domain.service.preorder.payment.costlist.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.enums.OrderStatus;
import com.sgs.gpo.domain.service.preorder.payment.costlist.ICostListDomainService;
import com.sgs.gpo.domain.service.preorder.payment.costlist.command.CostListQueryCMD;
import com.sgs.gpo.facade.model.payment.costlist.bo.CostListBO;
import com.sgs.gpo.domain.service.preorder.payment.costlist.context.CostListContext;
import com.sgs.gpo.facade.model.payment.costlist.req.CostListQueryReq;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @title: CostListServiceImpl
 * @projectName gpo-micro-service
 * @date 2023/6/1515:30
 */
@Service
public class CostListDomainServiceImpl implements ICostListDomainService {
    @Override
    public BaseResponse<IPage<CostListBO>> page(CostListQueryReq costListQueryReq, Integer page, Integer rows) {
        costListQueryReq.setCancelStatus(OrderStatus.Cancelled.getStatus());
        if (Func.isNotEmpty(costListQueryReq.getOrderNoBatch())){
            String[] split = costListQueryReq.getOrderNoBatch().split("\n");
            List<String> list = new ArrayList<>();
            for (String item : split){
                list.add(item);
            }
            if (Func.isNotEmpty(list)){
                costListQueryReq.setBatchOrderList(list);
            }
        }

        CostListContext<CostListQueryReq> costListContext = new CostListContext();
        costListContext.setUserInfo(SecurityContextHolder.getUserInfo());
        costListContext.setToken(SecurityContextHolder.getSgsToken());
        costListContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        costListContext.setCostListPage(new Page<CostListBO>());
        costListContext.setParam(costListQueryReq);
        costListContext.setLab(SystemContextHolder.getLab());
        costListContext.setPage(page);
        costListContext.setRows(rows);
        return BaseExecutor.start(CostListQueryCMD.class,costListContext);
    }

}
