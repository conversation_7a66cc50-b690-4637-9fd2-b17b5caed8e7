package com.sgs.gpo.domain.service.preorder.order;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.facade.model.preorder.order.req.OrderAttachmentQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.UpdateAttachmentSeqReq;
import com.sgs.gpo.facade.model.scantool.rsp.ScanToolFile;
import java.util.List;


public interface IOrderAttachmentDomainService {

    /**
     * 查询Order Attachment附件列表
     * @param orderNo
     * @return
     */
    BaseResponse<List<ScanToolFile>> queryScanFiles(String orderNo);


    BaseResponse updateSequence(UpdateAttachmentSeqReq updateAttachmentSeqReq);

    BaseResponse list(OrderAttachmentQueryReq orderAttachmentQueryReq);

    BaseResponse listToStarlimsAttachments(String id);
}
