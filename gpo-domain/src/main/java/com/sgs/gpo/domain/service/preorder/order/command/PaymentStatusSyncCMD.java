package com.sgs.gpo.domain.service.preorder.order.command;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.enums.PaymentStatus;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.BizLogConstant;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.SLOrderPO;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.domain.service.preorder.order.context.OrderContext;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IGeneralOrderService;
import com.sgs.gpo.domain.service.preorder.order.subdomain.ISLOrderService;
import com.sgs.gpo.facade.model.preorder.order.req.CalcPaymentStatusReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderPaymentStatusSyncReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.info.BizLogInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@Scope(value = "prototype")
public class PaymentStatusSyncCMD extends BaseCommand<OrderContext<OrderPaymentStatusSyncReq>> {

    @Autowired
    IOrderDomainService orderDomainService;
    @Autowired
    IGeneralOrderService generalOrderService;
    @Autowired
    ISLOrderService slOrderService;
    @Autowired
    BizLogClient bizLogClient;

    @Override
    public BaseResponse validateParam(OrderContext<OrderPaymentStatusSyncReq> context) {
        OrderPaymentStatusSyncReq req = context.getParam();
        Assert.isTrue(Func.isNotEmpty(req),"common.param.miss",null);
        Assert.isTrue(Func.isNotEmpty(req.getOrderNoList()) || Func.isNotEmpty(req.getOrderIdList()),"common.param.miss",null);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(OrderContext<OrderPaymentStatusSyncReq> context) {
        OrderPaymentStatusSyncReq req = context.getParam();
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        if(Func.isNotEmpty(req.getOrderIdList())){
            orderQueryReq.setOrderIdList(req.getOrderIdList());
        }
        if(Func.isNotEmpty(req.getOrderNoList())){
            orderQueryReq.setOrderNoList(req.getOrderNoList());
        }
        orderQueryReq.setBaseQuery(true);
        List<OrderBO> orderBOList = orderDomainService.queryBO(orderQueryReq).getData();
        context.setOrderBOList(orderBOList);
        return super.before(context);
    }

    @Override
    public BaseResponse execute(OrderContext<OrderPaymentStatusSyncReq> context) {
        OrderPaymentStatusSyncReq req = context.getParam();
        List<OrderBO> orderBOList = context.getOrderBOList();
        if(Func.isNotEmpty(orderBOList)){
            orderBOList.stream().forEach(orderBO ->{
                Integer oldPaymentStatus = orderBO.getPayment().getPaymentStatus();
                CalcPaymentStatusReq calcPaymentStatusReq = new CalcPaymentStatusReq();
                calcPaymentStatusReq.setOrderNo(orderBO.getId().getOrderNo());
                calcPaymentStatusReq.setCaseType(orderBO.getHeader().getCaseType());
                calcPaymentStatusReq.setProductLineCode(context.getProductLineCode());
                BaseResponse<PaymentStatus> paymentRsp = orderDomainService.paymentStatusCalculate(calcPaymentStatusReq);
                if(paymentRsp.isSuccess()){
                    PaymentStatus paymentStatus = paymentRsp.getData();
                    //更新GeneralOrder
                    GeneralOrderPO orderPO = new GeneralOrderPO();
                    orderPO.setPayStatus(paymentStatus.getType());
                    LambdaQueryWrapper <GeneralOrderPO> orderPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    orderPOLambdaQueryWrapper.eq(GeneralOrderPO::getId, orderBO.getId().getOrderId());
                    generalOrderService.update(orderPO,orderPOLambdaQueryWrapper);
                    //更新SLOrder
                    SLOrderPO slOrderPO = new SLOrderPO();
                    slOrderPO.setPaymentStatus(paymentStatus.getType());
                    LambdaQueryWrapper <SLOrderPO> slOrderPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    slOrderPOLambdaQueryWrapper.eq(SLOrderPO::getGeneralOrderID, orderBO.getId().getOrderId());
                    slOrderService.update(slOrderPO,slOrderPOLambdaQueryWrapper);
                    //记录BizLog
                    if (!NumberUtil.equals(oldPaymentStatus, paymentStatus.getType())) {
                        UserInfo userInfo = context.getUserInfo();
                        BizLogInfo bizLog = new BizLogInfo();
                        bizLog.setBu(Func.isNotEmpty(orderBO.getLab().getBuCode()) ? orderBO.getLab().getBuCode() : ProductLineContextHolder.getProductLineCode());
                        bizLog.setLab(orderBO.getLab().getLocationCode());
                        bizLog.setOpUser("System");
                        if(Func.isNotEmpty(userInfo)){
                            bizLog.setOpUser(userInfo.getRegionAccount());
                        }
                        bizLog.setBizId(orderBO.getId().getOrderNo());
                        bizLog.setOpType(req.getOpType());
                        if (Func.isNotEmpty(orderPO) && Func.isNotEmpty(orderPO.getPayStatus())) {
                            if (Func.isNotEmpty(oldPaymentStatus)) {
                                String oldPaymentCode = PaymentStatus.getType(oldPaymentStatus).getCode();
                                bizLog.setOriginalVal(oldPaymentCode);
                            }
                        }
                        if (Func.isNotEmpty(paymentStatus)) {
                            String newPaymentCode = PaymentStatus.getType(paymentStatus.getType()).getCode();
                            bizLog.setNewVal(newPaymentCode);
                        }
                        bizLog.setBizOpType(BizLogConstant.ORDER_OPERATION_HISTORY);
                        log.info("bizLog param: {}", JSON.toJSONString(bizLog));
                        bizLogClient.doSend(bizLog);
                    }
                }
            });
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse after(OrderContext<OrderPaymentStatusSyncReq> context) {
        OrderPaymentStatusSyncReq req = context.getParam();
        List<OrderBO> orderBOList = context.getOrderBOList();
        if(Func.isNotEmpty(orderBOList)){
            orderBOList.stream().forEach(orderBO ->{
                //EnableDelivery
                BaseResponse<Integer> enableDelivery = orderDomainService.buildEnableDelivery(orderBO.getId().getOrderNo());
                GeneralOrderPO orderPO = new GeneralOrderPO();
                orderPO.setEnableDelivery(enableDelivery.getData());
                LambdaQueryWrapper <GeneralOrderPO> orderPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
                orderPOLambdaQueryWrapper.eq(GeneralOrderPO::getId, orderBO.getId().getOrderId());
                if(enableDelivery.isSuccess() && Func.isNotEmpty(enableDelivery)) {
                    orderPO.setEnableDelivery(enableDelivery.getData());
                    generalOrderService.update(orderPO, orderPOLambdaQueryWrapper);
                }
            });
        }
        return super.after(context);
    }
}
