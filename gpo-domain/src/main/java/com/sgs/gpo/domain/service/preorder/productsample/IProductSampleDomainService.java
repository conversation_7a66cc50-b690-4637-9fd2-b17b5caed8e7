package com.sgs.gpo.domain.service.preorder.productsample;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.common.productsample.ProductSampleBO;
import com.sgs.gpo.facade.model.preorder.productsample.req.ProductSampleQueryReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/5 09:27
 */
public interface IProductSampleDomainService {

    /**
     * 查询订单的产品样品信息
     * @param productSampleQueryReq
     * @return
     */
    BaseResponse<List<ProductSampleBO>> queryOrderProductSample(ProductSampleQueryReq productSampleQueryReq);
    /**
     * 查询询价单的产品样品信息
     * @param productSampleQueryReq
     * @return
     */
    BaseResponse<List<ProductSampleBO>> queryEnquiryProductSample(ProductSampleQueryReq productSampleQueryReq);
}
