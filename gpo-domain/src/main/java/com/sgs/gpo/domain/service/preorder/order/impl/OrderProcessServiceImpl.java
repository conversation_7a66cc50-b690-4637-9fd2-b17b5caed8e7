package com.sgs.gpo.domain.service.preorder.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.util.SpringUtil;
import com.sgs.framework.i18n.util.MessageUtil;
import com.sgs.framework.model.common.object.ObjectBO;
import com.sgs.framework.model.common.object.ObjectIdBO;
import com.sgs.framework.model.common.object.StatusControlBO;
import com.sgs.framework.model.common.object.action.ObjectActionIdBO;
import com.sgs.framework.model.common.object.process.ObjectProcessBO;
import com.sgs.framework.model.common.object.process.ObjectProcessHeaderBO;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.model.order.v2.OrderHeaderBO;
import com.sgs.framework.model.order.v2.OrderIdBO;
import com.sgs.framework.open.platform.model.req.ObjectStatusVerifyReq;
import com.sgs.framework.open.platform.model.req.StatusControlReq;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.OrderStatus;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IGeneralOrderService;
import com.sgs.gpo.domain.service.preorder.order.IOrderProcessService;
import com.sgs.gpo.domain.service.setting.object.IObjectDomainService;
import com.sgs.gpo.domain.service.setting.object.IObjectProcessDomainService;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.setting.object.submodel.process.req.ObjectProcessVerifyReq;
import com.sgs.gpo.integration.eventClient.EventCenterClient;
import com.sgs.preorder.core.common.EventType;
import com.sgs.preorder.core.common.PreEvent;
import com.sgs.preorder.core.common.StandardObjectType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/15 08:27
 */
@Service
@Slf4j
public class OrderProcessServiceImpl implements IOrderProcessService {

    @Autowired
    private IObjectProcessDomainService objectProcessDomainService;
    @Autowired
    private MessageUtil messageUtil;
    @Autowired
    private IGeneralOrderService generalOrderService;
    @Autowired
    private EventCenterClient eventCenterClient;

    @Override
    public BaseResponse<List<StatusControlBO<OrderBO>>> statusControl(StatusControlReq<OrderIdBO> statusControlReq) {

        // 入参校验
        Assert.isTrue(Func.isNotEmpty(statusControlReq.getIdList()),"common.param.miss",new Object[]{Constants.OBJECT.ORDER.OBJECT_CODE});
        Assert.isTrue(Func.isNotEmpty(statusControlReq.getTargetStatusId()),"common.param.miss",new Object[]{"targetStatus"});
        Assert.isTrue(Func.isNotEmpty(statusControlReq.getActionCode()),"common.param.miss",new Object[]{"action"});

        // 处理规则校验
        List<StatusControlBO> orderStatusControlDTOList = Lists.newArrayList();
        if(Func.isNotEmpty(statusControlReq.getIdList())) {
            // 查询最新的订单状态
            List<OrderBO> orderBOList = this.queryStatus(statusControlReq.getIdList()).getData();
            // 校验状态是否满足要求
            orderBOList.forEach(orderBO -> {
                StatusControlBO orderStatusControlDTO= new StatusControlBO();
                orderStatusControlDTO.setDomain(orderBO);
                ObjectProcessVerifyReq objectProcessVerifyReq = new ObjectProcessVerifyReq();
                ObjectIdBO objectIdBO = new ObjectIdBO();
                objectIdBO.setObjectCode(Constants.OBJECT.ORDER.OBJECT_CODE);
                objectProcessVerifyReq.setObjectBO(statusControlReq.getObjectBO());
                objectProcessVerifyReq.setTargetStatus(statusControlReq.getTargetStatusId());
                objectProcessVerifyReq.setLabId(SystemContextHolder.getLab().getLabId());
                ObjectActionIdBO action = new ObjectActionIdBO();
                action.setActionCode(statusControlReq.getActionCode());
                objectProcessVerifyReq.setAction(action);
                Map<String,Object> variable = Maps.newHashMap();
                variable.put("orderStatus",orderBO.getHeader().getOrderStatus());
                objectProcessVerifyReq.setVariable(variable);
                try {
                    BaseResponse<Boolean> verifyRsp = objectProcessDomainService.verify(objectProcessVerifyReq);
                    orderStatusControlDTO.setVerifyResult(verifyRsp.getData());
                    orderStatusControlDTO.setVerifyMessage(verifyRsp.getMessage());
                }catch (Exception e){
                    orderStatusControlDTO.setVerifyResult(false);
                    orderStatusControlDTO.setVerifyMessage(e.getMessage());
                }
                orderStatusControlDTOList.add(orderStatusControlDTO);
            });
        }
        return BaseResponse.newSuccessInstance(orderStatusControlDTOList);
    }

    @Override
    public BaseResponse<List<OrderBO>> statusVerify(ObjectStatusVerifyReq<OrderBO, OrderIdBO> orderStatusVerifyReq) {
        BaseResponse<List<ObjectProcessBO>> objectProcessRsp = this.getProcess();

        Set<Integer> statusList = objectProcessRsp.getData().stream().map(ObjectProcessBO::getHeader)
                .filter(header->Func.isEmpty(orderStatusVerifyReq.getStatusTypeList())?true:orderStatusVerifyReq.getStatusTypeList().contains(header.getStatusType()))
                .filter(header->Func.isEmpty(orderStatusVerifyReq.getEditableFlag())?true:Func.equalsSafe(orderStatusVerifyReq.getEditableFlag(),header.getEditableFlag()))
                .filter(header->Func.isEmpty(orderStatusVerifyReq.getValidFlag())?true:Func.equalsSafe(orderStatusVerifyReq.getValidFlag(),header.getValidFlag()))
                .map(ObjectProcessHeaderBO::getStatusId).collect(Collectors.toSet());

        List<OrderBO> errorList = orderStatusVerifyReq.getBoList().stream().filter(orderBO -> statusList.contains(orderBO.getHeader().getOrderStatus())).collect(Collectors.toList());

        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setData(errorList);
        if(Func.isEmpty(errorList)){
            baseResponse.setStatus(ResponseCode.SUCCESS.getCode());
        }else{
            baseResponse.setStatus(ResponseCode.FAIL.getCode());
            Set<String> errorNoList = errorList.stream().map(OrderBO::getId).map(OrderIdBO::getOrderNo).collect(Collectors.toSet());
            baseResponse.setMessage(messageUtil.get("common.process.status.not.allowed",new Object[]{errorNoList.toString()}));
        }
        return baseResponse;
    }

    public BaseResponse<List<ObjectProcessBO>> getProcess(){
        return objectProcessDomainService.queryStatus(SystemContextHolder.getLab().getLabId(),Constants.OBJECT.ORDER.OBJECT_CODE);
    }

    @Override
    public BaseResponse<List<OrderBO>> queryStatus(List<OrderIdBO> orderIdBOList) {
        Assert.isTrue(Func.isNotEmpty(orderIdBOList),"common.param.miss",new Object[]{"orderId/orderNo"});

        List<OrderBO> orderBOList = Lists.newArrayList();

        Set<String> orderIds = orderIdBOList.stream().map(OrderIdBO::getOrderId).collect(Collectors.toSet());
        Set<String> orderNos = orderIdBOList.stream().map(OrderIdBO::getOrderNo).collect(Collectors.toSet());

        LambdaQueryWrapper<GeneralOrderPO> queryWrapper = new LambdaQueryWrapper<>();
        //TODO 先用No查询，OrderId存在ID不一致的情况
//        if(Func.isNotEmpty(orderIds)){
//            queryWrapper.in(GeneralOrderPO::getId,orderIds);
//        }
//        else
        if(Func.isNotEmpty(orderNos)){
            queryWrapper.in(GeneralOrderPO::getOrderNo,orderNos);
        }
        List<GeneralOrderPO> generalOrderPOList = generalOrderService.list(queryWrapper);
        if(Func.isNotEmpty(generalOrderPOList)){
            generalOrderPOList.forEach(generalOrderPO -> {
                OrderBO orderBO = new OrderBO();

                OrderIdBO orderIdBO = new OrderIdBO();
                orderIdBO.setOrderId(generalOrderPO.getId());
                orderIdBO.setOrderNo(generalOrderPO.getOrderNo());
                orderBO.setId(orderIdBO);

                OrderHeaderBO orderHeaderBO = new OrderHeaderBO();
                orderHeaderBO.setOrderStatus(generalOrderPO.getOrderStatus());
                orderBO.setHeader(orderHeaderBO);

                orderBOList.add(orderBO);
            });
        }
        return BaseResponse.newSuccessInstance(orderBOList);
    }

    @Override
    public BaseResponse<List<StatusControlBO<OrderBO>>> testing(List<OrderIdBO> orderIdBOList) {
        // 查询订单状态
        List<OrderBO> orderBOList = this.queryStatus(orderIdBOList).getData();
        // 状态更新管控
        StatusControlReq<OrderIdBO> statusControlReq = new StatusControlReq<>();
        statusControlReq.setTargetStatusId(OrderStatus.Testing.getStatus());
        //TODO 需要配置
        statusControlReq.setActionCode(Constants.OBJECT.ORDER.ACTION.ToTesting.name());
        statusControlReq.setIdList(orderIdBOList);
        ObjectIdBO objectIdBO = new ObjectIdBO();
        objectIdBO.setObjectCode(Constants.OBJECT.ORDER.OBJECT_CODE);
        ObjectBO objectBO = SpringUtil.getBean(IObjectDomainService.class).getDetail(objectIdBO).getData();
        statusControlReq.setObjectBO(objectBO);
        BaseResponse<List<StatusControlBO<OrderBO>>> orderStatusRsp = this.statusControl(statusControlReq);
        List<OrderIdBO> orderVerifyList = orderStatusRsp.getData().stream().filter(orderStatusControlDTO -> orderStatusControlDTO.getVerifyResult()).map(StatusControlBO::getDomain).map(OrderBO::getId).collect(Collectors.toList());
        Set<String> orderIdSet = orderVerifyList.stream().map(OrderIdBO::getOrderId).collect(Collectors.toSet());
        Set<String> orderNoSet = orderVerifyList.stream().map(OrderIdBO::getOrderNo).collect(Collectors.toSet());

        // 更新校验通过的
        LambdaUpdateChainWrapper<GeneralOrderPO> updateChainWrapper = new LambdaUpdateChainWrapper<>(generalOrderService.getBaseMapper());
        updateChainWrapper.set(GeneralOrderPO::getOrderStatus,OrderStatus.Testing.getStatus());
        if(Func.isNotEmpty(orderIdSet)) {
            updateChainWrapper.in(GeneralOrderPO::getId, orderIdSet);
            updateChainWrapper.update();
        }else if (Func.isNotEmpty(orderNoSet)){
            updateChainWrapper.in(GeneralOrderPO::getOrderNo, orderNoSet);
            updateChainWrapper.update();
        }
        //通过校验的同步外部系统
        for(OrderIdBO orderId : orderIdBOList){
            OrderQueryReq orderQueryReq = new OrderQueryReq();
            if(Func.isNotEmpty(orderId.getOrderNo())){
                orderQueryReq.setOrderNoList(Sets.newHashSet(orderId.getOrderNo()));
            }
            if(Func.isNotEmpty(orderId.getOrderId())){
                orderQueryReq.setOrderIdList(Sets.newHashSet(orderId.getOrderId()));
            }
            BaseResponse<List<GeneralOrderPO>> orderInfoRsp = generalOrderService.query2(orderQueryReq);
            if(Func.isEmpty(orderInfoRsp.getData()) || orderInfoRsp.isFail()){
                continue;
            }
            GeneralOrderPO generalOrderPO = orderInfoRsp.getData().get(0);
            PreEvent preEvent = new PreEvent();
            preEvent.setEventSource(StandardObjectType.Order.getName());
            //根据当前订单状态传值
            preEvent.setEventType(EventType.Testing.getTypeName());
            preEvent.setEventSourceStatus(generalOrderPO.getOrderStatus());
            preEvent.setToken(SystemContextHolder.getSgsToken());
            preEvent.setEventSourceNo(generalOrderPO.getOrderNo());
            preEvent.setEventSourceId(generalOrderPO.getId());
            preEvent.setOrderId(generalOrderPO.getId());
            preEvent.setOrderNo(generalOrderPO.getOrderNo());
            preEvent.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            preEvent.setOperatorUsername(SecurityContextHolder.getUserInfoFillSystem().getRegionAccount());
            eventCenterClient.sendEventMessage(preEvent,SystemContextHolder.getLabCode());
        }

        return orderStatusRsp;
    }

}
