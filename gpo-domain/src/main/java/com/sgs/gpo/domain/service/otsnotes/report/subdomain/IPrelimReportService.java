package com.sgs.gpo.domain.service.otsnotes.report.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.PrelimResultPO;
import com.sgs.gpo.facade.model.report.req.PrelimReportQueryReq;

import java.util.List;

public interface IPrelimReportService extends IService<PrelimResultPO> {

    /**
     * PrelimReport基本查询
     */
    List<PrelimResultPO> query(PrelimReportQueryReq prelimReportQueryReq);

}
