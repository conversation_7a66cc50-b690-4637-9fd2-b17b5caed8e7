package com.sgs.gpo.domain.service.preorder.enquiry.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.enquiry.EnquiryReportReceiverMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryReportReceiverPO;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryReportReceiverService;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class EnquiryReportReceiverServiceImpl extends ServiceImpl<EnquiryReportReceiverMapper, EnquiryReportReceiverPO>
        implements IEnquiryReportReceiverService {
    @Override
    public BaseResponse<List<EnquiryReportReceiverPO>> select(EnquiryIdReq enquiryIdReq) {
        if(Func.isEmpty(enquiryIdReq)||Func.isEmpty(enquiryIdReq.getEnquiryIdList())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        LambdaQueryWrapper<EnquiryReportReceiverPO> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EnquiryReportReceiverPO::getEnquiryId, enquiryIdReq.getEnquiryIdList());
        return BaseResponse.newSuccessInstance( this.baseMapper.selectList(wrapper));
    }
}
