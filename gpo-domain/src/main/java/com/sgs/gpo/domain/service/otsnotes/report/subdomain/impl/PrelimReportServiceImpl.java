package com.sgs.gpo.domain.service.otsnotes.report.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.report.PrelimReportMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.PrelimResultPO;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IPrelimReportService;
import com.sgs.gpo.facade.model.report.req.PrelimReportQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class PrelimReportServiceImpl extends ServiceImpl<PrelimReportMapper, PrelimResultPO> implements IPrelimReportService {
    @Override
    public List<PrelimResultPO> query(PrelimReportQueryReq prelimReportQueryReq) {
        if(Func.isEmpty(prelimReportQueryReq) ||
                (Func.isAllEmpty(prelimReportQueryReq.getPrelimResultId(),prelimReportQueryReq.getPrelimResultNo(),prelimReportQueryReq.getReportId()) &&
                        Func.isAnyEmpty(prelimReportQueryReq.getBuCode(), prelimReportQueryReq.getDeliveredDateStart(), prelimReportQueryReq.getDeliveredDateEnd()))){
            return null;
        }
        LambdaQueryWrapper<PrelimResultPO> wrapper= Wrappers.lambdaQuery();
        if(Func.isNotEmpty(prelimReportQueryReq.getPrelimResultId())){
            wrapper.eq(PrelimResultPO::getId,prelimReportQueryReq.getPrelimResultId());
        }
        if(Func.isNotEmpty(prelimReportQueryReq.getPrelimResultNo())){
            wrapper.eq(PrelimResultPO::getPrelimResultNo,prelimReportQueryReq.getPrelimResultNo());
        }
        if(Func.isNotEmpty(prelimReportQueryReq.getReportId())){
            wrapper.eq(PrelimResultPO::getReportId,prelimReportQueryReq.getReportId());
        }
        if(Func.isNotEmpty(prelimReportQueryReq.getBuCode())){
            wrapper.eq(PrelimResultPO::getBuCode,prelimReportQueryReq.getBuCode());
        }
        if(Func.isNotEmpty(prelimReportQueryReq.getDeliveredDateStart())){
            wrapper.ge(PrelimResultPO::getDeliveredDate,prelimReportQueryReq.getDeliveredDateStart());
        }
        if (Func.isNotEmpty(prelimReportQueryReq.getDeliveredDateEnd())){
            wrapper.le(PrelimResultPO::getDeliveredDate,prelimReportQueryReq.getDeliveredDateEnd());
        }
        if (Func.isNotEmpty(prelimReportQueryReq.getPrelimResultStatus())){
            wrapper.eq(PrelimResultPO::getPrelimResultStatus,prelimReportQueryReq.getPrelimResultStatus());
        }
        return baseMapper.selectList(wrapper);
    }
}
