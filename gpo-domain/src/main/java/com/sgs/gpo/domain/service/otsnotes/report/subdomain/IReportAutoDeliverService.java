package com.sgs.gpo.domain.service.otsnotes.report.subdomain;


import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.PageBO;
import com.sgs.gpo.facade.model.report.bo.ReportAutoDeliverBO;
import com.sgs.gpo.facade.model.report.req.ReportAutoDeliverQueryReq;

public interface IReportAutoDeliverService {
    BaseResponse<PageBO<ReportAutoDeliverBO>> page(ReportAutoDeliverQueryReq queryReq, Integer page, Integer rows);
}
