package com.sgs.gpo.domain.service.preorder.order.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.order.v2.OrderIdBO;
import com.sgs.framework.redis.utils.RedisUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.DBConstants;
import com.sgs.gpo.core.enums.ObjectType;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.OrderIndexMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderIndexPO;
import com.sgs.gpo.domain.service.common.index.IIndexService;
import com.sgs.gpo.domain.service.common.index.Index;
import com.sgs.gpo.domain.service.otsnotes.report.IReportDomainService;
import com.sgs.gpo.facade.model.kafka.MaxWellMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Service
@Index(object = ObjectType.Order)
@Slf4j
@Primary
public class OrderIndexServiceImpl extends ServiceImpl<OrderIndexMapper, OrderIndexPO> implements IIndexService<OrderIndexPO, OrderIdBO> {

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private IReportDomainService reportDomainService;

    @Override
    public List<OrderIndexPO> searchFromSource(OrderIndexPO index) {
        List<OrderIndexPO> list = baseMapper.selectSourceList(index);
        List<OrderIndexPO> payerList = null;
        List<OrderIndexPO> productList = null;
        if(Func.isNotEmpty(list)){
            Set<String> orderIdSet = list.stream().map(OrderIndexPO::getOrderId).collect(Collectors.toSet());
            if(Func.isNotEmpty(orderIdSet)) {
                payerList = baseMapper.getPayerByOrder(orderIdSet);
                productList = baseMapper.getProductByOrder(orderIdSet);
            }
        }
        if(Func.isNotEmpty(payerList)) {
            List<OrderIndexPO> finalPayerList = payerList;
            list.forEach(item -> {
                OrderIndexPO payerIndex = finalPayerList.stream().filter(payer->item.getOrderId().equals(payer.getOrderId())).findFirst().orElse(null);
                if(Func.isNotEmpty(payerIndex)){
                    item.setPayerId(payerIndex.getPayerId());
                    item.setPayerNo(payerIndex.getPayerNo());
                    item.setPayerName(payerIndex.getPayerName());
                    item.setPaymentTerm(payerIndex.getPaymentTerm());
                }else{
                    log.error(item.getOrderNo()+" not found payer");
                }
            });
        }
        if(Func.isNotEmpty(productList)) {
            List<OrderIndexPO> finalProductList = productList;
            list.forEach(item -> {
                OrderIndexPO productIndex = finalProductList.stream().filter(product->item.getOrderId().equals(product.getOrderId())).findFirst().orElse(null);
                if(Func.isNotEmpty(productIndex)){
                    item.setProduct(productIndex.getProduct());
                }else{
                    log.error(item.getOrderNo()+" not found product");
                }
            });
        }
        return list;
    }

    @Override
    public boolean saveOrUpdate(OrderIdBO orderId) {
        // 获取OrderId / OrderNo
        Assert.isTrue(Func.isNotEmpty(orderId) && (Func.isNotEmpty(orderId.getOrderId() ) || Func.isNotEmpty(orderId.getOrderNo())), ResponseCode.PARAM_MISS,"Message invalid (not id information)");

        // 读取DB数据
        OrderIndexPO dbOrderIndex = getByIdFromSource(orderId);
        if (Func.isEmpty(dbOrderIndex)) {
            log.error("OrderIndex[not found]:{}", orderId);
            return false;
        }
        dbOrderIndex.setIndexId(dbOrderIndex.getOrderId());

        //补充DB中的OrderId
        orderId.setOrderId(dbOrderIndex.getOrderId());

        // 保存/更新订单索引
        if (!hasIndex(orderId)) {
            log.info("OrderIndex[create]:{}",dbOrderIndex.getOrderNo());
            return save(dbOrderIndex);
        } else {
            log.info("OrderIndex[update]:{}",dbOrderIndex.getOrderNo());
            return updateById(dbOrderIndex);
        }
    }

    @Override
    public boolean saveOrUpdate(MaxWellMessage maxWellMessage) {

        Assert.notNull(maxWellMessage,"Message is null");

        // 获取OrderId / OrderNo
        OrderIdBO orderId = getId(maxWellMessage);
        Assert.notNull(orderId,String.format("Message invalid (not id information), Message:%s",Func.toStr(maxWellMessage)));

        Long xid = maxWellMessage.getXid();
        Long xoffset = maxWellMessage.getXoffset();
        log.info("OrderIndex[receive],orderId:{},XID{},XOffSet{}",orderId,xid,xoffset);

        // 60s内忽略相同DB事务多次更新的消息
        String key = String.format("DB_XID_%s_%s_%s", maxWellMessage.getDatabase(),xid,orderId.hashCode());
        if(redisUtil.hasKey(key)){
            log.debug("OrderIndex[receive repeat]:{}",maxWellMessage);
            return false;
        }
        redisUtil.set(key,xid,60l);
        saveOrUpdate(orderId);
        return true;
    }


    @Override
    public OrderIdBO getId(MaxWellMessage maxWellMessage) {
        if(Func.isEmpty(maxWellMessage)){
            return null;
        }
        Object orderId = maxWellMessage.getData().get(DBConstants.DATA_BASE.PREORDER.SL_ORDER.orderId);
        Object orderNo = maxWellMessage.getData().get(DBConstants.DATA_BASE.PREORDER.GENERAL_ORDER.orderNo);
        if (Func.isEmpty(orderId)) {
            orderId = maxWellMessage.getData().get(DBConstants.DATA_BASE.PREORDER.GENERAL_ORDER.id);
        }
        OrderIdBO orderIdBO = new OrderIdBO();
        orderIdBO.setOrderId(Func.toStr(orderId) );
        orderIdBO.setOrderNo(Func.toStr(orderNo));
        return orderIdBO;
    }

    @Override
    public boolean hasIndex(OrderIdBO id) {
        return baseMapper.hasIndex(id.getOrderId())>0;
    }

    @Override
    public OrderIndexPO idToIndex(OrderIdBO orderId){
        if(Func.isEmpty(orderId)){
            return null;
        }
        OrderIndexPO orderIndex = new OrderIndexPO();
        orderIndex.setOrderId(orderId.getOrderId());
        orderIndex.setOrderNo(orderId.getOrderNo());
        return orderIndex;
    }

}
