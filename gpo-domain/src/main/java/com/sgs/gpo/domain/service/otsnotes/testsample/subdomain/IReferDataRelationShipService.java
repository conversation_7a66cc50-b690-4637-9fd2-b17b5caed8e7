package com.sgs.gpo.domain.service.otsnotes.testsample.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.ReferDataRelationshipPO;
import com.sgs.gpo.facade.model.otsnotes.testsample.bo.ReferDataBO;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.ReferDataRelationShipReq;

import java.util.List;

public interface IReferDataRelationShipService extends IService<ReferDataRelationshipPO> {

    /**
     * 查询样品Refer关系
     * @param req
     * @return
     */
    BaseResponse<List<ReferDataRelationshipPO>> query(ReferDataRelationShipReq req);

    /**
     * 根据订单号查询样品Refer关系(到testLine)
     */
    BaseResponse<List<ReferDataBO>> selectReferDataByOrder(ReferDataRelationShipReq req);


}
