package com.sgs.gpo.domain.service.otsnotes.report.subdomain;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.PageBO;
import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.framework.model.report.report.v2.ReportIdBO;
import com.sgs.framework.open.platform.base.service.IBaseService;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.conclusion.ConclusionListPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.TestSampleReportPO;
import com.sgs.gpo.facade.model.otsnotes.conclusion.ConclusionDTO;
import com.sgs.gpo.facade.model.otsnotes.report.req.ReportTestMatrixModelGetReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import com.sgs.gpo.facade.model.report.req.QuerySampleReportReq;
import com.sgs.gpo.facade.model.report.req.ReferenceReportQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportAccreditationUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportEntryModeUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportIdReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportTestResultStatusUpdateReq;
import com.sgs.gpo.facade.model.trackingtool.TrackingToolDataReq;
import com.sgs.gpo.facade.model.trackingtool.TrackingToolDataRsp;

import java.util.List;

/**
 * <AUTHOR>
 * @title: IReportService
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/26 15:55
 */
public interface IReportService
        extends IBaseService<ReportBO, ReportPO, ReportIdBO, ReportQueryReq> {

    BaseResponse<List<TestSampleReportPO>> queryReportByTestSample(QuerySampleReportReq querySampleReportReq);
    BaseResponse<List<ReportPO>> queryReportByOrderNo(OrderIdReq orderIdReq);

    /**
     * 查询报告对应的Conclusion
     * @param reportId
     * @return
     */
    BaseResponse<ConclusionListPO> queryReportConclusion(String reportId);

    BaseResponse<List<ConclusionDTO>> queryReportConclusionList(ReportIdReq reportIdReq);

    BaseResponse<List<ConclusionDTO>> queryReportMatrixConclusionList(ReportIdReq reportIdReq);

    BaseResponse<List<ReportPO>> select(ReportQueryReq reportIdReq);

    /**
     * 报告主信息查询
     * @param reportQueryReq
     * @return
     */
    BaseResponse<List<ReportPO>> queryV1(ReportQueryReq reportQueryReq);

    /**
     *
     */
   String queryJobValidateByReportId(String reportId);

   BaseResponse<String> getReportTestMatrixMode(ReportTestMatrixModelGetReq reportTestMatrixModelGetReq);

   BaseResponse<Boolean> updateReportTestResultStatus(ReportTestResultStatusUpdateReq reportTestResultStatusUpdateReq);

   BaseResponse<Boolean> updateReportEntryMode(ReportEntryModeUpdateReq reportEntryModeUpdateReq);

   List<ReportPO> queryReferReportList(ReferenceReportQueryReq req);

    PageBO<TrackingToolDataRsp> selectTrackingToolData(TrackingToolDataReq trackingToolDataReq, Integer page, Integer rows);

    Boolean updateReportAccreditation(ReportAccreditationUpdateReq reportAccreditationUpdateReq);

   List<ReportPO> queryReportBySubReport(ReportQueryReq reportQueryReq);
}
