package com.sgs.gpo.domain.service.otsnotes.subcontract.command;

import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.common.lab.LabContactBO;
import com.sgs.framework.model.common.process.ProcessBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementReportBO;
import com.sgs.framework.model.order.v2.OrderIdRelBO;
import com.sgs.framework.model.order.v2.TestLineIdRelBO;
import com.sgs.framework.model.order.v2.TestSampleIdRelBO;
import com.sgs.framework.model.report.subreport.SubReportIdRelBO;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractBO;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractChildrenBO;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractHeaderBO;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractIdBO;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractParentBO;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractPaymentBO;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractRelationshipBO;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractToBO;
import com.sgs.framework.model.test.testline.v2.TestLineSampleBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractRequirementPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractTestLinePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subreport.SubReportPO;
import com.sgs.gpo.domain.service.otsnotes.subcontract.context.SubcontractContext;
import com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.ISubcontractRequirementService;
import com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.ISubcontractService;
import com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.ISubcontractTestLineService;
import com.sgs.gpo.domain.service.otsnotes.subreport.ISubReportService;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.ITestSampleService;
import com.sgs.gpo.facade.model.otsnotes.subreport.req.SubReportQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractQueryReq;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractRequirementQueryReq;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractTestLineQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/26 16:55
 * @Description subcontract查询输出标准结构SubcontractBO
 */
@Primary
@Service
@Slf4j
public class SubcontractQueryCMD extends BaseCommand<SubcontractContext<SubcontractQueryReq>> {

    @Autowired
    private ISubcontractService subcontractService;
    @Autowired
    private ISubcontractRequirementService subcontractRequirementService;
    @Autowired
    private ISubcontractTestLineService subcontractTestLineService;
    @Autowired
    private ITestSampleService testSampleService;
    @Autowired
    private ISubReportService subReportService;

    @Override
    public BaseResponse validateParam(SubcontractContext<SubcontractQueryReq> context) {
        Assert.isTrue(Func.isNotEmpty(context.getParam()),"common.param.miss",new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(context.getParam().getLab()),"common.param.miss",new Object[]{Constants.TERM.LAB.getCode()});
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(SubcontractContext<SubcontractQueryReq> context) {
        //查询SubcontractPO List
        List<SubcontractPO> subcontractPOList = Lists.newArrayList();
        SubcontractQueryReq subcontractQueryReq = context.getParam();
        BaseResponse<List<SubcontractPO>> subcontractListRsp = subcontractService.query2(subcontractQueryReq);
        if(subcontractListRsp.isSuccess() && Func.isNotEmpty(subcontractListRsp.getData())){
            subcontractPOList = subcontractListRsp.getData();
        }
        context.setSubcontractPOList(subcontractPOList);
        //分包对应的测试要求
        List<SubcontractRequirementPO> subcontractRequirementList = Lists.newArrayList();
        Set<String> subContractIds = subcontractPOList.stream().map(e -> e.getId()).collect(Collectors.toSet());
        SubcontractRequirementQueryReq requirementQueryReq = new SubcontractRequirementQueryReq();
        requirementQueryReq.setSubContractIds(subContractIds);
        BaseResponse<List<SubcontractRequirementPO>> subcontractRequirementRsp = subcontractRequirementService.query(requirementQueryReq);
        if(subcontractListRsp.isSuccess() && Func.isNotEmpty(subcontractRequirementRsp.getData())){
            subcontractRequirementList =  subcontractRequirementRsp.getData();
        }
        context.setSubcontractRequirementList(subcontractRequirementList);
        //分包单对应的TestLine
        List<SubcontractTestLinePO> subcontractTestLineList = Lists.newArrayList();
        SubcontractTestLineQueryReq subcontractTestLineQueryReq = new SubcontractTestLineQueryReq();
        subcontractTestLineQueryReq.setSubcontractIds(subContractIds);
        BaseResponse<List<SubcontractTestLinePO>> subcontractTestLineRsp =  subcontractTestLineService.query(subcontractTestLineQueryReq);
        if(subcontractTestLineRsp.isSuccess() && Func.isNotEmpty(subcontractTestLineRsp.getData())){
            subcontractTestLineList = subcontractTestLineRsp.getData();
        }
        context.setSubcontractTestLineList(subcontractTestLineList);
        //查询SubReport
        List<SubReportPO> subReportList = Lists.newArrayList();
        SubReportQueryReq subReportQueryReq = new SubReportQueryReq();
        subReportQueryReq.setSubContractIdList(subContractIds);
        BaseResponse<List<SubReportPO>> subReportListRsp = subReportService.query(subReportQueryReq);
        if(subReportListRsp.isSuccess() && Func.isNotEmpty(subReportListRsp.getData())){
            subReportList = subReportListRsp.getData();
        }
        context.setSubReportList(subReportList);
        return super.before(context);
    }

    @Override
    public BaseResponse<List<SubcontractBO>> buildDomain(SubcontractContext<SubcontractQueryReq> context) {

        List<SubcontractBO> subcontractBOList = Lists.newArrayList();
        List<SubcontractPO> subcontractList = context.getSubcontractPOList();
        List<SubcontractTestLinePO> subcontractTestLineList = context.getSubcontractTestLineList();
        List<SubcontractRequirementPO> subcontractRequirementList = context.getSubcontractRequirementList();
        List<SubReportPO> subReportList = context.getSubReportList();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        subcontractList.forEach(subcontract->{
            SubcontractBO subcontractBO = new SubcontractBO();
            // id
            SubcontractIdBO id = new SubcontractIdBO();
            id.setSubcontractId(subcontract.getId());
            id.setSubcontractNo(subcontract.getSubContractNo());
            subcontractBO.setId(id);
            // relationship
            assembleRelationship(subcontractBO,subcontractTestLineList,subReportList,subcontract.getOrderNo());
            // header
            SubcontractHeaderBO header = new SubcontractHeaderBO();
            if(Func.isNotEmpty(subcontract.getSubContractExpectDueDate())){
                header.setSubcontractExpectDueDate(sdf.format(subcontract.getSubContractExpectDueDate()));
            }
            header.setSubcontractStatus(subcontract.getStatus());
            header.setSubcontractRemark(subcontract.getSubContractRemark());
            subcontractBO.setHeader(header);
            // subcontractTo
            SubcontractToBO subcontractTo = new SubcontractToBO();
            subcontractTo.setLabCode(subcontract.getSubContractLabCode());
            subcontractTo.setLabName(subcontract.getSubContractLabName());
            subcontractTo.setLabId(subcontract.getSubContractLabId());
            LabContactBO labContact = new LabContactBO();
            labContact.setContactEmail(subcontract.getSubContractContractEmail());
            labContact.setContactName(subcontract.getSubContractContract());
            labContact.setContactTelephone(subcontract.getSubContractContractTel());
            subcontractTo.setLabContact(labContact);
            subcontractBO.setSubcontractTo(subcontractTo);
            //executeOrder
            if(Func.isNotEmpty(subcontractRequirementList)){
                SubcontractRequirementPO subcontractRequirement = subcontractRequirementList.stream()
                        .filter(item->Func.equalsSafe(item.getSubContractId(),subcontractBO.getId().getSubcontractId()))
                        .findAny().orElse(null);
                if(Func.isNotEmpty(subcontractRequirement)){
                    // payment
                    SubcontractPaymentBO payment = new SubcontractPaymentBO();
                    payment.setSubcontractCurrency(subcontractRequirement.getSubcontractFeeCurrency());
                    payment.setSubcontractFee(subcontractRequirement.getSubcontractFee());
                    subcontractBO.setPayment(payment);
                    //serviceRequirement
                    ServiceRequirementBO serviceRequirement = new ServiceRequirementBO();
                    serviceRequirement.setOtherRequestRemark(subcontractRequirement.getOtherRequirements());
                    ServiceRequirementReportBO report = new ServiceRequirementReportBO();
                    if(Func.isNotEmpty(subcontractRequirement.getReportLanguage())){
                        report.setReportLanguage(Integer.valueOf(subcontractRequirement.getReportLanguage()));
                    }
                    Func.copy(subcontractRequirement,report);
                    serviceRequirement.setReport(report);
                    subcontractBO.setServiceRequirement(serviceRequirement);
                }
            }
            // processList
            List<ProcessBO> processList = Lists.newArrayList();
            if(Func.isNotEmpty(subcontract.getCreatedDate())){
                ProcessBO create = new ProcessBO();
                create.setOperator(subcontract.getCreatedBy());
                create.setCompletedDateTime(subcontract.getCreatedDate());
                create.setNodePoint(Constants.OBJECT.SUBCONTRACT.ACTION.CREATE.name());
                processList.add(create);
            }
            subcontractBOList.add(subcontractBO);
        });
        context.setSubcontractBOList(subcontractBOList);
        return BaseResponse.newSuccessInstance(context.getSubcontractBOList());
    }

    private void assembleRelationship(SubcontractBO subcontractBO, List<SubcontractTestLinePO> subcontractTestLineList, List<SubReportPO> subReportList,String orderNo){
        SubcontractRelationshipBO relationship = new SubcontractRelationshipBO();
        // parent
        SubcontractParentBO parent = new SubcontractParentBO();
        OrderIdRelBO order = new OrderIdRelBO();
        //orderId暂不处理
        //order.setOrderId(context.getOrderId());
        order.setOrderNo(orderNo);
        parent.setOrder(order);
        relationship.setParent(parent);
        // children
        SubcontractChildrenBO children = new SubcontractChildrenBO();
        //testLineList
        if(Func.isNotEmpty(subcontractTestLineList)){
            List<SubcontractTestLinePO> testLineList = subcontractTestLineList.stream().filter(subcontractTestLine ->
                    Func.equalsSafe(subcontractTestLine.getSubContractId(),subcontractBO.getId().getSubcontractId())).collect(Collectors.toList());
            if(Func.isNotEmpty(testLineList)){
                children.setTestLineList(Func.copy(testLineList,SubcontractTestLinePO.class,TestLineIdRelBO.class));
                // testSampleList
                // TODO testSampleBOList 查询替换
                TestSampleQueryReq testSampleQueryReq = new TestSampleQueryReq();
                testSampleQueryReq.setTestLineInstanceId(testLineList.stream().map(SubcontractTestLinePO::getTestLineInstanceId).collect(Collectors.toSet()));
                BaseResponse<List<TestLineSampleBO>> testSampleResponse = testSampleService.queryListByTestLine(testSampleQueryReq);
                if(testSampleResponse.isSuccess() && Func.isNotEmpty(testSampleResponse.getData())){
                    children.setTestSampleList(Func.copy(testSampleResponse.getData(),TestLineSampleBO.class, TestSampleIdRelBO.class));
                }
            }
        }
        // subReportList
        if(Func.isNotEmpty(subReportList)){
            List<SubReportPO> subReportPOList = subReportList.stream().filter(
                    e -> Func.equalsSafe(e.getSubcontractId(),subcontractBO.getId().getSubcontractId())).collect(Collectors.toList());
            List<SubReportIdRelBO> subReportBOList = Lists.newArrayList();
            if(Func.isNotEmpty(subReportPOList)){
                subReportPOList.stream().forEach(subReportPO -> {
                    SubReportIdRelBO subReportIdRelBO = new SubReportIdRelBO();
                    subReportIdRelBO.setSubReportId(subReportPO.getId());
                    subReportIdRelBO.setSubReportNo(subReportPO.getSubReportNo());
                    subReportBOList.add(subReportIdRelBO);
                });
                children.setSubReportList(subReportBOList);
            }
        }
        relationship.setChildren(children);
        subcontractBO.setRelationship(relationship);
    }


    @Override
    public BaseResponse execute(SubcontractContext<SubcontractQueryReq> context) {
        return this.buildDomain(context);
    }
}
