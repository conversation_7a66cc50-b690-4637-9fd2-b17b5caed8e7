package com.sgs.gpo.domain.service.preorder.order.command;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.customer.CustomerPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.domain.service.preorder.customer.subdomain.ICustomerService;
import com.sgs.gpo.domain.service.preorder.order.context.OrderContext;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IGeneralOrderService;
import com.sgs.gpo.facade.model.enums.EnableDeliveryEnums;
import com.sgs.gpo.facade.model.enums.QuotationFlag;
import com.sgs.gpo.facade.model.preorder.order.dto.OrderQuotationDTO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.integration.framework.FrameworkClient;
import com.sgs.framework.facade.domain.req.BuParamReq;
import com.sgs.preorder.facade.model.enums.PaymentStatus;
import com.sgs.priceengine.facade.model.enums.CustomerUsage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
@Scope(value = "prototype")
public class BuildEnableDeliveryCMD extends BaseCommand<OrderContext<String>> {

    @Autowired
    IGeneralOrderService generalOrderService;
    @Autowired
    ICustomerService customerService;
    @Autowired
    FrameworkClient frameworkClient;

    @Override
    public BaseResponse validateParam(OrderContext<String> context) {
        String orderNo = context.getParam();
        if(Func.isEmpty(orderNo)){
            return BaseResponse.newSuccessInstance(EnableDeliveryEnums.DEFAULT_VALUE.getStatus());
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(OrderContext<String> context) {
        String orderNo = context.getParam();
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setOrderNoList(Sets.newHashSet(orderNo));
        BaseResponse<List<GeneralOrderPO>> orderPORsp = generalOrderService.query2(orderQueryReq);
        if(orderPORsp.isFail() || Func.isEmpty(orderPORsp.getData())){
            return BaseResponse.newSuccessInstance(EnableDeliveryEnums.DEFAULT_VALUE.getStatus());
        }
        GeneralOrderPO order = orderPORsp.getData().get(0);
        String paymentTermName = "";
        LambdaQueryWrapper<CustomerPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CustomerPO::getGeneralOrderId,order.getId());
        lambdaQueryWrapper.eq(CustomerPO::getCustomerUsage,CustomerUsage.Payer.getCode());
        List<CustomerPO> customerPOList = customerService.list(lambdaQueryWrapper);
        if(Func.isNotEmpty(customerPOList)){
            paymentTermName = customerPOList.get(0).getPaymentTermName();
        }
        context.setOrderPO(order);
        context.setPaymentTermName(paymentTermName);
        return super.before(context);
    }

    @Override
    public BaseResponse execute(OrderContext<String> context) {
        String paymentTermName = context.getPaymentTermName();
        GeneralOrderPO orderPO = context.getOrderPO();
        Integer enableDelivery = EnableDeliveryEnums.DEFAULT_VALUE.getStatus();
        Integer paymentStatus = orderPO.getPayStatus();
        if (Func.isNotEmpty(paymentTermName) && !Func.equals("IMMEDIATE", paymentTermName)) {
            //更新为月结
            enableDelivery = orderPO.getEnableDelivery() | EnableDeliveryEnums.MONTHLY_PAYMENT.getStatus();
        } else if (EnableDeliveryEnums.check(orderPO.getEnableDelivery(), EnableDeliveryEnums.MONTHLY_PAYMENT)) {
            enableDelivery = orderPO.getEnableDelivery() ^ EnableDeliveryEnums.MONTHLY_PAYMENT.getStatus();
        } else {
            enableDelivery = orderPO.getEnableDelivery();
        }
        if (Func.isEmpty(paymentStatus)) {
            paymentStatus = orderPO.getPayStatus();
        }
        if (Func.isEmpty(paymentStatus)) {
            paymentStatus = PaymentStatus.UN_PAID.getType();
        }
        List<OrderQuotationDTO> orderQuotationDTOS = generalOrderService.getQuotationOrderForOrderId(orderPO.getId());
        boolean allQuotationFree = true;
        if (Func.isNotEmpty(orderQuotationDTOS)) {
            long count = orderQuotationDTOS.stream().filter(item -> Func.isEmpty(item.getQuotationFlag()) || !QuotationFlag.hasFreeQuotationApprovedFlags(item.getQuotationFlag())).count();
            //存在非免单或免单未审批的
            if (count > 0) {
                log.info("calc enableDelivery,{},exists unApproved free quotation", orderPO.getOrderNo());
                allQuotationFree = false;
            }
        }

        log.info("calc enableDelivery,{},payStatus:{}", orderPO.getOrderNo(), PaymentStatus.getType(paymentStatus).getCode());
        if (paymentStatus == PaymentStatus.NA.getType()) {
            //NA 且所有的quotation都免单了
            if (allQuotationFree) {
                enableDelivery = enableDelivery | EnableDeliveryEnums.FREE_ORDER.getStatus();
            } else {
//                NA 但免单申请未审批
                if (EnableDeliveryEnums.check(enableDelivery, EnableDeliveryEnums.FREE_ORDER)) {
                    enableDelivery = enableDelivery ^ EnableDeliveryEnums.FREE_ORDER.getStatus();
                }
            }
        } else {
            Map<Integer, Boolean> result = this.reportDeliveryWithPaymentStatus(new HashSet<>(Arrays.asList(paymentStatus)));
            Boolean parPaidSendEmail = (Func.isNotEmpty(result) && result.containsKey(paymentStatus)) ? result.get(paymentStatus) : false;
            if (parPaidSendEmail) {
                enableDelivery = enableDelivery | EnableDeliveryEnums.UPLOAD_PAID_UP.getStatus();
            } else if (EnableDeliveryEnums.check(enableDelivery, EnableDeliveryEnums.UPLOAD_PAID_UP)) {
                enableDelivery = enableDelivery ^ EnableDeliveryEnums.UPLOAD_PAID_UP.getStatus();
            }else if (EnableDeliveryEnums.check(enableDelivery, EnableDeliveryEnums.FREE_ORDER)) {
                enableDelivery = enableDelivery ^ EnableDeliveryEnums.FREE_ORDER.getStatus();
            }
        }
        log.info("calc enableDelivery,{},after calc enableDelivery Status:{}", orderPO.getOrderNo(),enableDelivery);
        return BaseResponse.newSuccessInstance(enableDelivery);
    }
    public Map<Integer, Boolean> reportDeliveryWithPaymentStatus(Set<Integer> paymentStatusList) {
        Map<Integer, Boolean> result = null;
        if(Func.isEmpty(paymentStatusList)){
            return result;
        }
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        buParamReq.setGroupCode(Constants.BU_PARAM.REPORT.GROUP);
        buParamReq.setParamCode(Constants.BU_PARAM.REPORT.REPORT_DELIVERY_PAY_MENT_RULE.CODE);
        List<BuParamValueRsp> buParamValueRsps = frameworkClient.getBuParams(buParamReq);
        Set<Integer> enableDeliverySet = new HashSet<>();
        try{
            enableDeliverySet = Func.parse(buParamValueRsps.get(0).getParamValue(),Set.class);
        }catch (Exception e){
            log.error("{}",e);
            return result;
        }
        result = new HashMap<>();
        for(Integer paymentStatus:paymentStatusList){
            result.put(paymentStatus,false);
            if(enableDeliverySet.contains(paymentStatus)) {
                result.put(paymentStatus,true);
            }
        }
        return result;
    }

}
