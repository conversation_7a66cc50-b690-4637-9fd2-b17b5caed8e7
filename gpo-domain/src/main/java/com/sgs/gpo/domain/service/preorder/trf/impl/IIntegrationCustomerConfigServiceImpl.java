package com.sgs.gpo.domain.service.preorder.trf.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.trf.IntegrationCustomerConfigInfoMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.trf.IntegrationCustomerConfigPO;
import com.sgs.gpo.domain.service.preorder.trf.IIntegrationCustomerConfigService;
import com.sgs.gpo.facade.model.trf.dto.TrfConfigDTO;
import com.sgs.gpo.facade.model.trf.dto.TrfConfigRuleDTO;
import com.sgs.gpo.facade.model.trf.req.QueryTrfConfigReq;
import lombok.extern.log4j.Log4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Log4j
public class IIntegrationCustomerConfigServiceImpl extends ServiceImpl<IntegrationCustomerConfigInfoMapper, IntegrationCustomerConfigPO> implements IIntegrationCustomerConfigService {
    @Override
    public BaseResponse<List<TrfConfigDTO>> queryBindTrfConfigList(QueryTrfConfigReq queryTrfConfigReq) {
        List<TrfConfigDTO> trfConfigDTOList = new ArrayList<>();
        List<IntegrationCustomerConfigPO> integrationCustomerConfigPOS = baseMapper.queryTrfConfigList(queryTrfConfigReq);
        if(Func.isNotEmpty(integrationCustomerConfigPOS)){
            for (IntegrationCustomerConfigPO integrationCustomerConfigPO : integrationCustomerConfigPOS) {
                TrfConfigDTO trfConfigDTO = Func.copy(integrationCustomerConfigPO, TrfConfigDTO.class);
                String customerRules = trfConfigDTO.getCustomerRules();
                if(Func.isNotEmpty(customerRules)){
                    TrfConfigRuleDTO trfConfigRuleDTO = JSONObject.parseObject(customerRules, TrfConfigRuleDTO.class);
                    if(Func.isNotEmpty(trfConfigRuleDTO)){
                        trfConfigDTO.setIntegrationChannel(trfConfigRuleDTO.getIntegrationChannel());
                        if(Func.isNotEmpty(trfConfigRuleDTO.getBindTrf()) && trfConfigRuleDTO.getBindTrf().isShowBind()){
                            trfConfigDTOList.add(trfConfigDTO);
                        }
                    }

                }
            }
        }
        return BaseResponse.newSuccessInstance(trfConfigDTOList);
    }
}
