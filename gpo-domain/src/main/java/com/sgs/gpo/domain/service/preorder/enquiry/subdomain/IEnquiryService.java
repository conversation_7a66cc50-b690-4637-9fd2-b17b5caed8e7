package com.sgs.gpo.domain.service.preorder.enquiry.subdomain;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.order.enquiry.EnquiryBO;
import com.sgs.framework.model.order.enquiry.EnquiryIdBO;
import com.sgs.framework.open.platform.base.service.IBaseService;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryPO;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryQueryReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/7 12:00
 */
public interface IEnquiryService extends IBaseService<EnquiryBO, EnquiryPO, EnquiryIdBO, EnquiryQueryReq> {

    BaseResponse<List<EnquiryPO>> select(EnquiryIdReq enquiryIdReq);
}
