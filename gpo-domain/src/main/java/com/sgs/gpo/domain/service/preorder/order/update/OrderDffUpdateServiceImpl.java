package com.sgs.gpo.domain.service.preorder.order.update;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.annotation.Section;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.productinstance.ProductInstancePO;
import com.sgs.gpo.domain.convertor.ProductConvert;
import com.sgs.gpo.domain.service.preorder.factory.update.UpdateBaseService;
import com.sgs.gpo.domain.service.preorder.factory.update.context.UpdateContext;
import com.sgs.gpo.domain.service.preorder.productinstance.IProductInstanceService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Section(order = 0, attribute = "OrderDFF")
@AllArgsConstructor
@Service("orderDffUpdateServiceImpl")
public class OrderDffUpdateServiceImpl extends UpdateBaseService {
    private IProductInstanceService productInstanceService;

    @Override
    public BaseResponse update(UpdateContext updateContext) {
        Assert.notNull(updateContext);
        OrderBO orderBO = updateContext.getOrderBO();
        List<ProductInstancePO> allProductInstancePOList = new ArrayList<>();

        if (Func.isNotEmpty(orderBO)) {
            String orderId = orderBO.getId().getOrderId();
            if (Func.isNotEmpty(orderBO.getProduct())) {
                List<ProductInstancePO> productInstancePOList = ProductConvert.convertProductBOToProductPO(orderBO.getProduct());
                if (Func.isNotEmpty(productInstancePOList)) {
                    allProductInstancePOList.addAll(productInstancePOList);
                }
            }
            if (Func.isNotEmpty(orderBO.getSampleList())) {
                List<ProductInstancePO> productInstancePOList = ProductConvert.convertSampleBOToProductPO(orderBO.getSampleList());
                if (Func.isNotEmpty(productInstancePOList)) {
                    allProductInstancePOList.addAll(productInstancePOList);
                }
            }
            allProductInstancePOList.forEach(productInstancePO -> {
                if (Func.isEmpty(productInstancePO.getGeneralOrderID())) {
                    productInstancePO.setGeneralOrderID(orderId);
                }
            });
        }

        // 处理 allProductInstancePOList，保存到数据库
         productInstanceService.saveOrUpdateBatch(allProductInstancePOList);
        return BaseResponse.newSuccessInstance(true);
    }
}
