package com.sgs.gpo.domain.service.extservice.sci;

import com.google.common.collect.Lists;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.tool.utils.Func;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class SciTrfService {
    private static List<Integer> SCI_TRF_REF_SYSTEMS = Lists.newArrayList();

    static {
        SCI_TRF_REF_SYSTEMS.add(RefSystemIdEnum.Shein.getRefSystemId());
        SCI_TRF_REF_SYSTEMS.add(RefSystemIdEnum.SheinSupplier.getRefSystemId());
        SCI_TRF_REF_SYSTEMS.add(RefSystemIdEnum.SGSMart.getRefSystemId());
        SCI_TRF_REF_SYSTEMS.add(RefSystemIdEnum.LOWES.getRefSystemId());
    }

    public boolean checkSciTrf(Integer... refSystemIdList) {
        boolean result = false;
        if (Func.isEmpty(refSystemIdList)) {
            return result;
        }
        int paramSize = 0;
        for (Integer refSystemIdParam : refSystemIdList) {
            if (SCI_TRF_REF_SYSTEMS.contains(refSystemIdParam)) {
                paramSize += 1;
            }
        }
        result = (paramSize == refSystemIdList.length);
        return result;
    }

    public boolean checkSciTrf(Collection<Integer> refSystemIdList) {
        if (Func.isEmpty(refSystemIdList)) {
            return false;
        }
        return checkSciTrf(refSystemIdList.toArray(new Integer[]{}));
    }

    public boolean checkSciTrf(String... refSystemIdList) {
        boolean result = false;
        if (Func.isEmpty(refSystemIdList)) {
            return result;
        }
        int paramSize = 0;
        for (String refSystemIdParam : refSystemIdList) {
            if (Func.isEmpty(refSystemIdParam)) {
                continue;
            }
            if (SCI_TRF_REF_SYSTEMS.contains(Integer.valueOf(refSystemIdParam))) {
                paramSize += 1;
            }
        }
        result = (paramSize == refSystemIdList.length);
        return result;
    }

    public List<Integer> listSciTrfRefSystem() {
        return SCI_TRF_REF_SYSTEMS;
    }
}
