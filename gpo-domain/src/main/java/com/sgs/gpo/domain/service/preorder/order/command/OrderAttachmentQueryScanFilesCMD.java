package com.sgs.gpo.domain.service.preorder.order.command;

import com.alibaba.fastjson.JSONArray;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.test.testmatrix.TestMatrixBO;
import com.sgs.framework.model.test.testsample.TestSampleBO;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.domain.service.otsnotes.testline.command.TestLineQueryCMD;
import com.sgs.gpo.domain.service.preorder.order.context.OrderContext;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixService;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.ITestSampleService;
import com.sgs.gpo.domain.service.otsnotes.testline.context.TestLineQueryContext;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;
import com.sgs.gpo.integration.scantool.ScanToolClient;
import com.sgs.gpo.integration.scantool.req.ScanToolFilesReq;
import com.sgs.gpo.facade.model.scantool.rsp.ScanToolFile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@Scope(value = "prototype")
public class OrderAttachmentQueryScanFilesCMD extends BaseCommand<OrderContext<String>> {

    @Autowired
    private ScanToolClient scanToolClient;

    @Autowired
    private ITestSampleService testSampleService;

    @Autowired
    private ITestMatrixService testMatrixService;


    @Override
    public BaseResponse validateParam(OrderContext<String> context) {
        if(Func.isEmpty(context)||Func.isEmpty(context.getParam())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(OrderContext<String> context) {

        ScanToolFilesReq scanToolFilesReq = new ScanToolFilesReq();
        scanToolFilesReq.setUserName("robot");
        scanToolFilesReq.setPassword("Um9ib3QxMjNvYV9lcnBfZG93b3Jr");
        scanToolFilesReq.setSystemId(SgsSystem.GPO.getSgsSystemId());
       List<String> orderNoList = new ArrayList<>();
        orderNoList.add(context.getParam());
         scanToolFilesReq.setOrderNoList(orderNoList);
        BaseResponse<List<ScanToolFile>> clientRsp = scanToolClient.getScanToolFilesByOrderNo(scanToolFilesReq);
        if(Func.isEmpty(clientRsp) || Func.isEmpty(clientRsp.getData())){
           return BaseResponse.newSuccessInstance(true);
        }
        List<ScanToolFile> scanToolFiles = clientRsp.getData();
//
//        String scanFileListStr = "[{\"scanCode\":\"GZMR2403001108 T85908\",\"scanCodeType\":\"TestItemNo\",\"attchTypeId\":null,\"attchTypeName\":\"Testing Form\",\"fileTypeId\":null,\"fileTypeName\":\"PDF\",\"fileName\":\"gzmr2403001108 t85908-*********.pdf\",\"createdBy\":\"David-sy_zhou\",\"createDate\":\"2024-03-27 09:06:03\",\"filePath\":\"https://sgscnsacne2mrpsuat.blob.core.chinacloudapi.cn/sgsmr/gzmr2403001108%20t85908-*********.pdf\",\"sampleNo\":\"A,B,C,D\",\"testLine\":\"盐雾试验\",\"testStandard\":\"Q/JLY J7111591B-2022 第 5.2.2节\",\"fileUrl\":\"https://sgscnsacne2mrpsuat.blob.core.chinacloudapi.cn/sgsmr/gzmr2403001108%20t85908-*********.pdf?sig=EdxRvGrL4D%2FSwsfNlmnBidgDSAgsnM0FEBK7dRPDu3U%3D&se=2024-08-09T02%3A29%3A42Z&sv=2017-07-29&sp=r&sr=b\"},{\"scanCode\":\"GZMR2403001108 T89303\",\"scanCodeType\":\"TestItemNo\",\"attchTypeId\":null,\"attchTypeName\":\"Testing Form\",\"fileTypeId\":null,\"fileTypeName\":\"PDF\",\"fileName\":\"gzmr2403001108 t89303-*********.pdf\",\"createdBy\":\"David-sy_zhou\",\"createDate\":\"2024-03-27 09:05:59\",\"filePath\":\"https://sgscnsacne2mrpsuat.blob.core.chinacloudapi.cn/sgsmr/gzmr2403001108%20t89303-*********.pdf\",\"sampleNo\":\"A,B,C,D\",\"testLine\":\"盐雾\",\"testStandard\":\"GMW 3172-2022 第9.4.7节\",\"fileUrl\":\"https://sgscnsacne2mrpsuat.blob.core.chinacloudapi.cn/sgsmr/gzmr2403001108%20t89303-*********.pdf?sig=0h5XUQWTDsPWlJ%2BFmicEDx801DSHKOpSryOhe6TfwLM%3D&se=2024-08-09T02%3A29%3A42Z&sv=2017-07-29&sp=r&sr=b\"},{\"scanCode\":\"GZMR2403001108\",\"scanCodeType\":\"TestItemNo\",\"attchTypeId\":null,\"attchTypeName\":\"Testing Form\",\"fileTypeId\":null,\"fileTypeName\":\"PDF\",\"fileName\":\"gzmr2403001108-*********.pdf\",\"createdBy\":\"David-sy_zhou\",\"createDate\":\"2024-03-27 09:06:32\",\"filePath\":\"https://sgscnsacne2mrpsuat.blob.core.chinacloudapi.cn/sgsmr/gzmr2403001108-*********.pdf\",\"sampleNo\":null,\"testLine\":null,\"testStandard\":null,\"fileUrl\":\"https://sgscnsacne2mrpsuat.blob.core.chinacloudapi.cn/sgsmr/gzmr2403001108-*********.pdf?sig=o7x1TZpqqsmprYW1mMd%2FeS51FAQbP4lgV4uzxfcsOGg%3D&se=2024-08-09T02%3A29%3A42Z&sv=2017-07-29&sp=r&sr=b\"},{\"scanCode\":\"GZMR2403001108 T66739\",\"scanCodeType\":\"TestItemNo\",\"attchTypeId\":null,\"attchTypeName\":\"Testing Form\",\"fileTypeId\":null,\"fileTypeName\":\"PDF\",\"fileName\":\"gzmr2403001108 t66739-*********.pdf\",\"createdBy\":\"David-sy_zhou\",\"createDate\":\"2024-03-27 09:05:50\",\"filePath\":\"https://sgscnsacne2mrpsuat.blob.core.chinacloudapi.cn/sgsmr/gzmr2403001108%20t66739-*********.pdf\",\"sampleNo\":\"A,C\",\"testLine\":\"简支梁冲击强度\",\"testStandard\":\"GB/T 1303.2-2009 Section 5.4.2 & GB/T 1043.1-2008\",\"fileUrl\":\"https://sgscnsacne2mrpsuat.blob.core.chinacloudapi.cn/sgsmr/gzmr2403001108%20t66739-*********.pdf?sig=VB7Fc6oQyf55%2BxHR6VjBMWpbX8otzmS0zrKy0ou3MXQ%3D&se=2024-08-09T02%3A29%3A42Z&sv=2017-07-29&sp=r&sr=b\"},{\"scanCode\":\"GZMR2403001108 T66729\",\"scanCodeType\":\"TestItemNo\",\"attchTypeId\":null,\"attchTypeName\":\"Testing Form\",\"fileTypeId\":null,\"fileTypeName\":\"PDF\",\"fileName\":\"gzmr2403001108 t66729-*********.pdf\",\"createdBy\":\"David-sy_zhou\",\"createDate\":\"2024-03-27 09:05:54\",\"filePath\":\"https://sgscnsacne2mrpsuat.blob.core.chinacloudapi.cn/sgsmr/gzmr2403001108%20t66729-*********.pdf\",\"sampleNo\":\"A,B,C,D\",\"testLine\":\"拉伸测试\",\"testStandard\":\"<span style='color:red'>测试中文</span>\",\"fileUrl\":\"https://sgscnsacne2mrpsuat.blob.core.chinacloudapi.cn/sgsmr/gzmr2403001108%20t66729-*********.pdf?sig=Kxq%2F%2BOfFYT51AM662mJwXdHv%2FhPON708A%2FpsrgYKbfU%3D&se=2024-08-09T02%3A29%3A42Z&sv=2017-07-29&sp=r&sr=b\"},{\"scanCode\":\"GZMR2403001108 T85908\",\"scanCodeType\":\"TestItemNo\",\"attchTypeId\":null,\"attchTypeName\":\"Testing Form\",\"fileTypeId\":null,\"fileTypeName\":\"PDF\",\"fileName\":\"gzmr2403001108 t85908-*********.pdf\",\"createdBy\":\"David-sy_zhou\",\"createDate\":\"2024-03-27 09:06:13\",\"filePath\":\"https://sgscnsacne2mrpsuat.blob.core.chinacloudapi.cn/sgsmr/gzmr2403001108%20t85908-*********.pdf\",\"sampleNo\":\"A,B,C,D\",\"testLine\":\"盐雾试验\",\"testStandard\":\"Q/JLY J7111591B-2022 第 5.2.2节\",\"fileUrl\":\"https://sgscnsacne2mrpsuat.blob.core.chinacloudapi.cn/sgsmr/gzmr2403001108%20t85908-*********.pdf?sig=E3ZQ99KQrZhrjMJOJlTshoq5kUqK%2F86UpYhQHKF4dEc%3D&se=2024-08-09T02%3A29%3A42Z&sv=2017-07-29&sp=r&sr=b\"},{\"scanCode\":\"GZMR2403001108 T89303\",\"scanCodeType\":\"TestItemNo\",\"attchTypeId\":null,\"attchTypeName\":\"Testing Form\",\"fileTypeId\":null,\"fileTypeName\":\"PDF\",\"fileName\":\"gzmr2403001108 t89303-*********.pdf\",\"createdBy\":\"David-sy_zhou\",\"createDate\":\"2024-03-27 09:06:07\",\"filePath\":\"https://sgscnsacne2mrpsuat.blob.core.chinacloudapi.cn/sgsmr/gzmr2403001108%20t89303-*********.pdf\",\"sampleNo\":\"A,B,C,D\",\"testLine\":\"盐雾\",\"testStandard\":\"GMW 3172-2022 第9.4.7节\",\"fileUrl\":\"https://sgscnsacne2mrpsuat.blob.core.chinacloudapi.cn/sgsmr/gzmr2403001108%20t89303-*********.pdf?sig=D%2Ft8QBe3EfJDJ3wf71nqAjfY3PiRp%2FM5CL%2F2pV%2FCdr0%3D&se=2024-08-09T02%3A29%3A42Z&sv=2017-07-29&sp=r&sr=b\"}]";
//
//        List<ScanToolFile> scanToolFiles =  JSONArray.parseArray(scanFileListStr,ScanToolFile.class);
        //格式转大写
        for(ScanToolFile item : scanToolFiles){
            if(Func.isNotEmpty(item.getFileTypeName())){
                item.setFileTypeName(item.getFileTypeName().toUpperCase());
            }
        }
        context.setScanToolFiles(scanToolFiles);
        Set<String> testItemNo = scanToolFiles.stream().map(e -> e.getScanCode()).filter(Func::isNotEmpty).collect(Collectors.toSet());
        if(Func.isEmpty(testItemNo)){
            return BaseResponse.newSuccessInstance(true);
        }
        //查询TestLine和Standard
        Set<String> orderNoSet = orderNoList.stream().collect(Collectors.toSet());
        OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
        orderTestLineReq.setTestItemNoList(testItemNo);
        orderTestLineReq.setOrderNoList(orderNoSet);
        TestLineQueryContext<OrderTestLineReq, TestLineBO> testLineQueryContext = initContext(orderTestLineReq);
        BaseResponse response =  BaseExecutor.start(TestLineQueryCMD.class, testLineQueryContext);
        if(response.isSuccess() && Func.isNotEmpty(response.getData())){
            List<TestLineBO> testLineBOList = (List<TestLineBO>) response.getData();
            Map<String, List<TestLineBO>> testLineBOMap = testLineBOList.stream().collect(Collectors.groupingBy(TestLineBO::getTestItemNo));
            context.setTestLineBOMap(testLineBOMap);
            //聚合TestLineInstanceID
            Set<String> testInstanceIds = new HashSet<>();
            for(TestLineBO item : testLineBOList){
                if(Func.isNotEmpty(item) && Func.isNotEmpty(item.getTestLineInstanceId())){
                    testInstanceIds.add(item.getId());
                }
            }
            //查询TestMatrix
            TestMatrixQueryReq testMatrixQueryReq = new TestMatrixQueryReq();
            testMatrixQueryReq.setTestLineInstanceIdList(testInstanceIds);
            BaseResponse<List<TestMatrixBO>> testMatrixResponse = testMatrixService.queryV1(testMatrixQueryReq);
            List<TestMatrixBO> testMatrixBOS = testMatrixResponse.getData();
            if(Func.isNotEmpty(testMatrixBOS)){
                Map<String, List<TestMatrixBO>> testMatrixBOMap = testMatrixBOS.stream().collect(Collectors.groupingBy(TestMatrixBO::getTestLineInstanceId));
                context.setTestMatrixBOMap(testMatrixBOMap);
            }
            //组装testSampleid查询TestSample
            Set<String> testSampleInstanceIds = new HashSet<>();
            for(TestMatrixBO item : testMatrixBOS){
                if(Func.isNotEmpty(item) && Func.isNotEmpty(item.getTestSampleInstanceId())){
                    testSampleInstanceIds.add(item.getTestSampleInstanceId());
                }
            }
            //查询SampleNo信息
            TestSampleQueryReq testSampleQueryReq = new TestSampleQueryReq();
            testSampleQueryReq.setTestSampleInstanceIdList(testSampleInstanceIds);
            BaseResponse<List<TestSampleBO>> testSampleRes = testSampleService.queryV1(testSampleQueryReq);
            List<TestSampleBO> testSampleBOList = testSampleRes.getData();
            if(Func.isNotEmpty(testSampleBOList)){
                Map<String, List<TestSampleBO>> testSampleBOMap = testSampleBOList.stream().collect(Collectors.groupingBy(TestSampleBO::getTestSampleInstanceId));
                context.setTestSampleBOMap(testSampleBOMap);
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(OrderContext<String> context) {
        // 组装数据
        List<ScanToolFile> result = new ArrayList<>();
        List<ScanToolFile> scanToolFiles = context.getScanToolFiles();
        Map<String, List<TestLineBO>> testLineBOMap = context.getTestLineBOMap();
        Map<String, List<TestMatrixBO>> testMatrixBOMap = context.getTestMatrixBOMap();
        Map<String, List<TestSampleBO>> testSampleBOMap = context.getTestSampleBOMap();
        if(Func.isNotEmpty(scanToolFiles)) {
            for(ScanToolFile item : scanToolFiles){
                String testItemNo,testLineInstanceId;
                List<String> testSampleInstanceId = new ArrayList<>();
                if(Func.isNotEmpty(item) && Func.isNotEmpty(item.getScanCode()) && Func.isNotEmpty(testLineBOMap)){
                    testItemNo = item.getScanCode();
                    //查询TestLine和Standard进行拼装
                    if(testLineBOMap.containsKey(testItemNo)){
                        List<TestLineBO> listTestLine = testLineBOMap.get(testItemNo);
                        if(Func.isNotEmpty(listTestLine)){
                            item.setTestLine(listTestLine.get(0).getEvaluationName());
                            item.setTestStandard(listTestLine.get(0).getCitation().getCitationFullName());
                            testLineInstanceId = listTestLine.get(0).getTestLineInstanceId();
                            //根据TestLineInstanceId查找testMatrix
                            if(Func.isNotEmpty(testMatrixBOMap) && testMatrixBOMap.containsKey(testLineInstanceId)){
                                List<TestMatrixBO> listTestMatrix = testMatrixBOMap.get(testLineInstanceId);
                                if(Func.isNotEmpty(listTestMatrix)){
                                    for(int i = 0; i < listTestMatrix.size(); i++){
                                        testSampleInstanceId.add(listTestMatrix.get(i).getTestSampleInstanceId());
                                    }
                                }
                                //根据TestSampleInstanceId查找Sample
                                List<String> sampleNos = new ArrayList<>();
                                for(String s : testSampleInstanceId){
                                    if(Func.isNotEmpty(testSampleBOMap) && testSampleBOMap.containsKey(s)){
                                        List<TestSampleBO> listTestSample = testSampleBOMap.get(s);
                                        for(TestSampleBO sampleBO : listTestSample){
                                            sampleNos.add(sampleBO.getTestSampleNo());
                                        }
                                    }
                                }
                                Collections.sort(sampleNos);
                                item.setSampleNo(String.join(",",sampleNos));
                            }
                        }
                    }
                }
                result.add(item);
            }
        }
        return BaseResponse.newSuccessInstance(result);
    }

    private <Domain> TestLineQueryContext<OrderTestLineReq,Domain> initContext(OrderTestLineReq orderTestLineReq){
        TestLineQueryContext<OrderTestLineReq,Domain> testLineQueryContext = new TestLineQueryContext<>();
        testLineQueryContext.setParam(orderTestLineReq);
        testLineQueryContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        testLineQueryContext.setToken(SecurityContextHolder.getSgsToken());
        testLineQueryContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return testLineQueryContext;
    }
}
