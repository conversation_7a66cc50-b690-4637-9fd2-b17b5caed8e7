package com.sgs.gpo.domain.service.otsnotes.testline.command;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.gpo.domain.service.otsnotes.testline.context.TestLineAssignSampleContext;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineAssignSampleReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @title: TestLineAssignSampleCMD
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/12/4 19:05
 */
@Service
@Slf4j
public class TestLineAssignSampleCMD extends BaseCommand<TestLineAssignSampleContext<TestLineAssignSampleReq>> {

    @Override
    public BaseResponse validateParam(TestLineAssignSampleContext<TestLineAssignSampleReq> context) {
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(TestLineAssignSampleContext<TestLineAssignSampleReq> context) {
        //删除reportMatrix

        //删除Condition

        //删除conditionGroup

        //删除position

        //删除productAttr By Matrix

        //删除productAttr By LimitGroupId

        //删除limit

        //删除limitGroup

        //删除conclusion By Matrix

        //删除conclusion By PPSampleRelId

        //删除ppSampleRel

        //删除Condition Group

        //删除Matrix

        //添加ppSampleRel

        //添加matrix

        //添加reportMatrix

        //更新testLine的ConditionStatus

        return BaseResponse.newSuccessInstance(true);
    }
}
