package com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractTestLinePO;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractTestLineQueryReq;

import java.util.List;

public interface ISubcontractTestLineService extends IService<SubcontractTestLinePO> {

    BaseResponse<List<SubcontractTestLinePO>> query(SubcontractTestLineQueryReq subcontractTestLineQueryReq);

}
