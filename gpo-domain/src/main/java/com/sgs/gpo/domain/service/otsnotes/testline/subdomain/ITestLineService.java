package com.sgs.gpo.domain.service.otsnotes.testline.subdomain;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.test.testline.v2.TestLineBO;
import com.sgs.framework.model.test.testline.v2.TestLineIdBO;
import com.sgs.framework.open.platform.base.service.IBaseService;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testline.TestLineInstancePO;
import com.sgs.gpo.facade.model.otsnotes.job.req.JobAssignOwnerReq;
import com.sgs.gpo.facade.model.otsnotes.testline.dto.PPTestLineDTO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.QueryJobTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineBatchUpdateReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineExpectDueDateReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineLabInReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineLabOutReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineNameReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineRetestReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineUpdateDRFlagReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineUpdateStatusReq;
import com.sgs.gpo.facade.model.otsnotes.testline.rsp.TestLineNameNewRsp;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/6/7 12:00
 */
public interface ITestLineService extends IBaseService<TestLineBO,TestLineInstancePO, TestLineIdBO,OrderTestLineReq> {
    BaseResponse<List<com.sgs.framework.model.test.testline.TestLineBO>> selectPPTestLine(OrderTestLineReq orderTestLineReq);
    BaseResponse<List<TestLineNameNewRsp>> queryTestLineNameList(TestLineNameReq testLineNameReq);
    /**
     * 更新预计完成时间
     * @param expectDueDateList
     * @return
     */
    BaseResponse<Integer> updateExpectDueDate(List<TestLineExpectDueDateReq> expectDueDateList);
    BaseResponse<Integer> updateTestLineEngineerByJob(JobAssignOwnerReq jobAssignOwnerReq);
    BaseResponse<Boolean> updateTestLineForLabOut(TestLineLabOutReq testLineLabOutReq);
    BaseResponse<List<TestLineInstancePO>> queryTestLineBaseByJob(QueryJobTestLineReq jobTestLineReq);
    BaseResponse<List<TestLineInstancePO>> queryTestLineBase(Set<String> testLineInstanceIdList);
    BaseResponse<List<TestLineInstancePO>> queryTestLineBaseByOrderId(Set<String> orderInstanceIdList);
    BaseResponse<List<TestLineInstancePO>> batchUpdateTestLineStatus(TestLineUpdateStatusReq testLineUpdateStatusReq);
    /**
     * TL LabIn
     * @param testLineLabInReq
     * @return
     */
    BaseResponse<List<String>> labIn(TestLineLabInReq testLineLabInReq);

    /**
     * 回退LabIn
     * @return
     */
    BaseResponse<Integer> rollTestLineBackLabIn(TestLineLabInReq testLineLabInReq);

    /**
     * TL LabOut
     * @param TestLineLabOutReq
     * @return
     */
    BaseResponse<List<String>> labOut(TestLineLabOutReq testLineLabOutReq);

    /**
     * 更新TestLine Document ReviewFlag
     * @param testLineUpdateDRFlagReq
     * @return
     */
    BaseResponse<Integer> updateDRFlag(TestLineUpdateDRFlagReq testLineUpdateDRFlagReq);

    /**
     * 同步TestLine使用记录
     * @return
     */
    BaseResponse syncTestLineUsageRecord();

    BaseResponse<List<PPTestLineDTO>> selectBasePPTestLine(OrderTestLineReq orderTestLineReq);

    BaseResponse testLineRetest(TestLineRetestReq testLineRetestReq);

    BaseResponse updateTestLineRemark(TestLineBatchUpdateReq testLineBatchUpdateReq);
}
