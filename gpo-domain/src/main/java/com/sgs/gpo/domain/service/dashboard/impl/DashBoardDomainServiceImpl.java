package com.sgs.gpo.domain.service.dashboard.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.util.StringPool;
import com.sgs.framework.model.enums.TestLineStatus;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.domain.service.dashboard.IDashBoardDomainService;
import com.sgs.gpo.domain.service.dashboard.command.*;
import com.sgs.gpo.domain.service.dashboard.context.DashboardContext;
import com.sgs.gpo.domain.service.dashboard.context.DashboardReportTaskDetailContext;
import com.sgs.gpo.domain.service.otsnotes.testline.command.TestLinePageQueryCMD;
import com.sgs.gpo.dbstorages.mybatis.mapper.dashboard.DashboardMapper;
import com.sgs.gpo.facade.model.dashboard.req.DashBoardDetailQueryRep;
import com.sgs.gpo.facade.model.dashboard.rsp.*;
import com.sgs.gpo.facade.model.dashboard.req.DashBoardQueryRep;
import com.sgs.gpo.facade.model.dashboard.rsp.TodaySummaryRsp;
import com.sgs.gpo.domain.service.otsnotes.testline.context.TestLineQueryContext;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service
public class DashBoardDomainServiceImpl extends ServiceImpl<DashboardMapper, StatusTodaySummaryRsp> implements IDashBoardDomainService {

    @Override
    public BaseResponse<TodaySummaryRsp> queryTodaySummary(DashBoardQueryRep dashBoardQueryRep) {
        DashboardContext dashboardContext = new DashboardContext();
        dashboardContext.setParam(dashBoardQueryRep);
        dashboardContext.setUserInfo(SecurityContextHolder.getUserInfo());
        dashboardContext.setToken(SecurityContextHolder.getSgsToken());
        dashboardContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(TodaySummaryQueryCMD.class,dashboardContext);
    }

    @Override
    public BaseResponse<MatrixTodayProgressRsp> queryMatrixTodayProgress(DashBoardQueryRep dashBoardQueryRep) {
        DashboardContext dashboardContext = new DashboardContext();
        dashboardContext.setParam(dashBoardQueryRep);
        dashboardContext.setUserInfo(SecurityContextHolder.getUserInfo());
        dashboardContext.setToken(SecurityContextHolder.getSgsToken());
        dashboardContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(MatrixTodayProgressQueryCMD.class,dashboardContext);
    }

    @Override
    public BaseResponse<MatrixFuturePlanRsp> queryMatrixFuturePlan(DashBoardQueryRep dashBoardQueryRep) {
        DashboardContext dashboardContext = new DashboardContext();
        dashboardContext.setParam(dashBoardQueryRep);
        dashboardContext.setUserInfo(SecurityContextHolder.getUserInfo());
        dashboardContext.setToken(SecurityContextHolder.getSgsToken());
        dashboardContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(MatrixFuturePlanQueryCMD.class,dashboardContext);
    }

    @Override
    public BaseResponse<List<TestLineBO>> queryMatrixPlanDetail(DashBoardDetailQueryRep dashBoardDetailQueryRep,Integer page,Integer rows) {
        // 入参校验
        if(dashBoardDetailQueryRep==null){
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        /*if (Func.isEmpty(dashBoardDetailQueryRep.getEngineer()) ) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"engineer"});
        }*/
        if (!dashBoardDetailQueryRep.getDelay() && (Func.isEmpty(dashBoardDetailQueryRep.getStartDate())||Func.isEmpty(dashBoardDetailQueryRep.getEndDate()))) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"due date"});
        }
        if(dashBoardDetailQueryRep.getDelay()){
            dashBoardDetailQueryRep.setStartDate(null);
            dashBoardDetailQueryRep.setEndDate(new Date());
        }

        // 执行查询
        TestLineQueryContext<OrderTestLineReq, TestLineBO> testLinePageContext = new TestLineQueryContext<>();
        OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
        orderTestLineReq.setEngineer(StringPool.DASH.equals(dashBoardDetailQueryRep.getEngineer())?"":dashBoardDetailQueryRep.getEngineer());
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); //格式化
        if(Func.isNotEmpty(dashBoardDetailQueryRep.getStartDate())){
            orderTestLineReq.setTestDueDateFrom(format.format(dashBoardDetailQueryRep.getStartDate()));
        }
        orderTestLineReq.setTestDueDateTo(format.format(dashBoardDetailQueryRep.getEndDate()));
        orderTestLineReq.setDelay(dashBoardDetailQueryRep.getDelay());
        orderTestLineReq.setDashBoard(true);
        orderTestLineReq.setIsPending(false);
        orderTestLineReq.setTestLineStatusList(Sets.newHashSet(Func.toStr(TestLineStatus.Typing.getStatus()),Func.toStr(TestLineStatus.Submit.getStatus()),Func.toStr(TestLineStatus.Entered.getStatus())));
        orderTestLineReq.setLabSectionBaseIdList(dashBoardDetailQueryRep.getLabSectionBaseIdList());
        testLinePageContext.setParam(orderTestLineReq);
        testLinePageContext.setPage(page);
        testLinePageContext.setRows(rows);
        testLinePageContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        testLinePageContext.setUserInfo(SecurityContextHolder.getUserInfo());
        return BaseExecutor.start(TestLinePageQueryCMD.class,testLinePageContext);
    }

    @Override
    public BaseResponse<List<ReportTaskDetailRsp>> queryReportTaskDetail(DashBoardQueryRep dashBoardQueryRep, Integer page, Integer rows) {
        DashboardReportTaskDetailContext context = new DashboardReportTaskDetailContext();
        context.setParam(dashBoardQueryRep);
        context.setUserInfo(SecurityContextHolder.getUserInfo());
        context.setToken(SecurityContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        context.setPage(page);
        context.setRows(rows);
        return BaseExecutor.start(ReportTaskDetailCMD.class,context);
    }
}
