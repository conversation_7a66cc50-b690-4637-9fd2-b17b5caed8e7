package com.sgs.gpo.domain.service.otsnotes.testdata.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.i18n.util.MessageUtil;
import com.sgs.framework.model.common.object.ObjectIdBO;
import com.sgs.framework.model.common.object.StatusControlBO;
import com.sgs.framework.model.common.object.action.ObjectActionIdBO;
import com.sgs.framework.model.common.object.process.ObjectProcessBO;
import com.sgs.framework.model.common.object.process.ObjectProcessHeaderBO;
import com.sgs.framework.model.enums.TestDataHeaderStatus;
import com.sgs.framework.model.test.testdata.TestDataHeaderBO;
import com.sgs.framework.model.test.testdata.TestDataHeaderIdBO;
import com.sgs.framework.open.platform.model.req.ObjectStatusVerifyReq;
import com.sgs.framework.open.platform.model.req.OperationReq;
import com.sgs.framework.open.platform.model.req.StatusControlReq;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.MatrixActionEnums;
import com.sgs.gpo.core.enums.TLReportActionEnums;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportMatrixRelService;
import com.sgs.gpo.domain.service.otsnotes.testdata.ITestDataHeaderService;
import com.sgs.gpo.domain.service.otsnotes.testdata.ITestHeaderProcessService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.ITestMatrixProcessService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixService;
import com.sgs.gpo.domain.service.setting.object.IObjectProcessDomainService;
import com.sgs.gpo.facade.model.otsnotes.testdata.req.TLDataHeaderQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testdata.req.TLDataHeaderStatusUpdateReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixProcessReq;
import com.sgs.gpo.facade.model.report.bo.ReportMatrixRelBO;
import com.sgs.gpo.facade.model.report.req.ReportMatrixQueryReq;
import com.sgs.gpo.facade.model.setting.object.submodel.process.req.ObjectProcessStatusReq;
import com.sgs.gpo.facade.model.setting.object.submodel.process.req.ObjectProcessVerifyReq;
import com.sgs.otsnotes.facade.model.enums.MatrixStatus;
import lombok.extern.log4j.Log4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Log4j
public class TestDataHeaderProcessImpl implements ITestHeaderProcessService {
    @Autowired
    private ITestMatrixService testMatrixService;
    @Autowired
    private ITestMatrixProcessService testMatrixProcessService;
    @Autowired
    private ITestDataHeaderService testDataHeaderService;
    @Autowired
    private IObjectProcessDomainService objectProcessDomainService;
    @Autowired
    private MessageUtil messageUtil;
    @Autowired
    private IReportMatrixRelService reportMatrixRelService;


    @Override
    public BaseResponse<List<StatusControlBO<TestDataHeaderBO>>> statusControl(StatusControlReq<TestDataHeaderIdBO> statusControlReq) {
        List<StatusControlBO<TestDataHeaderBO>> statusControlBOList = Lists.newArrayList();
        // 查询当前的TestDataHeader状态
        List<TestDataHeaderBO> testDataHeaderBOList = this.queryStatus(statusControlReq.getIdList()).getData();
        // TestDataHeader状态规则校验
        if (Func.isNotEmpty(testDataHeaderBOList)) {
            testDataHeaderBOList.forEach(testDataHeaderBO -> {
                StatusControlBO statusControlBO = new StatusControlBO();
                statusControlBO.setDomain(testDataHeaderBO);
                ObjectProcessVerifyReq objectProcessVerifyReq = new ObjectProcessVerifyReq();
                objectProcessVerifyReq.setObjectBO(statusControlReq.getObjectBO());
                objectProcessVerifyReq.setTargetStatus(statusControlReq.getTargetStatusId());
                objectProcessVerifyReq.setLabId(SystemContextHolder.getLab().getLabId());
                ObjectActionIdBO action = new ObjectActionIdBO();
                action.setActionCode(statusControlReq.getActionCode());
                objectProcessVerifyReq.setAction(action);
                Map<String, Object> variable = Func.toMap(testDataHeaderBO);
                objectProcessVerifyReq.setVariable(variable);
                try {
                    BaseResponse<Boolean> verifyRsp = objectProcessDomainService.verify(objectProcessVerifyReq);
                    statusControlBO.setVerifyResult(verifyRsp.getData());
                    statusControlBO.setVerifyMessage(verifyRsp.getMessage());
                } catch (Exception e) {
                    statusControlBO.setVerifyResult(false);
                    statusControlBO.setVerifyMessage(e.getMessage());
                }
                statusControlBOList.add(statusControlBO);
            });
        }
        return BaseResponse.newSuccessInstance(statusControlBOList);
    }

    @Override
    public BaseResponse<List<TestDataHeaderBO>> statusVerify(ObjectStatusVerifyReq<TestDataHeaderBO, TestDataHeaderIdBO> orderStatusVerifyReq) {
        BaseResponse<List<ObjectProcessBO>> objectProcessRsp = this.getProcess();

        Set<Integer> statusList = objectProcessRsp.getData().stream().map(ObjectProcessBO::getHeader)
                .filter(header -> Func.isEmpty(orderStatusVerifyReq.getStatusTypeList()) ? true : orderStatusVerifyReq.getStatusTypeList().contains(header.getStatusType()))
                .filter(header -> Func.isEmpty(orderStatusVerifyReq.getEditableFlag()) ? true : Func.equalsSafe(orderStatusVerifyReq.getEditableFlag(), header.getEditableFlag()))
                .filter(header -> Func.isEmpty(orderStatusVerifyReq.getValidFlag()) ? true : Func.equalsSafe(orderStatusVerifyReq.getValidFlag(), header.getValidFlag()))
                .map(ObjectProcessHeaderBO::getStatusId).collect(Collectors.toSet());

        List<TestDataHeaderBO> errorList = orderStatusVerifyReq.getBoList().stream().filter(testDataHeaderBO -> statusList.contains(0)).collect(Collectors.toList());

        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setData(errorList);
        if (Func.isEmpty(errorList)) {
            baseResponse.setStatus(ResponseCode.SUCCESS.getCode());
        } else {
            baseResponse.setStatus(ResponseCode.FAIL.getCode());
            Set<String> errorIdList = errorList.stream().map(TestDataHeaderBO::getId).collect(Collectors.toSet());
            baseResponse.setMessage(messageUtil.get("common.process.status.not.allowed", new Object[]{errorIdList.toString()}));
        }
        return baseResponse;
    }

    @Override
    public BaseResponse<List<ObjectProcessBO>> getProcess() {
        ObjectProcessStatusReq orderObjectProcessStatusReq = new ObjectProcessStatusReq();
        orderObjectProcessStatusReq.setLabId(SystemContextHolder.getLab().getLabId());
        ObjectIdBO objectIdBO = new ObjectIdBO();
        objectIdBO.setObjectCode(Constants.OBJECT.TEST_DATA_HEADER.OBJECT_CODE);
        orderObjectProcessStatusReq.setObject(objectIdBO);
        BaseResponse<List<ObjectProcessBO>> orderObjectProcess =objectProcessDomainService.queryStatus(orderObjectProcessStatusReq);
        return orderObjectProcess;
    }

    @Override
    public BaseResponse<List<TestDataHeaderBO>> queryStatus(List<TestDataHeaderIdBO> testDataHeaderIdBOList) {
        Set<String> testDataHeaderIdList = testDataHeaderIdBOList.stream().map(TestDataHeaderIdBO::getId)
                .filter(Func::isNotEmpty)
                .collect(Collectors.toSet());
        Assert.isTrue(Func.isNotEmpty(testDataHeaderIdList), "common.param.miss", new Object[]{"ID"});
        TLDataHeaderQueryReq tlDataHeaderQueryReq = new TLDataHeaderQueryReq();
        tlDataHeaderQueryReq.setIdList(testDataHeaderIdList);
        return BaseResponse.newSuccessInstance(testDataHeaderService.selectTlDataEntryList(null, tlDataHeaderQueryReq));
    }



}
