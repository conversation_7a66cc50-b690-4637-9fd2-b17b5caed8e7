package com.sgs.gpo.domain.service.preorder.order;


import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.order.order.OrderBO;
import com.sgs.framework.model.order.v2.OrderIdBO;
import com.sgs.framework.model.order.v2.OrderQuotationBO;
import com.sgs.framework.model.quotation.QuotationBO;
import com.sgs.framework.model.quotation.QuotationHeadBO;
import com.sgs.framework.open.platform.base.service.IDomainService;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.quotation.QuotationHeaderPO;
import com.sgs.gpo.facade.model.payment.costlist.req.ActualFeeSaveReq;
import com.sgs.gpo.facade.model.payment.paymentlist.req.PaymentInvoiceDownloadReq;
import com.sgs.gpo.facade.model.preorder.order.req.*;
import com.sgs.gpo.facade.model.preorder.order.rsp.OrderEditableRsp;
import com.sgs.gpo.facade.model.preorder.order.rsp.OrderReferenceNoRsp;
import com.sgs.gpo.facade.model.preorder.productsample.req.QuotationQueryReq;
import com.sgs.gpo.facade.model.sci.req.GpoSciOrderToTrfReq;
import com.sgs.gpo.facade.model.sci.req.GpoSciTrfSyncReq;
import com.sgs.gpo.facade.model.sci.rsp.Order2TrfRsp;
import com.sgs.gpo.facade.model.sci.rsp.OrderSyncTrfRsp;


import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/5 15:31
 */
public interface IOrderDomainService extends IDomainService<com.sgs.framework.model.order.v2.OrderBO, OrderIdBO,OrderQueryReq> {

    /**
     * 查询订单列表
     *
     * @param orderQueryReq
     * @return
     */
    // TODO 为了兼容V2结构的查询，更新名称
    @Deprecated
    BaseResponse<List<OrderBO>> queryV1(OrderQueryReq orderQueryReq);

    BaseResponse<List<OrderEditableRsp>> queryOrderEditable(OrderEditableReq orderEditableReq);

    BaseResponse<Boolean> updatePaidStatus(OrderUpdatePaidStatusReq orderUpdatePaidStatusReq);

    /**
     * 更新 tb_order_report_receiver ReportInfoLock 为Lock
     * @param orderQueryReq
     * @return
     */
    BaseResponse<Boolean> lockOrderReportReceiver(OrderQueryReq orderQueryReq);
    BaseResponse<Order2TrfRsp> order2Trf(GpoSciOrderToTrfReq orderToTrfReq);
    BaseResponse<Boolean> unBindSciTrf(GpoSciTrfSyncReq sciTrfSyncReq);

    /**
     * 查询订单下的referenceNo
     * 输出外部系统使用（生成报告，打印等场景）
     */
    BaseResponse<List<OrderReferenceNoRsp>> queryOrderReferenceNo(List<String> orderNos);
    /**
     * 基于订单查询WorkFlow配置
     */
    BaseResponse<Integer> queryWorkFlowConfig(String orderNo);

    /**
     * 批量更新Order Payment Status
     */
    BaseResponse<Boolean> paymentStatusSync(OrderPaymentStatusSyncReq req);

    /**
     * 计算Order Payment Status
     */
    BaseResponse paymentStatusCalculate(CalcPaymentStatusReq calcPaymentStatusReq);

    BaseResponse<Integer> buildEnableDelivery(String orderNo);


    /**
     * 查询订单下的quotation列表
     * @param quotationQueryReq
     * @return
     */
    BaseResponse<List<QuotationHeadBO>> queryQuotationList(QuotationQueryReq quotationQueryReq);

    BaseResponse updateActualFeeByOrderNo(List<ActualFeeSaveReq> request);

    BaseResponse queryToSgsMartDetail(OrderToSgsMartReq orderToSgsMartReq);

    BaseResponse updateOrderInfoToSGSMart(OrderToSgsMartReq orderToSgsMartReq);

    BaseResponse updateInvoicePDF(PaymentInvoiceDownloadReq paymentInvoiceDownloadReq);

    /**
     * Order Confirm的流程
     * @param orderConfirmReq
     * @return
     */
    BaseResponse confirm(OrderConfirmReq orderConfirmReq);
}
