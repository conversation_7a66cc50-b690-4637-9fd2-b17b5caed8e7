package com.sgs.gpo.domain.service.otsnotes.testline.context;

import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.gpo.facade.model.otsnotes.testline.vo.TestLineEditVO;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: TestLineUpdateContext
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/28 10:39
 */
@Data
public class TestLineUpdateContext<Input> extends BaseContext<Input, TestLineEditVO> {

}
