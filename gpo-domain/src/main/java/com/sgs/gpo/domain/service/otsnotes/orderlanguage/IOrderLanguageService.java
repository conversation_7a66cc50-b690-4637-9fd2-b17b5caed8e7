package com.sgs.gpo.domain.service.otsnotes.orderlanguage;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.orderlanguage.OrderLanguagePO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/6/30 10:17
 */
public interface IOrderLanguageService extends IService<OrderLanguagePO> {
    /**
     * 基于OrderID 查询订单语言信息
     *
     * @param orderIdList
     * @return
     */
    BaseResponse<List<OrderLanguagePO>> queryByOrderId(Set<String> orderIdList);

}
