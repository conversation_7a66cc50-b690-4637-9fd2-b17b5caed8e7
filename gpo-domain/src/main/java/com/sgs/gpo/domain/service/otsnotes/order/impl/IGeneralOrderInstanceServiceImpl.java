package com.sgs.gpo.domain.service.otsnotes.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.order.GeneralOrderInstanceMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.order.GeneralOrderInstancePO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.domain.service.otsnotes.order.IGeneralOrderInstanceService;
import com.sgs.gpo.facade.model.otsnotes.order.OrderInstanceQueryReq;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @title: IGeneralOrderInstanceServiceImpl
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/10 15:12
 */
@Service
public class IGeneralOrderInstanceServiceImpl extends ServiceImpl<GeneralOrderInstanceMapper, GeneralOrderInstancePO> implements IGeneralOrderInstanceService {
    @Override
    public BaseResponse<List<GeneralOrderInstancePO>> select(OrderInstanceQueryReq orderInstanceQueryReq) {
        if (Func.isEmpty(orderInstanceQueryReq) || (Func.isEmpty(orderInstanceQueryReq.getOrderIdList()) && Func.isEmpty(orderInstanceQueryReq.getOrderNoList()))) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        if (Func.isNotEmpty(orderInstanceQueryReq.getOrderIdList())) {
            queryWrapper.in(GeneralOrderInstancePO.COLUMN.ID, orderInstanceQueryReq.getOrderIdList());
        }else if(Func.isNotEmpty(orderInstanceQueryReq.getOrderNoList())) {
            queryWrapper.in(GeneralOrderInstancePO.COLUMN.ORDER_NO, orderInstanceQueryReq.getOrderNoList());
        }
        return BaseResponse.newSuccessInstance(baseMapper.selectList(queryWrapper));
    }
}
