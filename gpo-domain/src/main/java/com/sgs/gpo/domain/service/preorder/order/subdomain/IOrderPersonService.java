package com.sgs.gpo.domain.service.preorder.order.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderPersonPO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;

import java.util.List;

public interface IOrderPersonService extends IService<OrderPersonPO> {
    /**
     *
     */
    BaseResponse<List<OrderPersonPO>> select(OrderIdReq orderIdReq);

}
