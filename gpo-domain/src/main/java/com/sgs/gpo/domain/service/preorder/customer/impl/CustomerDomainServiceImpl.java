package com.sgs.gpo.domain.service.preorder.customer.impl;

import com.sgs.customer.facade.domain.black.BlackDTO;
import com.sgs.customer.facade.domain.black.CheckCustomerBlackVO;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.facade.domain.req.BuParamReq;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.model.common.customer.CustomerBO;
import com.sgs.framework.model.enums.CustomerType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.domain.service.preorder.customer.ICustomerDomainService;
import com.sgs.gpo.facade.model.customer.req.CustomerBlackCheckReq;
import com.sgs.gpo.integration.customer.CustomerClient;
import com.sgs.gpo.integration.framework.FrameworkClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 *
 */
@Service
@Slf4j
public class CustomerDomainServiceImpl implements ICustomerDomainService {

    @Resource
    private FrameworkClient frameworkClient;
    @Resource
    private CustomerClient customerClient;

    @Override
    public BaseResponse checkIsBlack(CustomerBlackCheckReq blackCheckReq) {
        if (Func.isEmpty(blackCheckReq) || Func.isEmpty(blackCheckReq.getCustomerList())) {
            return BaseResponse.newSuccessInstance("");
        }
        // 查询BU Param 配置 -- 黑名单的客户是否允许开单
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setGroupCode(Constants.BU_PARAM.BACKLIST.GROUP);
        buParamReq.setParamCode(Constants.BU_PARAM.BACKLIST.BACKLISTCREATEORDER.CODE);
        buParamReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BuParamValueRsp buParamValueRsp = frameworkClient.getBuParam(buParamReq);
        if (Func.equalsSafe(buParamValueRsp.getParamValue(), "true")) {
            return BaseResponse.newSuccessInstance("");
        }
        // 不允许开单就需要校验客户数据
        for (CustomerBO customerBO : blackCheckReq.getCustomerList()) {
            if (Func.isEmpty(customerBO.getCustomerId()) || Func.isEmpty(customerBO.getCustomerUsage())) {
                continue;
            }
            CustomerType customerType = CustomerType.findStatus(customerBO.getCustomerUsage());
            if (customerType.check(CustomerType.Payer, CustomerType.Buyer, CustomerType.Applicant)) {
                CheckCustomerBlackVO checkCustomerReq = new CheckCustomerBlackVO();
                if (Func.isNotEmpty(customerBO.getBossSiteUserId())) {
                    checkCustomerReq.setSiteUsageId(String.valueOf(customerBO.getBossSiteUserId()));
                }
                if (Func.isNotEmpty(customerBO.getBossContactId())) {
                    checkCustomerReq.setBossContactID(String.valueOf(customerBO.getBossContactId()));
                }
                checkCustomerReq.setCustomerID(customerBO.getCustomerId());
                checkCustomerReq.setBuCode(ProductLineContextHolder.getProductLineCode());
                BlackDTO customerBlackDTO = customerClient.checkCustomerBlack(checkCustomerReq);
                //
                if (Func.isEmpty(customerBlackDTO)) {
                    continue;
                }
                //黑名单不允许开单
                if (customerBlackDTO.getCustomerBlackFlag()) {
                    return BaseResponse.newFailInstance(customerBlackDTO.getBlackMsg());
                }
                //applicant,payer还需要校验site,contact级别
                if (customerType.check(CustomerType.Applicant, CustomerType.Payer)) {
                    if (customerBlackDTO.getContactBlackFlag() || customerBlackDTO.getSiteUsageBlackFlag()) {
                        return BaseResponse.newFailInstance(customerBlackDTO.getBlackMsg());
                    }
                }
            }
        }
        return BaseResponse.newSuccessInstance("");
    }
}
