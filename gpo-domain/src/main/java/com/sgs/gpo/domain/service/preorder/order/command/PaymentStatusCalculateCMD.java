package com.sgs.gpo.domain.service.preorder.order.command;


import cn.hutool.core.util.NumberUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.model.enums.PaymentStatus;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.BossOrderInvoicePO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.PaidUpPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.SLOrderPO;
import com.sgs.gpo.domain.service.preorder.order.context.OrderContext;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IBossOrderInvoiceService;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IGeneralOrderService;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IPaidUpService;
import com.sgs.gpo.domain.service.preorder.order.subdomain.ISLOrderService;
import com.sgs.gpo.facade.model.enums.CaseType;
import com.sgs.gpo.facade.model.enums.ProductLines;
import com.sgs.gpo.facade.model.preorder.order.req.CalcPaymentStatusReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.integration.framework.FrameworkClient;
import com.sgs.gpo.integration.quotation.QuotationClient;
import com.sgs.gpo.integration.quotation.req.SwitchNewQuotationConfigReq;
import com.sgs.gpo.integration.quotation.rsp.SwitchNewQuotationConfigRsp;
import com.sgs.gpo.integration.sodapreorder.SodaPreorderClient;
import com.sgs.gpo.integration.sodapreorder.req.SodaPreOrderAmountReq;
import com.sgs.gpo.integration.sodapreorder.rsp.SodaPreOrderAmountRsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@Scope(value = "prototype")
public class PaymentStatusCalculateCMD extends BaseCommand<OrderContext<CalcPaymentStatusReq>> {

    @Autowired
    IGeneralOrderService generalOrderService;
    @Autowired
    ISLOrderService slOrderService;
    @Autowired
    IPaidUpService paidUpService;
    @Autowired
    IBossOrderInvoiceService bossOrderInvoiceService;
    @Autowired
    SodaPreorderClient sodaPreorderClient;
    @Autowired
    FrameworkClient frameworkClient;
    @Autowired
    QuotationClient quotationClient;

    @Override
    public BaseResponse validateParam(OrderContext<CalcPaymentStatusReq> context) {
        CalcPaymentStatusReq calcPaymentStatusReq = context.getParam();
        if (Func.isEmpty(calcPaymentStatusReq) || Func.isEmpty(calcPaymentStatusReq.getOrderNo()) || Func.isEmpty(calcPaymentStatusReq.getCaseType())) {
            log.warn("calc paymentStatus fail, param miss");
            return BaseResponse.newSuccessInstance(PaymentStatus.NA);
        }
        return BaseResponse.newSuccessInstance(true);
    }


    @Override
    public BaseResponse execute(OrderContext<CalcPaymentStatusReq> context) {
        CalcPaymentStatusReq calcPaymentStatusReq = context.getParam();
        SLOrderPO slOrderInfo = null;
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setOrderNoList(Sets.newHashSet(calcPaymentStatusReq.getOrderNo()));
        BaseResponse<List<GeneralOrderPO>> orderInfoBaseRes = generalOrderService.query2(orderQueryReq);
        //GPO2-15033
        SwitchNewQuotationConfigReq switchNewQuotationConfigReq = new SwitchNewQuotationConfigReq();
        String locationCode = context.getLab().getLocationCode();
        switchNewQuotationConfigReq.setLocationCode(locationCode);
        SwitchNewQuotationConfigRsp rsp = quotationClient.getSwitchNewQuotationConfig(switchNewQuotationConfigReq);
        boolean isNewQuotation = false;
        if(Func.isNotEmpty(rsp)){
            if(Func.equalsSafe(rsp.getLocationCode(),locationCode) && Func.equalsSafe(1,rsp.getNewQuotationSwitch())){
                isNewQuotation = true;
            }
        }
        if(Func.isNotEmpty(orderInfoBaseRes.getData())) {
            GeneralOrderPO orderPO = orderInfoBaseRes.getData().stream().filter(e -> e.getOrderNo().equals(calcPaymentStatusReq.getOrderNo())).findAny().orElse(null);
            OrderIdReq orderIdReq = new OrderIdReq();
            orderIdReq.setOrderIdList(Sets.newHashSet(orderPO.getId()));
            BaseResponse<List<SLOrderPO>> slOrderPORsp = slOrderService.select(orderIdReq);
            if(Func.isNotEmpty(slOrderPORsp) && Func.isNotEmpty(slOrderPORsp.getData())) {
                slOrderInfo = slOrderPORsp.getData().stream().filter(e -> e.getGeneralOrderID().equals(orderPO.getId())).findAny().orElse(null);
            }
        }
        String productLineCode = context.getProductLineCode();
        if (Func.isEmpty(productLineCode)) {
            productLineCode = ProductLineContextHolder.getProductLineCode();
        }
        String orderType = calcPaymentStatusReq.getCaseType();
        String orderNo = calcPaymentStatusReq.getOrderNo();
        BigDecimal orderTotalAmount = null;

        //没有水单,取Order的总金额
        //有水单,根据Order先查询到Paid Up,再查询Paid Up下关联的所有的Order的totalPrice和币种
        String orderCurrency = "";
        if (!(StringUtils.equalsIgnoreCase(productLineCode, ProductLines.SOFTLINE.getCode())) || (isNewQuotation && StringUtils.equalsIgnoreCase(productLineCode, ProductLines.SOFTLINE.getCode()))) {
            orderTotalAmount = generalOrderService.getAllTotalAmountByPaidUp(orderNo);
            if (Func.isNotEmpty(slOrderInfo)) {
                orderCurrency = slOrderInfo.getQuoteCurrencyID();
            }
        }
        List<PaidUpPO> paidUpPOList = paidUpService.getPaidUpByOrderNo(orderNo);
        BigDecimal paidUpAmount = BigDecimal.ZERO;
        if (Func.isNotEmpty(paidUpPOList)) {
            //SL处理逻辑，先查询出order对应的paidUp,再查询出paidUp下所有的Order,根据OrderNos调用接口获取totalAmount和Currency
            if (StringUtils.equalsIgnoreCase(productLineCode, ProductLines.SOFTLINE.getCode()) && !isNewQuotation) {
                List<String> paidUpIdList = paidUpPOList.stream().map(PaidUpPO::getId).collect(Collectors.toList());
                List<String> orderNoList = paidUpService.getOrderByPaidUpIds(paidUpIdList);
                if (Func.isNotEmpty(orderNoList)) {
                    BaseResponse<List<SodaPreOrderAmountRsp>> sodaPreOrderAmountRes = sodaPreorderClient.queryOrderAmount(new SodaPreOrderAmountReq(orderNoList));
                    if (Func.isNotEmpty(sodaPreOrderAmountRes) && Func.isNotEmpty(sodaPreOrderAmountRes.getData())) {
                        List<SodaPreOrderAmountRsp> preOrderAmountRspList = sodaPreOrderAmountRes.getData();
                        //计算OrderTotalAmount、获取当前Order的Currency
                        SodaPreOrderAmountRsp sodaPreOrderAmountRsp = preOrderAmountRspList.stream().filter(i -> Func.equals(i.getOrderNo(), orderNo)).findAny().orElse(null);
                        if (Func.isNotEmpty(sodaPreOrderAmountRsp)) {
                            orderCurrency = sodaPreOrderAmountRsp.getQuoteCurrency();
                        }
                        orderTotalAmount = preOrderAmountRspList.stream().map(SodaPreOrderAmountRsp::getAmount).filter(Func::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_DOWN);
                    }
                }
            }
            //找出与Order的币种不一致的水单
            //调用接口查询实时汇率，为避免重复调用，按水单币种分组
            Map<String, List<PaidUpPO>> currencyPaidUpMap = paidUpPOList
                    .parallelStream().collect(Collectors.groupingBy(PaidUpPO::getCurrencyCode));
            String conversionDate = DateUtil.format(new Date(), DateUtil.PATTERN_DATE);
            for (Map.Entry<String, List<PaidUpPO>> paidUpEntry : currencyPaidUpMap.entrySet()) {
                String paidUpCurrency = paidUpEntry.getKey();
                List<PaidUpPO> paidUpPOS = paidUpEntry.getValue();
                BigDecimal sameCurrencyTotalPaidUpAmount = paidUpPOS.stream().map(PaidUpPO::getRemittanceAmount).filter(Func::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_DOWN);
                if (Func.isNotEmpty(paidUpCurrency) && Func.isNotEmpty(orderCurrency) && !StringUtils.equalsIgnoreCase(paidUpCurrency, orderCurrency)) {
                    //水单币种与Order币种不一致，调用接口查询实时汇率
                    BigDecimal currencyExchangeRate = frameworkClient.getCurrencyExchangeRate(paidUpCurrency, orderCurrency, conversionDate);
                    BigDecimal afterRatePaidUpAmount = sameCurrencyTotalPaidUpAmount.multiply(currencyExchangeRate).setScale(2, BigDecimal.ROUND_HALF_UP);
                    paidUpAmount = paidUpAmount.add(afterRatePaidUpAmount);
                } else {
                    paidUpAmount = paidUpAmount.add(sameCurrencyTotalPaidUpAmount);
                }
            }
        } else if (StringUtils.equalsIgnoreCase(productLineCode, ProductLines.SOFTLINE.getCode()) && !isNewQuotation) {
            BaseResponse<List<SodaPreOrderAmountRsp>> sodaPreOrderAmountRes = sodaPreorderClient.queryOrderAmount(new SodaPreOrderAmountReq(Lists.newArrayList(orderNo)));
            if (Func.isEmpty(sodaPreOrderAmountRes) && Func.isNotEmpty(sodaPreOrderAmountRes.getData())) {
                SodaPreOrderAmountRsp sodaPreOrderAmountRsp = sodaPreOrderAmountRes.getData().get(0);
                if (Func.isNotEmpty(sodaPreOrderAmountRsp)) {
                    orderTotalAmount = sodaPreOrderAmountRsp.getAmount();
                }
            }
        }
        if (Func.isEmpty(orderTotalAmount)) {
            orderTotalAmount = BigDecimal.ZERO;
        }
//        log.info("{},calcPaymentStatus total amount:{}",orderNo,totalAmount);
        if (CaseType.check(orderType, CaseType.IDB, CaseType.IDN, CaseType.IDNTJ)) {
            return BaseResponse.newSuccessInstance(PaymentStatus.NA);
        } else if (CaseType.check(orderType, CaseType.Local, CaseType.OverseaPayment, CaseType.HKPayment) && (Func.isEmpty(orderTotalAmount) || NumberUtil.equals(orderTotalAmount, BigDecimal.ZERO))) {
            if (StringUtils.equalsIgnoreCase(productLineCode, ProductLines.SOFTLINE.getCode())) {
                return BaseResponse.newSuccessInstance(PaymentStatus.UN_PAID);
            } else {
                return BaseResponse.newSuccessInstance(PaymentStatus.NA);
            }
        }
        // 查询Order 下所有的Invoice,排除冲抵的Invoice
        BaseResponse<List<BossOrderInvoicePO>> orderInvoiceRes = bossOrderInvoiceService.getInvoiceByOrderNo(orderNo);
        List<BossOrderInvoicePO> invoiceList = orderInvoiceRes.getData();
        //查询Order下所有的PaidUp
        List<PaidUpPO> paidUpList = paidUpService.getPaidUpByOrderNo(orderNo);
        BigDecimal invoiceAmount = null;
        BigDecimal invoiceBalanceAmount = null;
        if (Func.isNotEmpty(invoiceList)) {
            // sum(invoiceAmount)
            invoiceAmount = invoiceList.stream().map(BossOrderInvoicePO::getInvoiceAmount).filter(Func::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
            // sum(invoiceBalanceAmount)
            invoiceBalanceAmount = invoiceList.stream().map(BossOrderInvoicePO::getBalanceAmount).filter(Func::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
        }


        log.info("【syncPayment】sync orderNo[{}] payment info : orderTotalAmt={},invoiceAmt={},invoiceBalanceAmount={},PaidUpAmount={}",
                orderNo,
                orderTotalAmount,
                invoiceAmount,
                invoiceBalanceAmount,
                paidUpAmount
        );
        if (Func.isNotEmpty(invoiceList) && isALLQuotationInvoiceAndPayment(orderNo, invoiceBalanceAmount)) {
            return BaseResponse.newSuccessInstance(PaymentStatus.PAID);
        } else if (Func.isNotEmpty(paidUpList) && NumberUtil.isGreaterOrEqual(paidUpAmount, orderTotalAmount)
                && (Func.isEmpty(invoiceList) || NumberUtil.equals(invoiceBalanceAmount, invoiceAmount))) {
            if (StringUtils.equalsIgnoreCase(productLineCode, ProductLines.SOFTLINE.getCode())) {
                return BaseResponse.newSuccessInstance(PaymentStatus.PAID);
            } else {
                return BaseResponse.newSuccessInstance(PaymentStatus.PRE_PAID);
            }
        } else if ((Func.isNotEmpty(invoiceList) && !NumberUtil.equals(invoiceAmount, invoiceBalanceAmount))
                || (Func.isNotEmpty(paidUpList) && NumberUtil.isLess(paidUpAmount, orderTotalAmount))) {
            return BaseResponse.newSuccessInstance(PaymentStatus.PART_PAID);
        } else {
            return BaseResponse.newSuccessInstance(PaymentStatus.UN_PAID);
        }
    }

    private boolean isALLQuotationInvoiceAndPayment(String orderNo, BigDecimal invoiceBalanceAmount) {
        // 查询不存在Invoice或Invoice被冲抵的Quotation数量
        Long noInvoiceQuotationCount = bossOrderInvoiceService.getNoInvoiceQuotationCount(orderNo);
        if ((Func.isEmpty(noInvoiceQuotationCount) || noInvoiceQuotationCount == 0) && invoiceBalanceAmount.compareTo(BigDecimal.ZERO) == 0) {
            return true;
        } else {
            return false;
        }
    }
}
