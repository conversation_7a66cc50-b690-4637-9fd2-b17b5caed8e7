package com.sgs.gpo.domain.service.preorder.productsample.context;


import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.common.dff.DFFTemplateBO;
import com.sgs.framework.model.common.productsample.ProductSampleBO;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryProductPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.productinstance.ProductInstancePO;
import com.sgs.gpo.facade.model.preorder.productsample.req.ProductSampleQueryReq;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/5 09:29
 */
@Data
public class ProductSampleContext extends BaseContext<ProductSampleQueryReq, ProductSampleBO> {

    private LanguageType primaryLanguage = LanguageType.English;
    private List<ProductInstancePO> productInstanceList;
    private List<EnquiryProductPO> enquiryProductList;
    private List<DFFTemplateBO> dffTemplateList;
}
