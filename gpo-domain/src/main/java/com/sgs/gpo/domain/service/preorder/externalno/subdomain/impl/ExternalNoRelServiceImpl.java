package com.sgs.gpo.domain.service.preorder.externalno.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.open.platform.base.service.impl.AbstractBaseService;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.externalno.ExternalNoRelMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.externalno.ExternalNoRelPO;
import com.sgs.gpo.domain.service.preorder.externalno.subdomain.IExternalNoRelService;
import com.sgs.gpo.facade.model.preorder.externalno.bo.ExternalNoBO;
import com.sgs.gpo.facade.model.preorder.externalno.bo.ExternalNoIdBO;
import com.sgs.gpo.facade.model.preorder.externalno.req.ExternalNoQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @title: ExternalNoRelServiceImpl
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/8 17:55
 */
@Service
@Slf4j
public class ExternalNoRelServiceImpl
        extends AbstractBaseService<ExternalNoBO, ExternalNoRelPO, ExternalNoIdBO,ExternalNoRelMapper, ExternalNoQueryReq>
        implements IExternalNoRelService {
    @Override
    public List<ExternalNoRelPO> select(ExternalNoQueryReq externalNoQueryReq) {
        if(Func.isEmpty(externalNoQueryReq) || (Func.isEmpty(externalNoQueryReq.getObjectIdList()) && Func.isEmpty(externalNoQueryReq.getExternalOrderNoList()))){
            return null;
        }
        LambdaQueryWrapper<ExternalNoRelPO> queryWrapper = new LambdaQueryWrapper<>();
        if(Func.isNotEmpty(externalNoQueryReq.getObjectIdList())){
            queryWrapper.in(ExternalNoRelPO::getGeneralOrderId,externalNoQueryReq.getObjectIdList());
        }
        if(Func.isNotEmpty(externalNoQueryReq.getExternalOrderNoList())){
            queryWrapper.in(ExternalNoRelPO::getExternalOrderNo, externalNoQueryReq.getExternalOrderNoList());
        }
        List<ExternalNoRelPO> externalNoRelPOS = baseMapper.selectList(queryWrapper);
        return externalNoRelPOS;
    }

    @Override
    public List<ExternalNoBO> convertToBO(Collection<ExternalNoRelPO> poList) {
        return null;
    }

    @Override
    public List<ExternalNoRelPO> convertToPO(Collection<ExternalNoBO> boList) {
        return null;
    }

    @Override
    public LambdaQueryWrapper<ExternalNoRelPO> createWrapper(ExternalNoQueryReq queryReq) {
        return null;
    }
}
