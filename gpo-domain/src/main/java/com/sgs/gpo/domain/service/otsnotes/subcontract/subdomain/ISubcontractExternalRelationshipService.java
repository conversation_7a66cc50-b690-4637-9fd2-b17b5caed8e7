package com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractExternalRelationshipPO;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractExternalRelQueryReq;

import java.util.List;

public interface ISubcontractExternalRelationshipService extends IService<SubcontractExternalRelationshipPO> {

    BaseResponse<List<SubcontractExternalRelationshipPO>> query(SubcontractExternalRelQueryReq subcontractExternalRelQueryReq);

}
