package com.sgs.gpo.domain.service.preorder.enquiry.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryReportReceiverPO;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;

import java.util.List;

public interface IEnquiryReportReceiverService extends IService<EnquiryReportReceiverPO> {
    BaseResponse<List<EnquiryReportReceiverPO>>  select(EnquiryIdReq enquiryIdReq);

}
