package com.sgs.gpo.domain.service.preorder.enquiry.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.enquiry.EnquiryCustomerMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryCustomerPO;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryCustomerService;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class EnquiryCustomerServiceImpl extends ServiceImpl<EnquiryCustomerMapper, EnquiryCustomerPO> implements
        IEnquiryCustomerService {


    @Override
    public BaseResponse<List<EnquiryCustomerPO>> select(EnquiryIdReq enquiryIdReq) {
        if (Func.isEmpty(enquiryIdReq) || Func.isEmpty(enquiryIdReq.getEnquiryIdList())) {
            return BaseResponse.newFailInstance("common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        }
        LambdaQueryWrapper<EnquiryCustomerPO> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EnquiryCustomerPO::getEnquiryId, enquiryIdReq.getEnquiryIdList());
        return BaseResponse.newSuccessInstance(baseMapper.selectList(wrapper));
    }

}
