package com.sgs.gpo.domain.service.setting.notifyconfig.command;

import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.common.contact.ContactPersonBO;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.model.enums.CustomerType;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.util.DateUtils;
import com.sgs.gpo.dbstorages.mybatis.model.notify.NotifyConfigPO;
import com.sgs.gpo.dbstorages.mybatis.model.notify.NotifyLogPO;
import com.sgs.gpo.domain.service.setting.notifyconfig.context.NotifyConfigContext;
import com.sgs.gpo.domain.service.setting.notifyconfig.subdomain.INotifyConfigService;
import com.sgs.gpo.domain.service.setting.notifyconfig.subdomain.INotifyLogService;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.domain.service.setting.buparam.IBUParam;
import com.sgs.gpo.facade.model.buparam.NotifyConfigRsp;
import com.sgs.gpo.facade.model.notifyconfig.req.NotifyConfigQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.integration.framework.NotificationClient;
import com.sgs.gpo.integration.framework.req.SendEmailReq;
import com.sgs.otsnotes.facade.model.rsp.email.SendEmailResultRsp;
import com.sgs.priceengine.facade.model.enums.CustomerUsage;
import com.sgs.testdatabiz.facade.model.enums.ContactUsage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class NotifyConfigSendEmailTaskCMD extends BaseCommand<NotifyConfigContext<NotifyConfigPO>>{
    @Autowired
    private INotifyConfigService  notifyConfigService;
    @Autowired
    private INotifyLogService notifyLogService;
    @Resource
    private NotificationClient notificationClient;
    @Autowired
    private IBUParam buParam;
    @Autowired
    private IOrderDomainService orderDomainService;

    @Override
    public BaseResponse validateParam(NotifyConfigContext<NotifyConfigPO> context) {
        Assert.isTrue(Func.isNotEmpty(context.getParam()), "common.miss", new Object[]{"param"});
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    @Transactional
    @DS(Constants.DB.COMMON)
    public BaseResponse execute(NotifyConfigContext<NotifyConfigPO> context) {
        List<NotifyConfigPO> notifyConfigPOList = context.getNotifyConfigPOList();
        if(Func.isNotEmpty(notifyConfigPOList)) {
            Date currDate = new Date();
            notifyConfigPOList.forEach(item -> {
                if(Func.isNotEmpty(item.getNotifyParams())) {
                    item.setRemark("Send email success");
                    //发送邮件
                    String emailSendResult = notificationClient.sendMailForGeneral(item.getNotifyParams());
                    //如果发送成功更新状态
                    if (Func.isNotEmpty(emailSendResult)) {
                        SendEmailResultRsp sendEmailResultRsp = JSON.parseObject(emailSendResult, SendEmailResultRsp.class);
                        if (sendEmailResultRsp.getResult()) {
                            item.setActiveIndicator(ActiveType.Disable.getStatus());
                        } else {
                            item.setRemark("Send email fail:" + emailSendResult);
                        }
                    }
                    item.setNotifyCount(item.getNotifyCount() + 1);
                    item.setModifiedDate(currDate);

                    NotifyLogPO notifyConfigLogPO = new NotifyLogPO();
                    notifyConfigLogPO.setId(IdUtil.uuId());
                    notifyConfigLogPO.setNotifyId(item.getId());
                    notifyConfigLogPO.setObjectNo(item.getObjectNo());
                    notifyConfigLogPO.setRequestBody(item.getNotifyParams());
                    notifyConfigLogPO.setResponseBody(emailSendResult);
                    notifyConfigLogPO.setNotifyTime(currDate);
                    notifyConfigLogPO.setCreatedDate(currDate);
                    notifyConfigLogPO.setModifiedDate(currDate);
                    //插入到日志表tb_object_notify_log
                    notifyLogService.save(notifyConfigLogPO);
                    //更新tb_object_notify_config
                    notifyConfigService.updateById(item);
                }
            });
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(NotifyConfigContext<NotifyConfigPO> context) {
        String productLineCode = context.getParam().getBuCode();
        String eventType = context.getParam().getEventType();
//取context.getParam()中的值

//        数据组装
        BaseResponse<List<NotifyConfigPO>> notifyConfigRes=addNotifyConfig(eventType,productLineCode);

//        Date currDate = new Date();
        //查询类型，下次通知时间小于当前时间，待运行的tb_object_notification_config
//        NotifyConfigQueryReq notifyConfigQueryReq = new NotifyConfigQueryReq();
//        notifyConfigQueryReq.setObjectType(NotifyObjectTypeEnum.PRELIM_REPORT.getName());
//        notifyConfigQueryReq.setEventTypeList(Lists.newArrayList(NotifyEventTypeEnum.CUSTOMER_CONFIRM.getName(), NotifyEventTypeEnum.ISSUE_REPORT.getName()));
//        notifyConfigQueryReq.setNotifyDateStart(DateUtils.addDay(currDate, -Constants.BU_PARAM.Notification.BEFORE_DAYS));//防止邮件发送失败
//        notifyConfigQueryReq.setNotifyDateEnd(currDate);
//        notifyConfigQueryReq.setActiveIndicator(ActiveType.Enable.getStatus());
//        notifyConfigQueryReq.setBuCode(productLineCode);
//        BaseResponse<List<NotifyConfigPO>> notifyConfigRes = notifyConfigService.select(notifyConfigQueryReq);
        if (notifyConfigRes.isSuccess() && Func.isNotEmpty(notifyConfigRes.getData())) {
            List<NotifyConfigPO> notifyConfigPOList = notifyConfigRes.getData();
            context.setNotifyConfigPOList(notifyConfigPOList);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    public BaseResponse addNotifyConfig(String sendType,String productLineCode) {
// 1. 查询buParam Notification配置
        BaseResponse<NotifyConfigRsp> notifyConfigParamRes = buParam.getNotifyConfig(sendType,productLineCode, null);
        if (notifyConfigParamRes.isFail()){
            return BaseResponse.newSuccessInstance(true);
        }
        NotifyConfigRsp notifyConfigParam=notifyConfigParamRes.getData();
        if(Func.isEmpty(notifyConfigParam)){
            return BaseResponse.newSuccessInstance(true);
        }
// 2. 如果开启DelayConfirm，则按intervalHours获取相应单据放入表中
// if (buParam != null && Boolean.TRUE.equals(buParam.getDelayConfirm())) {
        Integer intervalHours = notifyConfigParam.getIntervalHours();
        String objectType=notifyConfigParam.getObjectType();

//     // 查询New状态且创建时间超过指定间隔的订单 -改为查询confirm状态的订单
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setIntervalHours(intervalHours);
        orderQueryReq.setProductLineCode(productLineCode);
        List<com.sgs.framework.model.order.v2.OrderBO> orderBOList = orderDomainService.queryBO(orderQueryReq).getData();
        if(orderBOList.size()==0){
            return BaseResponse.newSuccessInstance(true);
        }
        // 提取所有订单号
        Set<String> allOrderNos = orderBOList.stream()
                .map(orderBO -> {
                    return orderBO.getHeader().getOrderNo();
                }).collect(Collectors.toSet());

        List<NotifyConfigPO> notifyConfigPOList = new ArrayList<NotifyConfigPO>();

// 1️ 批量查询：获取消息表中已存在的订单号
        NotifyConfigQueryReq notifyConfigQueryReq=new NotifyConfigQueryReq();
        notifyConfigQueryReq.setObjectNoList(allOrderNos);
        notifyConfigQueryReq.setObjectType(objectType);
        notifyConfigQueryReq.setEventType(sendType);

        BaseResponse<List<NotifyConfigPO>>  notifyConfigRes = notifyConfigService.select(notifyConfigQueryReq);
        if (notifyConfigRes.isSuccess() && Func.isNotEmpty(notifyConfigRes.getData())) {
            notifyConfigPOList = notifyConfigRes.getData();
        }

//  2 已存在的数据active_indicator改为1
        notifyConfigPOList.forEach(po -> po.setActiveIndicator(ActiveType.Enable.getStatus()));
//  3、不存在的数据新增
        List<NotifyConfigPO> finalNotifyConfigPOList = notifyConfigPOList;
        List<com.sgs.framework.model.order.v2.OrderBO> ordersToInsert = orderBOList.stream()
                .filter(order -> !finalNotifyConfigPOList.stream()
                        .anyMatch(po -> po.getObjectNo().equals(order.getHeader().getOrderNo())))
                .collect(Collectors.toList());
        if (Func.isNotEmpty(ordersToInsert)) {
            Date currDate = new Date();
            List<NotifyConfigPO> newConfigs = ordersToInsert.stream()
                    .map(order -> {
                        NotifyConfigPO notifyConfigPO = new NotifyConfigPO();
                        try {
                            notifyConfigPO.setId(IdUtil.uuId());
                            notifyConfigPO.setObjectId(order.getHeader().getOrderId());
                            notifyConfigPO.setObjectNo(order.getHeader().getOrderNo());
                            notifyConfigPO.setObjectType(objectType);
                            notifyConfigPO.setEventType(sendType);
                            notifyConfigPO.setLabId(order.getLab().getLabId());
                            notifyConfigPO.setLabCode(order.getLab().getLabCode());
                            notifyConfigPO.setBuId(order.getLab().getBuId());
                            notifyConfigPO.setBuCode(order.getLab().getBuCode());
                            notifyConfigPO.setLocationId(order.getLab().getLocationId());
                            notifyConfigPO.setLocationCode(order.getLab().getLocationCode());
                            notifyConfigPO.setNextNotifyDate(DateUtils.getDateAtMidnight(currDate));
                            notifyConfigPO.setNotifyCount(0);
                            notifyConfigPO.setCreatedBy("System");
                            notifyConfigPO.setCreatedDate(currDate);
                            notifyConfigPO.setModifiedBy("System");
                            notifyConfigPO.setModifiedDate(currDate);
                            notifyConfigPO.setActiveIndicator(ActiveType.Disable.getStatus());

                            Map<String, Object> params = new HashMap<>();
                            ContactPersonBO contactPersonBO = getPersonInfo(order, ContactUsage.SALES.getCode());
                            params.put("orderNo", Func.toStr(order.getHeader().getOrderNo()));
                            params.put("applicantName", getCustomerInfo(order, CustomerUsage.Applicant.getCode()));
                            params.put("labName", order.getLab().getLabCode());
                            params.put("sales", contactPersonBO.getRegionAccount());
                            params.put("toEmail", contactPersonBO.getContactEmail());
                            params.put("subject", "订单" + notifyConfigPO.getObjectNo() + "提交后超" + intervalHours + "小时未更新，请及时关注!");
//                        params.put("applicantNameEN", Func.toStr(expireDataDTO.getApplicantNameEN()));
//                        params.put("payerNameCN", Func.toStr(expireDataDTO.getPayerNameCN()));
//                        params.put("payerNameEN", Func.toStr(expireDataDTO.getPayerNameEN()));
//                        params.put("buyerNameCN", Func.toStr(expireDataDTO.getBuyerNameCN()));
//                        params.put("buyerNameEN", Func.toStr(expireDataDTO.getBuyerNameEN()));
                            String notifyParams = buildEmail(params, objectType, sendType);
                            notifyConfigPO.setNotifyParams(notifyParams);
                        } catch (Exception e) {
                            notifyConfigPO.setRemark(""+e.getMessage());
                            log.error("NotifyConfigDomainServiceImpl.add error:", e.getMessage());
                        }
                        return notifyConfigPO;
                    })
                    .collect(Collectors.toList());

            notifyConfigPOList.addAll(newConfigs); // 追加到原有列表
        }

        saveOrUpdateBatch(notifyConfigPOList);
        return BaseResponse.newSuccessInstance(notifyConfigPOList);
    }

    /**
     * 组装Email数据
     * @param params
     * @param objectType
     * @param eventType
     * @return
     */
    private String buildEmail(Map<String, Object> params, String objectType, String eventType){
        //邮件参数
//        Map<String, Object> params = new HashMap<>();
//        params.put("prelimResultNo", Func.toStr(expireDataDTO.getPrelimResultNo()));
//        params.put("orderNo", Func.toStr(expireDataDTO.getOrderNo()));
//        params.put("reportNo", Func.toStr(expireDataDTO.getReportNo()));
//        params.put("applicantNameCN", Func.toStr(expireDataDTO.getApplicantNameCN()));
//        params.put("applicantNameEN", Func.toStr(expireDataDTO.getApplicantNameEN()));
//        params.put("payerNameCN", Func.toStr(expireDataDTO.getPayerNameCN()));
//        params.put("payerNameEN", Func.toStr(expireDataDTO.getPayerNameEN()));
//        params.put("buyerNameCN", Func.toStr(expireDataDTO.getBuyerNameCN()));
//        params.put("buyerNameEN", Func.toStr(expireDataDTO.getBuyerNameEN()));

        SendEmailReq sendEmailReq = new SendEmailReq();
        sendEmailReq.setSystemId(Func.toStr(SgsSystem.GPO.getSgsSystemId()));
        sendEmailReq.setTemplateCode(objectType+"_"+eventType);
        List<String> emailToList = new ArrayList<>();
        if(Func.isNotEmpty(params.get("toEmail"))){
            emailToList.add((String) params.get("toEmail"));
        }

        sendEmailReq.setMailSubject((String) params.get("subject"));
        sendEmailReq.setMailTo(emailToList);
        sendEmailReq.setTemplateVariables(params);
//        sendEmailReq.setBuCode(expireDataDTO.getBuCode());
//        sendEmailReq.setLocationCode(expireDataDTO.getLocationCode());
        sendEmailReq.setSendBy(Constants.EMAIL.SEND_BY.SGS);
//        sendEmailReq.setTemplateType(Constants.EMAIL.TEMPLATE_TYPE.PRELIM_RESULT);
        sendEmailReq.setMailFrom(Constants.EMAIL.PUBLIC_EMAIL.PRE_ORDER);
        return JSON.toJSONString(sendEmailReq);
    }

    private String getCustomerInfo(com.sgs.framework.model.order.v2.OrderBO order, String type){
        String customerName = order.getCustomerList().stream()
                .filter(customer -> CustomerType.getCode(type).getStatus()==(customer.getCustomerUsage()))
                .map(com.sgs.framework.model.common.customer.CustomerBO::getCustomerName)
                .findFirst()
                .orElse(null);

        return customerName;
    }
    private ContactPersonBO getPersonInfo(com.sgs.framework.model.order.v2.OrderBO order, String type){
        ContactPersonBO personInfo = order.getContactPersonList().stream()
                .filter(person -> Func.equals(type,person.getContactUsage()))
//                .map(ContactPersonBO)
                .findFirst()
                .orElse(null);
        return personInfo;
    }
    @DS(Constants.DB.COMMON)
    public boolean saveOrUpdateBatch(List<NotifyConfigPO> notifyConfigPOList){
        return notifyConfigService.saveOrUpdateBatch(notifyConfigPOList);
    }
}
