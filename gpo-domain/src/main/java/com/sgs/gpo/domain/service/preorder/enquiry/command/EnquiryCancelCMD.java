package com.sgs.gpo.domain.service.preorder.enquiry.command;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.constant.KafkaTopicConsts;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.model.order.enquiry.EnquiryBO;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.BizLogConstant;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.EnquiryStatus;
import com.sgs.gpo.core.enums.KafkaActionType;
import com.sgs.gpo.core.enums.ObjectOperationTypeEnum;
import com.sgs.gpo.core.util.DateUtils;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryTrfRelationshipPO;
import com.sgs.gpo.dbstorages.mybatis.model.setting.operation.OperationHistoryPO;
import com.sgs.gpo.domain.service.preorder.enquiry.context.EnquiryContext;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryService;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryTrfRelationshipService;
import com.sgs.gpo.domain.service.extservice.sci.SciTrfService;
import com.sgs.gpo.domain.service.setting.operation.IOperationHistoryService;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryCancelReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryDelReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryQueryReq;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.info.BizLogInfo;
import com.sgs.preorder.core.common.EventType;
import com.sgs.preorder.core.common.PreEvent;
import com.sgs.preorder.core.common.StandardObjectType;
import com.sgs.preorder.core.order.dto.ExternalOrderDto;
import com.sgs.preorder.facade.model.common.KafkaMessage;
import com.sgs.preorder.facade.model.enums.OperationType;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class EnquiryCancelCMD extends BaseCommand<EnquiryContext<EnquiryCancelReq>> {


    @Resource
    private IEnquiryTrfRelationshipService trfRelationshipService;
    @Resource
    private IEnquiryService enquiryService;
    @Resource
    private SciTrfService sciTrfService;
    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;
    @Resource
    private IOperationHistoryService operationHistoryService;
    @Resource
    private BizLogClient bizLogClient;

    @Override
    public BaseResponse validateParam(EnquiryContext<EnquiryCancelReq> context) {
        // 入参校验
        EnquiryCancelReq enquiryCancelReq = context.getParam();
        Assert.isTrue(Func.isNotEmpty(enquiryCancelReq), "common.param.miss", new String[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(context.getLab()), "common.param.miss", new String[]{Constants.TERM.LAB.getCode()});
        Assert.isTrue(Func.isNotEmpty(enquiryCancelReq.getEnquiryId()), "common.param.miss", new String[]{"enquiryId"});
        Assert.isTrue(Func.isNotEmpty(enquiryCancelReq.getCancelReason()), "common.param.miss", new String[]{"cancelReason"});
        // 查询Enquiry详情
        EnquiryBO originalEnquiry = this.queryEnquiry(enquiryCancelReq.getEnquiryId());
        if (Func.isEmpty(originalEnquiry)) {
            return BaseResponse.newFailInstance("common.param.invalid", new String[]{"enquiryId"});
        }
        // Enquiry 状态校验
        Integer enquiryStatus = originalEnquiry.getHeader().getEnquiryStatus();
        if (EnquiryStatus.check(enquiryStatus, EnquiryStatus.Cancelled) || EnquiryStatus.check(enquiryStatus, EnquiryStatus.Closed)) {
            return BaseResponse.newFailInstance("common.process.status.not.allowed", null);
        }
        context.setOriginalEnquiry(originalEnquiry);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(EnquiryContext<EnquiryCancelReq> context) {
        // 查询绑定的TRF
        EnquiryIdReq enquiryIdReq = new EnquiryIdReq();
        enquiryIdReq.setEnquiryIdList(Sets.newHashSet(context.getParam().getEnquiryId()));
        BaseResponse<List<EnquiryTrfRelationshipPO>> trfListRes = trfRelationshipService.select(enquiryIdReq);
        if (Func.isNotEmpty(trfListRes.getData())) {
            List<EnquiryTrfRelationshipPO> enquiryTrfRelList = trfListRes.getData().parallelStream().filter(trfRef -> sciTrfService.checkSciTrf(trfRef.getRefSystemId()))
                    .collect(Collectors.toList());
            if (Func.isNotEmpty(enquiryTrfRelList)) {
                context.setEnquiryTrfList(enquiryTrfRelList);
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse execute(EnquiryContext<EnquiryCancelReq> context) {
        // 更新Enquiry状态为cancel
        EnquiryPO updateEnquiry = new EnquiryPO();
        updateEnquiry.setId(context.getParam().getEnquiryId());
        updateEnquiry.setEnquiryStatus(EnquiryStatus.Cancelled.getType());
        updateEnquiry.setModifiedDate(DateUtils.now());
        updateEnquiry.setModifiedBy(SystemContextHolder.getRegionAccount());
        enquiryService.updateById(updateEnquiry);
        // 清空sci trf 关系
        if (Func.isNotEmpty(context.getEnquiryTrfList())) {
            Set<Integer> refSystemIdList = context.getEnquiryTrfList().parallelStream().map(EnquiryTrfRelationshipPO::getRefSystemId).collect(Collectors.toSet());
            EnquiryDelReq enquiryDelReq = new EnquiryDelReq();
            enquiryDelReq.setEnquiryIdList(Sets.newHashSet(context.getParam().getEnquiryId()));
            enquiryDelReq.setRefSystemIdList(refSystemIdList);
            trfRelationshipService.delete(enquiryDelReq);
        }
        return BaseResponse.newSuccessInstance(true);
    }


    @Override
    public BaseResponse after(EnquiryContext<EnquiryCancelReq> context) {
        EnquiryBO enquiryBO = context.getOriginalEnquiry();
        List<ExternalOrderDto> externalOrderList = null;
        if(Func.isNotEmpty(context.getEnquiryTrfList())){
            externalOrderList = context.getEnquiryTrfList().parallelStream().
                    map(enquiryTrfRelationshipInfoPO -> {
                        ExternalOrderDto externalOrderDto = new ExternalOrderDto();
                        externalOrderDto.setExternalSubOrderNo(enquiryTrfRelationshipInfoPO.getRefNo());
                        externalOrderDto.setExternalOrderNo(enquiryTrfRelationshipInfoPO.getExternalOrderNo());
                        externalOrderDto.setRefSystemId(enquiryTrfRelationshipInfoPO.getRefSystemId());
                        externalOrderDto.setExtInfo(enquiryTrfRelationshipInfoPO.getExtData());
                        externalOrderDto.setExternalObjectId(enquiryTrfRelationshipInfoPO.getExtObjectId());
                        return externalOrderDto;
                    }).collect(Collectors.toList());
        }
        //发布Enquiry Cancelled事件
        if(Func.isEmpty(context.getParam().getOpSource())){
            PreEvent preEvent = new PreEvent();
            preEvent.setEventSource(StandardObjectType.Enquiry.getName());
            preEvent.setEventType(EventType.EnquiryCancelled.getTypeName());
            preEvent.setToken(SystemContextHolder.getSgsToken());
            preEvent.setEventSourceNo(enquiryBO.getId().getEnquiryNo());
            preEvent.setEventSourceId(enquiryBO.getId().getEnquiryId());
            preEvent.setOrderId(enquiryBO.getId().getEnquiryId());
            preEvent.setOrderNo(enquiryBO.getId().getEnquiryNo());
            preEvent.setReasonType(context.getParam().getCancelReason());
            preEvent.setReasonDetail(context.getParam().getCancelRemark());
            preEvent.setProductLineCode(SystemContextHolder.getBuCode());
            preEvent.setEventSourceStatus(EventType.Cancelled.getType());
            if(Func.isNotEmpty(externalOrderList)){
                preEvent.setExternalOrderList(externalOrderList);
            }
            KafkaMessage<PreEvent> message = new KafkaMessage();
            message.setAction(KafkaActionType.event.getCode());
            message.setSgsToken(preEvent.getToken());
            message.setProductLineCode(preEvent.getProductLineCode());
            message.setLabCode(SystemContextHolder.getLabCode());
            message.setData(preEvent);
            kafkaTemplate.send(KafkaTopicConsts.TOPIC_EVENT, context.getNewEnquiry().getEnquiryNo(), JSON.toJSONString(message));
        }
        // 维护Enquiry状态信息到Common库
        OperationHistoryPO operationHistory = new OperationHistoryPO();
        operationHistory.setId(IdUtil.uuId());
        operationHistory.setObjectId(enquiryBO.getId().getEnquiryId());
        operationHistory.setObjectNo(enquiryBO.getId().getEnquiryNo());
        operationHistory.setOperationType(OperationType.CancelEnquiry.getCode());
        operationHistory.setReasonType(context.getParam().getCancelReason());
        operationHistory.setRemark(context.getParam().getCancelRemark());
        operationHistory.setActiveIndicator(ActiveType.Enable.getStatus());
        operationHistory.setCreatedDate(DateUtil.now());
        operationHistory.setCreatedBy(SystemContextHolder.getRegionAccount());
        operationHistoryService.insert(Lists.newArrayList(operationHistory));
        // 记录Status log 记录到DB
        BizLogInfo bizLog = new BizLogInfo();
        bizLog.setBu(SystemContextHolder.getBuCode());
        bizLog.setLab(SystemContextHolder.getLab().getLocationCode());
        bizLog.setOpUser(SystemContextHolder.getRegionAccount());
        bizLog.setBizId(enquiryBO.getId().getEnquiryNo());
        bizLog.setBizOpType(BizLogConstant.ENQUIRY_STATUS_OPERATION_HISTORY);
        bizLog.setOpType(Constants.BIZ_LOG.OP_TYPE.ENQUIRY_CANCEL);
        bizLog.setNewVal(EnquiryStatus.Cancelled.getType());
        bizLog.setOriginalVal(enquiryBO.getHeader().getEnquiryStatus());
        bizLogClient.doSend(bizLog);
        // 记录操作 History 日志 记录到单据下,可以前台查看
        BizLogInfo operationBizLog = new BizLogInfo();
        operationBizLog.setBizId(enquiryBO.getId().getEnquiryNo());
        operationBizLog.setBu(SystemContextHolder.getBuCode());
        operationBizLog.setLab(SystemContextHolder.getLab().getLocationCode());
        operationBizLog.setBizOpType(BizLogConstant.ENQUIRY_OPERATION_HISTORY);
        operationBizLog.setOpType(ObjectOperationTypeEnum.ENQUIRY_CANCEL.getType());
        operationBizLog.setOpUser(SystemContextHolder.getRegionAccount());
        String newVal = "Cancel Reason:["+context.getParam().getCancelReason()+"],remark:["+context.getParam().getCancelRemark()+"]";
        operationBizLog.setNewVal(newVal);
        bizLogClient.doSend(operationBizLog);

        return BaseResponse.newSuccessInstance(true);
    }


    private EnquiryBO queryEnquiry(String enquiryId) {
        EnquiryContext<EnquiryQueryReq> context = new EnquiryContext<>();
        EnquiryQueryReq enquiryQueryReq = new EnquiryQueryReq();
        enquiryQueryReq.setEnquiryIdList(Lists.newArrayList(enquiryId));
        context.setParam(enquiryQueryReq);
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        context.setLab(SystemContextHolder.getLab());
        BaseResponse<List<EnquiryBO>> enquiryListRes = BaseExecutor.start(EnquiryQueryCMD.class, context);
        if (Func.isNotEmpty(enquiryListRes.getData())) {
            return enquiryListRes.getData().get(0);
        }
        return null;
    }
}
