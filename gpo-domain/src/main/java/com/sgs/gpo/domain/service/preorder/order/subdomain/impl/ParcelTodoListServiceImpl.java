package com.sgs.gpo.domain.service.preorder.order.subdomain.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.ParcelTodoListMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.ParcelTodoListPO;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IParcelTodoListService;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class ParcelTodoListServiceImpl extends ServiceImpl<ParcelTodoListMapper, ParcelTodoListPO> implements IParcelTodoListService {

    public int updateInvoiceNoByOrderNoBatch(List<ParcelTodoListPO> parcelTodoListPOList){
        return baseMapper.updateInvoiceNoByOrder<PERSON>o<PERSON><PERSON>(parcelTodoListPOList);
    }
}
