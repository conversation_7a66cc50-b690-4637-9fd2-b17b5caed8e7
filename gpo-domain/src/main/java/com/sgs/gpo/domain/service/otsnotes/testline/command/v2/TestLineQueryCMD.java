package com.sgs.gpo.domain.service.otsnotes.testline.command.v2;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.enums.YesOrNo;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.TestLineStatus;
import com.sgs.framework.model.order.v2.OrderIdRelBO;
import com.sgs.framework.model.test.analyte.v2.AnalyteBO;
import com.sgs.framework.model.test.analyte.v2.AnalyteHeaderBO;
import com.sgs.framework.model.test.analyte.v2.AnalyteIdBO;
import com.sgs.framework.model.test.analyte.v2.AnalyteResultBO;
import com.sgs.framework.model.test.analyte.v2.AnalyteResultLanguageBO;
import com.sgs.framework.model.test.analyte.v2.AnalyteResultOptionBO;
import com.sgs.framework.model.test.analyte.v2.AnalyteResultOptionLanguageBO;
import com.sgs.framework.model.test.pp.pptestline.v2.PPTestLineIdBO;
import com.sgs.framework.model.test.pp.pptestline.v2.PPTestLineRelBO;
import com.sgs.framework.model.test.pp.pptestline.v2.PPTestLineRelHeaderBO;
import com.sgs.framework.model.test.pp.pptestline.v2.RootPPRelBO;
import com.sgs.framework.model.test.testline.v2.TestLineBO;
import com.sgs.framework.model.test.testline.v2.TestLineChildrenBO;
import com.sgs.framework.model.test.testline.v2.TestLineHeaderBO;
import com.sgs.framework.model.test.testline.v2.TestLineIdBO;
import com.sgs.framework.model.test.testline.v2.TestLineParallelBO;
import com.sgs.framework.model.test.testline.v2.TestLineParentBO;
import com.sgs.framework.model.test.testline.v2.TestLineRelationshipBO;
import com.sgs.framework.model.test.testline.v2.TestLineSampleBO;
import com.sgs.framework.model.test.testline.v2.TestLineSubcontractBO;
import com.sgs.framework.model.trims.labsection.LabSectionBO;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.domain.service.otsnotes.testline.command.PPTestLineQueryCMD;
import com.sgs.gpo.domain.service.otsnotes.testline.context.TestLineQueryContext;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.ITestSampleService;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestAnalyteResultLangRsp;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestAnalyteResultOptionLangRsp;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestAnalyteResultOptionRsp;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestAnalyteResultRsp;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestLineAnalyteRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 查询并返回标准的V2结构的TestLine数据集合
 * TODO 目前是根据第一版BO转换出新的标准结构
 */
@Primary
@Service("TestLineQueryCMDV2")
@Slf4j
@Deprecated
//TOBE 废弃，使用PPTestLineQueryCMD
public class TestLineQueryCMD extends BaseCommand<TestLineQueryContext<OrderTestLineReq, com.sgs.framework.model.test.testline.TestLineBO>> {

    @Autowired
    private ITestSampleService testSampleService;

    @Override
    public BaseResponse validateParam(TestLineQueryContext context) {
        Assert.isTrue(Func.isNotEmpty(context.getParam()), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        //TODO TestLine 查询必填条件校验
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(TestLineQueryContext<OrderTestLineReq, com.sgs.framework.model.test.testline.TestLineBO> context) {
        // 查询PPTestLine集合
        TestLineQueryContext<OrderTestLineReq, com.sgs.framework.model.test.testline.TestLineBO> testLineQueryContext
                = new TestLineQueryContext<>();
        testLineQueryContext.setParam(context.getParam());
        testLineQueryContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        testLineQueryContext.setToken(SecurityContextHolder.getSgsToken());
        testLineQueryContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<List<com.sgs.framework.model.test.testline.TestLineBO>> ppTestLineRes;
        if(context.getParam().isMergePP()){
            ppTestLineRes = BaseExecutor.start(com.sgs.gpo.domain.service.otsnotes.testline.command.TestLineQueryCMD.class, testLineQueryContext);
        }else {
            ppTestLineRes = BaseExecutor.start(PPTestLineQueryCMD.class, testLineQueryContext);
        }

        if (ppTestLineRes.isSuccess() && Func.isNotEmpty(ppTestLineRes.getData())) {
            context.setTestLineList(ppTestLineRes.getData());
        }
        context.setTestLineAnalyteRspList(testLineQueryContext.getTestLineAnalyteRspList());
        return super.before(context);
    }

    @Override
    public BaseResponse<List<TestLineBO>> buildDomain(TestLineQueryContext<OrderTestLineReq, com.sgs.framework.model.test.testline.TestLineBO> context) {
        if (Func.isEmpty(context.getTestLineList())) {
            return BaseResponse.newSuccessInstance(Lists.newArrayList());
        }
        // 转换标准对象
        List<TestLineBO> testLineBOList = Lists.newArrayList();
        context.getTestLineList().stream().forEach(localTestLine -> {
            TestLineBO testLineBO = new TestLineBO();
            //id
            TestLineIdBO id = new TestLineIdBO();
            Func.copy(localTestLine, id);
            testLineBO.setId(id);
            //relationship
            assembleRelationship(testLineBO, localTestLine);
            //ppTestLineRelList
            assemblePPTestLineRelList(testLineBO, localTestLine);
            //header
            testLineBO.setHeader(Func.copy(localTestLine, TestLineHeaderBO.class));
            //TODO 修改为读取规则
            testLineBO.getHeader().setEditableFlag(TestLineStatus.check(localTestLine.getTestLineStatus(),TestLineStatus.Typing,TestLineStatus.Entered)? YesOrNo.Yes.name() : YesOrNo.No.name());
            // languageList
            testLineBO.getHeader().setLanguageList(localTestLine.getLanguageList());
            //labSectionList
            testLineBO.setLabSectionList(Func.copy(localTestLine.getLabSectionList(), LabSectionBO.class, LabSectionBO.class));
            //citation
            testLineBO.setCitation(localTestLine.getCitation());
            //wi
            testLineBO.setWiList(localTestLine.getWiList());
            //analyte
            assembleAnalyte(testLineBO, localTestLine,context);
            // TODO condition

            testLineBOList.add(testLineBO);
        });
        return BaseResponse.newSuccessInstance(testLineBOList);
    }

    private void assembleRelationship(TestLineBO testLineBO, com.sgs.framework.model.test.testline.TestLineBO localTestLine) {
        TestLineRelationshipBO relationship = new TestLineRelationshipBO();
        // parent
        TestLineParentBO parent = new TestLineParentBO();
        OrderIdRelBO order = new OrderIdRelBO();
        order.setOrderNo(localTestLine.getOrderNo());
        parent.setOrder(order);
        // subcontract
        parent.setSubcontract(Func.copy(localTestLine.getSubcontractBO(), TestLineSubcontractBO.class));
        relationship.setParent(parent);
        // children
        TestLineChildrenBO children = new TestLineChildrenBO();
        relationship.setChildren(children);
        // parallel
        TestSampleQueryReq testSampleQueryReq = new TestSampleQueryReq();
        testSampleQueryReq.setTestLineInstanceId(Sets.newHashSet(testLineBO.getId().getTestLineInstanceId()));
        BaseResponse<List<TestLineSampleBO>> testSampleResponse = testSampleService.queryListByTestLine(testSampleQueryReq);
        if (testSampleResponse.isSuccess() && Func.isNotEmpty(testSampleResponse.getData())) {
            TestLineParallelBO parallel = new TestLineParallelBO();
            parallel.setTestSampleList(testSampleResponse.getData());
            relationship.setParallel(parallel);
        }
        testLineBO.setRelationship(relationship);
    }

    private void assemblePPTestLineRelList(TestLineBO testLineBO, com.sgs.framework.model.test.testline.TestLineBO localTestLine) {
        if (Func.isEmpty(localTestLine.getPpTestLineRelList())) {
            return;
        }
        List<PPTestLineRelBO> ppTestLineRelList = Lists.newArrayList();
        localTestLine.getPpTestLineRelList().stream().forEach(ppRel -> {
            PPTestLineRelBO ppTestLineRelBO = new PPTestLineRelBO();
            ppTestLineRelBO.setId(Func.copy(ppRel, PPTestLineIdBO.class));
            ppTestLineRelBO.setHeader(Func.copy(ppRel, PPTestLineRelHeaderBO.class));
            if(Func.isNotEmpty(ppRel.getPpNo())){
                ppTestLineRelBO.getId().setPpNo(ppRel.getPpNo());
            }
            ppTestLineRelBO.getHeader().setLanguageList(ppRel.getLanguageList());
            if(Func.isNotEmpty(ppRel.getExtFields())){
                JSONObject extFieldsJSON = JSONObject.parseObject(ppRel.getExtFields());
                if(Func.isNotEmpty(extFieldsJSON)){
                    RootPPRelBO rootPPRel  = new RootPPRelBO();
                    if(Func.isNotEmpty(extFieldsJSON.get("ppAid"))){
                        rootPPRel.setPpAid(Integer.valueOf(extFieldsJSON.get("ppAid").toString()));
                    }
                    if(Func.isNotEmpty(extFieldsJSON.get("ppConstructionId"))){
                        rootPPRel.setPpConstructionId(extFieldsJSON.get("ppConstructionId").toString());
                    }
                    ppTestLineRelBO.getHeader().setRootPP(rootPPRel);
                }
            }

            ppTestLineRelList.add(ppTestLineRelBO);
        });
        testLineBO.setPpTestLineRelList(ppTestLineRelList);
    }

    private void assembleAnalyte(TestLineBO testLineBO, com.sgs.framework.model.test.testline.TestLineBO localTestLine,TestLineQueryContext<OrderTestLineReq, com.sgs.framework.model.test.testline.TestLineBO> context) {
        if (Func.isEmpty(localTestLine.getAnalyteList())) {
            return;
        }
        List<TestLineAnalyteRsp> testLineAnalyteRspList = context.getTestLineAnalyteRspList();

        List<AnalyteBO> analyteList = Lists.newArrayList();
        for (com.sgs.framework.model.test.analyte.AnalyteBO analyte : localTestLine.getAnalyteList()) {
            AnalyteBO analyteBO = new AnalyteBO();
            analyteBO.setId(Func.copy(analyte, AnalyteIdBO.class));
            analyteBO.setHeader(Func.copy(analyte, AnalyteHeaderBO.class));
            analyteBO.getHeader().setLanguageList(analyte.getLanguageList());
            List<AnalyteResultBO> analyteResultList = new ArrayList<>();
            if(Func.isNotEmpty(testLineAnalyteRspList)){
                TestLineAnalyteRsp testLineAnalyteRsp = testLineAnalyteRspList.stream().filter(item -> Func.equalsSafe(analyte.getAnalyteId(),item.getTestAnalyteId()) && Func.equalsSafe(item.getTestLineVersionId(), testLineBO.getId().getTestLineVersionId())).findAny().orElse(null);
                if(Func.isNotEmpty(testLineAnalyteRsp)){
                    List<TestAnalyteResultRsp> testAnalyteResultRspList = testLineAnalyteRsp.getTestResults();
                    if(Func.isNotEmpty(testAnalyteResultRspList)){
                        for (TestAnalyteResultRsp testAnalyteResultRsp : testAnalyteResultRspList) {
                            AnalyteResultBO analyteResultBO = new AnalyteResultBO();
                            analyteResultBO.setResultBaseId(testAnalyteResultRsp.getResultBaseId());
                            analyteResultBO.setResultId(testAnalyteResultRsp.getResultId());
                            analyteResultBO.setResultType(testAnalyteResultRsp.getResultType());
                            List<TestAnalyteResultOptionRsp> resultOptionRspList = testAnalyteResultRsp.getResultOptions();
                            List<AnalyteResultOptionBO> resultOptionList = new ArrayList<>();
                            if(Func.isNotEmpty(resultOptionRspList)){
                                for (TestAnalyteResultOptionRsp testAnalyteResultOptionRsp : resultOptionRspList) {
                                    AnalyteResultOptionBO analyteResultOptionBO = new AnalyteResultOptionBO();
                                    analyteResultOptionBO.setOptionBaseId(testAnalyteResultOptionRsp.getOptionBaseId());
                                    analyteResultOptionBO.setOptionId(testAnalyteResultOptionRsp.getOptionId());
                                    analyteResultOptionBO.setOptionValue(testAnalyteResultOptionRsp.getOptionValue());
                                    List<TestAnalyteResultOptionLangRsp> testAnalyteResultOptionLangRspList = testAnalyteResultOptionRsp.getLanguages();
                                    List<AnalyteResultOptionLanguageBO> analyteResultOptionLanguageBOList = new ArrayList<>();
                                    if(Func.isNotEmpty(testAnalyteResultOptionLangRspList)){
                                        TestAnalyteResultOptionLangRsp testAnalyteResultOptionLangRspEn = testAnalyteResultOptionLangRspList.stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.English)).findAny().orElse(null);
                                        if(Func.isEmpty(testAnalyteResultOptionLangRspEn)){
                                            testAnalyteResultOptionLangRspEn = new TestAnalyteResultOptionLangRsp();
                                            testAnalyteResultOptionLangRspEn.setLanguageId(LanguageType.English.getLanguageId());
                                            testAnalyteResultOptionLangRspEn.setOptionValue(testAnalyteResultOptionRsp.getOptionValue());
                                            testAnalyteResultOptionLangRspList.add(testAnalyteResultOptionLangRspEn);
                                        }
                                        for (TestAnalyteResultOptionLangRsp testAnalyteResultOptionLangRsp : testAnalyteResultOptionLangRspList) {
                                            AnalyteResultOptionLanguageBO analyteResultOptionLanguageBO = new AnalyteResultOptionLanguageBO();
                                            analyteResultOptionLanguageBO.setOptionValue(testAnalyteResultOptionLangRsp.getOptionValue());
                                            analyteResultOptionLanguageBO.setLanguageId(testAnalyteResultOptionLangRsp.getLanguageId());
                                            analyteResultOptionLanguageBOList.add(analyteResultOptionLanguageBO);
                                        }
                                    }
                                    analyteResultOptionBO.setLanguageList(analyteResultOptionLanguageBOList);
                                    resultOptionList.add(analyteResultOptionBO);
                                }
                            }
                            analyteResultBO.setResultOptionList(resultOptionList);
                            List<TestAnalyteResultLangRsp> testAnalyteResultLangRspList = testAnalyteResultRsp.getLanguages();
                            List<AnalyteResultLanguageBO> languageList = new ArrayList<>();
                            if(Func.isNotEmpty(testAnalyteResultLangRspList)){
                                TestAnalyteResultLangRsp TestAnalyteResultLangRspEn = testAnalyteResultLangRspList.stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.English)).findAny().orElse(null);
                                if(Func.isEmpty(TestAnalyteResultLangRspEn)){
                                    TestAnalyteResultLangRspEn = new TestAnalyteResultLangRsp();
                                    TestAnalyteResultLangRspEn.setLanguageId(LanguageType.English.getLanguageId());
                                    TestAnalyteResultLangRspEn.setResultType(testAnalyteResultRsp.getResultType());
                                    testAnalyteResultLangRspList.add(TestAnalyteResultLangRspEn);
                                }
                                for (TestAnalyteResultLangRsp testAnalyteResultLangRsp : testAnalyteResultLangRspList) {
                                    AnalyteResultLanguageBO analyteResultLanguageBO = new AnalyteResultLanguageBO();
                                    analyteResultLanguageBO.setLangBaseId(testAnalyteResultLangRsp.getLangBaseId());
                                    analyteResultLanguageBO.setResultType(testAnalyteResultLangRsp.getResultType());
                                    analyteResultLanguageBO.setLanguageId(testAnalyteResultLangRsp.getLanguageId());
                                    languageList.add(analyteResultLanguageBO);
                                }
                            }
                            analyteResultBO.setLanguageList(languageList);
                            analyteResultList.add(analyteResultBO);
                        }
                    }
                }
            }
            analyteBO.getHeader().setAnalyteResultList(analyteResultList);
            //
            if(Func.isNotEmpty(analyteBO.getHeader()) &&
                    (Func.equalsSafe(analyteBO.getHeader().getReportUnit(), Constants.NO_UNIT)) ||
                    (Func.equalsSafe(analyteBO.getHeader().getReportUnit(),Constants.TEXT_ONLY))){
                analyteBO.getHeader().setReportUnit("");
                if(Func.isNotEmpty(analyteBO.getHeader().getLanguageList())){
                    analyteBO.getHeader().getLanguageList().stream().forEach(item->{
                        item.setReportUnit("");
                    });
                }
            }
            analyteList.add(analyteBO);
        }
        testLineBO.setAnalyteList(analyteList);
    }

    @Override
    public BaseResponse<List<TestLineBO>> execute(TestLineQueryContext<OrderTestLineReq, com.sgs.framework.model.test.testline.TestLineBO> context) {
        return this.buildDomain(context);
    }
}
