package com.sgs.gpo.domain.service.otsnotes.testsample.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.LimitGroupPO;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.LimitGroupQueryReq;

import java.util.List;

public interface ILimitGroupService extends IService<LimitGroupPO> {

    BaseResponse<List<LimitGroupPO>> select(LimitGroupQueryReq limitGroupQueryReq);
}
