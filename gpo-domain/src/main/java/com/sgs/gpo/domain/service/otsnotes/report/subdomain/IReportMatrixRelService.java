package com.sgs.gpo.domain.service.otsnotes.report.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportMatrixRelPO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.report.bo.ReportMatrixRelBO;
import com.sgs.gpo.facade.model.report.req.ReportIdReq;
import com.sgs.gpo.facade.model.report.req.ReportMatrixQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportMatrixSeqUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;

import java.util.List;

public interface IReportMatrixRelService extends IService<ReportMatrixRelPO> {

    /**
     *基于ReportNo查询 ReportMatrix
     */

    BaseResponse<List<ReportMatrixRelBO>> queryReportMatrix(ReportMatrixQueryReq reportMatrixQueryReq);

    BaseResponse<List<ReportMatrixRelPO>> select(ReportQueryReq reportQueryReq);

    BaseResponse<List<ReportMatrixRelPO>> query(ReportQueryReq reportQueryReq);
    BaseResponse<Long> updateReportMatrixReq(ReportMatrixSeqUpdateReq req);

    BaseResponse<List<ReportMatrixRelPO>> delete(ReportQueryReq newReportQueryReq);
}
