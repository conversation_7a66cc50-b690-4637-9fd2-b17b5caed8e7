package com.sgs.gpo.domain.service.otsnotes.testline.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.order.order.OrderBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.test.testline.v2.TestLineIdBO;
import com.sgs.framework.open.platform.base.service.impl.AbstractDomainService;
import com.sgs.framework.security.annotation.AccessRule;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.BizLogConstant;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineDomainService;
import com.sgs.gpo.domain.service.otsnotes.testline.command.*;
import com.sgs.gpo.domain.service.otsnotes.testline.context.*;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.ITestLineService;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.facade.model.otsnotes.testline.TestLinePageBO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.*;
import com.sgs.gpo.facade.model.otsnotes.testline.rsp.LabSectionItemRsp;
import com.sgs.gpo.facade.model.otsnotes.testline.vo.TestLineEditVO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.info.BizLogInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/6 12:29
 */
@Service
@Deprecated
// 迁移到gpo-domain-pptestline PPTestLineQueryCMD
public class TestLineDomainServiceImpl
        extends AbstractDomainService<com.sgs.framework.model.test.testline.v2.TestLineBO, TestLineIdBO, OrderTestLineReq, ITestLineService>
        implements ITestLineDomainService {
    private static final Logger log = LoggerFactory.getLogger(TestLineDomainServiceImpl.class);
    @Autowired
    private BizLogClient bizLogClient;
    @Autowired
    private IOrderDomainService orderDomainService;


    @Override
    public BaseResponse<List<TestLineBO>> queryTestLine(OrderTestLineReq orderTestLineReq) {
//        orderTestLineReq.setCrossDbQuery(false);
        TestLineQueryContext<OrderTestLineReq,TestLineBO> testLineQueryContext = initContext(orderTestLineReq);
        return BaseExecutor.start(TestLineQueryCMD.class, testLineQueryContext);
    }

    @Override
    @AccessRule
    public BaseResponse<IPage<TestLinePageBO>> queryTestLinePage(OrderTestLineReq orderTestLineReq, Integer page, Integer rows) {
        TestLineQueryContext<OrderTestLineReq, TestLineBO> testLinePageContext = initTestLinePageContext(orderTestLineReq, page, rows);
        return BaseExecutor.start(TestLinePageQueryCMD.class,testLinePageContext);
    }

    @Override
    public BaseResponse<List<TestLineBO>> queryPPTestLine(OrderTestLineReq orderTestLineReq) {
        TestLineQueryContext<OrderTestLineReq, TestLineBO> testLineQueryContext = initContext(orderTestLineReq);
        return BaseExecutor.start(PPTestLineQueryCMD.class, testLineQueryContext);
    }

    @Override
    public BaseResponse<List<TestLineBO>> queryTestLineLabSection(OrderTestLineReq orderTestLineReq) {
        TestLineQueryContext<OrderTestLineReq,TestLineBO> testLineQueryContext = initContext(orderTestLineReq);
        return BaseExecutor.start(TestLineLabSectionQueryCMD.class, testLineQueryContext);
    }

    private <Domain> TestLineQueryContext<OrderTestLineReq,Domain> initContext(OrderTestLineReq orderTestLineReq){
        TestLineQueryContext<OrderTestLineReq,Domain> testLineQueryContext = new TestLineQueryContext<>();
        testLineQueryContext.setParam(orderTestLineReq);
        testLineQueryContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        testLineQueryContext.setToken(SecurityContextHolder.getSgsToken());
        testLineQueryContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return testLineQueryContext;
    }

    private <Domain> TestLineQueryContext<OrderTestLineReq,Domain> initTestLinePageContext(OrderTestLineReq orderTestLineReq, Integer page, Integer rows){
        TestLineQueryContext<OrderTestLineReq, Domain> testLineQueryContext = this.initContext(orderTestLineReq);
        testLineQueryContext.setPage(page);
        testLineQueryContext.setRows(rows);
        return testLineQueryContext;
    }

    @Override
    public BaseResponse<List<LabSectionItemRsp>> updateLabSection(LabSectionUpdateReq labSectionUpdateReq) {
        if(Func.isEmpty(labSectionUpdateReq)){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        LabSectionUpdateContext labSectionUpdateContext = new LabSectionUpdateContext();
        labSectionUpdateContext.setParam(labSectionUpdateReq);
        labSectionUpdateContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        labSectionUpdateContext.setToken(SecurityContextHolder.getSgsToken());
        labSectionUpdateContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());

        String orderId = labSectionUpdateReq.getOrderId();

        Set<String> testLineInstanceIdList = labSectionUpdateReq.getTestLineLabSectionList().stream().map(LabSectionUpdateItemReq::getTestLineInstanceId).collect(Collectors.toSet());
        if(Func.isNotEmpty(testLineInstanceIdList)) {
            OrderTestLineReq testLineSearchReq = new OrderTestLineReq();
            testLineSearchReq.setOrderIdList(Sets.newHashSet(orderId));
            testLineSearchReq.setTestLineInstanceIdList(testLineInstanceIdList);

            List<TestLineBO> testLineList = this.queryTestLine(testLineSearchReq).getData();
            if (testLineInstanceIdList.size() != testLineList.size()) {
                return BaseResponse.newFailInstance("common.param.miss", null);
            } else {
                labSectionUpdateContext.setTestLineList(testLineList);
            }
            BaseResponse response = BaseExecutor.start(LabSectionUpdateCMD.class,labSectionUpdateContext);
            return response;
        }else {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"test line instance id"});
        }
    }

    @Override
    public BaseResponse<Boolean> testLineLabOut(TestLineLabOutReq testLineLabOutReq) {
        TestLineLabOutContext testLineQueryContext = new TestLineLabOutContext();
        testLineQueryContext.setParam(testLineLabOutReq);
        testLineQueryContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        testLineQueryContext.setToken(SecurityContextHolder.getSgsToken());
        if(Func.isNotEmpty(testLineLabOutReq.getProductLineCode())){
            testLineQueryContext.setProductLineCode(testLineLabOutReq.getProductLineCode());
        }else{
            testLineQueryContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        }
        return BaseExecutor.start(TestLineLabOutCMD.class, testLineQueryContext);
    }

    @Override
    public BaseResponse<Boolean> testLineSubmit(TestLineSubmitReq testLineSubmitReq) {
        TestLineSubmitContext testLineSubmitContext = new TestLineSubmitContext();
        testLineSubmitContext.setParam(testLineSubmitReq);
        testLineSubmitContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        testLineSubmitContext.setUserInfo(SecurityContextHolder.getUserInfo());
        testLineSubmitContext.setToken(SecurityContextHolder.getSgsToken());
        return BaseExecutor.start(TestLineSubmitCMD.class, testLineSubmitContext);
    }

//    @Override
//    public BaseResponse<List<TestLineDetailVO>> testLineDetail(OrderTestLineReq orderTestLineReq) {
//        TestLineDetailContext<OrderTestLineReq> testLineDetailContext = new TestLineDetailContext<>();
//        testLineDetailContext.setParam(orderTestLineReq);
//        testLineDetailContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
//        testLineDetailContext.setToken(SecurityContextHolder.getSgsToken());
//        testLineDetailContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
//        return BaseExecutor.start(TestLineDetailCMD.class,testLineDetailContext);
//    }

    @Override
    public BaseResponse<List<TestLineEditVO>> testLineEdit(OrderTestLineReq orderTestLineReq) {
        TestLineEditContext<OrderTestLineReq> testLineEditContext = new TestLineEditContext<>();
        testLineEditContext.setParam(orderTestLineReq);
        testLineEditContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        testLineEditContext.setToken(SecurityContextHolder.getSgsToken());
        testLineEditContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(TestLineEditCMD.class,testLineEditContext);
    }

    @Override
    public BaseResponse<Boolean> testLineUpdate(TestLineUpdateReq testLineUpdateReq) {
        TestLineUpdateContext testLineUpdateContext = new TestLineUpdateContext();
        testLineUpdateContext.setParam(testLineUpdateReq);
        testLineUpdateContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        testLineUpdateContext.setToken(SecurityContextHolder.getSgsToken());
        testLineUpdateContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(TestLineUpdateCMD.class,testLineUpdateContext);
    }

    @Override
    public BaseResponse<Boolean> citationUpdate(CitationUpdateReq citationUpdateReq) {
        CitationUpdateContext citationUpdateContext = new CitationUpdateContext();
        citationUpdateContext.setParam(citationUpdateReq);
        citationUpdateContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        citationUpdateContext.setToken(SecurityContextHolder.getSgsToken());
        citationUpdateContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(CitationUpdateCMD.class,citationUpdateContext);
    }

    @Override
    public BaseResponse<Boolean> conditionUpdate(ConditionUpdateReq conditionUpdateReq) {
        ConditionUpdateContext conditionUpdateContext = new ConditionUpdateContext();
        conditionUpdateContext.setParam(conditionUpdateReq);
        conditionUpdateContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        conditionUpdateContext.setToken(SecurityContextHolder.getSgsToken());
        conditionUpdateContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(ConditionUpdateCMD.class,conditionUpdateContext);
    }

    @Override
    public BaseResponse<Boolean> analyteUpdate(AnalyteUpdateReq analyteUpdateReq) {
        AnalyteUpdateContext analyteUpdateContext = new AnalyteUpdateContext();
        analyteUpdateContext.setParam(analyteUpdateReq);
        analyteUpdateContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        analyteUpdateContext.setToken(SecurityContextHolder.getSgsToken());
        analyteUpdateContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(AnalyteUpdateCMD.class,analyteUpdateContext);
    }


    @Override
    public BaseResponse<List<String>> labIn(TestLineLabInReq testLineLabInReq) {
        TestLineLabInContext testLineLabInContext = new TestLineLabInContext(testLineLabInReq);
        return BaseExecutor.start(TestLineLabInCMD.class, testLineLabInContext);
    }


    @Override
    public BaseResponse<List<com.sgs.framework.model.test.testline.v2.TestLineBO>> queryBO(OrderTestLineReq orderTestLineReq){
        // 入参不允许为空
        Assert.isTrue(Func.isNotEmpty(orderTestLineReq),"common.param.miss",new Object[]{Constants.TERM.REQUEST});
        TestLineQueryContext<OrderTestLineReq, com.sgs.framework.model.test.testline.TestLineBO> testLineQueryContext = new TestLineQueryContext<>();
        testLineQueryContext.setParam(orderTestLineReq);
        return BaseExecutor.start(com.sgs.gpo.domain.service.otsnotes.testline.command.v2.TestLineQueryCMD.class, testLineQueryContext);
    }
    /**
     * 更新TestLine Document ReviewFlag
     *
     * @param testLineUpdateDRFlagReq
     * @return
     */
    @Override
    public BaseResponse<Boolean> updateDRFlag(TestLineUpdateDRFlagReq testLineUpdateDRFlagReq) {
        Assert.isTrue(Func.isNotEmpty(testLineUpdateDRFlagReq) && Func.isNotEmpty(testLineUpdateDRFlagReq.getTestLineInstanceId()), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
        orderTestLineReq.setTestLineInstanceIdList(Sets.newHashSet(testLineUpdateDRFlagReq.getTestLineInstanceId()));
        BaseResponse<List<TestLineBO>> testLineBOListRes = this.queryTestLine(orderTestLineReq);
        Integer updateCount = baseService.updateDRFlag(testLineUpdateDRFlagReq).getData();
        try {
            if(Func.isNotEmpty(updateCount) && updateCount>0){
                if(Func.isNotEmpty(testLineBOListRes) && Func.isNotEmpty(testLineBOListRes.isSuccess()) && Func.isNotEmpty(testLineBOListRes.getData())){
                    //记录bizlog
                    TestLineBO testLineBO = testLineBOListRes.getData().get(0);
                    // 查询Order信息
                    OrderQueryReq orderQueryReq = new OrderQueryReq();
                    orderQueryReq.setOrderNoList(Sets.newHashSet(testLineBO.getOrderNo()));
                    List<OrderBO> orderBOList = orderDomainService.queryV1(orderQueryReq).getData();
                    OrderBO order = null;
                    if (Func.isNotEmpty(orderBOList)){
                        order = orderBOList.stream().findFirst().orElse(null);
                    }
                    BizLogInfo bizLog = new BizLogInfo();
                    bizLog.setBizOpType(BizLogConstant.TEST_HISTORY);
                    boolean labIsNotEmpty = Func.isNotEmpty(order) && Func.isNotEmpty(order.getLab());
                    bizLog.setBu((labIsNotEmpty && Func.isNotEmpty(order.getLab().getBuCode())) ? order.getLab().getBuCode() : SecurityUtil.getProductLine());
                    bizLog.setLab((labIsNotEmpty && Func.isNotEmpty(order.getLab().getLocationCode())) ? order.getLab().getLocationCode() : SecurityUtil.getLocation());
                    bizLog.setOpUser(SecurityContextHolder.getUserInfoFillSystem().getRegionAccount());
                    bizLog.setBizId( testLineBO.getOrderNo());
                    bizLog.setOpType("Edit DR");
                    bizLog.setNewVal("Testing");
                    bizLog.setOriginalVal("DR");
                    bizLogClient.doSend(bizLog);
                }
            }
        } catch (Exception e) {
            log.error("record edit dr bizlog error:{}",e);
        }
        return BaseResponse.newSuccessInstance(true);
    }

}
