package com.sgs.gpo.domain.service.otsnotes.report.command;

import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.model.enums.TestLineStatus;
import com.sgs.framework.tool.constant.SgsConstant;
import com.sgs.framework.tool.jackson.JsonUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportExtPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractTestLinePO;
import com.sgs.gpo.domain.service.otsnotes.report.context.ReportContext;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportExtService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportMatrixRelService;
import com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.ISubcontractTestLineService;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.ITestLineLabSectionService;
import com.sgs.gpo.facade.model.otsnotes.labSection.req.QueryLabSectionReq;
import com.sgs.gpo.facade.model.otsnotes.labSection.rsp.ReportTestLineLabSectionRsp;
import com.sgs.gpo.facade.model.report.bo.ReportMatrixRelBO;
import com.sgs.gpo.facade.model.report.req.ReportExtForTLUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportMatrixQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractTestLineQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: ReportLabSectionUpdateCMD
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2024/2/28 17:43
 */
@Primary
@Service
@Slf4j
public class ReportExtUpdateCMD extends BaseCommand<ReportContext<ReportExtForTLUpdateReq>> {

    @Autowired
    private ITestLineLabSectionService testLineLabSectionService;

    @Autowired
    private IReportExtService reportExtService;

    @Autowired
    private IReportMatrixRelService reportMatrixRelService;
    @Autowired
    private ISubcontractTestLineService subcontractTestLineService;


    @Override
    public BaseResponse validateParam(ReportContext<ReportExtForTLUpdateReq> context) {
        Assert.isTrue(Func.isNotEmpty(context.getParam()),"common.param.miss",new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(context.getParam().getReportIdList()) || Func.isNotEmpty(context.getParam().getTestLineInstanceIdList()),"common.param.miss",new Object[]{"reportId/testLineInstanceId"});
        log.info("ReportLabSectionUpdate req:{}", JsonUtil.toJson(context.getParam()));
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(ReportContext<ReportExtForTLUpdateReq> context) {
        UserInfo userInfoFillSystem = SystemContextHolder.getUserInfoFillSystem();
        String regionAccount =userInfoFillSystem.getRegionAccount();
        List<ReportTestLineLabSectionRsp> reportTestLineLabSectionRspList = context.getReportTestLineLabSectionRspList();
        if(Func.isEmpty(reportTestLineLabSectionRspList)){
            return BaseResponse.newSuccessInstance(true);
        }
        List<ReportMatrixRelBO> reportMatrixRelBOList = context.getReportMatrixRelBOList();
        List<SubcontractTestLinePO> subcontractTestLinePOList = context.getSubcontractTestLinePOList();
        List<ReportExtPO> reportExtPOList = context.getReportExtPOList();
        if(null==reportExtPOList){
            reportExtPOList = new ArrayList<>();
        }
        String finalRegionAccount = regionAccount;

        Set<String> matrixReportIdList = reportTestLineLabSectionRspList.stream().map(ReportTestLineLabSectionRsp::getReportId).distinct().filter(Func::isNotEmpty).collect(Collectors.toSet());
        List<ReportExtPO> saveOrUpdateList = new ArrayList<>();
        //查询report关联的Subcontract是否被分包出去
        for (String reportId : matrixReportIdList) {
            List<ReportTestLineLabSectionRsp> matchReportTLLabSectionList = reportTestLineLabSectionRspList.stream().filter(item -> Func.equalsSafe(item.getReportId(), reportId)).collect(Collectors.toList());
            ReportExtPO reportExtPO = reportExtPOList.stream().filter(item -> Func.equalsSafe(item.getReportId(), reportId)).findAny().orElse(null);
            String labSectionIds =  "";
            long unCompletedTLCount = 0;
            if(Func.isNotEmpty(matchReportTLLabSectionList)){
                labSectionIds = matchReportTLLabSectionList.stream().filter(item->Func.isNotEmpty(item.getLabSectionId())).map(ReportTestLineLabSectionRsp::getLabSectionId).distinct().sorted().map(Func::toStr).filter(Func::isNotEmpty).collect(Collectors.joining(","));
                unCompletedTLCount = matchReportTLLabSectionList.stream().filter(item -> Func.equalsSafe(item.getReportId(), reportId) && !TestLineStatus.check(item.getTestLineStatus(), TestLineStatus.Cancelled, TestLineStatus.Completed)).count();
            }
            if(Func.isEmpty(reportExtPO)){
                reportExtPO = new ReportExtPO();
                reportExtPO.setId(IdUtil.uuId());
                reportExtPO.setReportId(reportId);
                reportExtPO.setCreatedBy(finalRegionAccount);
                reportExtPO.setCreatedDate(new Date());
                reportExtPO.setActiveIndicator(ActiveType.Enable.getStatus());
            }
            reportExtPO.setModifiedBy(finalRegionAccount);
            reportExtPO.setModifiedDate(new Date());
            reportExtPO.setLabSectionId(Func.toStr(labSectionIds));
            if(unCompletedTLCount>0){
                reportExtPO.setTestCompletedFlag(0);
            }else{
                reportExtPO.setTestCompletedFlag(1);
            }
            int subContractTLFlag = SgsConstant.DB_NO;;
            if(Func.isNotEmpty(reportMatrixRelBOList) && Func.isNotEmpty(subcontractTestLinePOList)){
                List<String> reportTestLineInstanceIdList = reportMatrixRelBOList.parallelStream().filter(item -> Func.equalsSafe(item.getReportId(), reportId)).map(ReportMatrixRelBO::getTestLineInstanceId).collect(Collectors.toList());
                if(Func.isNotEmpty(reportTestLineInstanceIdList)){
                    long count = subcontractTestLinePOList.parallelStream().filter(item -> reportTestLineInstanceIdList.contains(item.getTestLineInstanceId())).count();
                    if(count>0){
                        subContractTLFlag = SgsConstant.DB_YES;
                    }
                }
            }
            reportExtPO.setSubcontractTlStatus(subContractTLFlag);
            saveOrUpdateList.add(reportExtPO);
        }
        reportExtService.saveOrUpdateBatch(saveOrUpdateList);
        return BaseResponse.newSuccessInstance(true);
    }


    @Override
    public BaseResponse before(ReportContext<ReportExtForTLUpdateReq> context) {
        ReportExtForTLUpdateReq reportExtForTLUpdateReq = context.getParam();
        //查询Report关联的LabSection
        Set<String> reportIdList = reportExtForTLUpdateReq.getReportIdList();
        Set<String> testLineInstanceIdList = reportExtForTLUpdateReq.getTestLineInstanceIdList();
        Set<String> allReportIdList = new HashSet<>();
        if(Func.isNotEmpty(reportIdList)){
            allReportIdList.addAll(reportIdList);
        }
        QueryLabSectionReq queryLabSectionReq = new QueryLabSectionReq();
        queryLabSectionReq.setReportIdList(reportExtForTLUpdateReq.getReportIdList());
        queryLabSectionReq.setTestLineInstanceIdList(reportExtForTLUpdateReq.getTestLineInstanceIdList());
        BaseResponse<List<ReportTestLineLabSectionRsp>> reportLabSectionRsp = testLineLabSectionService.queryReportTestLineLabSection(queryLabSectionReq);
        if(Func.isNotEmpty(reportLabSectionRsp) && reportLabSectionRsp.isSuccess()){
            context.setReportTestLineLabSectionRspList(reportLabSectionRsp.getData());
            if(Func.isEmpty(reportIdList) && Func.isNotEmpty(testLineInstanceIdList) && Func.isNotEmpty(reportLabSectionRsp.getData())){
                reportIdList  = reportLabSectionRsp.getData().stream().map(ReportTestLineLabSectionRsp::getReportId).filter(Func::isNotEmpty).collect(Collectors.toSet());
                queryLabSectionReq = new QueryLabSectionReq();
                queryLabSectionReq.setReportIdList(reportIdList);
                reportLabSectionRsp = testLineLabSectionService.queryReportTestLineLabSection(queryLabSectionReq);
                if(reportLabSectionRsp.isSuccess()){
                    reportIdList = reportLabSectionRsp.getData().stream().map(ReportTestLineLabSectionRsp::getReportId).collect(Collectors.toSet());
                    allReportIdList.addAll(reportIdList);
                    context.setReportTestLineLabSectionRspList(reportLabSectionRsp.getData());
                }
            }
        }

        if(Func.isNotEmpty(allReportIdList)){
            ReportQueryReq reportQueryReq = new ReportQueryReq();
            reportQueryReq.setReportIdList(allReportIdList);
            BaseResponse<List<ReportExtPO>> reportExtRsp = reportExtService.query(reportQueryReq);
            if(Func.isNotEmpty(reportExtRsp) && reportExtRsp.isSuccess()){
                context.setReportExtPOList(reportExtRsp.getData());
            }
        }
        ReportMatrixQueryReq reportMatrixQueryReq = new ReportMatrixQueryReq();
        reportMatrixQueryReq.setReportIdList(allReportIdList);
        BaseResponse<List<ReportMatrixRelBO>> reportMatrixRes = reportMatrixRelService.queryReportMatrix(reportMatrixQueryReq);
        List<ReportMatrixRelBO> reportMatrixRelBOList =  new ArrayList<>();
        List<SubcontractTestLinePO> subcontractTestLinePOList = new ArrayList<>();
        if(Func.isNotEmpty(reportMatrixRes) && reportMatrixRes.isSuccess()){
            reportMatrixRelBOList = reportMatrixRes.getData();
            Set<String> allTestLineInstanceIdList = reportMatrixRelBOList.stream().map(ReportMatrixRelBO::getTestLineInstanceId).collect(Collectors.toSet());
            SubcontractTestLineQueryReq subcontractTestLineQueryReq = new SubcontractTestLineQueryReq();
            subcontractTestLineQueryReq.setTestLineInstanceIdList(allTestLineInstanceIdList);
            BaseResponse<List<SubcontractTestLinePO>> subcontractTestLineRes = subcontractTestLineService.query(subcontractTestLineQueryReq);
            if(Func.isNotEmpty(subcontractTestLineRes) && subcontractTestLineRes.isSuccess()){
                subcontractTestLinePOList = subcontractTestLineRes.getData();
            }
        }
        context.setSubcontractTestLinePOList(subcontractTestLinePOList);
        context.setReportMatrixRelBOList(reportMatrixRelBOList);
        return BaseResponse.newSuccessInstance(true);
    }
}
