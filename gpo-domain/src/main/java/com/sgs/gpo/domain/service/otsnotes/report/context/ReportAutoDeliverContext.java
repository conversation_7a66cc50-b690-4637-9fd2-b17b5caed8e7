package com.sgs.gpo.domain.service.otsnotes.report.context;


import com.sgs.framework.core.base.PageBO;
import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.gpo.facade.model.report.bo.ReportAutoDeliverBO;
import lombok.Data;

import java.util.List;


@Data
public class ReportAutoDeliverContext<Input> extends BaseContext<Input, ReportBO> {
    private PageBO<ReportAutoDeliverBO> reportAutoDeliverBOPage;
    private List<ReportAutoDeliverBO> reportAutoDeliverBOList;


}
