package com.sgs.gpo.domain.service.otsnotes.testline.context;

import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.test.testline.v2.TestLineBO;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: ConditionUpdateContext
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/28 17:00
 */
@Data
public class ConditionUpdateContext<Input> extends BaseContext<Input, TestLineBO> {
}
