package com.sgs.gpo.domain.service.preorder.enquiry.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.enquiry.EnquiryParcelMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryParcelPO;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryParcelService;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class EnquiryParcelServiceImpl extends ServiceImpl<EnquiryParcelMapper, EnquiryParcelPO> implements IEnquiryParcelService {

    @Override
    public List<EnquiryParcelPO> select(EnquiryIdReq enquiryIdReq) {
        if (Func.isEmpty(enquiryIdReq) || Func.isEmpty(enquiryIdReq.getEnquiryIdList())) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<EnquiryParcelPO> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EnquiryParcelPO::getEnquiryId, enquiryIdReq.getEnquiryIdList());
        return baseMapper.selectList(wrapper);
    }
}
