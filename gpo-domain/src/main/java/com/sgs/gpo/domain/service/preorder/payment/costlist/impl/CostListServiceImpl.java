package com.sgs.gpo.domain.service.preorder.payment.costlist.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.payment.costlist.CostListMapper;
import com.sgs.gpo.domain.service.preorder.payment.costlist.ICostListService;
import com.sgs.gpo.facade.model.payment.costlist.bo.CostListBO;
import com.sgs.gpo.facade.model.payment.costlist.req.CostListQueryReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @title: CostListServiceImpl
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/5 14:08
 */
@Service
public class CostListServiceImpl implements ICostListService {
    @Autowired
    private CostListMapper costListMapper;

    @Override
    public BaseResponse selectCostListPageCount(CostListQueryReq costListQueryReq) {
        if(Func.isEmpty(costListQueryReq)){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        Long count = costListMapper.selectCostListPageCount(costListQueryReq);
        return  BaseResponse.newSuccessInstance(count);
    }

    @Override
    public BaseResponse selectCostListPage(Page page, CostListQueryReq costListQueryReq) {
        if(Func.isEmpty(costListQueryReq)){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        IPage<CostListBO> costListPage = costListMapper.selectCostListPage(page, costListQueryReq);
        return  BaseResponse.newSuccessInstance(costListPage);
    }
}
