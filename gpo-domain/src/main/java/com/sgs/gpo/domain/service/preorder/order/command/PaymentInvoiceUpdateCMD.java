package com.sgs.gpo.domain.service.preorder.order.command;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.attachment.OrderAttachmentPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.domain.service.preorder.attachment.IOrderAttachmentService;
import com.sgs.gpo.domain.service.preorder.order.context.OrderContext;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IGeneralOrderService;
import com.sgs.gpo.facade.model.payment.paymentlist.req.PaymentInvoiceDownloadReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.integration.backoffice.BackOfficeClient;
import com.sgs.gpo.integration.backoffice.req.QueryInvoicePDFReq;
import com.sgs.gpo.integration.backoffice.rsp.QueryInvoicePDFRsp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PaymentInvoiceUpdateCMD extends BaseCommand<OrderContext<PaymentInvoiceDownloadReq>> {

    @Autowired
    IGeneralOrderService generalOrderService;
    @Autowired
    IOrderAttachmentService orderAttachmentService;
    @Autowired
    BackOfficeClient backOfficeClient;

    @Override
    public BaseResponse validateParam(OrderContext<PaymentInvoiceDownloadReq> context) {
        PaymentInvoiceDownloadReq paymentInvoiceDownloadReq = context.getParam();
        Assert.isTrue(Func.isNotEmpty(paymentInvoiceDownloadReq), "common.param.miss", null);
        Assert.isTrue(Func.isNotEmpty(paymentInvoiceDownloadReq.getOrderInvoiceList()), "common.param.miss", null);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(OrderContext<PaymentInvoiceDownloadReq> context) {
        //聚合查询Order
        List<PaymentInvoiceDownloadReq.OrderInvoice> orderInvoiceList = context.getParam().getOrderInvoiceList();
        Set<String> orderNoList = orderInvoiceList.stream().map(e -> e.getOrderNo()).filter(Func::isNotEmpty).collect(Collectors.toSet());
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setOrderNoList(orderNoList);
        List<GeneralOrderPO> orderPOList = generalOrderService.query2(orderQueryReq).getData();
        if (Func.isNotEmpty(orderPOList)) {
            context.setGeneralOrderList(orderPOList);
        }
        //查询OrderAttachment
        Set<String> invoiceNoList = orderInvoiceList.stream().map(e -> e.getInvoiceNo()).filter(Func::isNotEmpty).collect(Collectors.toSet());
        if (Func.isNotEmpty(invoiceNoList)) {
            List<OrderAttachmentPO> attachmentPOList = orderAttachmentService.selectByInvoiceNo(invoiceNoList);
            if (Func.isNotEmpty(attachmentPOList)) {
                context.setOrderAttachmentList(attachmentPOList);
            }
        }
        return super.before(context);
    }

    @Override
    public BaseResponse execute(OrderContext<PaymentInvoiceDownloadReq> context) {
        Set<String> noPDFInvoiceList = new HashSet<>();
        List<PaymentInvoiceDownloadReq.OrderInvoice> orderInvoiceList = context.getParam().getOrderInvoiceList();
        List<OrderAttachmentPO> attachmentPOList = context.getOrderAttachmentList();
        List<GeneralOrderPO> orderPOList = context.getGeneralOrderList();
        if (Func.isEmpty(orderPOList)) {
            return BaseResponse.newSuccessInstance(true);
        }
        // Convert lists to maps for efficient lookup
        Map<String, List<OrderAttachmentPO>> attachmentPOMap = Func.isNotEmpty(attachmentPOList) ?
                attachmentPOList.stream().collect(Collectors.groupingBy(OrderAttachmentPO::getObjectID)) :
                Collections.emptyMap();
        for (PaymentInvoiceDownloadReq.OrderInvoice orderInvoice : orderInvoiceList) {
            String invoiceNo = orderInvoice.getInvoiceNo();
            if (Func.isNotEmpty(invoiceNo) && !attachmentPOMap.containsKey(invoiceNo)) {
                noPDFInvoiceList.add(invoiceNo);
            }
        }
        if (Func.isEmpty(noPDFInvoiceList)) {
            return BaseResponse.newSuccessInstance(true);
        }
        //调用BO取invoice的PDF
        QueryInvoicePDFReq queryInvoicePDFReq = new QueryInvoicePDFReq();
        queryInvoicePDFReq.setOrgCode(orderPOList.get(0).getLegalEntityCode());
        queryInvoicePDFReq.setInvoiceList(noPDFInvoiceList);
       BaseResponse<List<QueryInvoicePDFRsp>> invoicePDFRsp = backOfficeClient.getInvoicePDFByInvoiceNo(queryInvoicePDFReq);
        if (invoicePDFRsp.isFail()) {
            return invoicePDFRsp;
        }
        List<QueryInvoicePDFRsp> invoicePDFRspList = invoicePDFRsp.getData().stream().filter(e -> Func.isNotEmpty(e.getCloudId())).collect(Collectors.toList());
        if (Func.isEmpty(invoicePDFRspList)) {
            return BaseResponse.newSuccessInstance(true);
        }
        //更新OrderAttachment表
        List<OrderAttachmentPO> orderAttachmentList = new ArrayList<>();
        invoicePDFRspList.stream().forEach(invoicePDF -> {
            String cloudId = invoicePDF.getCloudId();
            String invoiceNo = invoicePDF.getInvoiceNumber();
            List<PaymentInvoiceDownloadReq.OrderInvoice> orderInvoices = orderInvoiceList.stream().filter(e -> Func.equalsSafe(e.getInvoiceNo(), invoiceNo)).collect(Collectors.toList());
            if (Func.isNotEmpty(orderInvoices)) {
                orderInvoices.stream().forEach(orderInvoice -> {
                    GeneralOrderPO orderPO = orderPOList.stream().filter(e -> Func.equalsSafe(e.getOrderNo(), orderInvoice.getOrderNo())).findAny().orElse(new GeneralOrderPO());
                    if (Func.isNotEmpty(orderPO)) {
                        OrderAttachmentPO orderAttachmentPO = new OrderAttachmentPO();
                        orderAttachmentPO.setId(IdUtil.uuId());
                        orderAttachmentPO.setGeneralOrderID(orderPO.getId());
                        orderAttachmentPO.setCloudID(invoicePDF.getCloudId());
                        orderAttachmentPO.setObjectID(invoiceNo);
                        orderAttachmentPO.setBusinessType("INV");
                        orderAttachmentPO.setCreatedBy("system");
                        orderAttachmentPO.setCreatedDate(new Date());
                        orderAttachmentPO.setModifiedBy("system");
                        orderAttachmentPO.setModifiedDate(new Date());
                        int lastIndex = cloudId.lastIndexOf("/");
                        if (lastIndex != -1) {
                            String fileName = cloudId.substring(lastIndex + 1);
                            orderAttachmentPO.setAttachmentName(fileName);
                        } else {
                            orderAttachmentPO.setAttachmentName(cloudId);
                        }
                        orderAttachmentPO.setFileType("INV");
                        orderAttachmentList.add(orderAttachmentPO);
                    }
                });
            }
        });
        if (Func.isNotEmpty(orderAttachmentList)) {
            orderAttachmentService.saveBatch(orderAttachmentList);
        }
        return BaseResponse.newSuccessInstance(true);
    }
}
