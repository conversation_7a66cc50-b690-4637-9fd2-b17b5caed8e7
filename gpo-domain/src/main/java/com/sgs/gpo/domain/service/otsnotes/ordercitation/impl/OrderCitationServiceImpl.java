package com.sgs.gpo.domain.service.otsnotes.ordercitation.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.ordercitation.OrderCitationMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.ordercitation.OrderCitationPO;
import com.sgs.gpo.domain.service.otsnotes.ordercitation.IOrderCitationService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/6/30 10:17
 */
@Service
public class OrderCitationServiceImpl extends ServiceImpl<OrderCitationMapper, OrderCitationPO> implements IOrderCitationService {

    @Override
    public BaseResponse<List<OrderCitationPO>> queryByOrderId(Set<String> orderIdList) {

        if(Func.isEmpty(orderIdList)){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"orderIdList"});
        }

        QueryWrapper<OrderCitationPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(OrderCitationPO.COLUMN.ORDER_ID,orderIdList);
        return BaseResponse.newSuccessInstance(baseMapper.selectList(queryWrapper));
    }

}
