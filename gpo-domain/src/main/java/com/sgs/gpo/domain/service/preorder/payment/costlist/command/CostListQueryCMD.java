package com.sgs.gpo.domain.service.preorder.payment.costlist.command;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.ordertrfrel.OrderTrfRelationshipPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.productinstance.ProductInstancePO;
import com.sgs.gpo.domain.service.preorder.payment.costlist.ICostListService;
import com.sgs.gpo.domain.service.preorder.ordertrfrel.IOrderTrfRelationshipService;
import com.sgs.gpo.domain.service.preorder.productinstance.IProductInstanceService;
import com.sgs.gpo.domain.service.common.searchvalid.ISearchValidService;
import com.sgs.gpo.domain.service.setting.buparam.IBUParam;
import com.sgs.gpo.facade.model.payment.costlist.bo.CostListBO;
import com.sgs.gpo.domain.service.preorder.payment.costlist.context.CostListContext;
import com.sgs.gpo.facade.model.payment.costlist.req.CostListQueryReq;
import com.sgs.gpo.facade.model.searchvalid.req.SearchValidReq;
import com.sgs.gpo.facade.model.searchvalid.rsp.SearchValidRsp;
import com.sgs.gpo.integration.quotation.QuotationClient;
import com.sgs.priceengine.facade.model.DTO.QuotationServiceItemDTO;
import com.sgs.priceengine.facade.model.enums.QuotationStatus;
import com.sgs.priceengine.facade.model.request.OrderIdsRequest;
import com.sgs.priceengine.facade.model.request.QuotationServiceItemRequest;
import com.sgs.priceengine.facade.model.response.QuotationHeadRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @title: CostListQueryCMD
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/6/1515:31
 */
@Service
@Slf4j
@Scope(value = "prototype")
public class CostListQueryCMD extends BaseCommand<CostListContext<CostListQueryReq>> {
    @Autowired
    private ICostListService costListService;
    @Autowired
    private IBUParam ibuParam;
    @Autowired
    private QuotationClient quotationClient;
    @Autowired
    private IProductInstanceService productInstanceService;
    @Autowired
    private IOrderTrfRelationshipService orderTrfRelationshipService;
    @Autowired
    private ISearchValidService searchValidService;

    @Override
    public BaseResponse validateParam(CostListContext<CostListQueryReq> context) {
        if (Func.isEmpty(context) || Func.isEmpty(context.getParam())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        SearchValidReq searchValidReq = new SearchValidReq();
        searchValidReq.setObjectCode(Constants.OBJECT_ATTRIBUTE.COST_LIST.OBJECT_CODE);
        searchValidReq.setSearchCode(Constants.OBJECT_ATTRIBUTE.COST_LIST.OBJECT_CODE_SEARCH);
        searchValidReq.setRequestReqObj(context.getParam());
        BaseResponse<SearchValidRsp> validSearchRuleResponse = searchValidService.validSearchRule(searchValidReq);
        if(Func.isNotEmpty(validSearchRuleResponse)){
            SearchValidRsp searchValidRsp = validSearchRuleResponse.getData();
            context.setSearchValidRsp(searchValidRsp);
        }

        return validSearchRuleResponse;
    }

    @Override
    public BaseResponse before(CostListContext<CostListQueryReq> context) {
        if (Func.isNotEmpty(context.getUserInfo())) {
            context.getParam().setLabCode(context.getUserInfo().getCurrentLabCode());
        }
        Page<CostListBO> page = new Page<>(context.getPage(), context.getRows());
        page.setSearchCount(false);
        Long total = costListService.selectCostListPageCount(context.getParam()).getData();
        page.setTotal(total);
        IPage<CostListBO> costListPage = costListService.selectCostListPage(page, context.getParam()).getData();
        context.setCostListPage(costListPage);
        return super.before(context);
    }

    @Override
    public BaseResponse execute(CostListContext<CostListQueryReq> context) {
        BaseResponse buildResponse = this.buildDomain(context);
        if (buildResponse.isFail()) {
            return buildResponse;
        }
        return BaseResponse.newSuccessInstance(context.getCostListPage());
    }

    @Override
    public BaseResponse buildDomain(CostListContext<CostListQueryReq> context) {

        try {
            if(Func.isEmpty(context.getCostListPage()) || Func.isEmpty(context.getCostListPage().getRecords())){
                return super.buildDomain(context);
            }
            List<CostListBO> records = context.getCostListPage().getRecords();

            DecimalFormat formatter = new DecimalFormat("###,##0.00");
            //获取主币种
            String mainCurrencyCode = "";
            LanguageType primaryLanguage = LanguageType.English;
            BaseResponse<String> mainCurrencyCodeRsp = ibuParam.mainCurrencyCode(ProductLineContextHolder.getProductLineCode());
            BaseResponse<LanguageType> primaryLanguageRsp = ibuParam.getPrimaryLanguage(ProductLineContextHolder.getProductLineCode());
            List<BuParamValueRsp> buParamMainCurrencyList = null;
            if (Func.isNotEmpty(mainCurrencyCodeRsp)) {
                mainCurrencyCode = mainCurrencyCodeRsp.getData();
            }
            if (Func.isNotEmpty(primaryLanguageRsp)) {
                primaryLanguage = primaryLanguageRsp.getData();
            }
            boolean isChinese = LanguageType.check(primaryLanguage.getLanguageId(), LanguageType.Chinese);

            String finalMainCurrencyCode = mainCurrencyCode;
            List<String> orderIdList = records.stream().map(CostListBO::getOrderId).distinct().filter(Func::isNotEmpty).collect(Collectors.toList());
            List<String> orderNoList = records.stream().map(CostListBO::getOrderNo).distinct().filter(Func::isNotEmpty).collect(Collectors.toList());
            queryQuotationHead(orderIdList,context);
            queryQuotationServiceItem(orderNoList,context);
            queryProductInstance(orderIdList,context);
            queryOrderTrfRel(orderIdList,context);
            records.forEach(order -> {
                if (Func.isEmpty(order.getTotalPrice())) {
                    order.setTotalPrice("0");
                }
                if (Func.isEmpty(order.getSubContractFee())) {
                    order.setSubContractFee(new BigDecimal(0));
                }
                order.setSubContractFeeFormat(formatter.format(order.getSubContractFee()));
                //计算差价
                if (Func.isNotEmpty(order.getTotalPrice()) && Func.isNotEmpty(order.getSubContractFee())) {
                    BigDecimal diffPrice = new BigDecimal(order.getTotalPrice()).subtract(order.getSubContractFee());
                    order.setDiffPriceFormat(formatter.format(diffPrice));
                }
                // 格式化total amount显示
                if (Func.isNotEmpty(order.getTotalPrice())) {
                    order.setTotalPrice(formatter.format(new BigDecimal(order.getTotalPrice())));
                }
                order.setMainCurrencyCode(finalMainCurrencyCode);
                buildQuotationDomain(order, isChinese,context);
                buildProductInstanceDomain(order,context);
                buildOrderTrfDomain(order,context);
            });
            context.getCostListPage().setRecords(records);
            return super.buildDomain(context);
        } catch (Exception e) {
            e.printStackTrace();
            return BaseResponse.newFailInstance(e.getMessage());
        }
    }

    private void buildQuotationDomain(CostListBO costListBO, boolean isChinese,CostListContext<CostListQueryReq> context) {
        if(Func.isEmpty(costListBO)){
            return;
        }
        List<QuotationHeadRsp> quotationHeadRspList = context.getQuotationHeadRspList();
        List<QuotationServiceItemDTO> quotationServiceItemDTOList = context.getQuotationServiceItemDTOList();

        List<QuotationHeadRsp> quotationHeadDTOS = new ArrayList<>();
        String quotationFlag = "No", invoiceConfirmFlag = "No", costCodeFlag = "No";
        if(Func.isNotEmpty(quotationHeadRspList)){
            quotationHeadDTOS = quotationHeadRspList.stream().filter(quotation -> Func.equals(costListBO.getOrderId(), quotation.getOrderId())).collect(Collectors.toList());
            if (Func.isNotEmpty(quotationHeadDTOS)) {
                //设置quotationFlag(所有的Quotaiton状态都为Confirmed，排除PhaseOut状态)
                if (Func.isNotEmpty(quotationHeadDTOS)) {
                    List<QuotationHeadRsp> unPhaseOutQuotations = quotationHeadDTOS.stream().filter(i -> !QuotationStatus.check(i.getStatus(), QuotationStatus.PHASE_OUT)).collect(Collectors.toList());
                    if (Func.isNotEmpty(unPhaseOutQuotations)) {
                        List<QuotationHeadRsp> unConfirmedQuotations = quotationHeadDTOS.stream().filter(i -> !QuotationStatus.check(i.getStatus(), QuotationStatus.CONFIRM)).collect(Collectors.toList());
                        if (Func.isEmpty(unConfirmedQuotations)) {
                            quotationFlag = "Yes";
                        }
                    }
                }
            }
        }

        if (Func.isNotEmpty(costListBO.getInvoiceConfirmDate())) {
            invoiceConfirmFlag = "Yes";
        }
        costListBO.setInvoiceConfirmFlag(invoiceConfirmFlag);
        costListBO.setQuotationFlag(quotationFlag);
        List<String> phaseOutQhId = new ArrayList<>();
        if (Func.isNotEmpty(quotationHeadDTOS)) {
            phaseOutQhId = quotationHeadDTOS.stream().filter(i -> QuotationStatus.check(i.getStatus(), QuotationStatus.PHASE_OUT)).map(QuotationHeadRsp::getId).distinct().collect(Collectors.toList());
        }
        List<String> finalPhaseOutQhId = phaseOutQhId;
        List<QuotationServiceItemDTO> serviceItemDTOS = new ArrayList<>();
        if(Func.isNotEmpty(quotationServiceItemDTOList)){
            List<QuotationServiceItemDTO> allServiceItemDTOList = quotationServiceItemDTOList.stream().filter(i -> !finalPhaseOutQhId.contains(i.getHeadId())).collect(Collectors.toList());
            serviceItemDTOS = allServiceItemDTOList.stream().filter(i -> Func.equalsSafe(i.getOrderId(), costListBO.getOrderId())).collect(Collectors.toList());
        }

        if (Func.isNotEmpty(serviceItemDTOS)) {
            String serviceItems = "";
            // TODO-Trevor.Yuan 2023/7/5 目前Quotation接口返回的不是标准的语言结构，暂时先这样处理，等后面更新为标准结构后再调整
            if (isChinese) {
                serviceItems = serviceItemDTOS.stream().map(QuotationServiceItemDTO::getServiceItemNameCn).filter(Func::isNotEmpty).distinct().collect(Collectors.joining(";"));
            } else {
                serviceItems = serviceItemDTOS.stream().map(QuotationServiceItemDTO::getServiceItemName).filter(Func::isNotEmpty).distinct().collect(Collectors.joining(";"));
            }
            costListBO.setServiceItemName(serviceItems);
            //是否全部setCostCode,排除PhaseOut的Quotation
            List<QuotationServiceItemDTO> noCostCodeList = serviceItemDTOS.stream().filter(i -> Func.isEmpty(i.getCostCode())).collect(Collectors.toList());
            if (Func.isEmpty(noCostCodeList)) {
                costCodeFlag = "Yes";
            }
        }
        costListBO.setCostCodeFlag(costCodeFlag);
    }

    private void buildProductInstanceDomain(CostListBO costListBO,CostListContext<CostListQueryReq> context) {
        List<ProductInstancePO> productInstancePOList = context.getProductInstancePOList();
        if (Func.isNotEmpty(productInstancePOList)) {
            List<ProductInstancePO> productInfoPOS = productInstancePOList.stream().filter(i -> Func.equalsSafe(i.getGeneralOrderID(), costListBO.getOrderId())).collect(Collectors.toList());
            if (Func.isNotEmpty(productInfoPOS)) {
                productInfoPOS = productInfoPOS.stream().filter(item->Func.isNotEmpty(item.getHeaderID()) && (Func.isEmpty(item.getCancelFlag()) || NumberUtil.equals(item.getCancelFlag().intValue(),0))).collect(Collectors.toList());
                //处理双语
                List<ProductInstancePO> productInfoPOS_EN = productInfoPOS.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.English)).collect(Collectors.toList());
                List<ProductInstancePO> productInfoPOS_CN = productInfoPOS.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.Chinese)).collect(Collectors.toList());
                int en_size = productInfoPOS_EN.size();
                int cn_size = productInfoPOS_CN.size();
                int max = en_size > cn_size ? en_size : cn_size;
                costListBO.setSampleCount(max);
            } else {
                costListBO.setSampleCount(0);
            }
        } else {
            costListBO.setSampleCount(0);
        }
    }

    private void buildOrderTrfDomain(CostListBO costListBO,CostListContext<CostListQueryReq> context) {
        List<OrderTrfRelationshipPO> orderTrfRelationshipPOList = context.getOrderTrfRelationshipPOList();
        if (Func.isNotEmpty(orderTrfRelationshipPOList)) {
            List<OrderTrfRelationshipPO> orderTrfRelInfoList = orderTrfRelationshipPOList.stream().filter(i -> Func.equals(i.getOrderId(), costListBO.getOrderId())).collect(Collectors.toList());
            if (Func.isNotEmpty(orderTrfRelInfoList)) {
                String refNos = orderTrfRelInfoList.stream().map(OrderTrfRelationshipPO::getRefNo).distinct().filter(Func::isNotEmpty).collect(Collectors.joining(","));
                costListBO.setReferenceNo(refNos);
            }
        }
    }

    private void queryQuotationHead(List<String> orderIdList,CostListContext<CostListQueryReq> context) {
        String productLineCode = context.getProductLineCode();
        OrderIdsRequest orderIdsRequest = new OrderIdsRequest();
        orderIdsRequest.setOrderIds(orderIdList);
        orderIdsRequest.setProductLineCode(productLineCode);
        orderIdsRequest.setSystemId(15);
        BaseResponse<List<QuotationHeadRsp>> quotationHeadResponse = quotationClient.getActivedQuotationByOrderIds(orderIdsRequest);
        if (Func.isNotEmpty(quotationHeadResponse)) {
            List<QuotationHeadRsp> quotationHeadRspList = quotationHeadResponse.getData();
            context.setQuotationHeadRspList(quotationHeadRspList);
        }
    }

    private void queryQuotationServiceItem(List<String> orderNoList,CostListContext<CostListQueryReq> context) {
        QuotationServiceItemRequest quotationServiceItemRequest = new QuotationServiceItemRequest();
        quotationServiceItemRequest.setOrderNoList(orderNoList);
        quotationServiceItemRequest.setProductLineCode(context.getProductLineCode());
        quotationServiceItemRequest.setSystemId(15);
        BaseResponse<List<QuotationServiceItemDTO>> queryServiceItemResponse = quotationClient.queryServiceItemListForGeneralOrder(quotationServiceItemRequest);
        if (Func.isNotEmpty(queryServiceItemResponse)) {
            List<QuotationServiceItemDTO> quotationServiceItemDTOList = queryServiceItemResponse.getData();
            context.setQuotationServiceItemDTOList(quotationServiceItemDTOList);
        }
    }

    private void queryOrderTrfRel(List<String> orderIdList,CostListContext<CostListQueryReq> context) {
        if(Func.isEmpty(orderIdList)){
            return;
        }
        QueryWrapper<OrderTrfRelationshipPO> queryOrderTrfRelWrapper = new QueryWrapper<>();
        queryOrderTrfRelWrapper.in(OrderTrfRelationshipPO.COLUMN.ORDER_ID, orderIdList);
        List<OrderTrfRelationshipPO> orderTrfRelationshipPOList = orderTrfRelationshipService.list(queryOrderTrfRelWrapper);
        context.setOrderTrfRelationshipPOList(orderTrfRelationshipPOList);
    }

    private void queryProductInstance(List<String> orderIdList,CostListContext<CostListQueryReq> context) {
        if(Func.isEmpty(orderIdList)){
            return;
        }
        QueryWrapper<ProductInstancePO> queryProductWrapper = new QueryWrapper<>();
        queryProductWrapper.in(ProductInstancePO.COLUMN.GENERAL_ORDER_ID, orderIdList);
        List<ProductInstancePO> productInstancePOList = productInstanceService.list(queryProductWrapper);
        context.setProductInstancePOList(productInstancePOList);

    }

    @Override
    public BaseResponse after(CostListContext<CostListQueryReq> context) {
        SearchValidRsp searchValidRsp = context.getSearchValidRsp();
        IPage<CostListBO> costListPage = context.getCostListPage();
        if(Func.isNotEmpty(searchValidRsp) && Func.isNotEmpty(searchValidRsp.getOnlyUseDateRange()) && searchValidRsp.getOnlyUseDateRange() && (Func.isEmpty(costListPage) || Func.isEmpty(costListPage.getRecords()))){
            return BaseResponse.newFailInstance("search.result.empty.useOther",null);
        }else{
            return BaseResponse.newSuccessInstance(true);
        }
    }
}
