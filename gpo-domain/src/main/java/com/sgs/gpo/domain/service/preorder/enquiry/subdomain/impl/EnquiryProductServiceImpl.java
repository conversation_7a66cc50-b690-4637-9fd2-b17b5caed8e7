package com.sgs.gpo.domain.service.preorder.enquiry.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.enquiry.EnquiryProductMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryProductPO;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryProductService;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryProductReq;
import com.sgs.gpo.facade.model.preorder.productsample.req.ProductSampleQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class EnquiryProductServiceImpl extends ServiceImpl<EnquiryProductMapper, EnquiryProductPO> implements IEnquiryProductService {
    @Override
    public List<EnquiryProductPO> queryEnquiryProductByDff(EnquiryProductReq productReq) {
        if(Func.isEmpty(productReq) || Func.isEmpty(productReq.getEnquiryIds())){
            return null;
        }
        return this.getBaseMapper().queryEnquiryProductByDff(productReq);
    }

    @Override
    public BaseResponse<List<EnquiryProductPO>> queryEnquiryProductSampleByEnquiryId(ProductSampleQueryReq productSampleQueryReq) {
        if(Func.isEmpty(productSampleQueryReq) ||  (Func.isEmpty(productSampleQueryReq.getOrderIdList()))){
            return BaseResponse.newFailInstance("common.param.miss",new Object[]{"EnquiryID"});
        }
        LambdaQueryWrapper<EnquiryProductPO> wrapper = Wrappers.lambdaQuery();
        if (Func.isNotEmpty(productSampleQueryReq.getOrderIdList())) {
            wrapper.in(EnquiryProductPO::getEnquiryId, productSampleQueryReq.getOrderIdList());
        }
        List<EnquiryProductPO> enquiryProductPOList = baseMapper.selectList(wrapper);
        return BaseResponse.newSuccessInstance(enquiryProductPOList);
    }

    @Override
    public BaseResponse<List<EnquiryProductPO>> select(EnquiryIdReq enquiryIdReq) {
        if(Func.isEmpty(enquiryIdReq)||Func.isEmpty(enquiryIdReq.getEnquiryIdList())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        LambdaQueryWrapper<EnquiryProductPO> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EnquiryProductPO::getEnquiryId, enquiryIdReq.getEnquiryIdList());
        return BaseResponse.newSuccessInstance( this.baseMapper.selectList(wrapper));
    }

    @Override
    public Integer delete(EnquiryProductReq enquiryProductReq) {
        if(Func.isEmpty(enquiryProductReq) ||  (Func.isEmpty(enquiryProductReq.getEnquiryIds()))){
            return 0;
        }
        LambdaQueryWrapper<EnquiryProductPO> wrapper = Wrappers.lambdaQuery();
        if (Func.isNotEmpty(enquiryProductReq.getEnquiryIds())) {
            wrapper.in(EnquiryProductPO::getEnquiryId, enquiryProductReq.getEnquiryIds());
        }
        if(Func.isNotEmpty(enquiryProductReq.getLanguageIds())){
            wrapper.in(EnquiryProductPO::getLanguageId, enquiryProductReq.getLanguageIds());
        }
        return baseMapper.delete(wrapper);
    }
}
