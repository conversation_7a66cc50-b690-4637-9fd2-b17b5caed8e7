package com.sgs.gpo.domain.service.preorder.enquiry.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.enquiry.EnquiryTestRequestContactsMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryTestRequestContactsPO;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryTestRequestContactsService;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class EnquiryTestRequestContactsImpl extends ServiceImpl<EnquiryTestRequestContactsMapper, EnquiryTestRequestContactsPO>
        implements IEnquiryTestRequestContactsService {
    @Override
    public List<EnquiryTestRequestContactsPO> select(EnquiryIdReq enquiryIdReq) {
        if (Func.isEmpty(enquiryIdReq) || Func.isEmpty(enquiryIdReq.getEnquiryIdList())) {
            return null;
        }
        LambdaQueryWrapper<EnquiryTestRequestContactsPO> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EnquiryTestRequestContactsPO::getEnquiryId, enquiryIdReq.getEnquiryIdList());
        return this.baseMapper.selectList(wrapper);
    }
}
