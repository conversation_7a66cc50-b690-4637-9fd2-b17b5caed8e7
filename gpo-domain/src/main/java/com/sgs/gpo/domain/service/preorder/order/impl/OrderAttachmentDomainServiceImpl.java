package com.sgs.gpo.domain.service.preorder.order.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.attachment.OrderAttachmentPO;
import com.sgs.gpo.domain.service.preorder.attachment.IOrderAttachmentService;
import com.sgs.gpo.domain.service.preorder.order.IOrderAttachmentDomainService;
import com.sgs.gpo.domain.service.preorder.order.command.OrderAttachmentQueryCMD;
import com.sgs.gpo.domain.service.preorder.order.command.OrderAttachmentQueryScanFilesCMD;
import com.sgs.gpo.domain.service.preorder.order.context.OrderContext;
import com.sgs.gpo.facade.model.preorder.order.req.OrderAttachmentQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.UpdateAttachmentSeqReq;
import com.sgs.gpo.facade.model.scantool.rsp.ScanToolFile;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class OrderAttachmentDomainServiceImpl implements IOrderAttachmentDomainService {

    @Resource
    private IOrderAttachmentService orderAttachmentService;

    @Override
    public BaseResponse<List<ScanToolFile>> queryScanFiles(String orderNo) {
        OrderContext<String> context = new OrderContext<>();
        context.setParam(orderNo);
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        context.setToken(SecurityContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(OrderAttachmentQueryScanFilesCMD.class,context);
    }

    @Override
    public BaseResponse updateSequence(UpdateAttachmentSeqReq updateAttachmentSeqReq) {
        return orderAttachmentService.updateOrderAttachmentSeq(updateAttachmentSeqReq);
    }

    @Override
    public BaseResponse list(OrderAttachmentQueryReq orderAttachmentQueryReq) {
        OrderContext<OrderAttachmentQueryReq> context = new OrderContext<>();
        context.setParam(orderAttachmentQueryReq);
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        context.setToken(SecurityContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(OrderAttachmentQueryCMD.class,context);
    }

    @Override
    public BaseResponse listToStarlimsAttachments(String orderId) {
        if (Func.isEmpty(orderId)) {
            return  BaseResponse.newFailInstance("orderId is null");
        }
        OrderAttachmentQueryReq orderAttachmentQueryReq = new OrderAttachmentQueryReq();
        orderAttachmentQueryReq.setOrderIdList(Sets.newHashSet(orderId));
        BaseResponse<List<OrderAttachmentPO>> attachmentPOListRsp = orderAttachmentService.select(orderAttachmentQueryReq);
        if(attachmentPOListRsp.isSuccess() && Func.isNotEmpty(attachmentPOListRsp.getData())){
            List<OrderAttachmentPO> attachmentList = attachmentPOListRsp.getData();
            if (Func.isNotEmpty(attachmentList)) {
                attachmentList = attachmentList.stream().filter(item-> item.getToStarlims() == 1).collect(Collectors.toList());
                return BaseResponse.newSuccessInstance(attachmentList);
            }
        }
        return  BaseResponse.newSuccessInstance(Lists.newArrayList());
    }
}
