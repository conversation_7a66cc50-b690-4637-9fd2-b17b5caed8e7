package com.sgs.gpo.domain.service.otsnotes.testline.command;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.domain.service.otsnotes.testline.context.CitationUpdateContext;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testline.TestLineInstancePO;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.ITestLineService;
import com.sgs.gpo.facade.model.otsnotes.testline.req.CitationUpdateItemReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.CitationUpdateReq;
import com.sgs.gpo.integration.trims.TrimsClient;
import com.sgs.trimslocal.facade.model.testline.req.GetCitationBaseInfoReq;
import com.sgs.trimslocal.facade.model.testline.rsp.GetCitationBaseInfoRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: CitationUpdateCMD
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/28 15:32
 */
@Service
@Slf4j
public class CitationUpdateCMD extends BaseCommand<CitationUpdateContext<CitationUpdateReq>> {
    @Autowired
    private TrimsClient trimsClient;
    @Autowired
    private ITestLineService testLineService;

    @Override
    public BaseResponse validateParam(CitationUpdateContext<CitationUpdateReq> context) {
        if (Func.isEmpty(context) || Func.isEmpty(context.getParam())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        CitationUpdateReq citationUpdateReq = context.getParam();
        if (Func.isEmpty(citationUpdateReq.getTestLineInstanceId())) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"testLineInstanceId"});
        }
        if(Func.isEmpty(citationUpdateReq.getCitationUpdateItemReqList())){
            return BaseResponse.newFailInstance("common.select.required", new Object[]{"Test Standard"});
        }
        //是否都有CitationVersionID
        List<CitationUpdateItemReq> citationUpdateItemReqList = citationUpdateReq.getCitationUpdateItemReqList();
        long count = citationUpdateItemReqList.parallelStream().filter(item -> Func.isEmpty(item.getCitationVersionId())).count();
        if(count>0){
            return BaseResponse.newFailInstance("Invalidate standard!");
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(CitationUpdateContext<CitationUpdateReq> context) {
        CitationUpdateReq citationUpdateReq = context.getParam();
        List<CitationUpdateItemReq> citationUpdateItemReqList = citationUpdateReq.getCitationUpdateItemReqList();
        List<TestLineInstancePO> testLineInstancePOList = context.getTestLineInstancePOList();
        for (TestLineInstancePO testLineInstancePO : testLineInstancePOList) {
            CitationUpdateItemReq citationUpdateItemReq = citationUpdateItemReqList.get(0);
            testLineInstancePO.setCitationBaseId(citationUpdateItemReq.getCitationBaseId());
            testLineInstancePO.setCitationId(citationUpdateItemReq.getCitationId());
            testLineInstancePO.setCitationVersionId(citationUpdateItemReq.getCitationVersionId());
//            testLineInstancePO.setClientStandard(citationUpdateItemReq.getClientStandard());

//            testLineInstancePO.setCitationName(standard.getEditCitationName());
//            testLineInstancePO.setStandardVersionID(standard.getStandardVersionId());
            testLineInstancePO.setModifiedDate(new Date());
            testLineInstancePO.setModifiedBy(context.getUserInfo().getRegionAccount());
        }
        // 更新TestLine的Citation信息
        testLineService.updateBatchById(testLineInstancePOList);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse buildDomain(CitationUpdateContext<CitationUpdateReq> context) {
        List<TestLineInstancePO> testLineInstancePOList = context.getTestLineInstancePOList();
        for (TestLineInstancePO testLineInstancePO : testLineInstancePOList) {

        }

        return super.buildDomain(context);
    }

    @Override
    public BaseResponse before(CitationUpdateContext<CitationUpdateReq> context) {
        CitationUpdateReq citationUpdateReq = context.getParam();
        List<CitationUpdateItemReq> citationUpdateItemReqList = citationUpdateReq.getCitationUpdateItemReqList();
        Set<Long> citationBaseIdList = citationUpdateItemReqList.stream().map(CitationUpdateItemReq::getCitationBaseId).collect(Collectors.toSet());
        GetCitationBaseInfoReq getCitationBaseInfoReq = new GetCitationBaseInfoReq();
        getCitationBaseInfoReq.setCitationBaseIds(citationBaseIdList);
        getCitationBaseInfoReq.setLanguageIds(Lists.newArrayList(LanguageType.English.getLanguageId()));
        BaseResponse<List<GetCitationBaseInfoRsp>> citationBaseInfoRsp = trimsClient.getCitationBaseInfoList(getCitationBaseInfoReq);
        if(citationBaseInfoRsp.isSuccess()){
            List<GetCitationBaseInfoRsp> citationBaseInfoRspList = citationBaseInfoRsp.getData();
            context.setCitationBaseInfoRspList(citationBaseInfoRspList);
        }
        BaseResponse<List<TestLineInstancePO>> testLineBaseRsp = testLineService.queryTestLineBase(Sets.newHashSet(citationUpdateReq.getTestLineInstanceId()));
        if(testLineBaseRsp.isSuccess()){
            List<TestLineInstancePO> testLineInstancePOList = testLineBaseRsp.getData();
            context.setTestLineInstancePOList(testLineInstancePOList);
        }
        return BaseResponse.newSuccessInstance(true);
    }
}
