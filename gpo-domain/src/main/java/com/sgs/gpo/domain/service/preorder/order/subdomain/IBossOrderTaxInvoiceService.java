package com.sgs.gpo.domain.service.preorder.order.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.BossOrderTaxInvoicePO;

import java.util.List;

public interface IBossOrderTaxInvoiceService extends IService<BossOrderTaxInvoicePO> {

    int updateByInvoiceNoAndTaxBatch(List<BossOrderTaxInvoicePO> bossOrderTaxInvoiceInfoPOS);
}
