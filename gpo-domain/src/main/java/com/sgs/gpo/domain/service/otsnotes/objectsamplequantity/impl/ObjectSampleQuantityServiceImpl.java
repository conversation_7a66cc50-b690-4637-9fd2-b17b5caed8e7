package com.sgs.gpo.domain.service.otsnotes.objectsamplequantity.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.PageBO;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.objectsamplequantity.ObjectSampleQuantityMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.objectsamplequantity.ObjectSampleQuantityPO;
import com.sgs.gpo.domain.service.otsnotes.objectsamplequantity.IObjectSampleQuantityService;
import com.sgs.gpo.facade.model.otsnotes.objectsamplequantity.bo.ObjectSampleQuantityBO;
import com.sgs.gpo.facade.model.otsnotes.objectsamplequantity.req.ObjectSampleQuantityDeleteReq;
import com.sgs.gpo.facade.model.otsnotes.objectsamplequantity.req.ObjectSampleQuantityQueryReq;
import com.sgs.gpo.facade.model.otsnotes.objectsamplequantity.req.ObjectSampleQuantitySaveReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class ObjectSampleQuantityServiceImpl extends ServiceImpl<ObjectSampleQuantityMapper, ObjectSampleQuantityPO> implements IObjectSampleQuantityService {

    @Override
    public PageBO<ObjectSampleQuantityPO> page(ObjectSampleQuantityQueryReq objectSampleQuantityQueryReq, Integer page, Integer rows) {
        if(Func.isEmpty(objectSampleQuantityQueryReq) || Func.isAllEmpty(objectSampleQuantityQueryReq.getObjectIdList(),objectSampleQuantityQueryReq.getObjectNoList())){
            return new PageBO<>();
        }
        IPage<ObjectSampleQuantityPO> iPage = baseMapper.selectPage(new Page(page,rows),objectSampleQuantityQueryReq);
        PageBO<ObjectSampleQuantityPO> objectSampleQuantityPOPageBO = Func.copy(iPage,PageBO.class);
        return objectSampleQuantityPOPageBO;
    }

    @Override
    @Transactional
    public BaseResponse save(ObjectSampleQuantitySaveReq objectSampleQuantitySaveReq) {
        Assert.isTrue(Func.isNotEmpty(objectSampleQuantitySaveReq.getStylesQty()),"common.required",new Object[]{"Styles Qty"});
        Assert.isTrue(Func.isNotEmpty(objectSampleQuantitySaveReq.getPiecesQty()),"common.required",new Object[]{"Pieces Qty"});
        Assert.isTrue(Func.isNotEmpty(objectSampleQuantitySaveReq.getPartsQty()),"common.required",new Object[]{"Parts Qty"});

        //新增的 id重新赋值
        Date now = new Date();
        String user = SystemContextHolder.getUserInfoFillSystem().getRegionAccount();
        ObjectSampleQuantityPO objectSampleQuantityPO = Func.copy(objectSampleQuantitySaveReq,ObjectSampleQuantityPO.class);
        if(Func.isEmpty(objectSampleQuantityPO.getId())){
            objectSampleQuantityPO.setId(IdUtil.uuId());
            objectSampleQuantityPO.setCreatedDate(now);
            objectSampleQuantityPO.setCreatedBy(user);
        }
        objectSampleQuantityPO.setModifiedBy(user);
        objectSampleQuantityPO.setModifiedDate(now);
        saveOrUpdate(objectSampleQuantityPO);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    @Transactional
    public BaseResponse delete(ObjectSampleQuantityDeleteReq objectSampleQuantityDeleteReq) {
        if(Func.isEmpty(objectSampleQuantityDeleteReq) || Func.isEmpty(objectSampleQuantityDeleteReq.getIds())){
            BaseResponse.newFailInstance("common.param.miss",new Object[]{});
        }
        baseMapper.updateActiveIndicatorByIds(objectSampleQuantityDeleteReq.getIds(), ActiveType.Disable.getStatus());
        return BaseResponse.newSuccessInstance(true);
    }

    public List<ObjectSampleQuantityPO> select(ObjectSampleQuantityQueryReq objectSampleQuantityQueryReq){
        if(Func.isEmpty(objectSampleQuantityQueryReq) || Func.isAllEmpty(objectSampleQuantityQueryReq.getObjectIdList(),objectSampleQuantityQueryReq.getObjectNoList())){
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<ObjectSampleQuantityPO> wrapper = new LambdaQueryWrapper<>();
        if(Func.isNotEmpty(objectSampleQuantityQueryReq.getObjectIdList())){
            wrapper.in(ObjectSampleQuantityPO::getObjectId,objectSampleQuantityQueryReq.getObjectIdList());
        }
        if(Func.isNotEmpty(objectSampleQuantityQueryReq.getObjectNoList())){
            wrapper.in(ObjectSampleQuantityPO::getObjectNo,objectSampleQuantityQueryReq.getObjectNoList());
        }
        wrapper.eq(ObjectSampleQuantityPO::getActiveIndicator,ActiveType.Enable.getStatus());
        wrapper.orderByDesc(ObjectSampleQuantityPO::getCreatedDate);
        return baseMapper.selectList(wrapper);
    }


}
