package com.sgs.gpo.domain.service.preorder.devops.impl;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.devops.DevopsMapper;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.report.ReportMapper;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.subreport.SubReportMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subreport.ReportSubReportRelationshipPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subreport.SubReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.attachment.OrderAttachmentPO;
import com.sgs.gpo.domain.service.otsnotes.subreport.IReportSubReportRelationshipService;
import com.sgs.gpo.domain.service.otsnotes.subreport.ISubReportService;
import com.sgs.gpo.domain.service.preorder.devops.IDevopsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DevopsServiceImpl implements IDevopsService {

    @Autowired
    private DevopsMapper devopsMapper;
    @Autowired
    private ReportMapper reportMapper;
    @Autowired
    private ISubReportService subReportService;
    @Autowired
    private IReportSubReportRelationshipService reportSubReportRelationshipService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse cancelTestingOrder(List<String> orderNoList) {
       if(Func.isNotEmpty(orderNoList)){
           devopsMapper.cancelTestingOrder(orderNoList);
       }
       return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public List<ReportPO> getOrderNoListByAccNone(String buCode) {
        return devopsMapper.getOrderNoListByAccNone(buCode);
    }

    @Override
    public BaseResponse repairSubReport() {
        Instant instant = Instant.parse("2025-03-16T13:00:00Z"); // 使用ISO-8601格式字符串创建Instant对象
        // 将Instant转换为Date对象
        Date date = Date.from(instant);
        List<SubReportPO> subReportPOList = devopsMapper.getErrorSubReportPO();
        if(Func.isEmpty(subReportPOList)){
            return BaseResponse.newSuccessInstance(true);
        }
        List<ReportSubReportRelationshipPO> reportRelationshipPOS = new ArrayList<>();
        subReportPOList.stream().forEach(subReportPO -> {
            //查询Report 01
            ReportPO reportPO = devopsMapper.get01ReportByOrderId(subReportPO.getGeneralOrderInstanceId());
            if(Func.isEmpty(reportPO)){
                return;
            }
            //修复SubReport数据
            SubReportPO subReportPO1 = new SubReportPO();
            subReportPO1.setReportNo(reportPO.getReportNo());
            subReportPO1.setId(subReportPO.getId());
            subReportPO1.setModifiedDate(date);
            subReportService.updateById(subReportPO1);
            //增加report subReport关系
            ReportSubReportRelationshipPO reportSubReportRelationshipPO = new ReportSubReportRelationshipPO();
            reportSubReportRelationshipPO.setId(IdUtil.uuId());
            reportSubReportRelationshipPO.setActiveIndicator(1);
            reportSubReportRelationshipPO.setSubReportId(subReportPO.getId());
            reportSubReportRelationshipPO.setReportId(reportPO.getId());
            reportSubReportRelationshipPO.setCreatedBy("system");
            reportSubReportRelationshipPO.setCreatedDate(date);
            reportSubReportRelationshipPO.setModifiedBy("system");
            reportSubReportRelationshipPO.setModifiedDate(date);
            reportRelationshipPOS.add(reportSubReportRelationshipPO);
        });
        if(Func.isNotEmpty(reportRelationshipPOS)) {
            reportSubReportRelationshipService.saveBatch(reportRelationshipPOS);
        }
        return BaseResponse.newSuccessInstance(true);
    }
}
