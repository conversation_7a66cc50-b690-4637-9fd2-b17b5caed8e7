package com.sgs.gpo.domain.service.otsnotes.job.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.security.annotation.AccessRule;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.job.JobTestLineMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.job.JobTestLinePO;
import com.sgs.gpo.domain.service.otsnotes.job.subdomain.IJobTestLineService;
import com.sgs.gpo.facade.model.job.req.JobIdReq;
import com.sgs.gpo.facade.model.otsnotes.job.dto.JobTestLineAndStandardDTO;
import com.sgs.gpo.facade.model.otsnotes.job.dto.JobTestLineDTO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.QueryJobTestLineReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/7/3 09:45
 */
@Service

@Slf4j
public class JobTestLineServiceImpl extends ServiceImpl<JobTestLineMapper, JobTestLinePO> implements IJobTestLineService {

    @Override
    public BaseResponse<List<JobTestLinePO>> query(JobIdReq jobIdReq) {
        if (Func.isEmpty(jobIdReq) || Func.isEmpty(jobIdReq.getJobId())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        QueryWrapper<JobTestLinePO> queryWrapper = new QueryWrapper();
        queryWrapper.eq(JobTestLinePO.COLUMN.JOB_ID, jobIdReq.getJobId());
        List<JobTestLinePO> list = baseMapper.selectList(queryWrapper);
        return BaseResponse.newSuccessInstance(list);
    }

    @Override
    public BaseResponse<List<JobTestLinePO>> query(QueryJobTestLineReq jobTestLineReq) {
        if (Func.isEmpty(jobTestLineReq) || (Func.isEmpty(jobTestLineReq.getJobIdList()) && Func.isEmpty(jobTestLineReq.getTestLineInstanceIdList()))) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        QueryWrapper<JobTestLinePO> queryWrapper = new QueryWrapper();
        if (Func.isNotEmpty(jobTestLineReq.getJobIdList())) {
            queryWrapper.in(JobTestLinePO.COLUMN.JOB_ID, jobTestLineReq.getJobIdList());
        }else if (Func.isNotEmpty(jobTestLineReq.getTestLineInstanceIdList())) {
            queryWrapper.in(JobTestLinePO.COLUMN.TEST_LINE_INSTANCE_ID, jobTestLineReq.getTestLineInstanceIdList());
        }
        List<JobTestLinePO> list = baseMapper.selectList(queryWrapper);
        return BaseResponse.newSuccessInstance(list);
    }


    @Override
    public BaseResponse<Integer> removeByJobAndTL(String jobId, Set<String> testLineInstanceIdList) {
        if (Func.isEmpty(jobId) || Func.isEmpty(testLineInstanceIdList)) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq(JobTestLinePO.COLUMN.JOB_ID, jobId);
        queryWrapper.in(JobTestLinePO.COLUMN.TEST_LINE_INSTANCE_ID, testLineInstanceIdList);
        Integer count = baseMapper.delete(queryWrapper);
        return BaseResponse.newSuccessInstance(count);
    }

    @Override
    @AccessRule(needToken = false)
    public BaseResponse<List<JobTestLineDTO>> queryJobTestLineDTO(QueryJobTestLineReq jobTestLineReq) {
        if(Func.isEmpty(jobTestLineReq)){
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        return BaseResponse.newSuccessInstance(baseMapper.queryJobTestLineDto(jobTestLineReq));
    }

    @Override
    public List<JobTestLineAndStandardDTO> getTestLineAndStandardByOrderNo(Set<String> orderNoList) {
        if(Func.isEmpty(orderNoList)){
            return Lists.newArrayList();
        }
        return baseMapper.getTestLineAndStandardByOrderNo(orderNoList);
    }
}
