package com.sgs.gpo.domain.service.preorder.productinstance.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.productinstance.ProductInstanceMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.productinstance.ProductInstancePO;
import com.sgs.gpo.domain.service.preorder.productinstance.IProductInstanceService;
import com.sgs.gpo.facade.model.preorder.productsample.req.ProductSampleDeleteReq;
import com.sgs.gpo.facade.model.preorder.productsample.req.ProductSampleQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/4 08:59
 */
@Service
@Slf4j
public class ProductInstanceServiceImpl extends ServiceImpl<ProductInstanceMapper, ProductInstancePO> implements IProductInstanceService {

    @Override
    public BaseResponse<List<ProductInstancePO>> queryOrderProductSample(ProductSampleQueryReq productSampleQueryReq) {
        if (Func.isEmpty(productSampleQueryReq) || (Func.isEmpty(productSampleQueryReq.getOrderIdList()) && Func.isEmpty(productSampleQueryReq.getRefSampleIdList()))) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        QueryWrapper<ProductInstancePO> queryWrapper = new QueryWrapper<>();
        if (Func.isNotEmpty(productSampleQueryReq.getOrderIdList())) {
            queryWrapper.in(ProductInstancePO.COLUMN.GENERAL_ORDER_ID, productSampleQueryReq.getOrderIdList());
        }
        if (Func.isNotEmpty(productSampleQueryReq.getRefSampleIdList())) {
            queryWrapper.in(ProductInstancePO.COLUMN.REF_SAMPLE_ID, productSampleQueryReq.getRefSampleIdList());
        }
        queryWrapper.orderByAsc(ProductInstancePO.COLUMN.LANGUAGE_ID).orderByAsc(ProductInstancePO.COLUMN.SAMPLE_ID);
        List<ProductInstancePO> productInstanceList = baseMapper.selectList(queryWrapper);
        return BaseResponse.newSuccessInstance(productInstanceList);
    }

    @Override
    public Integer delete(ProductSampleDeleteReq deleteReq) {
        if (Func.isEmpty(deleteReq) || (Func.isEmpty(deleteReq.getOrderId()))) {
            return 0;
        }
        QueryWrapper<ProductInstancePO> queryWrapper = new QueryWrapper<>();
        if (Func.isNotEmpty(deleteReq.getOrderId())) {
            queryWrapper.eq(ProductInstancePO.COLUMN.GENERAL_ORDER_ID, deleteReq.getOrderId());
        }
        if (Func.isNotEmpty(deleteReq.getLanguageIds())) {
            queryWrapper.in(ProductInstancePO.COLUMN.LANGUAGE_ID, deleteReq.getLanguageIds());
        }
        return baseMapper.delete(queryWrapper);
    }
}
