package com.sgs.gpo.domain.service.dashboard.context;


import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.gpo.facade.model.dashboard.PieDashBoardBO;
import com.sgs.gpo.facade.model.dashboard.req.DashBoardQueryRep;
import lombok.Data;

@Data
public class DashboardContext extends BaseContext<DashBoardQueryRep, PieDashBoardBO> {

    private String labCode;
    private Integer labId;

}
