package com.sgs.gpo.domain.service.otsnotes.testline.context;

import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.common.print.OutPutDataBO;
import com.sgs.gpo.facade.model.otsnotes.testline.vo.TestLineDetailVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title: TestLineDetailContext
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/21 17:53
 */
@Data
public class TestLineDetailContext<Input> extends BaseContext<Input, TestLineDetailVO> {
    private List<TestLineDetailVO> testLineVoData;
    private OutPutDataBO dataBO;

}
