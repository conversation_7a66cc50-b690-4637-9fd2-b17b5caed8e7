package com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testsample.ReferDataRelationshipMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.ReferDataRelationshipPO;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.IReferDataRelationShipService;
import com.sgs.gpo.facade.model.otsnotes.testsample.bo.ReferDataBO;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.ReferDataRelationShipReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class ReferDataRelationShipServiceImpl extends ServiceImpl<ReferDataRelationshipMapper, ReferDataRelationshipPO>
        implements IReferDataRelationShipService{


    @Override
    public BaseResponse<List<ReferDataRelationshipPO>> query(ReferDataRelationShipReq req) {
        if(Func.isEmpty(req)||Func.isEmpty(req.getCurrentOrderNoList())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        LambdaQueryWrapper<ReferDataRelationshipPO> wrapper= Wrappers.<ReferDataRelationshipPO>lambdaQuery()
                .in(ReferDataRelationshipPO::getCurrentOrderNo,req.getCurrentOrderNoList());
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }

    @Override
    public BaseResponse<List<ReferDataBO>> selectReferDataByOrder(ReferDataRelationShipReq req) {
        if(Func.isEmpty(req)||Func.isEmpty(req.getCurrentOrderNoList())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        return BaseResponse.newSuccessInstance(baseMapper.selectReferDataByOrder(req));
    }


}
