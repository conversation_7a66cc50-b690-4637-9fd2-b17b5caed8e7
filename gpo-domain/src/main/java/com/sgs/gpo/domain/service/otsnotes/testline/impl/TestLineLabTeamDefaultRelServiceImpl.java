package com.sgs.gpo.domain.service.otsnotes.testline.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testline.TestLineLabTeamDefaultRelMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testline.TestLineLabTeamDefaultRelationshipPO;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.ITestLineLabTeamDefaultRelService;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineLabTeamSearchReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> @date 2023/6/30 10:56
 */
@Service
@Slf4j
public class TestLineLabTeamDefaultRelServiceImpl extends ServiceImpl<TestLineLabTeamDefaultRelMapper,TestLineLabTeamDefaultRelationshipPO> implements ITestLineLabTeamDefaultRelService {
    @Override
    public BaseResponse<List<TestLineLabTeamDefaultRelationshipPO>> select(TestLineLabTeamSearchReq testLineLabTeamReq) {
        if(Func.isEmpty(testLineLabTeamReq)){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"request"});
        }
        if(Func.isEmpty(testLineLabTeamReq.getTestLineIdList())){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"TestLineId"});
        }
        if(Func.isEmpty(testLineLabTeamReq.getLabCode())){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"LabCode"});
        }
        LambdaQueryWrapper<TestLineLabTeamDefaultRelationshipPO> wrapper= Wrappers.<TestLineLabTeamDefaultRelationshipPO>lambdaQuery()
                .in(TestLineLabTeamDefaultRelationshipPO::getTestLineId,testLineLabTeamReq.getTestLineIdList())
                .eq(TestLineLabTeamDefaultRelationshipPO::getLabCode,testLineLabTeamReq.getLabCode())
                .eq(TestLineLabTeamDefaultRelationshipPO::getActiveIndicator,1)
                .eq(TestLineLabTeamDefaultRelationshipPO::getMarkForDelete,1);
        List<TestLineLabTeamDefaultRelationshipPO> testLineLabTeamDefaultRelationshipPOS = baseMapper.selectList(wrapper);
        return BaseResponse.newSuccessInstance(testLineLabTeamDefaultRelationshipPOS);
    }
}
