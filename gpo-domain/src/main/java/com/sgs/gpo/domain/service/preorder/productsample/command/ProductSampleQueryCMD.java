package com.sgs.gpo.domain.service.preorder.productsample.command;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.model.common.dff.DFFAttrBO;
import com.sgs.framework.model.common.dff.DFFAttrLanguageBO;
import com.sgs.framework.model.common.dff.DFFTemplateBO;
import com.sgs.framework.model.common.productsample.ProductBO;
import com.sgs.framework.model.common.productsample.ProductSampleBO;
import com.sgs.framework.model.common.productsample.SampleBO;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.productinstance.ProductInstancePO;
import com.sgs.gpo.domain.service.common.dff.IDFFService;
import com.sgs.gpo.domain.service.preorder.productinstance.IProductInstanceService;
import com.sgs.gpo.domain.service.preorder.productsample.context.ProductSampleContext;
import com.sgs.gpo.domain.service.setting.buparam.IBUParam;
import com.sgs.gpo.facade.model.preorder.productsample.dto.ProductSampleExtDataDTO;
import com.sgs.gpo.facade.model.preorder.productsample.dto.ProductSampleExtDataLanguageDTO;
import com.sgs.gpo.facade.model.preorder.productsample.req.ProductSampleQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@Scope(value = "prototype")
public class ProductSampleQueryCMD extends BaseCommand<ProductSampleContext> {
    @Autowired
    private IDFFService dffService;
    @Autowired
    private IBUParam buParam;
    @Autowired
    private IProductInstanceService productInstanceService;

    @Override
    public BaseResponse validateParam(ProductSampleContext context) {
        if (Func.isEmpty(context) || Func.isEmpty(context.getParam()) ||
                Func.isEmpty(context.getParam().getOrderIdList()) &&
                        Func.isEmpty(context.getParam().getRefSampleIdList())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(ProductSampleContext context) {
        setPrimaryLanguage(context);
        return loadProductInstances(context);
    }

    private void setPrimaryLanguage(ProductSampleContext context) {
        BaseResponse<LanguageType> languageTypeBaseResponse = buParam.getPrimaryLanguage(ProductLineContextHolder.getProductLineCode());
        if (Func.isNotEmpty(languageTypeBaseResponse) && Func.isNotEmpty(languageTypeBaseResponse.getData())) {
            context.setPrimaryLanguage(languageTypeBaseResponse.getData());
        }
    }

    private BaseResponse loadProductInstances(ProductSampleContext context) {
        ProductSampleQueryReq productSampleQueryReq = context.getParam();
        BaseResponse<List<ProductInstancePO>> queryOrderProductSampleResponse = productInstanceService.queryOrderProductSample(productSampleQueryReq);
        if (queryOrderProductSampleResponse.isFail()) {
            return queryOrderProductSampleResponse;
        }

        List<ProductInstancePO> productInstanceList = queryOrderProductSampleResponse.getData();
        context.setProductInstanceList(productInstanceList);
        loadDFFTemplates(context, productInstanceList);
        return super.before(context);
    }

    private void loadDFFTemplates(ProductSampleContext context, List<ProductInstancePO> productInstanceList) {
        if (Func.isNotEmpty(productInstanceList)) {
            Set<String> dffTemplateIdList = productInstanceList.stream()
                    .map(ProductInstancePO::getDFFFormID)
                    .collect(Collectors.toSet());
            BaseResponse<List<DFFTemplateBO>> dffResponse = dffService.queryByIds(dffTemplateIdList);
            if (dffResponse.isFail() || Func.isEmpty(dffResponse.getData())) {
                throw new RuntimeException("NotFound DFF Template");
            }
            context.setDffTemplateList(dffResponse.getData());
        }
    }

    @Override
    public BaseResponse<List<ProductSampleBO>> buildDomain(ProductSampleContext context) {
        List<ProductInstancePO> productInstanceList = context.getProductInstanceList();
        List<ProductSampleBO> allProductSampleBOList = new ArrayList<>();

        if (Func.isNotEmpty(productInstanceList)) {
            Set<String> orderIdList = productInstanceList.stream()
                    .map(ProductInstancePO::getGeneralOrderID)
                    .collect(Collectors.toSet());

            for (String orderId : orderIdList) {
                ProductSampleBO productSampleBO = initOrGetProductSampleBO(allProductSampleBOList, orderId);
                buildProduct(context, orderId, productSampleBO);
                buildSample(context, orderId, productSampleBO);
                allProductSampleBOList.add(productSampleBO);
            }
        }
        return BaseResponse.newSuccessInstance(allProductSampleBOList);
    }

    private ProductSampleBO initOrGetProductSampleBO(List<ProductSampleBO> allProductSampleBOList, String orderId) {
        return allProductSampleBOList.stream()
                .filter(item -> Func.equalsSafe(item.getOrderId(), orderId))
                .findAny()
                .orElseGet(() -> initOrderProductSampleBO(orderId));
    }

    private void buildProduct(ProductSampleContext context, String orderId, ProductSampleBO productSampleBO) {
        List<ProductInstancePO> productInstanceList = context.getProductInstanceList();
        List<ProductInstancePO> productInstancePOList = productInstanceList.stream()
                .filter(item -> Func.isEmpty(item.getHeaderID()) && Func.equalsSafe(item.getGeneralOrderID(), orderId))
                .collect(Collectors.toList());

        ProductBO productBO = productSampleBO.getProduct();
        if (Func.isEmpty(productBO)) {
            productSampleBO.setProduct(new ProductBO());
        }
        buildProductAttributes(productInstancePOList, context, productBO);
    }

    private void buildProductAttributes(List<ProductInstancePO> productInstancePOList, ProductSampleContext context, ProductBO productBO) {
        if(Func.isEmpty(productInstancePOList)){
            return;
        }
        List<DFFTemplateBO> dffTemplateList = context.getDffTemplateList();
        LanguageType primaryLanguage = context.getPrimaryLanguage();
        String dffFormID = productInstancePOList.get(0).getDFFFormID();
        DFFTemplateBO dffTemplateBO = dffTemplateList.stream()
                .filter(item -> item.getTemplateId().equals(dffFormID))
                .findFirst()
                .orElse(null);
        if (Func.isNotEmpty(dffTemplateBO)) {
            productBO.setTemplateId(dffTemplateBO.getTemplateId());
            for (ProductInstancePO productInstancePO : productInstancePOList) {
                for (DFFAttrBO item : dffTemplateBO.getAttrList()) {
                    List<DFFAttrBO> dffAttrBOList = createDFFAttrBO(item, productBO.getProductAttrList(), productInstancePO);
                    productBO.setProductAttrList(dffAttrBOList);
                }
                productBO.setProductInstanceId(productInstancePO.getRefSampleID());
            }
            setPrimaryLanguageValue(productBO.getProductAttrList(), primaryLanguage);
        }
    }

    private  List<DFFAttrBO>  createDFFAttrBO(DFFAttrBO dffAttrBOItem, List<DFFAttrBO> productAttrList , ProductInstancePO productInstancePO) {
        if(Func.isNull(productAttrList)){
            productAttrList = new ArrayList<>();
        }
        Map<String, Object> productMap = Func.toMap(productInstancePO);
        Object value = productMap.getOrDefault(Func.firstCharToLower(dffAttrBOItem.getLabelCode()), productMap.get(dffAttrBOItem.getLabelCode()));
        DFFAttrBO dffAttrBO = productAttrList.stream().filter(item -> Func.equalsSafe(dffAttrBOItem.getLabelCode(), item.getLabelCode())).findAny().orElse(null);
        boolean existFlag  = true;
        if(dffAttrBO == null){
            existFlag = false;
            dffAttrBO = Func.copy(dffAttrBOItem, DFFAttrBO.class);
            dffAttrBO.setLanguageList(new ArrayList<>());
        }
        String labelName = dffAttrBOItem.getLabelName();
        List<DFFAttrLanguageBO> languageList = dffAttrBOItem.getLanguageList();
        if(Func.isNotEmpty(languageList)){
            DFFAttrLanguageBO dffAttrLanguageBO = languageList.stream().filter(item -> Func.equalsSafe(item.getLanguageId(), productInstancePO.getLanguageID())).findAny().orElse(null);
            if(Func.isNotEmpty(dffAttrLanguageBO) && Func.isNotEmpty(dffAttrLanguageBO.getLabelName())){
                labelName = dffAttrLanguageBO.getLabelName();
            }
        }
        DFFAttrLanguageBO dffAttrLanguageBO = new DFFAttrLanguageBO();
        dffAttrLanguageBO.setLanguageId(productInstancePO.getLanguageID());
        dffAttrLanguageBO.setLangId(productInstancePO.getID());
        dffAttrLanguageBO.setLabelName(labelName);
        dffAttrLanguageBO.setCustomerLabel(dffAttrBOItem.getCustomerLabel());
        dffAttrLanguageBO.setValue(value);
        dffAttrBO.getLanguageList().add(dffAttrLanguageBO);
        if(!existFlag){
            productAttrList.add(dffAttrBO);
        }
        return productAttrList;
    }

    private void setPrimaryLanguageValue(List<DFFAttrBO> productAttrList, LanguageType primaryLanguage) {
        for (DFFAttrBO dffAttrBO : productAttrList) {
            List<DFFAttrLanguageBO> languageList = dffAttrBO.getLanguageList();
            if (Func.isNotEmpty(languageList)) {
                DFFAttrLanguageBO primaryLanguageDFFBO = languageList.stream()
                        .filter(item -> LanguageType.check(item.getLanguageId(), primaryLanguage))
                        .findAny()
                        .orElseGet(() -> languageList.stream()
                                .filter(item -> !LanguageType.check(item.getLanguageId(), primaryLanguage))
                                .findAny()
                                .orElse(null));
                if (Func.isNotEmpty(primaryLanguageDFFBO)) {
                    dffAttrBO.setValue(primaryLanguageDFFBO.getValue());
                    dffAttrBO.setLabelName(primaryLanguageDFFBO.getLabelName());
                } else {
                    dffAttrBO.setValue(null);
                    dffAttrBO.setLabelName(null);
                }
            }
        }
    }

    private void buildSample(ProductSampleContext context, String orderId, ProductSampleBO productSampleBO) {
        List<ProductInstancePO> productInstanceList = context.getProductInstanceList();
        List<ProductInstancePO> sampleInstanceList = productInstanceList.stream()
                .filter(item -> Func.isNotEmpty(item.getHeaderID()) && Func.equalsSafe(item.getGeneralOrderID(), orderId))
                .collect(Collectors.toList());


        List<SampleBO> sampleBOList = productSampleBO.getSampleList();

        Map<String, ProductSampleExtDataDTO> extDataMap = new HashMap<>();
        for (ProductInstancePO productInstancePO : sampleInstanceList) {
            SampleBO sampleBO = initOrGetSampleBO(sampleBOList, productInstancePO);
            buildSampleAttributes(sampleBO, productInstancePO, context);
            if (!sampleBOList.contains(sampleBO)) {
                sampleBOList.add(sampleBO);
            }
//            ProductSampleExtDataDTO productSampleExtDataDTO = initSampleExtDataDTO(extDataMap, productInstancePO);
//            buildSampleExtData(productSampleExtDataDTO, productInstancePO, context);
//            extDataMap.put(productInstancePO.getSampleID(), productSampleExtDataDTO);
//        }
//        if(Func.isNotEmpty(extDataMap)){
//            sampleBOList.stream().forEach(sampleBO -> {
//                ProductSampleExtDataDTO productSampleExtDataDTO = extDataMap.get(sampleBO.getSampleNo());
//                if(Func.isNotEmpty(productSampleExtDataDTO)){
//                    sampleBO.setExtData(JSON.toJSONString(productSampleExtDataDTO));
//                }
//            });
        }
        productSampleBO.setSampleList(sampleBOList);
    }

    private ProductSampleExtDataDTO initSampleExtDataDTO(Map<String, ProductSampleExtDataDTO> extDataMap, ProductInstancePO productInstancePO) {
        ProductSampleExtDataDTO productSampleExtDataDTO = extDataMap.get(productInstancePO.getSampleID());
        if (Func.isEmpty(productSampleExtDataDTO)) {
            productSampleExtDataDTO = new ProductSampleExtDataDTO();
            productSampleExtDataDTO.setLanguageList(Lists.newArrayList());
        }
        return productSampleExtDataDTO;
    }

    private void buildSampleExtData(ProductSampleExtDataDTO productSampleExtDataDTO, ProductInstancePO productInstancePO, ProductSampleContext context){
        LanguageType primaryLanguage = context.getPrimaryLanguage();
        String extFields = productInstancePO.getExtFields();
        if(Func.isEmpty(extFields)) {
            return;
        }
        ProductSampleExtDataDTO extData = JSONObject.parseObject(productInstancePO.getExtFields(), ProductSampleExtDataDTO.class);
        if(Func.isEmpty(extData)) {
            return;
        }
        //primaryLanguage =  productInstancePO.getLanguageID();
        if(LanguageType.check(productInstancePO.getLanguageID(),primaryLanguage)){
            productSampleExtDataDTO.setSampleDescription(extData.getSampleDescription());
            productSampleExtDataDTO.setExternalSampleNo(extData.getExternalSampleNo());
            productSampleExtDataDTO.setTestSurfaceDirection(extData.getTestSurfaceDirection());
        }
        //组装languageList
        ProductSampleExtDataLanguageDTO productSampleExtDataLanguageDTO = new ProductSampleExtDataLanguageDTO();
        Func.copy(extData, productSampleExtDataLanguageDTO);
        productSampleExtDataLanguageDTO.setLanguageId(productInstancePO.getLanguageID());
        productSampleExtDataDTO.getLanguageList().add(productSampleExtDataLanguageDTO);
    }

    private SampleBO initOrGetSampleBO(List<SampleBO> sampleBOList, ProductInstancePO productInstancePO) {
        return sampleBOList.stream()
                .filter(item -> item.getTestSampleInstanceId().equals(productInstancePO.getRefSampleID()))
                .findFirst()
                .orElseGet(() -> initSampleBO(productInstancePO));
    }

    private SampleBO initSampleBO(ProductInstancePO productInstancePO) {
        SampleBO sampleBO = new SampleBO();
        sampleBO.setTestSampleInstanceId(productInstancePO.getRefSampleID());
        sampleBO.setTemplateId(productInstancePO.getDFFFormID());
        sampleBO.setSampleNo(productInstancePO.getSampleID());
        sampleBO.setExternalSampleNo(productInstancePO.getExternalSampleId());
        sampleBO.setSampleAttrList(new ArrayList<>());
        sampleBO.setProductItemNo(productInstancePO.getProductItemNo());
        return sampleBO;
    }

    private void buildSampleAttributes(SampleBO sampleBO, ProductInstancePO productInstancePO, ProductSampleContext context) {
        List<DFFTemplateBO> dffTemplateList = context.getDffTemplateList();
        LanguageType primaryLanguage = context.getPrimaryLanguage();
        DFFTemplateBO dffTemplateBO = dffTemplateList.stream()
                .filter(item -> item.getTemplateId().equals(productInstancePO.getDFFFormID()))
                .findFirst()
                .orElse(null);

        if (Func.isNotEmpty(dffTemplateBO)) {
            for (DFFAttrBO item : dffTemplateBO.getAttrList()) {
                List<DFFAttrBO> dffAttrBOList =  createDFFAttrBO(item, sampleBO.getSampleAttrList(), productInstancePO);
                sampleBO.setSampleAttrList(dffAttrBOList);
            }
        }
        setPrimaryLanguageValue(sampleBO.getSampleAttrList(), primaryLanguage);
    }

    private ProductSampleBO initOrderProductSampleBO(String orderId) {
        ProductSampleBO productSampleBO = new ProductSampleBO();
        productSampleBO.setOrderId(orderId);
        productSampleBO.setProduct(new ProductBO());
        productSampleBO.setSampleList(new ArrayList<>());
        return productSampleBO;
    }

    @Override
    public BaseResponse<List<ProductSampleBO>> execute(ProductSampleContext context) {
        return this.buildDomain(context);
    }
}