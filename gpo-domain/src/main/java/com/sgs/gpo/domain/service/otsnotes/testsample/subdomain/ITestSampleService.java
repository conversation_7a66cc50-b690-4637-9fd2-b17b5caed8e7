package com.sgs.gpo.domain.service.otsnotes.testsample.subdomain;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.test.testline.v2.TestLineSampleBO;
import com.sgs.framework.model.test.testsample.v2.TestSampleBO;
import com.sgs.framework.model.test.testsample.v2.TestSampleIdBO;
import com.sgs.framework.model.test.testsample.v2.TestSampleTestLineBO;
import com.sgs.framework.open.platform.base.service.IBaseService;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.TestSamplePO;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/7 12:00
 */
public interface ITestSampleService extends IBaseService<TestSampleBO,TestSamplePO, TestSampleIdBO,TestSampleQueryReq> {

    /**
     * 查询TestSample 业务对象
     * @param testSampleQueryReq
     * @return
     */
    BaseResponse<List<com.sgs.framework.model.test.testsample.TestSampleBO>> queryV1(TestSampleQueryReq testSampleQueryReq);


    BaseResponse<List<TestSamplePO>> select(TestSampleQueryReq testSampleQueryReq);
    /**
     *  根据TestLineInstanceId查询关联的样品列表
     */
    BaseResponse<List<TestLineSampleBO>> queryListByTestLine(TestSampleQueryReq testSampleQueryReq);


    /**
     * 根据TestSample查询关联的TestLine
     * @param testSampleQueryReq
     * @return
     */
    BaseResponse<List<TestSampleTestLineBO>> querySampleTestLineList(TestSampleQueryReq testSampleQueryReq);

    /**
     *查询样品列表 包括字样对应的原样
     */
    BaseResponse<List<TestSamplePO>> selectALL(TestSampleQueryReq testSampleQueryReq);
}
