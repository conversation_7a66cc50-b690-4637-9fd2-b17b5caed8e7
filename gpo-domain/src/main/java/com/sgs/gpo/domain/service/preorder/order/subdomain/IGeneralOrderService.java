package com.sgs.gpo.domain.service.preorder.order.subdomain;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.model.order.v2.OrderIdBO;
import com.sgs.framework.open.platform.base.service.IBaseService;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.quotation.QuotationHeaderPO;
import com.sgs.gpo.facade.model.payment.costlist.req.ActualFeeSaveReq;
import com.sgs.gpo.facade.model.preorder.order.dto.OrderQuotationDTO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.preorder.productsample.req.QuotationQueryReq;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/7 12:00
 */
public interface IGeneralOrderService extends IBaseService<OrderBO,GeneralOrderPO, OrderIdBO,OrderQueryReq> {

//    BaseResponse<List<GeneralOrderPO>> query(OrderQueryReq orderQueryReq);

    BaseResponse<List<GeneralOrderPO>> queryByRootOrderNo(OrderQueryReq orderQueryReq);
    BaseResponse<List<GeneralOrderPO>> query2(OrderQueryReq orderQueryReq);

    BigDecimal getAllTotalAmountByPaidUp(String orderNo);

    List<OrderQuotationDTO> getQuotationOrderForOrderId(String orderId);

    List<String> getOrderNoByBossOrder(List<String> bossOrderNoList);

    List<String> getOrderNoListByInvoiceNo(List<String> invoiceNos);


    List<QuotationHeaderPO> selectQuotationList(QuotationQueryReq quotationQueryReq);

    void updateActualFeeByOrderNo(List<ActualFeeSaveReq> request);
}
