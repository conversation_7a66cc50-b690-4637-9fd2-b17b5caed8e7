package com.sgs.gpo.domain.service.preorder.order.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderParcelPO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderParcelReq;

import java.util.List;

public interface IOrderParcelService extends IService<OrderParcelPO> {

    List<OrderParcelPO> query(OrderParcelReq orderParcelReq);
}
