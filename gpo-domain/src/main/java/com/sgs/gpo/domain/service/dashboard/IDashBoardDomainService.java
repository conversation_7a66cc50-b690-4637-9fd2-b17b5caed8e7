package com.sgs.gpo.domain.service.dashboard;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.gpo.facade.model.dashboard.req.DashBoardDetailQueryRep;
import com.sgs.gpo.facade.model.dashboard.rsp.*;
import com.sgs.gpo.facade.model.dashboard.req.DashBoardQueryRep;

import java.util.List;

public interface IDashBoardDomainService {

    /**
     * 初始化饼图看板数据
     */
    BaseResponse<TodaySummaryRsp> queryTodaySummary(DashBoardQueryRep dashBoardQueryRep) ;

    /**
     * 初始化matrix看板数据
     */
    BaseResponse<MatrixTodayProgressRsp> queryMatrixTodayProgress(DashBoardQueryRep dashBoardQueryRep);
    /**
     * 初始化matrix未来分布看板数据
     */
    BaseResponse<MatrixFuturePlanRsp> queryMatrixFuturePlan(DashBoardQueryRep dashBoardQueryRep);

    /**
     *  查询matrix报表的todayAndPlanDetail明细
     */
    BaseResponse<List<TestLineBO>>  queryMatrixPlanDetail(DashBoardDetailQueryRep dashBoardDetailQueryRep,Integer page,Integer rows);

    /**
     * 查询任务进度的report明细
     */
    BaseResponse<List<ReportTaskDetailRsp>> queryReportTaskDetail(DashBoardQueryRep dashBoardQueryRep, Integer page, Integer rows);
}
