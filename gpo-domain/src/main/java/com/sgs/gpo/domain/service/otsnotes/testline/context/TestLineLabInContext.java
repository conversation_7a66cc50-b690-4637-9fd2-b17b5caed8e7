package com.sgs.gpo.domain.service.otsnotes.testline.context;

import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.job.JobPO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineLabInReq;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @title: TestLineLabInContext
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/10/17 10:27
 */
@Data
public class TestLineLabInContext extends TestLineContext<TestLineLabInReq> {

    //暂存需要labin的TL关联的jobno
    private Set<String> jobNos;
    //需要labin的job集合
    private List<JobPO> needLabInJobs;
    //labIn的tl集合
    private Set<String> tlInstanceLabIns;

    //TestLine集合
    private List<TestLineBO> testLineBOList;

    public TestLineLabInContext(TestLineLabInReq testLineLabInReq){
        this.setLab(SystemContextHolder.getLab());
        this.setToken(SecurityContextHolder.getSgsToken());
        this.setUserInfo(SecurityContextHolder.getUserInfo());
        this.setProductLineCode(Func.isNotEmpty(testLineLabInReq.getProductLineCode())?testLineLabInReq.getProductLineCode():this.getLab().getBuCode());
        this.setParam(testLineLabInReq);
    }
}
