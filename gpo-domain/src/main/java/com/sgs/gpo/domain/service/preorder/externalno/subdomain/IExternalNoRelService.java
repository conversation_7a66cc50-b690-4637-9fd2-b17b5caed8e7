package com.sgs.gpo.domain.service.preorder.externalno.subdomain;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.open.platform.base.service.IBaseService;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.externalno.ExternalNoRelPO;
import com.sgs.gpo.facade.model.preorder.externalno.bo.ExternalNoBO;
import com.sgs.gpo.facade.model.preorder.externalno.bo.ExternalNoIdBO;
import com.sgs.gpo.facade.model.preorder.externalno.req.ExternalNoQueryReq;

import java.util.List;

/**
 * <AUTHOR>
 * @title: IExternalNoRelService
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/8 17:48
 */

public interface IExternalNoRelService extends IBaseService<ExternalNoBO, ExternalNoRelPO, ExternalNoIdBO, ExternalNoQueryReq> {
    List<ExternalNoRelPO> select(ExternalNoQueryReq externalNoQueryReq);
}
