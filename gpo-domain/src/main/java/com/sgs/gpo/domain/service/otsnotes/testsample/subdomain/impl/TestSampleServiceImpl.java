package com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.beust.jcommander.internal.Lists;
import com.beust.jcommander.internal.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.enums.SampleType;
import com.sgs.framework.model.test.testline.v2.TestLineSampleBO;
import com.sgs.framework.model.test.testsample.v2.TestSampleBO;
import com.sgs.framework.model.test.testsample.v2.TestSampleIdBO;
import com.sgs.framework.model.test.testsample.v2.TestSampleTestLineBO;
import com.sgs.framework.open.platform.base.service.impl.AbstractBaseService;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testsample.TestSampleGroupMapper;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testsample.TestSampleMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.TestSampleGroupPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.TestSamplePO;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.ITestSampleService;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/7 12:00
 */
@Service
public class TestSampleServiceImpl
        extends AbstractBaseService<TestSampleBO,TestSamplePO, TestSampleIdBO,TestSampleMapper,TestSampleQueryReq>
        implements ITestSampleService {

    @Autowired
    private TestSampleMapper testSampleMapper;
    @Autowired
    private TestSampleGroupMapper testSampleGroupMapper;

    @Override
    public BaseResponse<List<com.sgs.framework.model.test.testsample.TestSampleBO>> queryV1(TestSampleQueryReq testSampleQueryReq) {
        if(Func.isEmpty(testSampleQueryReq)|| (Func.isEmpty(testSampleQueryReq.getOrderId()) && Func.isEmpty(testSampleQueryReq.getOrderNo()) && Func.isEmpty(testSampleQueryReq.getTestSampleInstanceIdList()))){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"orderId/SampleId"});
        }
        return BaseResponse.newSuccessInstance(testSampleMapper.query(testSampleQueryReq));
    }

    @Override
    public BaseResponse<List<TestSamplePO>> select(TestSampleQueryReq testSampleQueryReq) {
        if(Func.isEmpty(testSampleQueryReq)||
                (Func.isEmpty(testSampleQueryReq.getOrderNo())
                        && Func.isEmpty(testSampleQueryReq.getTestSampleInstanceIdList())
                        && Func.isEmpty(testSampleQueryReq.getOrderNoList()))){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"orderId/SampleId"});
        }
        LambdaQueryWrapper<TestSamplePO> wrapper= Wrappers.lambdaQuery();
        if(Func.isNotEmpty(testSampleQueryReq.getTestSampleInstanceIdList())){
            wrapper.in(TestSamplePO::getId,testSampleQueryReq.getTestSampleInstanceIdList());
        }
        if(Func.isNotEmpty(testSampleQueryReq.getOrderNo())){
            wrapper.eq(TestSamplePO::getOrderNo,testSampleQueryReq.getOrderNo());
        }
        if(Func.isNotEmpty(testSampleQueryReq.getOrderNoList())){
            wrapper.in(TestSamplePO::getOrderNo,testSampleQueryReq.getOrderNoList());
        }
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }

    @Override
    public BaseResponse<List<TestLineSampleBO>> queryListByTestLine(TestSampleQueryReq testSampleQueryReq) {
        if(Func.isEmpty(testSampleQueryReq.getTestLineInstanceId())){
            return BaseResponse.newFailInstance("param miss");
        }
        return BaseResponse.newSuccessInstance(testSampleMapper.queryListByTestLine(testSampleQueryReq));
    }

    @Override
    public BaseResponse<List<TestSampleTestLineBO>> querySampleTestLineList(TestSampleQueryReq testSampleQueryReq) {
        if(Func.isEmpty(testSampleQueryReq)|| Func.isEmpty(testSampleQueryReq.getTestSampleInstanceIdList())){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"SampleId"});
        }
        return BaseResponse.newSuccessInstance(testSampleMapper.querySampleTestLineList(testSampleQueryReq));
    }

    @Override
    public BaseResponse<List<TestSamplePO>> selectALL(TestSampleQueryReq testSampleQueryReq) {
        if(Func.isEmpty(testSampleQueryReq)||Func.isEmpty(testSampleQueryReq.getTestSampleInstanceIdList())){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"SampleId"});
        }
        BaseResponse<List<TestSamplePO>> sampleListRes = this.select(testSampleQueryReq);
        if(sampleListRes.isFail() || Func.isEmpty(sampleListRes.getData())){
            return sampleListRes;
        }
        // 最终的样品列表
        List<TestSamplePO> allSampleList = sampleListRes.getData();
        Set<String> originalSampleIds = sampleListRes.getData().stream().filter(item->SampleType.check(item.getSampleType(),SampleType.OriginalSample))
                .map(TestSamplePO::getId).collect(Collectors.toSet());
        // 需要补充的原样
        List<TestSamplePO> originalSampleList = Lists.newArrayList();
        allSampleList.stream().forEach(item->{
            // 查找子样对应的样原样
            if(SampleType.check(item.getSampleType(),SampleType.Sample)){
                if(Func.isNotEmpty(item.getSampleParentId())){
                    TestSamplePO sampleParent = testSampleMapper.selectById(item.getSampleParentId());
                    if(SampleType.check(sampleParent.getSampleType(),SampleType.OriginalSample) && !originalSampleIds.contains(sampleParent.getId())){
                        originalSampleList.add(sampleParent);
                        originalSampleIds.add(sampleParent.getId());
                    }
                }
            }
            // 查询子子样对应的原样
            if(SampleType.check(item.getSampleType(),SampleType.SubSample)){
                if(Func.isNotEmpty(item.getSampleParentId())){
                    TestSamplePO sampleParent = testSampleMapper.selectById(item.getSampleParentId());
                    if(Func.isNotEmpty(sampleParent) && Func.isNotEmpty(sampleParent.getSampleParentId())){
                        TestSamplePO originalSample = testSampleMapper.selectById(sampleParent.getSampleParentId());
                        if(SampleType.check(originalSample.getSampleType(),SampleType.OriginalSample) && !originalSampleIds.contains(originalSample.getId())){
                            originalSampleList.add(originalSample);
                            originalSampleIds.add(originalSample.getId());
                        }
                    }
                }
            }
            // Mix Share
            if(SampleType.check(item.getSampleType(),SampleType.MixSample,SampleType.ShareSample)){
                // 查询Group 数据
                Set<String> testSampleIds = Sets.newHashSet();
                testSampleIds.add(item.getId());
                LambdaQueryWrapper<TestSampleGroupPO> wrapper= Wrappers.lambdaQuery();
                wrapper.in(TestSampleGroupPO::getSampleId,testSampleIds);
                List<TestSampleGroupPO> testSampleGroupList = testSampleGroupMapper.selectList(wrapper);
                if(Func.isNotEmpty(testSampleGroupList)){
                    testSampleGroupList.stream().forEach(sampleGroup->{
                        TestSamplePO testSample = testSampleMapper.selectById(sampleGroup.getSampleGroupId());
                        // 查找子样对应的样原样
                        if(SampleType.check(testSample.getSampleType(),SampleType.Sample)){
                            if(Func.isNotEmpty(testSample.getSampleParentId())){
                                TestSamplePO sampleParent = testSampleMapper.selectById(testSample.getSampleParentId());
                                if(SampleType.check(sampleParent.getSampleType(),SampleType.OriginalSample) && !originalSampleIds.contains(sampleParent.getId())){
                                    originalSampleList.add(sampleParent);
                                    originalSampleIds.add(sampleParent.getId());
                                }
                            }
                        }
                        // 查询子子样对应的原样
                        if(SampleType.check(testSample.getSampleType(),SampleType.SubSample)){
                            if(Func.isNotEmpty(testSample.getSampleParentId())){
                                TestSamplePO sampleParent = testSampleMapper.selectById(testSample.getSampleParentId());
                                if(Func.isNotEmpty(sampleParent) && Func.isNotEmpty(sampleParent.getSampleParentId())){
                                    TestSamplePO originalSample = testSampleMapper.selectById(sampleParent.getSampleParentId());
                                    if(SampleType.check(originalSample.getSampleType(),SampleType.OriginalSample) && !originalSampleIds.contains(originalSample.getId())){
                                        originalSampleList.add(originalSample);
                                        originalSampleIds.add(originalSample.getId());
                                    }
                                }
                            }
                        }
                    });
                }
            }
        });
        if(Func.isNotEmpty(originalSampleList)){
            allSampleList.addAll(originalSampleList);
        }
        return BaseResponse.newSuccessInstance(allSampleList);
    }

    @Override
    public List<TestSampleBO> convertToBO(Collection<TestSamplePO> poList) {
        return null;
    }

    @Override
    public List<TestSamplePO> convertToPO(Collection<TestSampleBO> boList) {
        return null;
    }

    @Override
    public LambdaQueryWrapper<TestSamplePO> createWrapper(TestSampleQueryReq queryReq) {
        return null;
    }
}
