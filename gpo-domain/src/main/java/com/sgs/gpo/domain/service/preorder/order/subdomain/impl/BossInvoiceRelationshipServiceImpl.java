package com.sgs.gpo.domain.service.preorder.order.subdomain.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.BossInvoiceRelationshipMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.BossInvoiceRelationshipPO;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IBossInvoiceRelationshipService;
import org.springframework.stereotype.Service;


@Service
public class BossInvoiceRelationshipServiceImpl extends ServiceImpl<BossInvoiceRelationshipMapper,BossInvoiceRelationshipPO> implements IBossInvoiceRelationshipService {

}
