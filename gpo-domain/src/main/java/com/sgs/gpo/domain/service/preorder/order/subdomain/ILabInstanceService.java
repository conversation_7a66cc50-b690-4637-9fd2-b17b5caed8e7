package com.sgs.gpo.domain.service.preorder.order.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.LabInstancePO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/6 09:53
 */
public interface ILabInstanceService extends IService<LabInstancePO> {

    /**
     * 查询实验室实例列表
     * @param orderIdReq
     * @return
     */
    BaseResponse<List<LabInstancePO>> select(OrderIdReq orderIdReq) ;
}
