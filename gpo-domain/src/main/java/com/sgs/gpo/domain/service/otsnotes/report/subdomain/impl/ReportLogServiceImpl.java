package com.sgs.gpo.domain.service.otsnotes.report.subdomain.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.report.ReportLogMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportLogPO;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportLogService;
import lombok.extern.log4j.Log4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Log4j
public class ReportLogServiceImpl extends ServiceImpl<ReportLogMapper, ReportLogPO> implements IReportLogService {
    @Autowired
    private ReportLogMapper reportLogMapper;

    @Override
    public BaseResponse saveReportLog(ReportLogPO reportLogPO) {
        try {
            reportLogMapper.insertReportLog(reportLogPO);
            return BaseResponse.newSuccessInstance("SaveReportLog Success");
        } catch (Exception e) {
            log.error("SaveReportLog error", e);
            return BaseResponse.newFailInstance("SaveReportLog Error");
        }
    }
}
