package com.sgs.gpo.domain.service.otsnotes.testline.command;

import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.model.order.order.OrderBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineDomainService;
import com.sgs.gpo.domain.service.otsnotes.testline.context.TestLineLabInContext;
import com.sgs.gpo.core.constants.BizLogConstant;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.ITestLineService;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineLabInReq;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.info.BizLogInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Primary
@Service
@Slf4j
public class TestLineLabInCMD extends BaseCommand<TestLineLabInContext> {

    @Autowired
    private ITestLineService testLineService;
    @Autowired
    private BizLogClient bizLogClient;
    @Autowired
    private ITestLineDomainService testLineDomainService;



    @Override
    public BaseResponse validateParam(TestLineLabInContext context) {
        if (Func.isEmpty(context) || Func.isEmpty(context.getParam())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        TestLineLabInReq testLineLabInReq = context.getParam();

        UserInfo userInfo = context.getUserInfo();

        if (Func.isEmpty(userInfo)) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"user"});
        }

        if (Func.isEmpty(testLineLabInReq.getTestLineInstanceIdList())) {
            return BaseResponse.newFailInstance("Please select a  test line first!", null);
        }

        return BaseResponse.newSuccessInstance(true);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse execute(TestLineLabInContext context) {
        TestLineLabInReq testLineLabInReq = context.getParam();
        // 更新TestLine 工程师及开始测试时间
        BaseResponse<List<String>> tlLabInResponse = testLineService.labIn(testLineLabInReq);

        return tlLabInResponse;
    }

    @Override
    public BaseResponse before(TestLineLabInContext context){
        if (Func.isEmpty(context.getTestLineBOList())) {
            Set<String> tlInstanceLabIns = context.getParam().getTestLineInstanceIdList();
            OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
            orderTestLineReq.setTestLineInstanceIdList(tlInstanceLabIns);
            BaseResponse<List<TestLineBO>> testLineResponse = testLineDomainService.queryTestLine(orderTestLineReq);
            if (Func.isNotEmpty(testLineResponse) && Func.isNotEmpty(testLineResponse.getData())) {
                List<TestLineBO> testLineBOList = testLineResponse.getData();
                context.setTestLineBOList(testLineBOList);
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse after(TestLineLabInContext context) {
        UserInfo userInfo = context.getUserInfo();
        // 记录Bizlog
        List<TestLineBO> testLineBOList = context.getTestLineBOList();
        List<OrderBO> orderBOList = context.getParam().getOrderBOList();
        if (Func.isNotEmpty(testLineBOList)) {
            //  此处按orderNo分组后，拼接testItemNo
            Map<String, List<TestLineBO>> ordertestLineMap = testLineBOList.stream().collect(Collectors.groupingBy(b -> b.getOrderNo()));
            ordertestLineMap.forEach((orderNo, testLineListDTOList) -> {
                OrderBO order = null;
                if(Func.isNotEmpty(orderBOList)) {
                    order = orderBOList.stream().filter(e -> Func.equals(e.getHeader().getOrderNo(), orderNo)).findFirst().orElse(null);
                }
                BizLogInfo bizLog = new BizLogInfo();
                bizLog.setBizId(orderNo);
                boolean labIsNotEmpty = Func.isNotEmpty(order) && Func.isNotEmpty(order.getLab());
                bizLog.setBu((labIsNotEmpty && Func.isNotEmpty(order.getLab().getBuCode())) ? order.getLab().getBuCode() : context.getProductLineCode());
                bizLog.setLab((labIsNotEmpty && Func.isNotEmpty(order.getLab().getLocationCode())) ? order.getLab().getLocationCode() : userInfo.getCurrentLabCode());
                bizLog.setBizOpType(BizLogConstant.TEST_HISTORY);
                bizLog.setOpType(Constants.OBJECT.TEST_LINE.ACTION.LabIn.name());
                bizLog.setOpUser(userInfo.getRegionAccount());
                String testItemNos = testLineListDTOList.stream().filter(i -> Func.isNotEmpty(i.getTestItemNo())).map(tl -> tl.getTestItemNo()).collect(Collectors.joining(","));
                String bizNewVal = "TestItemNo[" + testItemNos + "]";
                bizLog.setNewVal(bizNewVal);
                bizLogClient.doSend(bizLog);
            });
        }
        return super.after(context);
    }

}
