package com.sgs.gpo.domain.service.preorder.attachment.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.attachment.OrderAttachmentMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.attachment.OrderAttachmentPO;
import com.sgs.gpo.domain.service.preorder.attachment.IOrderAttachmentService;
import com.sgs.gpo.facade.model.preorder.order.req.OrderAttachmentItem;
import com.sgs.gpo.facade.model.preorder.order.req.OrderAttachmentQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.UpdateAttachmentSeqReq;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @title: OrderAttachmentServiceImpl
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/12 17:27
 */
@Service
public class OrderAttachmentServiceImpl extends ServiceImpl<OrderAttachmentMapper, OrderAttachmentPO> implements IOrderAttachmentService {



    @Override
    public BaseResponse<List<OrderAttachmentPO>> queryTestLineAttachment(Set<String> testLineInstanceIdList, Set<String> testSampleIdList) {
        if (Func.isEmpty(testLineInstanceIdList) && Func.isEmpty(testSampleIdList)) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        QueryWrapper<OrderAttachmentPO> queryWrapper = new QueryWrapper();
        if (Func.isNotEmpty(testLineInstanceIdList)) {
            queryWrapper.in(OrderAttachmentPO.COLUMN.TEST_LINE_INSTANCE_ID, testLineInstanceIdList);
        }
        if (Func.isNotEmpty(testSampleIdList)) {
            if (Func.isNotEmpty(testLineInstanceIdList)) {
                queryWrapper.or();
            }
            queryWrapper.in(OrderAttachmentPO.COLUMN.OBJECT_ID, testSampleIdList);
        }
        List<OrderAttachmentPO> orderAttachmentPOList = baseMapper.selectList(queryWrapper);
        return BaseResponse.newSuccessInstance(orderAttachmentPOList);
    }

    @Override
    public BaseResponse<List<OrderAttachmentPO>> select(OrderAttachmentQueryReq orderAttachmentQueryReq) {
        if (Func.isEmpty(orderAttachmentQueryReq) || (Func.isAllEmpty(orderAttachmentQueryReq.getOrderIdList(),orderAttachmentQueryReq.getReportNoList(),orderAttachmentQueryReq.getJobNoList()))) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        LambdaQueryWrapper<OrderAttachmentPO> wrapper = new LambdaQueryWrapper<>();
        if (Func.isNotEmpty(orderAttachmentQueryReq.getOrderIdList())) {
            wrapper.in(OrderAttachmentPO::getGeneralOrderID, orderAttachmentQueryReq.getOrderIdList());
        }
        if (Func.isNotEmpty(orderAttachmentQueryReq.getReportNoList())) {
            wrapper.in(OrderAttachmentPO::getReportNo, orderAttachmentQueryReq.getReportNoList());
        }
        if (Func.isNotEmpty(orderAttachmentQueryReq.getBusinessTypeList())) {
            wrapper.in(OrderAttachmentPO::getBusinessType, orderAttachmentQueryReq.getBusinessTypeList());
        }
        if(Func.isNotEmpty(orderAttachmentQueryReq.getSampleNoList())){
            wrapper.in(OrderAttachmentPO::getSampleNo,orderAttachmentQueryReq.getSampleNoList());
        }
        if(Func.isNotEmpty(orderAttachmentQueryReq.getJobNoList())){
            wrapper.in(OrderAttachmentPO::getJobNo,orderAttachmentQueryReq.getJobNoList());
        }
        wrapper.eq(OrderAttachmentPO::getActiveIndicator,1);
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }

    @Override
    public BaseResponse updateOrderAttachmentSeq(UpdateAttachmentSeqReq updateAttachmentSeqReq) {
        if (Func.isEmpty(updateAttachmentSeqReq) || Func.isEmpty(updateAttachmentSeqReq.getAttachmentList())) {
            return BaseResponse.newSuccessInstance(null);
        }
        // 按照大小分割List 分批匹配执行
        int updateNum = updateAttachmentSeqReq.getAttachmentList().size();
        if (updateNum <= Constants.BATCH_SIZE) {
            return BaseResponse.newSuccessInstance(baseMapper.updateSequence(updateAttachmentSeqReq.getAttachmentList())>0);
        }
        List<List<OrderAttachmentItem>> subSets = Lists.partition(updateAttachmentSeqReq.getAttachmentList(), Constants.BATCH_SIZE);
        subSets.stream().forEach(item -> baseMapper.updateSequence(item));
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public List<OrderAttachmentPO> selectByInvoiceNo(Set<String> invoiceNoList) {
        List<OrderAttachmentPO> list = new ArrayList<>();
        if(Func.isNotEmpty(invoiceNoList)){
            LambdaQueryWrapper<OrderAttachmentPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(OrderAttachmentPO::getObjectID, invoiceNoList);
            list = this.list(wrapper);
        }
        return list;
    }

}
