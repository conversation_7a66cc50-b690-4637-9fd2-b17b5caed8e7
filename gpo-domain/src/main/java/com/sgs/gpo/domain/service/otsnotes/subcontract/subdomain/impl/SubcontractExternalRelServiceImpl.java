package com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.subcontract.SubcontractExternalRelationshipMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractExternalRelationshipPO;
import com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.ISubcontractExternalRelationshipService;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractExternalRelQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class SubcontractExternalRelServiceImpl extends ServiceImpl<SubcontractExternalRelationshipMapper, SubcontractExternalRelationshipPO> implements ISubcontractExternalRelationshipService {
    @Override
    public BaseResponse<List<SubcontractExternalRelationshipPO>> query(SubcontractExternalRelQueryReq req) {
        if(Func.isEmpty(req)||Func.isEmpty(req.getSubContractNoList())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        LambdaQueryWrapper<SubcontractExternalRelationshipPO> wrapper= Wrappers.<SubcontractExternalRelationshipPO>lambdaQuery()
                .in(SubcontractExternalRelationshipPO::getSubContractNo,req.getSubContractNoList());
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }
}
