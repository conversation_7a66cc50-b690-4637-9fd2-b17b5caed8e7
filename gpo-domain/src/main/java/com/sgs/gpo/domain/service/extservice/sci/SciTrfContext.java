package com.sgs.gpo.domain.service.extservice.sci;

import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.common.productsample.CareLabelBO;
import com.sgs.framework.model.common.productsample.ProductBO;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.test.testmatrix.TestMatrixBO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportFilePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.TestSamplePO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryTrfRelationshipPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.operation.OrderOperationHistoryPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.BossOrderInvoicePO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.payment.BossOrderInvoiceDTO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.productinstance.ProductInstancePO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.status.GpoStatusPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.testrequest.TestRequestContactPO;
import com.sgs.gpo.facade.model.otsnotes.conclusion.ConclusionDTO;
import com.sgs.gpo.facade.model.preorder.order.bo.OrderTrfBO;
import com.sgs.gpo.facade.model.report.bo.ReportMatrixRelBO;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @title: SciTrfSyncContext
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/12/26 14:58
 */
@Data
public class SciTrfContext<Input, Domain> extends BaseContext<Input,Domain> {

    private Set<String> orderNoList;

    //Order2Trf
    private List<TestRequestContactPO> testRequestContactList;

    //Trf2Order


    //SciTrfSync
    private GeneralOrderPO rootOrderInfo;
    private String syncTrfRootOrderNo;
    private List<OrderBO> orderList;
    private List<GeneralOrderPO> childOrderList;
    private List<TestLineBO> testLineList;
    private List<TestSamplePO> testSampleList;
    private List<BossOrderInvoiceDTO> invoiceList;
    private List<ReportPO> reportList;
    private List<ReportBO> reportBOList;
    private List<ReportFilePO> reportFileList;
    private List<ReportMatrixRelBO> reportMatrixRelList;
    private List<TestMatrixBO> reportMatrixList;
    private List<ConclusionDTO> reportConclusionList;
    private List<ConclusionDTO> reportMatrixConclusionList;
    private List<EnquiryPO> enquiryList;
    private List<EnquiryTrfRelationshipPO> enquiryTrfRelationshipPOList;
    private List<ProductInstancePO> productInstanceList;
    private List<ProductBO> orderProductList;
    private List<TestMatrixBO> testMatrixBOList;
    private List<CareLabelBO> careLabelBOList;
    private List<OrderTrfBO> orderTrfBOList;
    private List<OrderOperationHistoryPO> orderOperationHistoryPOList;
    private List<GpoStatusPO> gpoStatusPOList;
    private String labCode;
    private String buCode;


    private String sciOrderNo;
    private String sciOrderId;

    private int primaryLanguageId;

    private String orderLabCode;
    private String orderBuCode;
}
