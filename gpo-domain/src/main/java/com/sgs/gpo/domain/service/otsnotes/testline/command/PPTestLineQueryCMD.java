package com.sgs.gpo.domain.service.otsnotes.testline.command;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.gpo.domain.service.otsnotes.testline.context.TestLineQueryContext;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/29 13:33
 */
@Service
@Slf4j
@Scope(value = "prototype")
@Primary
public class PPTestLineQueryCMD extends TestLineQueryBaseCMD {

    @Override
    public BaseResponse buildDomain(TestLineQueryContext<OrderTestLineReq, TestLineBO> context) {
        return super.buildDomain(context);
    }

    @Override
    public BaseResponse before(TestLineQueryContext<OrderTestLineReq, TestLineBO> context) {
        OrderTestLineReq orderTestLineReq = context.getParam();
        List<TestLineBO>  testLineList = testLineMapper.selectPPTestLine(context.getTestLineBOPage(),orderTestLineReq);
        context.setTestLineList(testLineList);
        return super.before(context);
    }

}
