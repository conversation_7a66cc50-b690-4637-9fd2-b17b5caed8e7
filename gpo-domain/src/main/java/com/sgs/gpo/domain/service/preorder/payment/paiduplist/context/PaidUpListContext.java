package com.sgs.gpo.domain.service.preorder.payment.paiduplist.context;

import com.sgs.framework.core.base.PageBO;
import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderPaidUpPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.PaidUpPO;
import com.sgs.gpo.facade.model.payment.costlist.dto.BossImportDTO;
import com.sgs.gpo.facade.model.payment.costlist.dto.PaidUpImportDTO;
import com.sgs.gpo.facade.model.payment.paiduplist.rsp.PaidUpListRsp;
import lombok.Data;
import org.apache.commons.lang3.mutable.MutableBoolean;

import java.util.List;

@Data
public class PaidUpListContext<Input> extends BaseContext<Input, String> {

    private PageBO<PaidUpListRsp> paidUpPage;

    private PaidUpPO paidUpPO;
    private List<OrderPaidUpPO> orderPaidUpPOList;
    private List<BossImportDTO> updateOrderList;
    private List<PaidUpImportDTO> paidUpImportDTOList;
    private MutableBoolean hasDiffCurrency;
    private List<String> orderNoList;


    //构造函数
    public PaidUpListContext(Input input){
        this.setLab(SystemContextHolder.getLab());
        this.setToken(SecurityContextHolder.getSgsToken());
        this.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        this.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        this.setParam(input);
    }
}
