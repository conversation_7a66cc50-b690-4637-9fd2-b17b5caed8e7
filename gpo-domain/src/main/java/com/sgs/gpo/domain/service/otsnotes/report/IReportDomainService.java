package com.sgs.gpo.domain.service.otsnotes.report;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.PageBO;
import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.framework.model.report.report.v2.ReportIdBO;
import com.sgs.framework.open.platform.base.service.IDomainService;
import com.sgs.gpo.facade.model.otsnotes.report.rsp.ReportScanFileRsp;
import com.sgs.gpo.facade.model.otsnotes.report.vo.ReportVO;
import com.sgs.gpo.facade.model.otsnotes.reportmatrix.vo.ReportMatrixVO;
import com.sgs.gpo.facade.model.report.bo.ReportAutoDeliverBO;
import com.sgs.gpo.facade.model.report.req.PrelimReportGenerateReq;
import com.sgs.gpo.facade.model.report.req.ReportAccreditationUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportAutoDeliverQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportBatchUpdateTestingTypeReq;
import com.sgs.gpo.facade.model.report.req.ReportExtForTLUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportFileQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportGenerateReq;
import com.sgs.gpo.facade.model.report.req.ReportIdReq;
import com.sgs.gpo.facade.model.report.req.ReportMatrixQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportUpdateTestingTypeReq;
import com.sgs.gpo.facade.model.report.req.TestResultGenerateReq;

import java.util.List;

public interface IReportDomainService extends IDomainService<ReportBO, ReportIdBO, ReportQueryReq> {

    /***
     * 生成报告时,报告数据处理
     * 状态更新，附件更新
     * @return
     */
    BaseResponse generateReport(ReportGenerateReq reportGenerateReq);

    BaseResponse<Boolean> updateReportExtForTL(ReportExtForTLUpdateReq reportExtForTLUpdateReq);
    BaseResponse<List<ReportMatrixVO>> queryReportMatrix(ReportMatrixQueryReq reportMatrixQueryReq);
    BaseResponse<ReportScanFileRsp> queryReportScanFile(ReportIdReq reportIdReq);
    /**
     * 更新报告签名信息(Editor,Reviewer)
     */
    BaseResponse updateReportSigner(ReportUpdateReq reportUpdateReq);

    /**
     * Prelim报告生成DB更新
     * @param req
     * @return
     */
    BaseResponse<String> generatePrelimReport(PrelimReportGenerateReq req);

    /**
     * 更新报告ExternalReportNo
     */

    BaseResponse updateExternalReportNo(ReportUpdateReq reportUpdateReq);

    /**
     * 生成报告之前更新Template数据
     */
    BaseResponse updateReportTemplate(List<TestResultGenerateReq> generateTestResultReqs);

    /**
     * 查询签名信息
     * @param regionAccount
     * @param labCode
     * @param signTypeCode
     * @return
     */
    String getSignatureInfo(String regionAccount, String labCode, Integer signTypeCode);

    /**
     * 查询订单下有效状态的报告清单
     * @param orderNo
     * @return
     */
    BaseResponse<List<ReportVO>> listByOrderNo(String orderNo);

    BaseResponse queryReportByReportNo(String reportNo);


    BaseResponse batchUpdateTestingType(ReportBatchUpdateTestingTypeReq reportBatchUpdateTestingTypeReq);

    BaseResponse updateTestingType(ReportUpdateTestingTypeReq reportUpdateTestingTypeReq);

    /**
     * 查询报告的History
     * @param reportFileQueryReq
     * @return
     */
    BaseResponse queryReportFileHistory(ReportFileQueryReq reportFileQueryReq);

    BaseResponse<Boolean> updateReportAccreditation(ReportAccreditationUpdateReq reportAccreditationUpdateReq);

    BaseResponse<PageBO<ReportAutoDeliverBO>> autoDeliverPage(ReportAutoDeliverQueryReq queryReq, Integer page, Integer rows);
}
