package com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testsample.LimitGroupMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.LimitGroupPO;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.ILimitGroupService;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.LimitGroupQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class LimitGroupServiceImpl extends ServiceImpl<LimitGroupMapper, LimitGroupPO> implements ILimitGroupService {


    @Override
    public BaseResponse<List<LimitGroupPO>> select(LimitGroupQueryReq limitGroupQueryReq) {
        if(Func.isEmpty(limitGroupQueryReq)||Func.isEmpty(limitGroupQueryReq.getTestSampleInstanceIds())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        LambdaQueryWrapper<LimitGroupPO> wrapper= Wrappers.<LimitGroupPO>lambdaQuery()
                .in(LimitGroupPO::getTestSampleId,limitGroupQueryReq.getTestSampleInstanceIds());
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }

}
