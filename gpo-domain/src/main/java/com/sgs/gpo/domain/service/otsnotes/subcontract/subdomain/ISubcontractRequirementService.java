package com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractRequirementPO;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractRequirementQueryReq;

import java.util.List;

public interface ISubcontractRequirementService extends IService<SubcontractRequirementPO> {

    BaseResponse<List<SubcontractRequirementPO>> query(SubcontractRequirementQueryReq req);
}
