package com.sgs.gpo.domain.service.dashboard.command;

import cn.hutool.core.util.NumberUtil;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.util.StringPool;
import com.sgs.framework.model.common.lab.LabBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.domain.service.dashboard.context.DashboardContext;
import com.sgs.gpo.dbstorages.mybatis.mapper.dashboard.DashboardMapper;
import com.sgs.gpo.domain.service.common.lab.ILabService;
import com.sgs.gpo.facade.model.dashboard.rsp.MatrixTodayProgressItem;
import com.sgs.gpo.facade.model.dashboard.req.DashBoardQueryRep;
import com.sgs.gpo.facade.model.dashboard.rsp.MatrixTodayProgressRsp;
import com.sgs.gpo.facade.model.lab.req.LabReq;
import com.sgs.gpo.facade.model.trims.labsection.rsp.LabSectionRsp;
import com.sgs.gpo.integration.trims.TrimsClient;
import com.sgs.trimslocal.facade.model.labsection.rsp.GetLabSectionBaseInfoRsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@Scope(value = "prototype")
public class MatrixTodayProgressQueryCMD extends BaseCommand<DashboardContext> {
    @Autowired
    private DashboardMapper dashboardMapper;
    @Autowired
    private TrimsClient trimsClient;
    @Autowired
    private ILabService labService;

    @Override
    public BaseResponse validateParam(DashboardContext context) {
        // VP1、校验入参不能为空
        if(Func.isEmpty(context)){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        DashBoardQueryRep dashBoardQueryRep = context.getParam();
        if(Func.isEmpty(dashBoardQueryRep)){
            dashBoardQueryRep = new DashBoardQueryRep();
        }
        // VP2、校验用户不能为空
        UserInfo userInfo = context.getUserInfo();
        if(Func.isEmpty(userInfo)){
            return BaseResponse.newFailInstance(ResponseCode.TokenExpire);
        }
        String labCode= userInfo.getCurrentLabCode();
        context.setLabCode(labCode);
        // 根据labCode获取labId
        LabReq labReq = new LabReq();
        labReq.setLabCode(labCode);
        BaseResponse<List<LabBO>> labListRes = labService.search(labReq);
        if(Func.isNotEmpty(labListRes) && Func.isNotEmpty(labListRes.getData())){
            Integer labId = labListRes.getData().get(0).getLabId();
            context.setLabId(labId);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(DashboardContext context) {
        // 初始化查询条件
        Date currentDate = new Date();
        DashBoardQueryRep dashBoardQueryRep = context.getParam();
        dashBoardQueryRep.setLabCode(context.getLabCode());
        dashBoardQueryRep.setCurrentDate(currentDate);
        dashBoardQueryRep.setStartDate(currentDate);
        dashBoardQueryRep.setEndDate(getDate(currentDate,LocalTime.MAX));

        // 查询数据
        List<MatrixTodayProgressItem> matrixDashboards = dashboardMapper.selectMatrixTodayProgress(dashBoardQueryRep);
        List<MatrixTodayProgressItem> delaySummarys = dashboardMapper.selectEngineerDelaySummary(dashBoardQueryRep);
        Set<String> allEngineers = new HashSet<>();
        if(Func.isNotEmpty(matrixDashboards)){
            Set<String> todayEngineers = matrixDashboards.stream().map(item->Func.toStr(item.getEngineer())).collect(Collectors.toSet());
            if(Func.isNotEmpty(todayEngineers)){
                allEngineers.addAll(todayEngineers);
            }
        }
        if(Func.isNotEmpty(delaySummarys)){
            Set<String> delayEngineers = delaySummarys.stream().map(item->Func.toStr(item.getEngineer())).collect(Collectors.toSet());
            if(Func.isNotEmpty(delayEngineers)){
                allEngineers.addAll(delayEngineers);
            }
        }
        MatrixTodayProgressRsp matrixTodayProgressRsp = new MatrixTodayProgressRsp();
        List<MatrixTodayProgressItem> resultList = new ArrayList<>();
        if(Func.isNotEmpty(allEngineers)){
            for (String engineer : allEngineers) {
                MatrixTodayProgressItem matrixTodayProgressItem = new MatrixTodayProgressItem();
                matrixTodayProgressItem.setEngineer(engineer);
                //amCount,pmCount
                matrixTodayProgressItem.setTodayAMQty(0l);
                matrixTodayProgressItem.setTodayPMQty(0l);
                if(Func.isNotEmpty(matrixDashboards)){
                    List<MatrixTodayProgressItem> todayProgressRspList = matrixDashboards.stream().filter(item -> StringUtils.equalsIgnoreCase(Func.toStr(item.getEngineer()), Func.toStr(engineer))).collect(Collectors.toList());
                    if(Func.isNotEmpty(todayProgressRspList)){
                        LongSummaryStatistics todayAMQty = todayProgressRspList.stream().collect(Collectors.summarizingLong(MatrixTodayProgressItem::getTodayAMQty));
                        LongSummaryStatistics todayPMQty = todayProgressRspList.stream().collect(Collectors.summarizingLong(MatrixTodayProgressItem::getTodayPMQty));
                        matrixTodayProgressItem.setTodayAMQty(todayAMQty.getSum());
                        matrixTodayProgressItem.setTodayPMQty(todayPMQty.getSum());
                    }
                }
                //delayCount
                matrixTodayProgressItem.setDelayQty(0l);
                if(Func.isNotEmpty(delaySummarys)){
                    List<MatrixTodayProgressItem> delayProgressRspList = delaySummarys.stream().filter(item -> StringUtils.equalsIgnoreCase(Func.toStr(item.getEngineer()), Func.toStr(engineer))).collect(Collectors.toList());
                    if(Func.isNotEmpty(delayProgressRspList)){
                        LongSummaryStatistics delayQty = delayProgressRspList.stream().collect(Collectors.summarizingLong(MatrixTodayProgressItem::getDelayQty));
                        matrixTodayProgressItem.setDelayQty(delayQty.getSum());
                    }
                }
                matrixTodayProgressItem.setTotalQty(0l);
                Long totalQty = NumberUtil.add(matrixTodayProgressItem.getDelayQty(),matrixTodayProgressItem.getTodayAMQty(),matrixTodayProgressItem.getTodayPMQty()).longValue();
                matrixTodayProgressItem.setTotalQty(totalQty);
                resultList.add(matrixTodayProgressItem);
            }
        }
        for (MatrixTodayProgressItem matrixTodayProgressItem : resultList) {
            if(StringPool.NULL.equals(matrixTodayProgressItem.getEngineer())||Func.isEmpty(matrixTodayProgressItem.getEngineer())){
                matrixTodayProgressItem.setEngineer(StringPool.DASH);
            }
        }

        Collections.sort(resultList, Comparator.comparing(MatrixTodayProgressItem::getTotalQty).reversed());
        matrixTodayProgressRsp.setTodayProgressList(resultList);
        // 查询当前Lab下的labSection列表
        Set<Integer> labIds = Sets.newHashSet();
        labIds.add(context.getLabId());
        BaseResponse<List<GetLabSectionBaseInfoRsp>> trimsLabSectionRes = trimsClient.getLabSectionByLabId(labIds);
        if(Func.isNotEmpty(trimsLabSectionRes)&&Func.isNotEmpty(trimsLabSectionRes.getData())){
            List<LabSectionRsp> labSectionList = Func.copy(trimsLabSectionRes.getData(),GetLabSectionBaseInfoRsp.class,LabSectionRsp.class);
            matrixTodayProgressRsp.setLabSectionList(labSectionList);
        }
        return BaseResponse.newSuccessInstance(matrixTodayProgressRsp);
    }

    private Date getDate(Date date,LocalTime localTime){
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(localTime);
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }
}
