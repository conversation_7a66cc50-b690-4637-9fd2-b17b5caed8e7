package com.sgs.gpo.domain.service.preorder.order.command;

import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.model.common.customer.CustomerBO;
import com.sgs.framework.model.enums.RefIntegrationChannel;
import com.sgs.framework.model.order.trf.TrfBO;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.tool.jackson.JsonUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.domain.service.preorder.order.context.OrderContext;
import com.sgs.gpo.facade.model.preorder.order.dto.OrderExtFieldsDTO;
import com.sgs.gpo.facade.model.preorder.order.dto.OrderExtTrfTemplateDTO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderToSgsMartReq;
import com.sgs.gpo.facade.model.preorder.order.rsp.orderToSGSMartInfo.CustomerDepartment;
import com.sgs.gpo.facade.model.preorder.order.rsp.orderToSGSMartInfo.OrderToSGSMartInfoRsp;
import com.sgs.gpo.facade.model.preorder.order.rsp.orderToSGSMartInfo.SgsMartAccount;
import com.sgs.gpo.integration.customer.CustomerClient;
import com.sgs.gpo.integration.customer.req.CustomerInfoReq;
import com.sgs.gpo.integration.customer.rsp.CustomerInfoRsp;
import com.sgs.gpo.integration.sgsmart.SGSMartClient;
import com.sgs.gpo.integration.sgsmart.req.TrfTemplateQueryReq;
import com.sgs.gpo.integration.sgsmart.rsp.TrfTemplate;
import com.sgs.gpo.integration.sgsmart.rsp.TrfTemplateRsp;
import com.sgs.gpo.integration.usermanagement.UserManagementClient;
import com.sgs.gpo.integration.usermanagement.rsp.CompanyEmployeeRsp;
import com.sgs.preorder.facade.model.dto.dm.CustomerDepartmentDTO;
import com.sgs.priceengine.facade.model.enums.CustomerUsage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class QueryToSgsMartDetailCMD extends BaseCommand<OrderContext<OrderToSgsMartReq>> {

    @Autowired
    IOrderDomainService orderDomainService;
    @Autowired
    SGSMartClient sgsMartClient;
    @Autowired
    CustomerClient customerClient;
    @Autowired
    UserManagementClient userManagementClient;

    @Override
    public BaseResponse validateParam(OrderContext<OrderToSgsMartReq> context) {
        Assert.isTrue(Func.isNotEmpty(context.getParam()), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(OrderContext<OrderToSgsMartReq> context) {
        //查order - 查customer pk - 调用外部接口获取待选值数据
        String orderNo = context.getParam().getOrderNo();
        Lab lab = context.getLab();
        boolean customerDeptIsRequired = false;
        //查询Order信息
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setOrderNoList(Sets.newHashSet(orderNo));
        BaseResponse<List<OrderBO>> orderBORsp = orderDomainService.queryBO(orderQueryReq);
        if(orderBORsp.isFail() || Func.isEmpty(orderBORsp.getData())){
            return BaseResponse.newFailInstance("order.result.empty",new Object[]{orderNo});
        }
        List<OrderBO> orderBOList = orderBORsp.getData();
        OrderBO order = orderBOList.stream().filter(e -> Func.equalsSafe(e.getId().getOrderNo(), orderNo)).findAny().orElse(null);
        if(Func.isEmpty(order)){
            return BaseResponse.newFailInstance("order.result.empty",new Object[]{orderNo});
        }
        context.setOrderBO(order);
        //Buyer Applicant PK 取值确认
        List<CustomerBO> customerBOList = order.getCustomerList();
        if(Func.isEmpty(customerBOList)){
            return BaseResponse.newFailInstance("order customer Not Found !");
        }
        CustomerBO applicant = customerBOList.stream().filter(e -> Func.equalsSafe(e.getCustomerUsage(),CustomerUsage.Applicant.getStatus())).findAny().orElse(null);
        CustomerBO buyer = customerBOList.stream().filter(e -> Func.equalsSafe(e.getCustomerUsage(),CustomerUsage.Buyer.getStatus())).findAny().orElse(null);
        if(Func.isAllEmpty(applicant,buyer)){
            return BaseResponse.newFailInstance("order customer Not Found !");
        }
        //查询Applicant和Buyer的 UserManagement配置
        CustomerInfoReq customerInfoReq = new CustomerInfoReq();
        customerInfoReq.setBuCode(lab.getBuCode());
        customerInfoReq.setLocationCode(lab.getLocationCode());
        CustomerInfoRsp applicantConfig = null, buyerConfig = null;
        if(Func.isNotEmpty(applicant)){
            customerInfoReq.setCustomerId(applicant.getCustomerId());
            applicantConfig = customerClient.queryCustomerExtInfoByBu(customerInfoReq);
        }
        if(Func.isNotEmpty(buyer)){
            customerInfoReq.setCustomerId(buyer.getCustomerId());
            buyerConfig = customerClient.queryCustomerExtInfoByBu(customerInfoReq);
        }
        if(Func.isAllEmpty(applicantConfig,buyerConfig)){
            return BaseResponse.newFailInstance("get customer configuration fail");
        }
        //规则 Applicant 勾选 OrderToTrf 展示 or Buyer 只要勾选ToDMFlag或者Order2Trf 就展示
        // Trf Template 入参优先使用Buyer
        CustomerDepartmentDTO customerDepartmentDTO = new CustomerDepartmentDTO();
        TrfTemplateQueryReq trfTemplateQueryReq = new TrfTemplateQueryReq();
        trfTemplateQueryReq.setProductLineCode(lab.getProductLineCode());
        trfTemplateQueryReq.setDffFormId(order.getProduct().getTemplateId());
        if(Func.isNotEmpty(buyer) && Func.isNotEmpty(buyerConfig) && (Func.equalsSafe(buyerConfig.getDmkaFlag(),"1") || Func.equalsSafe(buyerConfig.getOrderToTRFFlag(),"1"))){
            //查询customer Department
            customerDepartmentDTO.setAccountID(buyer.getAccountId());
            trfTemplateQueryReq.setCustomerGroupCode(buyer.getCustomerGroupCode());
            trfTemplateQueryReq.setCustomerNo(buyer.getBossNo().toString());
            if(Func.equalsSafe(buyerConfig.getDmkaFlag(),"1")){
                customerDeptIsRequired = true;
            }
        } else if (Func.isNotEmpty(applicant) && Func.isNotEmpty(applicantConfig) && Func.equalsSafe(applicantConfig.getOrderToTRFFlag(),"1")){
            customerDepartmentDTO.setAccountID(applicant.getAccountId());
            trfTemplateQueryReq.setCustomerGroupCode(applicant.getCustomerGroupCode());
            trfTemplateQueryReq.setCustomerNo(applicant.getBossNo().toString());
        } else {
            return BaseResponse.newFailInstance("Customer is not setting OrderToTRF！");
        }
        List<CustomerDepartmentDTO> customerDepartmentDTOList = customerClient.queryDepartmentList(customerDepartmentDTO);
        context.setCustomerDepartmentDTOList(customerDepartmentDTOList);
        BaseResponse<TrfTemplateRsp> trfTemplateRsp = sgsMartClient.trfTemplateQuery(trfTemplateQueryReq);
        if(trfTemplateRsp.isFail() || Func.isEmpty(trfTemplateRsp.getData())){
            return BaseResponse.newFailInstance("Query Trf Template Failed");
        }
        TrfTemplateRsp trfTemplate = trfTemplateRsp.getData();
        context.setTrfTemplateRsp(trfTemplate);
        if(Func.isNotEmpty(applicant)){
            List<CompanyEmployeeRsp> companyEmployee = userManagementClient.querySGSMartAccountByCustomerNo(applicant.getBossNo());
            context.setCompanyEmployeeRspList(companyEmployee);
        }
        context.setDeptIsRequired(customerDeptIsRequired);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(OrderContext<OrderToSgsMartReq> context) {
        OrderToSGSMartInfoRsp orderToSGSMartInfoRsp = new OrderToSGSMartInfoRsp();
        List<CustomerDepartmentDTO> customerDepartmentDTOList = context.getCustomerDepartmentDTOList();
        TrfTemplateRsp trfTemplateRsp = context.getTrfTemplateRsp();
        List<CompanyEmployeeRsp> companyEmployeeRspList = context.getCompanyEmployeeRspList();
        OrderBO orderBO = context.getOrderBO();
        //组装当前Order信息
        orderToSGSMartInfoRsp.setDeptIsRequired(context.isDeptIsRequired());
        //1.order2TrfFlag
        List<TrfBO> trfList = orderBO.getRelationship().getParent().getTrfList();
        if(Func.isNotEmpty(trfList)){
            TrfBO trfBO = trfList.stream().filter(e -> RefIntegrationChannel.check(e.getIntegrationChannel(),RefIntegrationChannel.SCI)).findAny().orElse(null);
            if(Func.isNotEmpty(trfBO)){
                orderToSGSMartInfoRsp.setOrderToTrfFlag(true);
            } else {
                orderToSGSMartInfoRsp.setOrderToTrfFlag(false);
            }
        } else {
            orderToSGSMartInfoRsp.setOrderToTrfFlag(false);
        }
        //2.trfTemplate
        if(Func.isNotEmpty(orderBO.getOthers()) && Func.isNotEmpty(orderBO.getOthers().getExtFields())){
            //解析Json
            OrderExtFieldsDTO dto = JsonUtil.parse(orderBO.getOthers().getExtFields(), new com.fasterxml.jackson.core.type.TypeReference<OrderExtFieldsDTO>(){});
            if(Func.isNotEmpty(dto) && Func.isNotEmpty(dto.getTrfTemplate()) && Func.isNotEmpty(dto.getTrfTemplate().getTemplateId()) && Func.isNotEmpty(dto.getTrfTemplate().getTemplateName())){
                orderToSGSMartInfoRsp.setTemplateId(dto.getTrfTemplate().getTemplateId());
                orderToSGSMartInfoRsp.setTemplateName(dto.getTrfTemplate().getTemplateName());
            }
        }
        //3.sgsMartAccount
        if(Func.isNotEmpty(orderBO.getCustomerList())){
            CustomerBO applicant = orderBO.getCustomerList().stream().filter(e -> Func.equalsSafe(e.getCustomerUsage(),CustomerUsage.Applicant.getStatus())).findAny().orElse(null);
            if(Func.isNotEmpty(applicant) && Func.isNotEmpty(applicant.getCustomerContactList())){
                orderToSGSMartInfoRsp.setSgsMartUserId(applicant.getCustomerContactList().get(0).getContactSgsMartUserId());
                orderToSGSMartInfoRsp.setSgsMartAccount(applicant.getCustomerContactList().get(0).getContactSgsMartAccount());
            }
        }
        //4.KACustomerDeptCode
        orderToSGSMartInfoRsp.setKACustomerDeptCode(orderBO.getOthers().getDepartmentCode());
        //下拉菜单
        if(Func.isNotEmpty(customerDepartmentDTOList)){
            List<CustomerDepartment> customerDepartmentList = new ArrayList<>();
            customerDepartmentDTOList.stream().forEach(department ->{
                CustomerDepartment customerDepartment = new CustomerDepartment();
                customerDepartment.setDepartmentCode(department.getDepartmentCode());
                customerDepartment.setDepartmentDescription(department.getDepartmentDescription());
                customerDepartmentList.add(customerDepartment);
            });
            orderToSGSMartInfoRsp.setCustomerDepartmentDTOList(customerDepartmentList);
        }
        if(Func.isNotEmpty(trfTemplateRsp) && Func.isNotEmpty(trfTemplateRsp.getTemplateList())){
            List<OrderExtTrfTemplateDTO> orderExtTrfTemplateDTOList = new ArrayList<>();
            for(TrfTemplate trfTemplate : trfTemplateRsp.getTemplateList()){
                OrderExtTrfTemplateDTO orderExtTrfTemplateDTO = new OrderExtTrfTemplateDTO();
                orderExtTrfTemplateDTO.setTemplateId(trfTemplate.getId());
                orderExtTrfTemplateDTO.setTemplateName(trfTemplate.getTemplateName());
                orderExtTrfTemplateDTOList.add(orderExtTrfTemplateDTO);
            }
            orderToSGSMartInfoRsp.setTrfTemplateList(orderExtTrfTemplateDTOList);
        }
        if(Func.isNotEmpty(companyEmployeeRspList)){
            List<SgsMartAccount> sgsMartAccountList = new ArrayList<>();
            companyEmployeeRspList.forEach(companyEmployee ->{
                SgsMartAccount sgsMartAccount = new SgsMartAccount();
                sgsMartAccount.setSgsMartAccount(companyEmployee.getCode());
                sgsMartAccount.setSgsMartUserId(companyEmployee.getId());
                sgsMartAccountList.add(sgsMartAccount);
            });
            orderToSGSMartInfoRsp.setSgsMartAccountList(sgsMartAccountList);
        }
        return BaseResponse.newSuccessInstance(orderToSGSMartInfoRsp);
    }
}
