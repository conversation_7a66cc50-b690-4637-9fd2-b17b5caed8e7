package com.sgs.gpo.domain.service.preorder.customer.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.customer.CustomerPO;
import com.sgs.gpo.facade.model.customer.req.CustomerQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ICustomerService extends IService<CustomerPO> {

    /**
     *
     */
    BaseResponse<List<CustomerPO>> select(OrderIdReq orderIdReq);

    List<CustomerPO> queryByName(CustomerQueryReq req);

    int checkPayerIsSame(List<String> orderIds);
}
