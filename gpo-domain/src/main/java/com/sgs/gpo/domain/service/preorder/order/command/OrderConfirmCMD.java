package com.sgs.gpo.domain.service.preorder.order.command;


import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.gpo.domain.service.preorder.order.context.OrderConfirmContext;
import com.sgs.gpo.facade.model.preorder.order.req.OrderConfirmReq;
import org.springframework.stereotype.Service;

@Service
public class OrderConfirmCMD extends BaseCommand<OrderConfirmContext<OrderConfirmReq>> {

    @Override
    public BaseResponse validateParam(OrderConfirmContext<OrderConfirmReq> context) {
        //1 基础校验 token user
        //2 订单自动保存？ 先调用Dubbo接口执行Save
        //3 订单Confirm的业务校验

        return null;
    }

    @Override
    public BaseResponse execute(OrderConfirmContext<OrderConfirmReq> context) {
        return null;
    }
}
