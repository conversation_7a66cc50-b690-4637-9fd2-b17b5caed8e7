package com.sgs.gpo.domain.service.otsnotes.subreport.impl;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.ReportStatus;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subreport.SubReportPO;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.otsnotes.subreport.ISubReportDomainService;
import com.sgs.gpo.domain.service.otsnotes.subreport.ISubReportService;
import com.sgs.gpo.facade.model.otsnotes.subreport.req.UpdateCombineFlagReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class SubReportDomainServiceImpl implements ISubReportDomainService {

    @Resource
    private ISubReportService subReportService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private IReportService reportService;

    @Override
    public BaseResponse updateCombineFlag(UpdateCombineFlagReq updateCombineFlagReq) {
        //0 基础入参校验
        if (Func.isEmpty(updateCombineFlagReq)) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        if (Func.isEmpty(updateCombineFlagReq.getCombineFlag())) {
            return BaseResponse.newFailInstance("common.param.miss", new Object[]{"combineFlag"});
        }
        if (Func.isEmpty(updateCombineFlagReq.getSubReportId())) {
            return BaseResponse.newFailInstance("common.param.miss", new Object[]{"subReportId"});
        }
        //1 判断当前单据状态是否允许更新
        String subReportId = updateCombineFlagReq.getSubReportId();
        Integer combineFlag = updateCombineFlagReq.getCombineFlag();
        SubReportPO subReportPO = subReportService.getById(subReportId);
        if (Func.isEmpty(subReportPO)) {
            return BaseResponse.newFailInstance("common.param.invalid", new Object[]{"subReportId"});
        }
        ReportQueryReq reportQueryReq = new ReportQueryReq();
        reportQueryReq.setSubReportId(subReportId);
        // 检查SubReport对应的报告状态排除无效后应该都是New
        List<ReportPO> reportList =  reportService.queryReportBySubReport(reportQueryReq);
        if(reportList.stream().anyMatch(item -> !ReportStatus.checkCategory(item.getReportStatus(), Constants.OBJECT.REPORT.STATUS_CATEGORY.INACTIVE)
                && !ReportStatus.check(item.getReportStatus(), ReportStatus.New))){
            return BaseResponse.newFailInstance("report.status.not.match", new Object[]{ReportStatus.New.getMessage()});
        }
        //2 如果DB的值跟要更新的值一样，那么不需要处理
        if (Func.equalsSafe(subReportPO.getCombineFlag(), combineFlag)) {
            return BaseResponse.newSuccessInstance(true);
        }
        //3 执行DB更新
        SubReportPO newSubReportPO = new SubReportPO();
        newSubReportPO.setId(subReportId);
        newSubReportPO.setCombineFlag(combineFlag);
        newSubReportPO.setModifiedBy(SystemContextHolder.getUserInfoFillSystem().getRegionAccount());
        newSubReportPO.setModifiedDate(DateUtil.now());
        transactionTemplate.execute(transactionStatus -> subReportService.updateById(newSubReportPO));
        //4 返回结果
        return BaseResponse.newSuccessInstance(true);
    }
}
