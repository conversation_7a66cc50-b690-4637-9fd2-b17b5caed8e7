package com.sgs.gpo.domain.service.otsnotes.status;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.status.GpnStatusLogPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.status.GpnStatusPO;
import com.sgs.gpo.facade.model.preorder.order.req.GpnStatusReq;
import com.sgs.gpo.facade.model.report.req.ReportIdReq;
import com.sgs.gpo.facade.model.status.req.StatusLogReq;

import java.util.List;


public interface IGpnStatusLogService extends IService<GpnStatusLogPO> {

    int delete(StatusLogReq statusLogReq);

}
