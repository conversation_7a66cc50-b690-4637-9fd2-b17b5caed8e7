package com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.ProductAttributeInstancePO;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.ProductAttributeReq;

import java.util.List;

public interface IProductAttributeService extends IService<ProductAttributeInstancePO> {


    BaseResponse<List<ProductAttributeInstancePO>> query(ProductAttributeReq productAttributeReq);

}
