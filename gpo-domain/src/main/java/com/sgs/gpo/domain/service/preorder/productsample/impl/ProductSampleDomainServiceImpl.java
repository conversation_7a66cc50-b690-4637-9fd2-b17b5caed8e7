package com.sgs.gpo.domain.service.preorder.productsample.impl;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.model.common.productsample.ProductSampleBO;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.domain.service.preorder.productsample.IProductSampleDomainService;
import com.sgs.gpo.domain.service.preorder.productsample.command.EnquiryProductSampleQueryCMD;
import com.sgs.gpo.domain.service.preorder.productsample.command.ProductSampleQueryCMD;
import com.sgs.gpo.domain.service.preorder.productsample.context.ProductSampleContext;
import com.sgs.gpo.facade.model.preorder.productsample.req.ProductSampleQueryReq;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/5 09:27
 */
@Service
public class ProductSampleDomainServiceImpl implements IProductSampleDomainService {

    @Override
    public BaseResponse<List<ProductSampleBO>> queryOrderProductSample(ProductSampleQueryReq productSampleQueryReq) {
        ProductSampleContext context = new ProductSampleContext();
        context.setParam(productSampleQueryReq);
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        return BaseExecutor.start(ProductSampleQueryCMD.class,context);
    }

    /**
     * 查询询价单的产品样品信息
     *
     * @param productSampleQueryReq
     * @return
     */
    @Override
    public BaseResponse<List<ProductSampleBO>> queryEnquiryProductSample(ProductSampleQueryReq productSampleQueryReq) {
        ProductSampleContext context = new ProductSampleContext();
        context.setParam(productSampleQueryReq);
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        return BaseExecutor.start(EnquiryProductSampleQueryCMD.class,context);
    }
}
