package com.sgs.gpo.domain.service.preorder.payment.payment.context;

import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.BossOrderInvoicePO;
import lombok.Data;

import java.util.List;

@Data
public class PaymentContext<Input extends BaseRequest> extends BaseContext<Input, String> {

    private List<BossOrderInvoicePO> bossOrderInvoicePOList;

    public PaymentContext (Input input){
        this.setLab(SystemContextHolder.getLab());
        this.setToken(SystemContextHolder.getSgsToken());
        this.setUserInfo(SystemContextHolder.getUserInfo());
        this.setParam(input);
    }

}
