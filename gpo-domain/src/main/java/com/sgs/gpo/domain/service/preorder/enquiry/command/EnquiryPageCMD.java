package com.sgs.gpo.domain.service.preorder.enquiry.command;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.PageBO;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.log.SystemLogHelper;
import com.sgs.framework.log.model.SystemLog;
import com.sgs.framework.model.enums.CustomerType;
import com.sgs.framework.model.order.enquiry.EnquiryBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.enums.SystemLogObject;
import com.sgs.gpo.core.enums.SystemLogType;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryCustomerPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryPO;
import com.sgs.gpo.domain.service.common.searchvalid.ISearchValidService;
import com.sgs.gpo.domain.service.preorder.enquiry.IEnquiryDomainService;
import com.sgs.gpo.domain.service.preorder.enquiry.context.EnquiryContext;
import com.sgs.gpo.domain.service.preorder.enquiry.convertor.ServiceItemConvertor;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryService;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryCustomerReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryQueryReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.ServiceItemReq;
import com.sgs.gpo.facade.model.searchvalid.req.SearchValidReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分页查询Enquiry，返回Rsp对象
 */
@Service
@Slf4j
public class EnquiryPageCMD extends EnquiryQueryCMD {

    @Autowired
    private IEnquiryService enquiryService;
    @Autowired
    private ISearchValidService searchValidService;
    @Autowired
    private IEnquiryDomainService enquiryDomainService;
    @Autowired
    private SystemLogHelper systemLogHelper;


    /**
     * 分页查询数据校验
     *
     * @param context
     * @return
     */
    @Override
    public BaseResponse validateParam(EnquiryContext<EnquiryQueryReq> context) {
        // 分页查询基础校验入参
        SearchValidReq searchValidReq = new SearchValidReq();
        searchValidReq.setSearchCode("Search");
        searchValidReq.setObjectCode("EnquiryList");
        searchValidReq.setRequestReqObj(context.getParam());
        BaseResponse validSearchRuleResponse = searchValidService.validSearchRule(searchValidReq);
        if(!validSearchRuleResponse.isSuccess()){
            return BaseResponse.newFailInstance(validSearchRuleResponse.getMessage());
        }
        if (Func.isEmpty(context.getPage())) {
            context.setPage(1);
        }
        if (Func.isEmpty(context.getRows())) {
            context.setRows(10);
        }
        // 执行查询的基础校验
        return super.validateParam(context);
    }

    /**
     * 数据查询，返回PO结构
     *
     * @param context
     * @return
     */
    @Override
    public BaseResponse before(EnquiryContext<EnquiryQueryReq> context) {
        EnquiryQueryReq enquiryQueryReq = context.getParam();
        // 设置labCode查询条件
        if (Func.isEmpty(enquiryQueryReq.getLabCodeList())){
            enquiryQueryReq.setLabCode(context.getLab().getLabCode());
        }
        // 批量查询字段处理
        if (Func.isNotEmpty(enquiryQueryReq.getEnquiryNoBatch())){
            String[] split = enquiryQueryReq.getEnquiryNoBatch().split("\n");
            List<String> list = new ArrayList<>();
            for (String item : split){
                list.add(item);
            }
            if (Func.isNotEmpty(list)){
                enquiryQueryReq.setEnquiryNoList(list);
            }
        }

        if(Func.isNotEmpty(enquiryQueryReq.getServiceItem())){
            List<ServiceItemReq> serviceItemList = ServiceItemConvertor.batchConvertToServiceItemDto(enquiryQueryReq.getServiceItem());
            enquiryQueryReq.setServiceItemList(serviceItemList);
        }
        // productCategory处理
        String[] productCategories = enquiryQueryReq.getProductCategories();
        if(Func.isNotEmpty(productCategories)){
            if(productCategories.length == 1){
                // Product Category
                enquiryQueryReq.setProductCategory(productCategories[0]);
            }else if(productCategories.length == 2){
                // Product Category
                enquiryQueryReq.setProductCategory(productCategories[0]);
                //Product subCategory
                enquiryQueryReq.setProductSubCategory(productCategories[1]);
            }
        }

        enquiryQueryReq.setRegionAccount(context.getUserInfo().getRegionAccount());
        // 客户查询条件处理
        BaseResponse dealCustomerRes = this.dealSearchCustomerReq(enquiryQueryReq);
        if(dealCustomerRes.isFail()){
            return BaseResponse.newFailInstance(dealCustomerRes.getMessage());
        }
        //
        if(Func.isEmpty(enquiryQueryReq.getTemplateFlag())){
            enquiryQueryReq.setTemplateFlag(0);
        }
        // 执行分页接口查询，返回EnquiryPO信息
        PageBO<EnquiryPO> enquiryPagePO =  enquiryService.page(enquiryQueryReq, context.getPage(), context.getRows());
        context.setEnquiryPagePO(enquiryPagePO);
        context.setEnquiryPOList(enquiryPagePO.getRecords());
        return BaseResponse.newSuccessInstance(true);
    }

    private BaseResponse dealSearchCustomerReq(EnquiryQueryReq queryReq){
        if(Func.isEmpty(queryReq)){
            return BaseResponse.newFailInstance("查询条件不能为空");
        }
        //根据customerName先查询
        EnquiryCustomerReq customerSelectSearchReq = new EnquiryCustomerReq();
        customerSelectSearchReq.setLabCode(queryReq.getLabCode());
        if(Func.isNotEmpty(queryReq.getPayer())){
            if(Func.isEmpty(queryReq.getPayer().getCustomerNo()) && Func.isNotEmpty(StringUtils.trim(queryReq.getPayer().getCustomerName()))){
                if(StringUtils.trim(queryReq.getPayer().getCustomerName()).length()<2){
                    return BaseResponse.newFailInstance("客户名称最少输入2个字符，请调整关键词后再查询");
                }
                customerSelectSearchReq.setCustomerName(StringUtils.trim(queryReq.getPayer().getCustomerName()));
                customerSelectSearchReq.setCustomerUsage(CustomerType.Payer.getCode());
//                List<CustomerSelectDTO> customerSelectDTOS = customerExtMapper.queryCustomerByName(customerSelectSearchReq);
//                EnquiryCustomerReq enquiryCustomerReq = new EnquiryCustomerReq();
                BaseResponse<List<EnquiryCustomerPO>> payerRes = enquiryDomainService.queryCustomerList(customerSelectSearchReq);
                List<EnquiryCustomerPO> customerSelectDTOS = payerRes.getData();
                if(Func.isNotEmpty(customerSelectDTOS)){
                    if(customerSelectDTOS.size()>100){
                        return BaseResponse.newFailInstance("客户名称关键字查询超100个客户，请调整关键词或点放大镜查找后再查询");
                    }
                    List<EnquiryCustomerPO> noBossNumberList = customerSelectDTOS.stream().filter(customerSelectDTO -> Func.isEmpty(customerSelectDTO.getBossNumber()) || customerSelectDTO.getBossNumber() == 0).collect(Collectors.toList());
                    if(Func.isNotEmpty(noBossNumberList)){
                        //使用Name查询
                        List<String> customerNameCnList = customerSelectDTOS.stream().filter(c-> StringUtils.containsIgnoreCase(c.getCustomerNameCn(),StringUtils.trim(queryReq.getPayer().getCustomerName()))).map(EnquiryCustomerPO::getCustomerNameCn).distinct().filter(Func::isNotEmpty).collect(Collectors.toList());
                        List<String> customerNameEnList = customerSelectDTOS.stream().filter(c-> StringUtils.containsIgnoreCase(c.getCustomerNameEn(),StringUtils.trim(queryReq.getPayer().getCustomerName()))).map(EnquiryCustomerPO::getCustomerNameEn).distinct().filter(Func::isNotEmpty).collect(Collectors.toList());
                        queryReq.getPayer().setCustomerCnNameList(customerNameCnList);
                        queryReq.getPayer().setCustomerEnNameList(customerNameEnList);
                    }else{
                        //使用BossNumber查询
                        List<Long> customerNumberList = customerSelectDTOS.stream().map(EnquiryCustomerPO::getBossNumber).distinct().filter(Func::isNotEmpty).collect(Collectors.toList());
                        queryReq.getPayer().setCustomerNumberList(customerNumberList);
                    }
                }else{
                    queryReq.getPayer().setCustomerEnNameList(Lists.newArrayList(queryReq.getPayer().getCustomerName().trim()));
                }
            }
        }
        if(Func.isNotEmpty(queryReq.getBuyer())){
            if(Func.isEmpty(queryReq.getBuyer().getCustomerNo()) && Func.isNotEmpty(StringUtils.trim(queryReq.getBuyer().getCustomerName()))){
                if(StringUtils.trim(queryReq.getBuyer().getCustomerName()).length()<2){
                    return BaseResponse.newFailInstance("客户名称最少输入2个字符，请调整关键词后再查询");
                }
                customerSelectSearchReq.setCustomerName(StringUtils.trim(queryReq.getBuyer().getCustomerName()));
                customerSelectSearchReq.setCustomerUsage(CustomerType.Buyer.getCode());
//                List<CustomerSelectDTO> customerSelectDTOS = customerExtMapper.queryCustomerByName(customerSelectSearchReq);
                BaseResponse<List<EnquiryCustomerPO>> buyerRes = enquiryDomainService.queryCustomerList(customerSelectSearchReq);
                List<EnquiryCustomerPO> customerSelectDTOS = buyerRes.getData();
                if(Func.isNotEmpty(customerSelectDTOS)){
                    if(customerSelectDTOS.size()>100){
                        return BaseResponse.newFailInstance("客户名称关键字查询超100个客户，请调整关键词或点放大镜查找后再查询");
                    }
                    List<EnquiryCustomerPO> noBossNumberList = customerSelectDTOS.stream().filter(customerSelectDTO -> Func.isEmpty(customerSelectDTO.getBossNumber()) || customerSelectDTO.getBossNumber() == 0).collect(Collectors.toList());
                    if(Func.isNotEmpty(noBossNumberList)){
                        //使用Name查询
                        List<String> customerNameCnList = customerSelectDTOS.stream().filter(c-> Func.isNotEmpty(c.getCustomerNameCn()) && StringUtils.containsIgnoreCase(c.getCustomerNameCn(),StringUtils.trim(queryReq.getBuyer().getCustomerName()))).map(EnquiryCustomerPO::getCustomerNameCn).distinct().filter(Func::isNotEmpty).collect(Collectors.toList());
                        List<String> customerNameEnList = customerSelectDTOS.stream().filter(c-> Func.isNotEmpty(c.getCustomerNameEn()) && StringUtils.containsIgnoreCase(c.getCustomerNameEn(),StringUtils.trim(queryReq.getBuyer().getCustomerName()))).map(EnquiryCustomerPO::getCustomerNameEn).distinct().filter(Func::isNotEmpty).collect(Collectors.toList());
                        queryReq.getBuyer().setCustomerCnNameList(customerNameCnList);
                        queryReq.getBuyer().setCustomerEnNameList(customerNameEnList);
                    }else{
                        //使用BossNumber查询
                        List<Long> customerNumberList = customerSelectDTOS.stream().map(EnquiryCustomerPO::getBossNumber).distinct().filter(Func::isNotEmpty).collect(Collectors.toList());
                        queryReq.getBuyer().setCustomerNumberList(customerNumberList);
                    }
                }else{
                    queryReq.getBuyer().setCustomerEnNameList(Lists.newArrayList(queryReq.getBuyer().getCustomerName().trim()));
                }
            }
        }
        if(Func.isNotEmpty(queryReq.getApplicant())){
            if(Func.isEmpty(queryReq.getApplicant().getCustomerNo()) && Func.isNotEmpty(StringUtils.trim(queryReq.getApplicant().getCustomerName()))){
                if(StringUtils.trim(queryReq.getApplicant().getCustomerName()).length()<2){
                    return BaseResponse.newFailInstance("客户名称最少输入2个字符，请调整关键词后再查询");
                }
                customerSelectSearchReq.setCustomerName(StringUtils.trim(queryReq.getApplicant().getCustomerName()));
                customerSelectSearchReq.setCustomerUsage(CustomerType.Applicant.getCode());
//                List<CustomerSelectDTO> customerSelectDTOS = customerExtMapper.queryCustomerByName(customerSelectSearchReq);
                BaseResponse<List<EnquiryCustomerPO>> applicantRes = enquiryDomainService.queryCustomerList(customerSelectSearchReq);
                List<EnquiryCustomerPO> customerSelectDTOS = applicantRes.getData();
                if(Func.isNotEmpty(customerSelectDTOS)){
                    if(customerSelectDTOS.size()>100){
                        return BaseResponse.newFailInstance("客户名称关键字查询超100个客户，请调整关键词或点放大镜查找后再查询");
                    }
                    List<EnquiryCustomerPO> noBossNumberList = customerSelectDTOS.stream().filter(customerSelectDTO -> Func.isEmpty(customerSelectDTO.getBossNumber()) || customerSelectDTO.getBossNumber() == 0).collect(Collectors.toList());
                    if(Func.isNotEmpty(noBossNumberList)){
                        //使用Name查询
                        List<String> customerNameCnList = customerSelectDTOS.stream().filter(c-> Func.isNotEmpty(c.getCustomerNameCn()) && StringUtils.containsIgnoreCase(c.getCustomerNameCn(),StringUtils.trim(queryReq.getApplicant().getCustomerName()))).map(EnquiryCustomerPO::getCustomerNameCn).distinct().filter(Func::isNotEmpty).collect(Collectors.toList());
                        List<String> customerNameEnList = customerSelectDTOS.stream().filter(c-> Func.isNotEmpty(c.getCustomerNameEn()) && StringUtils.containsIgnoreCase(c.getCustomerNameEn(),StringUtils.trim(queryReq.getApplicant().getCustomerName()))).map(EnquiryCustomerPO::getCustomerNameEn).distinct().filter(Func::isNotEmpty).collect(Collectors.toList());
                        queryReq.getApplicant().setCustomerCnNameList(customerNameCnList);
                        queryReq.getApplicant().setCustomerEnNameList(customerNameEnList);
                    }else{
                        //使用BossNumber查询
                        List<Long> customerNumberList = customerSelectDTOS.stream().map(EnquiryCustomerPO::getBossNumber).distinct().filter(Func::isNotEmpty).collect(Collectors.toList());
                        queryReq.getApplicant().setCustomerNumberList(customerNumberList);
                    }
                }else{
                    queryReq.getApplicant().setCustomerEnNameList(Lists.newArrayList(queryReq.getApplicant().getCustomerName().trim()));
                }
            }
        }
        return new BaseResponse(ResponseCode.SUCCESS.getCode(),"");
    }


    /**
     * 返回BO的分页结构
     *
     * @param context
     * @return
     */
    @Override
    public BaseResponse execute(EnquiryContext<EnquiryQueryReq> context) {
        // 拼装参数
        super.buildDomain(context);
        PageBO<EnquiryBO> page = Func.copy(context.getEnquiryPagePO(),PageBO.class);
        page.setRecords(context.getEnquiryBOList());
        return BaseResponse.newSuccessInstance(page);
    }

    @Override
    public BaseResponse after(EnquiryContext<EnquiryQueryReq> context) {
        super.after(context);
        SystemLog systemLog = new SystemLog();
        EnquiryQueryReq enquiryQueryReq = context.getParam();
        if(enquiryQueryReq.isSalesEnquiry()){
            systemLog.setObjectType(SystemLogObject.ENQUIRY_SALES.getType());
            systemLog.setObjectNo(SystemLogObject.ENQUIRY_SALES.getType());
            systemLog.setRemark(SystemLogObject.ENQUIRY_SALES.getDesc());
            systemLog.setOperationType(SystemLogObject.ENQUIRY_SALES.getOpType());
        }else{
            systemLog.setObjectType(SystemLogObject.ENQUIRY.getType());
            systemLog.setObjectNo(SystemLogObject.ENQUIRY.getType());
            systemLog.setRemark(SystemLogObject.ENQUIRY.getDesc());
            systemLog.setOperationType(SystemLogObject.ENQUIRY.getOpType());
        }
        systemLog.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        systemLog.setType(SystemLogType.BURIED_POINT.getType());
        systemLog.setRequest(JSON.toJSONString(enquiryQueryReq));
        systemLog.setCreateBy(context.getUserInfo().getRegionAccount());
        systemLogHelper.save(systemLog);
        return BaseResponse.newSuccessInstance(true);
    }




}
