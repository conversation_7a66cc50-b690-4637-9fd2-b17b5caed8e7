package com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractRequirementContactsPO;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractRequirementContactsQueryReq;
import java.util.List;

public interface ISubcontractRequirementContactsService extends IService<SubcontractRequirementContactsPO> {

    BaseResponse<List<SubcontractRequirementContactsPO>> query(SubcontractRequirementContactsQueryReq req);
}
