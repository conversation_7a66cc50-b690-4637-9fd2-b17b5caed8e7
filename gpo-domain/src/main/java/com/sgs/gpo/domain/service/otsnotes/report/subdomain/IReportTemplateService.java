package com.sgs.gpo.domain.service.otsnotes.report.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportTemplatePO;
import com.sgs.gpo.facade.model.report.req.ReportTemplateReq;

import java.util.List;

/**
 *
 */
public interface IReportTemplateService extends IService<ReportTemplatePO> {

    BaseResponse<List<ReportTemplatePO>> queryReportTemplateList(ReportTemplateReq reportTemplateReq);

}
