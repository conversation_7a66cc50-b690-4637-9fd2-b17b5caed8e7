package com.sgs.gpo.domain.service.otsnotes.objectsamplequantity;


import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.PageBO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.objectsamplequantity.ObjectSampleQuantityPO;
import com.sgs.gpo.facade.model.otsnotes.objectsamplequantity.req.ObjectSampleQuantityDeleteReq;
import com.sgs.gpo.facade.model.otsnotes.objectsamplequantity.req.ObjectSampleQuantityQueryReq;
import com.sgs.gpo.facade.model.otsnotes.objectsamplequantity.req.ObjectSampleQuantitySaveReq;

import java.util.List;

public interface IObjectSampleQuantityService extends IService<ObjectSampleQuantityPO> {
    PageBO<ObjectSampleQuantityPO> page(ObjectSampleQuantityQueryReq objectSampleQuantityQueryReq, Integer page, Integer rows);

    BaseResponse save(ObjectSampleQuantitySaveReq objectSampleQuantitySaveReq);

    BaseResponse delete(ObjectSampleQuantityDeleteReq objectSampleQuantityDeleteReq);

    List<ObjectSampleQuantityPO> select(ObjectSampleQuantityQueryReq objectSampleQuantityQueryReq);

}
