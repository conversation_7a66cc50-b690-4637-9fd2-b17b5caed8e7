package com.sgs.gpo.domain.service.preorder.enquiry.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryProductPO;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryProductReq;
import com.sgs.gpo.facade.model.preorder.productsample.req.ProductSampleQueryReq;

import java.util.List;

public interface IEnquiryProductService extends IService<EnquiryProductPO> {

    List<EnquiryProductPO> queryEnquiryProductByDff(EnquiryProductReq productReq);
    BaseResponse<List<EnquiryProductPO>> queryEnquiryProductSampleByEnquiryId(ProductSampleQueryReq productSampleQueryReq);

    BaseResponse<List<EnquiryProductPO>> select(EnquiryIdReq enquiryIdReq);

    /**
     * 基于EnquiryId删除DFF数据
     * @param enquiryProductReq
     * @return
     */
    Integer delete(EnquiryProductReq enquiryProductReq);

}
