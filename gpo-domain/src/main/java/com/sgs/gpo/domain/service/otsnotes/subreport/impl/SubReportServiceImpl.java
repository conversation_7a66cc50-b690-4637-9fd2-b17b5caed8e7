package com.sgs.gpo.domain.service.otsnotes.subreport.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.subreport.SubReportMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subreport.SubReportPO;
import com.sgs.gpo.domain.service.otsnotes.subreport.ISubReportService;
import com.sgs.gpo.facade.model.otsnotes.subreport.req.SubReportQueryReq;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @title: SubReportServiceImpl
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/8 17:24
 */
@Service
public class SubReportServiceImpl extends ServiceImpl<SubReportMapper, SubReportPO> implements ISubReportService {

    @Override
    public BaseResponse<List<SubReportPO>> query(SubReportQueryReq subReportQueryReq) {
        if(Func.isEmpty(subReportQueryReq) || (Func.isEmpty(subReportQueryReq.getObjectNoList()) && Func.isEmpty(subReportQueryReq.getSubContractIdList()))){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        LambdaQueryWrapper<SubReportPO> queryWrapper = Wrappers.<SubReportPO>lambdaQuery();
        if(Func.isNotEmpty(subReportQueryReq.getObjectNoList())){
            queryWrapper.in(SubReportPO::getObjectNo,subReportQueryReq.getObjectNoList());
        }
        if(Func.isNotEmpty(subReportQueryReq.getSubContractIdList())){
            queryWrapper.in(SubReportPO::getSubcontractId,subReportQueryReq.getSubContractIdList());
        }
        List<SubReportPO> subReportPOS = baseMapper.selectList(queryWrapper);
        return BaseResponse.newSuccessInstance(subReportPOS);
    }

    @Override
    public List<SubReportPO> select(SubReportQueryReq subReportQueryReq) {
        // 条件为空时不允许查询
        if(Func.isEmpty(subReportQueryReq) ||
                (Func.isEmpty(subReportQueryReq.getObjectNoList()) && Func.isEmpty(subReportQueryReq.getSubContractIdList())&&
                        Func.isEmpty(subReportQueryReq.getReportNo()))){
            return null;
        }
        return baseMapper.select(subReportQueryReq);
    }

    @Override
    public List<SubReportPO> selectSubReportsNoMatrix(SubReportQueryReq subReportQueryReq) {
        // 条件为空时不允许查询
        if(Func.isEmpty(subReportQueryReq) ||
                (Func.isEmpty(subReportQueryReq.getOrderNo()))){
            return null;
        }
        return baseMapper.selectSubReportsNoMatrix(subReportQueryReq);
    }
}
