package com.sgs.gpo.domain.service.preorder.devops;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;

import java.util.List;

public interface IDevopsService {

    /**
     * Cancel QA Testing Order
     * @param orderNoList
     * @return
     */
    BaseResponse cancelTestingOrder(List<String> orderNoList);

    List<ReportPO> getOrderNoListByAccNone(String buCode);

    BaseResponse repairSubReport();
}
