package com.sgs.gpo.domain.service.otsnotes.testline.context;

import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.test.testline.TestLineBO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title:
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/10/17 10:27
 */
@Data
public class TestLineContext<Input> extends BaseContext<Input, TestLineBO> {
    private List<TestLineBO> testLineBOList;
}
