package com.sgs.gpo.domain.convertor;

import com.sgs.framework.model.order.trf.TrfBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryTrfRelationshipPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.ordertrfrel.OrderTrfRelationshipPO;
import com.sgs.gpo.facade.model.preorder.order.bo.OrderTrfBO;
import com.sgs.preorder.core.order.dto.ExternalOrderDto;
import com.sgs.preorder.facade.model.info.ReferenceInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public abstract class ReferenceConvertor {

    public static Set<Integer> toRefSystemIdListByExternalList(List<OrderTrfBO> orderTrfBOList) {
        if (Func.isEmpty(orderTrfBOList)) {
            return null;
        }
        return orderTrfBOList.parallelStream().map(orderTrfBO ->Integer.valueOf(orderTrfBO.getRefSystemId())).collect(Collectors.toSet());
    }
    public static List<ExternalOrderDto> toExternalOrderListByRefList(List<ReferenceInfo> refList) {
        if (Func.isEmpty(refList)) {
            return null;
        }
        return refList.parallelStream().map(ReferenceConvertor::toExternalOrder).collect(Collectors.toList());
    }
    public static List<ExternalOrderDto> toExternalOrderListByTrfBOList(List<TrfBO> trfList) {
        if (Func.isEmpty(trfList)) {
            return null;
        }
        List<ExternalOrderDto> resultList = new ArrayList<>();
        for (TrfBO trfBO : trfList) {
            ExternalOrderDto  externalOrderDto= new ExternalOrderDto();
            externalOrderDto.setExternalSubOrderNo(trfBO.getTrfNo());
            externalOrderDto.setRefSystemId(trfBO.getRefSystemId());
            externalOrderDto.setExternalOrderNo(trfBO.getExternalOrderNo());
            externalOrderDto.setIntegrationChannel(trfBO.getIntegrationChannel());
            resultList.add(externalOrderDto);
        }
        return resultList;
    }
    public static  ExternalOrderDto toExternalOrder(ReferenceInfo ref) {
        if (Func.isEmpty(ref)) {
            return null;
        }
        ExternalOrderDto  externalOrderDto= new ExternalOrderDto();
        externalOrderDto.setExtInfo(ref.getExtData());
        externalOrderDto.setExternalSubOrderNo(ref.getReferenceNo());
        externalOrderDto.setRefSystemId(ref.getRefSystemId());
        externalOrderDto.setExternalObjectId(ref.getExtObjectId());
        externalOrderDto.setExternalOrderNo(ref.getExternalOrderNo());
        externalOrderDto.setIntegrationChannel(ref.getIntegrationChannel());
        return externalOrderDto;
    }
    public static List<ReferenceInfo> toReferenceInfoListByEnquiry(List<EnquiryTrfRelationshipPO> trfRelList) {
        if (Func.isEmpty(trfRelList)) {
            return null;
        }
        return trfRelList.parallelStream().map(ReferenceConvertor::toReferenceInfo).collect(Collectors.toList());
    }
    public static ReferenceInfo toReferenceInfo(EnquiryTrfRelationshipPO orderTrfRelInfo) {
        if (Func.isEmpty(orderTrfRelInfo)) {
            return null;
        }
        ReferenceInfo referenceInfo = new ReferenceInfo();
        referenceInfo.setRefSystemId(orderTrfRelInfo.getRefSystemId());
        referenceInfo.setReferenceNo(orderTrfRelInfo.getRefNo());
        referenceInfo.setCreateType(orderTrfRelInfo.getCreateType());
        referenceInfo.setTrfId(orderTrfRelInfo.getId());
        referenceInfo.setExtObjectId(orderTrfRelInfo.getExtObjectId());
        referenceInfo.setExternalOrderNo(orderTrfRelInfo.getExternalOrderNo());
        referenceInfo.setExtData(orderTrfRelInfo.getExtData());
        referenceInfo.setIntegrationChannel(orderTrfRelInfo.getIntegrationChannel());
        return referenceInfo;
    }
    public static List<ReferenceInfo> toReferenceInfoListByOrder(List<OrderTrfRelationshipPO> trfRelList) {
        if (Func.isEmpty(trfRelList)) {
            return null;
        }
        return trfRelList.parallelStream().map(ReferenceConvertor::toReferenceInfo).collect(Collectors.toList());
    }
    public static ReferenceInfo toReferenceInfo(OrderTrfRelationshipPO orderTrfRelInfo) {
        if (Func.isEmpty(orderTrfRelInfo)) {
            return null;
        }
        ReferenceInfo referenceInfo = new ReferenceInfo();
        referenceInfo.setRefSystemId(orderTrfRelInfo.getRefSystemId());
        referenceInfo.setReferenceNo(orderTrfRelInfo.getRefNo());
        referenceInfo.setCreateType(orderTrfRelInfo.getCreateType());
        referenceInfo.setTrfId(orderTrfRelInfo.getId());
        referenceInfo.setExtObjectId(orderTrfRelInfo.getExtObjectId());
        referenceInfo.setExternalOrderNo(orderTrfRelInfo.getExternalOrderNo());
        referenceInfo.setExternalOrderNoSuffix(orderTrfRelInfo.getExternalOrderNoSuffix());
        referenceInfo.setTrfSourceType(orderTrfRelInfo.getTrfSourceType());
        referenceInfo.setExtData(orderTrfRelInfo.getExtData());
        referenceInfo.setIntegrationChannel(orderTrfRelInfo.getIntegrationChannel());
        return referenceInfo;
    }
}
