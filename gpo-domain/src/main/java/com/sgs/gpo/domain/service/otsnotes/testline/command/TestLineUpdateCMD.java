package com.sgs.gpo.domain.service.otsnotes.testline.command;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineDomainService;
import com.sgs.gpo.domain.service.otsnotes.testline.context.TestLineUpdateContext;
import com.sgs.gpo.facade.model.otsnotes.testline.req.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @title: TestLineUpdateCMD
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/28 10:39
 */
@Service
@Slf4j
public class TestLineUpdateCMD extends BaseCommand<TestLineUpdateContext<TestLineUpdateReq>> {
    @Autowired
    private ITestLineDomainService testLineDomainService;

    @Override
    public BaseResponse validateParam(TestLineUpdateContext<TestLineUpdateReq> context) {
        if (Func.isEmpty(context) || Func.isEmpty(context.getParam())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        TestLineUpdateReq testLineUpdateReq = context.getParam();
        if (Func.isEmpty(testLineUpdateReq.getTestLineInstanceId())) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"testLineInstanceId"});
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    @Transactional
    public BaseResponse execute(TestLineUpdateContext<TestLineUpdateReq> context) {

        TestLineUpdateReq testLineUpdateReq = context.getParam();
        String testLineInstanceId = testLineUpdateReq.getTestLineInstanceId();
        List<CitationUpdateReq> citationUpdateReqList = testLineUpdateReq.getCitationUpdateReqList();
        CitationUpdateReq citationUpdateReq = new CitationUpdateReq();
        citationUpdateReq.setTestLineInstanceId(testLineInstanceId);
        testLineDomainService.citationUpdate(citationUpdateReq);


        List<ConditionUpdateReq> conditionUpdateReqList = testLineUpdateReq.getConditionUpdateReqList();

        List<LabSectionUpdateReq> labSectionUpdateReqList = testLineUpdateReq.getLabSectionUpdateReqList();

        List<AnalyteUpdateReq> analyteUpdateReqList = testLineUpdateReq.getAnalyteUpdateReqList();




        ConditionUpdateReq conditionUpdateReq = new ConditionUpdateReq();
        testLineDomainService.conditionUpdate(conditionUpdateReq);
        AnalyteUpdateReq analyteUpdateReq = new AnalyteUpdateReq();
        testLineDomainService.analyteUpdate(analyteUpdateReq);
        LabSectionUpdateReq labSectionUpdateReq = new LabSectionUpdateReq();
        testLineDomainService.updateLabSection(labSectionUpdateReq);

        return BaseResponse.newSuccessInstance(true);
    }
}
