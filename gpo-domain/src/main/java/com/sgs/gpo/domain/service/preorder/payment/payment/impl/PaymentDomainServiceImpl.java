package com.sgs.gpo.domain.service.preorder.payment.payment.impl;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.domain.service.payment.paymentlist.subdomain.IPaymentListService;
import com.sgs.gpo.domain.service.preorder.payment.payment.IPaymentDomainService;
import com.sgs.gpo.facade.model.payment.paymentlist.bo.PaymentBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/7 12:00
 */
@Service
@Slf4j
public class PaymentDomainServiceImpl implements IPaymentDomainService {

    @Autowired
    IPaymentListService paymentListService;

    @Override
    public BaseResponse<List<PaymentBO>> queryPaymentByOrderNo(String orderNo) {
        List<PaymentBO> paymentBOList = paymentListService.selectPaymentInfoByOrderNo(orderNo);
        return BaseResponse.newSuccessInstance(paymentBOList);
    }
}
