package com.sgs.gpo.domain.service.extservice.sci.impl;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.gpo.domain.service.extservice.sci.IOrderTrfService;
import com.sgs.gpo.facade.model.extsystem.sci.req.GetSCICustomerInfoReq;
import com.sgs.gpo.facade.model.sci.dto.TrfDTO;
import com.sgs.gpo.integration.sci.SciClient;
import com.sgs.gpo.integration.sci.req.SciHeaderReq;
import com.sgs.gpo.integration.sci.req.SciTrfReq;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j;
import org.springframework.stereotype.Service;

@Service
@Log4j
@AllArgsConstructor
public class OrderTrfServiceImpl implements IOrderTrfService {
    private SciClient sciClient;
    @Override
    public BaseResponse<TrfDTO> getTrfFromSCI(GetSCICustomerInfoReq getSCICustomerInfoReq) {
        SciTrfReq<GetSCICustomerInfoReq> sciTrfReq = new SciTrfReq<>();
        SciHeaderReq sciHeaderReq = new SciHeaderReq();
        sciHeaderReq.setLabCode(SystemContextHolder.getLabCode());
        sciHeaderReq.setOperator(SystemContextHolder.getUserInfoFillSystem().getRegionAccount());
        sciHeaderReq.setToken(SystemContextHolder.getSgsToken());
        sciHeaderReq.setProductLineCode(SystemContextHolder.getBuCode());
        sciTrfReq.setHeader(sciHeaderReq);
        sciTrfReq.setBody(getSCICustomerInfoReq);
        BaseResponse<TrfDTO> customerInfo = sciClient.getCustomerInfo(sciTrfReq);
        return customerInfo;
    }
}
