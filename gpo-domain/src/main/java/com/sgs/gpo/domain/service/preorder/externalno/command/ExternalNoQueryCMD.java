package com.sgs.gpo.domain.service.preorder.externalno.command;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.facade.domain.req.BuParamReq;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.externalno.ExternalNoRelPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.preorder.externalno.context.ExternalNoContext;
import com.sgs.gpo.domain.service.preorder.externalno.subdomain.IExternalNoRelService;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IGeneralOrderService;
import com.sgs.gpo.facade.model.preorder.externalno.bo.ExternalNoBO;
import com.sgs.gpo.facade.model.preorder.externalno.req.ExternalNoQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.gpo.integration.framework.FrameworkClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Service
public class ExternalNoQueryCMD extends BaseCommand<ExternalNoContext<ExternalNoQueryReq>> {

    @Autowired
    IReportService reportService;
    @Autowired
    IGeneralOrderService generalOrderService;
    @Autowired
    IExternalNoRelService externalNoRelService;
    @Autowired
    FrameworkClient frameworkClient;

    @Override
    public BaseResponse validateParam(ExternalNoContext<ExternalNoQueryReq> context) {
        ExternalNoQueryReq externalNoQueryReq = context.getParam();
        Assert.isTrue(Func.isNotEmpty(externalNoQueryReq), "common.param.miss", new Object[]{Constants.TERM.REQUEST});
        Assert.isTrue(Func.isNotEmpty(externalNoQueryReq.getObjectIdList()), "common.param.miss", new Object[]{Constants.TERM.REQUEST});
        Assert.isTrue(Func.isNotEmpty(externalNoQueryReq.getObjectType()), "common.param.miss", new Object[]{Constants.TERM.REQUEST});
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(ExternalNoContext<ExternalNoQueryReq> context) {
        boolean showExternalNo = true;
        ExternalNoQueryReq externalNoQueryReq = context.getParam();
        String objectType = externalNoQueryReq.getObjectType();
        Set<String> objectIdList = externalNoQueryReq.getObjectIdList();
        Lab lab = context.getLab();
        //查询BUParam
        if(Func.isNotEmpty(lab)){
            BuParamReq buParamReq = new BuParamReq();
            buParamReq.setProductLineCode(lab.getBuCode());
            buParamReq.setLocationCode(lab.getLocationCode());
            buParamReq.setGroupCode(Constants.BU_PARAM.ORDER.GROUP);
            buParamReq.setParamCode(Constants.BU_PARAM.ORDER.SHOW_ORDER_EXTERNAL_NO.CODE);
            BuParamValueRsp buParamValueRsp = frameworkClient.getBuParam(buParamReq);
            if(Func.isNotEmpty(buParamValueRsp)){
                if(Func.equalsSafe(buParamValueRsp.getParamValue(),Constants.BU_PARAM.ORDER.SHOW_ORDER_EXTERNAL_NO.VALUE.FALSE)){
                    showExternalNo = false;
                }
            }
        }
        context.setShowExternalNo(showExternalNo);
        if(Func.equalsSafe(objectType, Constants.OBJECT.ORDER.OBJECT_CODE)){
            //OrderId 查询外部号
            List<ExternalNoRelPO> externalNoRelPOList = externalNoRelService.select(externalNoQueryReq);
            //查询Order信息
            OrderQueryReq orderQueryReq = new OrderQueryReq();
            orderQueryReq.setOrderIdList(externalNoQueryReq.getObjectIdList());
            List<GeneralOrderPO> generalOrderPOList = generalOrderService.query2(orderQueryReq).getData();
            context.setExternalNoRelPOList(externalNoRelPOList);
            context.setGeneralOrderPOList(generalOrderPOList);
        } else if(Func.equalsSafe(objectType, Constants.OBJECT.REPORT.OBJECT_CODE)){
            //ReportNo 查询ActualReportNo
            ReportQueryReq reportQueryReq = new ReportQueryReq();
            reportQueryReq.setReportIdList(objectIdList);
            List<ReportPO> reportPOList = reportService.select(reportQueryReq).getData();
            context.setReportPOList(reportPOList);
        }
        this.buildDomain(context);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse buildDomain(ExternalNoContext<ExternalNoQueryReq> context) {
        boolean showExternalNo = context.isShowExternalNo();
        ExternalNoQueryReq externalNoQueryReq = context.getParam();
        String objectType = externalNoQueryReq.getObjectType();
        List<ExternalNoRelPO> externalNoRelPOList = context.getExternalNoRelPOList();
        List<ReportPO> reportPOList = context.getReportPOList();
        List<GeneralOrderPO> generalOrderPOList = context.getGeneralOrderPOList();
        List<ExternalNoBO> externalNoBOList = new ArrayList<>();
        if(Func.equalsSafe(objectType, Constants.OBJECT.ORDER.OBJECT_CODE)){
           if(Func.isNotEmpty(externalNoRelPOList)){
               externalNoRelPOList.stream().forEach(externalNoRelPO -> {
                   ExternalNoBO externalNoBO = new ExternalNoBO();
                   externalNoBO.setObjectId(externalNoRelPO.getGeneralOrderId());
                   if(showExternalNo){
                       externalNoBO.setExternalNo(externalNoRelPO.getExternalOrderNo());
                   } else {
                       if(Func.isNotEmpty(generalOrderPOList)){
                           GeneralOrderPO orderPO = generalOrderPOList.stream().filter(e -> Func.equalsSafe(e.getId(),externalNoRelPO.getGeneralOrderId())).findAny().orElse(new GeneralOrderPO());
                           externalNoBO.setExternalNo(orderPO.getOrderNo());
                       }
                   }
                   externalNoBOList.add(externalNoBO);
               });
           }
        } else if(Func.equalsSafe(objectType, Constants.OBJECT.REPORT.OBJECT_CODE)){
            if(Func.isNotEmpty(reportPOList)){
                reportPOList.stream().forEach(reportPO -> {
                    ExternalNoBO externalNoBO = new ExternalNoBO();
                    externalNoBO.setObjectId(reportPO.getId());
                    if(showExternalNo){
                        externalNoBO.setExternalNo(reportPO.getActualReportNo());
                    } else {
                        externalNoBO.setExternalNo(reportPO.getReportNo());
                    }
                    externalNoBOList.add(externalNoBO);
                });
            }
        }
        context.setExternalNoBOList(externalNoBOList);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(ExternalNoContext<ExternalNoQueryReq> context) {
        return BaseResponse.newSuccessInstance(context.getExternalNoBOList());
    }
}
