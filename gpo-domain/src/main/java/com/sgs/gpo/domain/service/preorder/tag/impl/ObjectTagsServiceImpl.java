package com.sgs.gpo.domain.service.preorder.tag.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.tag.ObjectTagsMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.tag.ObjectTagPO;
import com.sgs.gpo.domain.service.preorder.tag.IObjectTagsService;
import com.sgs.gpo.facade.model.tag.req.ObjectTagsQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class ObjectTagsServiceImpl extends ServiceImpl<ObjectTagsMapper, ObjectTagPO> implements IObjectTagsService {
    @Override
    public List<ObjectTagPO> query(ObjectTagsQueryReq tagsQueryReq) {
        if (Func.isEmpty(tagsQueryReq) || (Func.isEmpty(tagsQueryReq.getObjectId()) && Func.isEmpty(tagsQueryReq.getObjectIdList()))) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<ObjectTagPO> wrapper = Wrappers.lambdaQuery();
        if(Func.isNotEmpty(tagsQueryReq.getObjectId())){
            wrapper.eq(ObjectTagPO::getObjectId, tagsQueryReq.getObjectId());
        }
        if(Func.isNotEmpty(tagsQueryReq.getObjectIdList())){
            wrapper.in(ObjectTagPO::getObjectId, tagsQueryReq.getObjectIdList());
        }
        if(Func.isNotEmpty(tagsQueryReq.getObject())){
            wrapper.eq(ObjectTagPO::getObject, tagsQueryReq.getObject());
        }
        return baseMapper.selectList(wrapper);
    }
}
