package com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixBO;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixIdBO;
import com.sgs.framework.open.platform.base.service.IBaseService;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.TestMatrixPO;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixProcessReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.rsp.TestMatrixSampleRsp;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/3 10:52
 */
public interface ITestMatrixService extends IBaseService<TestMatrixBO,TestMatrixPO, TestMatrixIdBO,TestMatrixQueryReq> {

    /**
     * 查询TestMatrix业务对象集合
     *
     * @param testMatrixQueryReq
     * @return
     */
    BaseResponse<List<com.sgs.framework.model.test.testmatrix.TestMatrixBO>> queryV1(TestMatrixQueryReq testMatrixQueryReq);

    BaseResponse<List<TestMatrixPO>> queryList(TestMatrixQueryReq testMatrixQueryReq);

    BaseResponse<Boolean> updateMatrixStatus(TestMatrixProcessReq testMatrixProcessReq);

    BaseResponse<List<TestMatrixSampleRsp>> selectTestMatrixSample(TestMatrixQueryReq testMatrixQueryReq);
}
