package com.sgs.gpo.domain.service.preorder.enquiry.command;


import com.beust.jcommander.internal.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.common.attachment.AttachmentBO;
import com.sgs.framework.model.common.contact.ContactPersonBO;
import com.sgs.framework.model.common.customer.CustomerBO;
import com.sgs.framework.model.common.customer.CustomerContactBO;
import com.sgs.framework.model.common.customer.CustomerLanguageBO;
import com.sgs.framework.model.common.dff.DFFParallelAttrBO;
import com.sgs.framework.model.common.lab.LabBO;
import com.sgs.framework.model.common.productsample.ProductSampleBO;
import com.sgs.framework.model.common.servicerequirement.DeliverBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementReportBO;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.order.enquiry.*;
import com.sgs.framework.model.order.trf.TrfBO;
import com.sgs.framework.model.order.v2.OrderIdRelBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.attachment.OrderAttachmentPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.*;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.tag.ObjectTagPO;
import com.sgs.gpo.domain.service.preorder.attachment.IOrderAttachmentService;
import com.sgs.gpo.domain.service.preorder.enquiry.context.EnquiryContext;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.*;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IGeneralOrderService;
import com.sgs.gpo.domain.service.preorder.productsample.IProductSampleDomainService;
import com.sgs.gpo.domain.service.preorder.tag.IObjectTagsService;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderAttachmentQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.preorder.productsample.req.ProductSampleQueryReq;
import com.sgs.gpo.facade.model.tag.req.ObjectTagsQueryReq;
import com.sgs.priceengine.facade.model.enums.CustomerUsage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Enquiry查询方法，返回BO结构
 */
@Primary
@Service("enquiryQueryCMD")
@Slf4j
public class EnquiryQueryCMD extends BaseCommand<EnquiryContext<EnquiryQueryReq>> {

    @Autowired
    private IEnquiryService enquiryService;
    @Autowired
    private IEnquiryCustomerService enquiryCustomerService;
    @Autowired
    private IEnquiryPersonService enquiryPersonService;
    @Autowired
    private IObjectTagsService objectTagsService;
    @Autowired
    private IGeneralOrderService generalOrderService;
    @Autowired
    private IEnquiryTrfRelationshipService enquiryTrfRelationshipService;
    @Autowired
    private IEnquiryTestRequestService testRequestService;
    @Autowired
    private IEnquiryReportReceiverService reportReceiverService;
    @Autowired
    private IEnquiryTestRequestContactsService testRequestContactsService;
    @Autowired
    private IEnquiryOrderMatrixService orderMatrixService;
    @Autowired
    private IProductSampleDomainService productSampleDomainService;

    @Autowired
    private IOrderAttachmentService orderAttachmentService;

    @Autowired
    private IEnquiryProductService enquiryProductService;


    /**
     * 基础查询校验
     *
     * @param context
     * @return
     */
    @Override
    public BaseResponse validateParam(EnquiryContext<EnquiryQueryReq> context) {
        Assert.isTrue(Func.isNotEmpty(context.getParam()), "common.param.miss", new String[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(context.getLab()), "common.param.miss", new String[]{Constants.TERM.LAB.getCode()});
        return BaseResponse.newSuccessInstance(true);
    }


    @Override
    public BaseResponse before(EnquiryContext<EnquiryQueryReq> context) {
        List<EnquiryPO> enquiryList = enquiryService.query(context.getParam());
        if (Func.isNotEmpty(enquiryList)) {
            context.setEnquiryPOList(enquiryList);
            Set<String> enquiryIdList = enquiryList.stream().map(EnquiryPO::getId).collect(Collectors.toSet());
            ProductSampleQueryReq productSampleQueryReq = new ProductSampleQueryReq();
            productSampleQueryReq.setOrderIdList(enquiryIdList);
            BaseResponse<List<ProductSampleBO>> productSampleRsp = productSampleDomainService.queryEnquiryProductSample(productSampleQueryReq);
            if(productSampleRsp.isSuccess() && Func.isNotEmpty(productSampleRsp.getData())){
                List<ProductSampleBO> productSampleBOList = productSampleRsp.getData();
                context.setProductSampleBOList(productSampleBOList);
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(EnquiryContext<EnquiryQueryReq> context) {
        this.buildDomain(context);
        return BaseResponse.newSuccessInstance(context.getEnquiryBOList());
    }

    /**
     * PO->BO
     *
     * @param context
     * @return
     */
    @Override
    public BaseResponse buildDomain(EnquiryContext<EnquiryQueryReq> context) {
        if (Func.isEmpty(context.getEnquiryPOList())) {
            context.setEnquiryBOList(Lists.newArrayList());
            return BaseResponse.newSuccessInstance(true);
        }
        // Enquiry
        Set<String> enquiryIdList = context.getEnquiryPOList().stream().map(EnquiryPO::getId).collect(Collectors.toSet());
        EnquiryIdReq enquiryIdReq = new EnquiryIdReq();
        enquiryIdReq.setEnquiryIdList(enquiryIdList);
        // 查询EnquiryCustomer
        List<EnquiryCustomerPO> enquiryCustomerList = new ArrayList<>();
        BaseResponse<List<EnquiryCustomerPO>> enquiryCustomerListRes = enquiryCustomerService.select(enquiryIdReq);
        if(Func.isNotEmpty(enquiryCustomerListRes) && enquiryCustomerListRes.isSuccess()){
            enquiryCustomerList = enquiryCustomerListRes.getData();
        }
        // 查询EnquiryPerson信息
        List<EnquiryPersonPO> enquiryPersonList = enquiryPersonService.select(enquiryIdReq);
        // 查询Enquiry关联的Order列表
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setEnquiryIdList(enquiryIdList);
        List<GeneralOrderPO> enquiryOrderList = generalOrderService.query2(orderQueryReq).getData();
        // 查询Tag
        ObjectTagsQueryReq tagsQueryReq = new ObjectTagsQueryReq();
        tagsQueryReq.setObjectIdList(enquiryIdList);
        tagsQueryReq.setObject("Enquiry");
        List<ObjectTagPO> enquiryTags = objectTagsService.query(tagsQueryReq);
        // 查询TrfNo
        BaseResponse<List<EnquiryTrfRelationshipPO>> enquiryTrfRes = enquiryTrfRelationshipService.select(enquiryIdReq);
        // 查询testRequest
        List<EnquiryTestRequestPO> testRequestList = testRequestService.select(enquiryIdReq);
        // 查询reportInfo
        BaseResponse<List<EnquiryReportReceiverPO>> reportReceiver = reportReceiverService.select(enquiryIdReq);
        List<EnquiryReportReceiverPO> reportReceiverList;
        if (Func.isNotEmpty(reportReceiver)) {
            reportReceiverList = reportReceiver.getData();
        } else {
            reportReceiverList = null;
        }
        List<EnquiryTestRequestContactsPO> testRequestContacts = testRequestContactsService.select(enquiryIdReq);
        List<EnquiryOrderMatrixPO> enquiryOrderMatrixList = orderMatrixService.select(enquiryIdReq);
        List<EnquiryBO> enquiryBOList = Lists.newArrayList();
        List<EnquiryCustomerPO> finalEnquiryCustomerList = enquiryCustomerList;
        List<ProductSampleBO> productSampleBOList = context.getProductSampleBOList();

        List<EnquiryProductPO> enquiryProductPo = enquiryProductService.select(enquiryIdReq).getData();

        OrderAttachmentQueryReq orderAttachmentQueryReq = new OrderAttachmentQueryReq();
        orderAttachmentQueryReq.setOrderIdList(enquiryIdList);
        List<OrderAttachmentPO> enquiryAttachmentList = orderAttachmentService.select(orderAttachmentQueryReq).getData();

        context.getEnquiryPOList().stream().forEach(item -> {
            EnquiryBO enquiry = new EnquiryBO();
            enquiry.setCreateDate(item.getCreatedDate());
            enquiry.setCreatedBy(item.getCreatedBy());
            // EnquiryId
            EnquiryIdBO enquiryId = new EnquiryIdBO();
            enquiryId.setEnquiryId(item.getId());
            enquiryId.setEnquiryNo(item.getEnquiryNo());
            enquiry.setId(enquiryId);
            // EnquiryHeader
            EnquiryHeaderBO enquiryHeader = new EnquiryHeaderBO();
            Func.copy(item, enquiryHeader);
            enquiry.setHeader(enquiryHeader);
            //lab
            LabBO lab = new LabBO();
            Func.copy(item, lab);
            enquiry.setLab(lab);
            //flags
            EnquiryFlagBO flags = new EnquiryFlagBO();
            Func.copy(item, flags);
            if(Func.isEmpty(item.getSelfTestFlag())){
                flags.setSelfTestFlag(0);
            }
            enquiry.setFlags(flags);
            //others
            EnquiryOthersBO others = new EnquiryOthersBO();
            Func.copy(item, others);
            if (Func.isNotEmpty(enquiryTags)) {
                List<ObjectTagPO> thisTagList = enquiryTags.stream().filter(tag -> Func.equalsSafe(tag.getObjectId(), item.getId()))
                        .collect(Collectors.toList());
                if (Func.isNotEmpty(thisTagList) && Func.isNotEmpty(thisTagList.get(0).getData())) {
                    others.setTags(thisTagList.get(0).getData());
                }
            }
            enquiry.setOthers(others);
            // customer
            this.assembleEnquiryCustomer(finalEnquiryCustomerList, enquiry);
            // person
            this.assembleEnquiryPerson(enquiryPersonList, enquiry);
            // relation
            this.assembleRelationShip(enquiryOrderList, enquiry, enquiryTrfRes.getData());
            // TestRequest
            this.assembleTestRequest(enquiry, testRequestList, testRequestContacts);
            // reportinfo
            this.assembleReportInfo(enquiry, reportReceiverList, testRequestContacts);
            // enquiryMatrix
            this.assembleEnquiryMatrix(enquiry,enquiryOrderMatrixList);
            this.assembleProductSampleBO(enquiry,productSampleBOList);
            // dff
            this.assembleEnquiryDFF(enquiry, enquiryProductPo);
            // attachmentList
            this.assembleEnquiryAttachment(enquiry, enquiryAttachmentList);
            enquiryBOList.add(enquiry);
        });
        context.setEnquiryBOList(enquiryBOList);
        return BaseResponse.newSuccessInstance(true);
    }

    private void assembleEnquiryDFF(EnquiryBO enquiry, List<EnquiryProductPO> enquiryProductPoList) {
        List<DFFParallelAttrBO> dffParallelAttrList = new ArrayList<>();
        if(Func.isNotEmpty(enquiryProductPoList)){
            List<EnquiryProductPO> newEnquiryProductList = enquiryProductPoList.stream().filter(e -> Func.equalsSafe(e.getEnquiryId(),enquiry.getId().getEnquiryId())).collect(Collectors.toList());
            if (Func.isNotEmpty(newEnquiryProductList)) {
                newEnquiryProductList.stream().forEach(item -> {
                    DFFParallelAttrBO dFFParallelAttrBO = Func.deepCopy(item, DFFParallelAttrBO.class);
                    dffParallelAttrList.add(dFFParallelAttrBO);
                });
            }
        }
        enquiry.setDffAttrList(dffParallelAttrList);
    }

    private void assembleEnquiryAttachment(EnquiryBO enquiry, List<OrderAttachmentPO> enquiryAttachmentList) {
        List<AttachmentBO> attachmentList = new ArrayList<>();
        if(Func.isNotEmpty(enquiryAttachmentList)){
            List<OrderAttachmentPO> newEnquiryAttachmentList = enquiryAttachmentList.stream().filter(e -> Func.equalsSafe(e.getGeneralOrderID(),enquiry.getId().getEnquiryId())).collect(Collectors.toList());
            if (Func.isNotEmpty(newEnquiryAttachmentList)) {
                newEnquiryAttachmentList.stream().forEach(item -> {
                    AttachmentBO attachmentBO = new AttachmentBO();
                    attachmentBO.setId(item.getFileID());
                    attachmentBO.setFileName(item.getAttachmentName());
                    attachmentBO.setFileType(item.getFileType());
                    attachmentBO.setCloudId(item.getCloudID());
                    attachmentBO.setDisplayInReport(item.getShowInReport() == 1);
                    attachmentBO.setObjectType(item.getTestLineInstanceId() == null ? 1 : 2);
                    attachmentBO.setObjectId(item.getObjectID());
                    attachmentBO.setBusinessType(item.getBusinessType());
                    attachmentBO.setSampleNo(item.getSampleNo());
                    attachmentBO.setPhotoType(item.getPhotoType());
                    attachmentBO.setDisplaySeq(item.getSequence());
                    attachmentBO.setToCp(item.getToCp());
                    attachmentBO.setDescription(item.getDescription());
                    attachmentBO.setSource(item.getSource());
                    attachmentBO.setReportNo(item.getReportNo());
                    attachmentBO.setJobNo(item.getJobNo());
                    attachmentBO.setTag(item.getTag());
                    attachmentList.add(attachmentBO);
                });
            }
        }
        enquiry.setAttachmentList(attachmentList);
    }

    private void assembleReportInfo(EnquiryBO enquiry, List<EnquiryReportReceiverPO> reportInfoList, List<EnquiryTestRequestContactsPO> testRequestContactsList) {
        ServiceRequirementBO serviceRequirement = enquiry.getServiceRequirement();
        if (Func.isAllEmpty(reportInfoList, testRequestContactsList, serviceRequirement)) {
            return;
        }
        ServiceRequirementReportBO report = serviceRequirement.getReport();
        if (Func.isEmpty(report)) {
            return;
        }

        if (Func.isNotEmpty(reportInfoList)) {
            List<EnquiryReportReceiverPO> thisReportInfoList = reportInfoList.stream().filter(item -> Func.equalsSafe(item.getEnquiryId(), enquiry.getId().getEnquiryId()))
                    .collect(Collectors.toList());
            if (Func.isEmpty(thisReportInfoList)) {
                return;
            }
            EnquiryReportReceiverPO reportInfo = thisReportInfoList.get(0);
            if (Func.isEmpty(reportInfo)) {
                return;
            }
            report.setReportHeader(reportInfo.getReportHeader());
            report.setReportAddress(reportInfo.getReportDeliveredTo());
        }
        serviceRequirement.setReport(report);
        enquiry.setServiceRequirement(serviceRequirement);
    }

    private void assembleProductSampleBO(EnquiryBO enquiry,List<ProductSampleBO> productSampleBOList) {
        if(Func.isNotEmpty(productSampleBOList)){
            ProductSampleBO productSampleBO = productSampleBOList.stream().filter(e -> Func.equalsSafe(e.getOrderId(),enquiry.getId().getEnquiryId())).findAny().orElse(null);
            if (Func.isNotEmpty(productSampleBO)) {
                if (Func.isNotEmpty(productSampleBO.getProduct())) {
                    enquiry.setProduct(productSampleBO.getProduct());
                }
                if (Func.isNotEmpty(productSampleBO.getSampleList())) {
                    // 中英文Qty不一致时以主语言的数量为主
                    productSampleBO.getSampleList().stream().forEach(sample -> {
                        if (Func.isEmpty(sample.getSampleAttrList())) {
                            return;
                        }
                        sample.getSampleAttrList().stream().forEach(attr -> {
                            if (!Func.equalsSafe(attr.getLabelCode(), "NoOfSample")) {
                                return;
                            }
                            if (Func.isEmpty(attr.getValue())) {
                                return;
                            }
                            if (Func.isEmpty(attr.getLanguageList())) {
                                return;
                            }
                            Integer sampleQty = Integer.valueOf(attr.getValue().toString());
                            attr.getLanguageList().stream().forEach(language -> {
                                if (Func.isNotEmpty(language.getValue())) {
                                    language.setValue(sampleQty);
                                }
                            });
                        });
                    });
                    enquiry.setSampleList(productSampleBO.getSampleList());
                }
            }
        }
    }

    private void assembleEnquiryMatrix(EnquiryBO enquiry,List<EnquiryOrderMatrixPO> enquiryOrderMatrixList){
        if(Func.isEmpty(enquiryOrderMatrixList)){
            return;
        }
        List<EnquiryMatrixBO> enquiryMatrixList  = enquiryOrderMatrixList.stream().map(enquiryOrderMatrixPO -> Func.copy(enquiryOrderMatrixPO,EnquiryMatrixBO.class))
                .collect(Collectors.toList());
        enquiry.setEnquiryMatrixList(enquiryMatrixList);
    }

    /**
     * 组装Enquiry关联数据
     *
     * @param enquiryOrderList
     * @param enquiry
     */
    private void assembleRelationShip(List<GeneralOrderPO> enquiryOrderList, EnquiryBO enquiry, List<EnquiryTrfRelationshipPO> enquiryTrfRelationshipList) {
        EnquiryRelationshipBO relationshipBO = new EnquiryRelationshipBO();
        // 过滤对应的Order列表
        if (Func.isNotEmpty(enquiryOrderList)) {
            List<GeneralOrderPO> thisOrderList = enquiryOrderList.stream().filter(item -> Func.equalsSafe(item.getEnquiryId(), enquiry.getId().getEnquiryId()))
                    .collect(Collectors.toList());
            if (Func.isNotEmpty(thisOrderList)) {
                EnquiryChildrenBO children = new EnquiryChildrenBO();
                List<OrderIdRelBO> orderList = Lists.newArrayList();
                thisOrderList.stream().forEach(order -> {
                    OrderIdRelBO orderIdRelBO = new OrderIdRelBO();
                    orderIdRelBO.setOrderId(order.getId());
                    orderIdRelBO.setOrderNo(order.getOrderNo());
                    orderList.add(orderIdRelBO);
                });
                children.setOrderList(orderList);
                relationshipBO.setChildren(children);
            }
        }
        // 过滤TrfNo
        if (Func.isNotEmpty(enquiryTrfRelationshipList)) {
            List<EnquiryTrfRelationshipPO> thisTrfList = enquiryTrfRelationshipList.stream().filter(item -> Func.equalsSafe(item.getEnquiryId(), enquiry.getId().getEnquiryId()))
                    .collect(Collectors.toList());
            if (Func.isNotEmpty(thisTrfList)) {
                EnquiryParentBO parent = new EnquiryParentBO();
                List<TrfBO> trfList = Lists.newArrayList();
                thisTrfList.stream().forEach(item -> {
                    TrfBO trfBO = new TrfBO();
                    trfBO.setTrfNo(item.getRefNo());
                    trfBO.setRefSystemId(item.getRefSystemId());
                    trfBO.setExternalOrderNo(item.getExternalOrderNo());
                    trfBO.setExtData(item.getExtData());
                    trfList.add(trfBO);
                });
                parent.setTrfList(trfList);
                relationshipBO.setParent(parent);
            }

        }
        enquiry.setRelationship(relationshipBO);
    }

    /**
     * 组装客户数据
     *
     * @param enquiryCustomerList
     * @param enquiry
     */
    private void assembleEnquiryCustomer(List<EnquiryCustomerPO> enquiryCustomerList, EnquiryBO enquiry) {
        if (Func.isEmpty(enquiryCustomerList)) {
            return;
        }
        List<EnquiryCustomerPO> thisCustomerList = enquiryCustomerList.stream().filter(item -> Func.equalsSafe(item.getEnquiryId(), enquiry.getId().getEnquiryId()))
                .collect(Collectors.toList());
        if (Func.isEmpty(thisCustomerList)) {
            return;
        }
        List<CustomerBO> customerList = Lists.newArrayList();
        thisCustomerList.stream().forEach(cus -> {
            CustomerBO customer = new CustomerBO();
            customer.setCustomerId(cus.getCustomerId());
            customer.setPaymentTerm(cus.getPaymentTermName());
            customer.setCustomerUsage(Func.isNotEmpty(cus.getCustomerUsage()) && Func.isNotEmpty(CustomerUsage.getCode(cus.getCustomerUsage())) ? CustomerUsage.getCode(cus.getCustomerUsage()).getStatus() : 0);
            customer.setCustomerName(Func.isNotEmpty(cus.getCustomerNameEn()) ? cus.getCustomerNameEn() : cus.getCustomerNameCn());
            customer.setCustomerAddress(Func.isNotEmpty(cus.getCustomerAddressEn()) ? cus.getCustomerAddressEn() : cus.getCustomerAddressCn());
            customer.setBossNo(cus.getBossNumber());
            customer.setCustomerGroupId(cus.getCustomerGroupId());
            customer.setCustomerGroupCode(cus.getBuyerGroup());
            customer.setPrimaryFlag(cus.getPrimaryFlag());
            customer.setMonthlyPayment(cus.getMonthlyPayment());
            customer.setBossLocationCode(cus.getBossLocationCode());
            customer.setAccountId(cus.getAccountID());
            // languageList
            List<CustomerLanguageBO> languageList = Lists.newArrayList();
            if (Func.isNotEmpty(cus.getCustomerNameCn())) {
                CustomerLanguageBO customerCn = new CustomerLanguageBO();
                customerCn.setCustomerName(cus.getCustomerNameCn());
                customerCn.setCustomerAddress(cus.getCustomerAddressCn());
                customerCn.setLanguageId(LanguageType.Chinese.getLanguageId());
                languageList.add(customerCn);
            }
            if (Func.isNotEmpty(cus.getCustomerNameEn())) {
                CustomerLanguageBO customerEn = new CustomerLanguageBO();
                customerEn.setCustomerName(cus.getCustomerNameEn());
                customerEn.setCustomerAddress(cus.getCustomerAddressEn());
                customerEn.setLanguageId(LanguageType.English.getLanguageId());
                languageList.add(customerEn);
            }
            // 联系人
            List<CustomerContactBO> customerContactList = Lists.newArrayList();
            CustomerContactBO customerContact = new CustomerContactBO();
            customerContact.setContactName(cus.getContactPersonName());
            customerContact.setContactTelephone(cus.getContactPersonPhone1());
            customerContact.setContactMobile(cus.getContactPersonPhone2());
            customerContact.setContactEmail(cus.getContactPersonEmail());
            customerContact.setContactPersonRemark(cus.getContactPersonRemark());
            customerContact.setCustomerContactId(cus.getCustomerId());
            customerContact.setCustomerContactAddressId(cus.getContactAddressId());
            customerContact.setBossContactId(cus.getBossContactId());
            customerContact.setBossSiteUseId(cus.getBossSiteUserId());
            customerContact.setContactSgsMartAccount(cus.getSgsMartAccount());
            customerContact.setContactSgsMartUserId(cus.getSgsMartUserId());
            customerContact.setContactFAX(cus.getContactPersonFax());
            customerContactList.add(customerContact);
            customer.setCustomerContactList(customerContactList);
            customer.setLanguageList(languageList);
            customerList.add(customer);
        });
        enquiry.setCustomerList(customerList);
    }

    private void assembleTestRequest(EnquiryBO enquiry, List<EnquiryTestRequestPO> testRequestList, List<EnquiryTestRequestContactsPO> testRequestContactsList) {
        if (Func.isEmpty(testRequestList) && Func.isEmpty(testRequestContactsList)) {
            return;
        }

        ServiceRequirementBO serviceRequirement = new ServiceRequirementBO();
        ServiceRequirementReportBO report = new ServiceRequirementReportBO();
        List<DeliverBO> delivers  = Lists.newArrayList();

        if (Func.isNotEmpty(testRequestList)) {
            List<EnquiryTestRequestPO> thisTestRequestList = testRequestList.stream().filter(item -> Func.equalsSafe(item.getEnquiryId(), enquiry.getId().getEnquiryId()))
                    .collect(Collectors.toList());
            if (Func.isEmpty(thisTestRequestList)) {
                return;
            }
            EnquiryTestRequestPO firstRequest = thisTestRequestList.get(0);
            if (Func.isEmpty(firstRequest)) {
                return;
            }
            serviceRequirement = Func.copy(firstRequest,ServiceRequirementBO.class);
            serviceRequirement.setReturnTestSampleFlag(firstRequest.getReturnTestedSampleFlag());
            report = Func.copy(firstRequest,ServiceRequirementReportBO.class);
            report.setNeedPhoto(firstRequest.getTakePhotoFlag());
            report.setPdfReportSecurity(firstRequest.getPdfReportSecurity());
            if (Func.isNotEmpty(firstRequest.getReportLanguage())) {
                try {
                    report.setReportLanguage(Integer.parseInt(firstRequest.getReportLanguage()));
                } catch (NumberFormatException e) {
                    // 可根据业务需求记录日志或设置默认值
                    report.setReportLanguage(null); // 默认值或保留原值
                }
            }

        }
        if (Func.isNotEmpty(testRequestContactsList)) {
            List<EnquiryTestRequestContactsPO> thisTestRequestContactsList = testRequestContactsList.stream().filter(item -> Func.equalsSafe(item.getEnquiryId(), enquiry.getId().getEnquiryId()))
                    .collect(Collectors.toList());
            if (Func.isEmpty(thisTestRequestContactsList)) {
                return;
            }
            thisTestRequestContactsList.stream().forEach(contact->{
                DeliverBO deliverBO = new DeliverBO();
                deliverBO.setId(contact.getId());
                if(Func.isNotEmpty(contact.getDeliverTo())){
                    String[] deliveryTo = contact.getDeliverTo().split(",");
                    if(Func.isNotEmpty(deliveryTo)){
                        deliverBO.setDeliveryTo(Arrays.stream(deliveryTo).map(item->Integer.valueOf(item)).collect(Collectors.toList()));
                    }
                }
                deliverBO.setContactsType(contact.getContactsType());
                deliverBO.setDeliveryOthers(contact.getDeliverOthers());
                delivers.add(deliverBO);
            });
        }
        serviceRequirement.setReport(report);
        serviceRequirement.setDelivers(delivers);
        enquiry.setServiceRequirement(serviceRequirement);
    }

    /**
     * 组装person数据
     */
    private void assembleEnquiryPerson(List<EnquiryPersonPO> enquiryPersonList, EnquiryBO enquiry) {
        if (Func.isEmpty(enquiryPersonList)) {
            return;
        }
        List<EnquiryPersonPO> thisPersonList = enquiryPersonList.stream().filter(item -> Func.equalsSafe(item.getEnquiryId(), enquiry.getId().getEnquiryId()))
                .collect(Collectors.toList());
        if (Func.isEmpty(thisPersonList)) {
            return;
        }
        List<ContactPersonBO> contactPersonList = Lists.newArrayList();
        thisPersonList.stream().forEach(thisPerson -> {
            ContactPersonBO contactPerson = new ContactPersonBO();
            contactPerson.setContactUsage(thisPerson.getPersonType());
            contactPerson.setRegionAccount(thisPerson.getRegionAccount());
            contactPersonList.add(contactPerson);
        });
        enquiry.setContactPersonList(contactPersonList);
    }

    @Override
    public BaseResponse after(EnquiryContext<EnquiryQueryReq> context) {

        return BaseResponse.newSuccessInstance(true);
    }
}
