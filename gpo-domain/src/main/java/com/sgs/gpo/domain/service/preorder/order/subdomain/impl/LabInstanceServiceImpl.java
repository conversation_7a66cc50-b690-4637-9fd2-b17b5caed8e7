package com.sgs.gpo.domain.service.preorder.order.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.LabInstanceMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.LabInstancePO;
import com.sgs.gpo.domain.service.preorder.order.subdomain.ILabInstanceService;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/6 09:53
 */
@Service
public class LabInstanceServiceImpl extends ServiceImpl<LabInstanceMapper, LabInstancePO> implements ILabInstanceService {

    @Override
    public BaseResponse<List<LabInstancePO>> select(OrderIdReq orderIdReq) {
        if(Func.isEmpty(orderIdReq)||Func.isEmpty(orderIdReq.getOrderIdList())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        QueryWrapper<LabInstancePO> queryWrapper = new QueryWrapper();
        if(Func.isNotEmpty(orderIdReq.getOrderIdList())) {
            queryWrapper.in(LabInstancePO.COLUMN.ORDER_ID,orderIdReq.getOrderIdList());
        }
        List<LabInstancePO> list = baseMapper.selectList(queryWrapper);
        return BaseResponse.newSuccessInstance(list);
    }
}
