package com.sgs.gpo.domain.service.otsnotes.report.subdomain.impl;

import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.beust.jcommander.internal.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.PageBO;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.framework.model.report.report.v2.ReportIdBO;
import com.sgs.framework.open.platform.base.service.impl.AbstractBaseService;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.report.ReportMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.conclusion.ConclusionListPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.TestSampleReportPO;
import com.sgs.gpo.domain.service.otsnotes.report.command.ReportTestMatrixMergeModeBizCMD;
import com.sgs.gpo.domain.service.otsnotes.report.command.ReportTestResultStatusUpdateCMD;
import com.sgs.gpo.domain.service.otsnotes.report.context.ReportContext;
import com.sgs.gpo.domain.service.otsnotes.report.context.ReportUpdateContext;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.facade.model.otsnotes.conclusion.ConclusionDTO;
import com.sgs.gpo.facade.model.otsnotes.report.req.ReportTestMatrixModelGetReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import com.sgs.gpo.facade.model.report.req.QuerySampleReportReq;
import com.sgs.gpo.facade.model.report.req.ReferenceReportQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportAccreditationUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportEntryModeUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportIdReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportTestResultStatusUpdateReq;
import com.sgs.gpo.facade.model.trackingtool.TrackingToolDataReq;
import com.sgs.gpo.facade.model.trackingtool.TrackingToolDataRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/6/7 12:00
 */
@Service
@Slf4j
public class ReportServiceImpl
        extends AbstractBaseService<ReportBO, ReportPO, ReportIdBO, ReportMapper, ReportQueryReq>
        implements IReportService {
    @Override
    public BaseResponse<List<TestSampleReportPO>> queryReportByTestSample(QuerySampleReportReq querySampleReportReq) {
        if (Func.isEmpty(querySampleReportReq) || Func.isEmpty(querySampleReportReq.getTestSampleIdList())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        List<TestSampleReportPO> testSampleReportPOS = baseMapper.queryReportByTestSample(querySampleReportReq);
        return BaseResponse.newSuccessInstance(testSampleReportPOS);
    }

    @Override
    public BaseResponse<List<ReportPO>> queryReportByOrderNo(OrderIdReq orderIdReq) {
        if (Func.isEmpty(orderIdReq) || Func.isEmpty(orderIdReq.getOrderNoList())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        QueryWrapper<ReportPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("orderNo", orderIdReq.getOrderNoList());
        queryWrapper.orderByAsc("createdDate");
        List<ReportPO> gpnReportPOS = baseMapper.selectList(queryWrapper);
        return BaseResponse.newSuccessInstance(gpnReportPOS);
    }

    @Override
    public BaseResponse<ConclusionListPO> queryReportConclusion(String reportId) {
        BaseResponse<ConclusionListPO> response = new BaseResponse<>();
        if (Func.isEmpty(reportId)) {
            return response;
        }
        response.setData(this.baseMapper.queryReportConclusion(reportId));
        return response;
    }

    @Override
    public BaseResponse<List<ConclusionDTO>> queryReportConclusionList(ReportIdReq reportIdReq) {
        BaseResponse<List<ConclusionDTO>> response = new BaseResponse<>();
        if (Func.isEmpty(reportIdReq) || Func.isEmpty(reportIdReq.getReportIdList())) {
            return BaseResponse.newFailInstance("Param  missing.");
        }
        Set<String> reportIdList = reportIdReq.getReportIdList();
        List<ConclusionDTO> conclusionDTOS = this.baseMapper.queryReportConclusionList(reportIdList);
        response.setData(conclusionDTOS);
        return response;
    }

    @Override
    public BaseResponse<List<ConclusionDTO>> queryReportMatrixConclusionList(ReportIdReq reportIdReq) {
        BaseResponse<List<ConclusionDTO>> response = new BaseResponse<>();
        if (Func.isEmpty(reportIdReq) || Func.isEmpty(reportIdReq.getReportIdList())) {
            return BaseResponse.newFailInstance("Param  missing.");
        }
        Set<String> reportIdList = reportIdReq.getReportIdList();
        List<ConclusionDTO> conclusionDTOS = this.baseMapper.queryReportMatrixConclusionList(reportIdList);
        response.setData(conclusionDTOS);
        return response;
    }

    @Override
    public BaseResponse<List<ReportPO>> select(ReportQueryReq reportIdReq) {
        if (Func.isEmpty(reportIdReq) ||
                (Func.isAllEmpty(reportIdReq.getReportIdList(), reportIdReq.getReportNoList(), reportIdReq.getReportNo(),reportIdReq.getOrderNoList()) &&
                Func.isAnyEmpty(reportIdReq.getActualReportNoList(), reportIdReq.getLabId()))) {
            return BaseResponse.newFailInstance("Param  missing.");
        }
        LambdaQueryWrapper<ReportPO> wrapper = Wrappers.lambdaQuery();
        if (Func.isNotEmpty(reportIdReq.getReportId())) {
            wrapper.eq(ReportPO::getId, reportIdReq.getReportId());
        }
        if (Func.isNotEmpty(reportIdReq.getReportIdList())) {
            wrapper.in(ReportPO::getId, reportIdReq.getReportIdList());
        }
        if (Func.isNotEmpty(reportIdReq.getReportNoList())) {
            wrapper.in(ReportPO::getReportNo, reportIdReq.getReportNoList());
        }
        if (Func.isNotEmpty(reportIdReq.getReportNo())) {
            wrapper.eq(ReportPO::getReportNo, reportIdReq.getReportNo());
        }
        if (Func.isNotEmpty(reportIdReq.getOrderNoList())) {
            wrapper.in(ReportPO::getOrderNo, reportIdReq.getOrderNoList());
        }
        if (Func.isNotEmpty(reportIdReq.getActualReportNoList())) {
            wrapper.in(ReportPO::getActualReportNo, reportIdReq.getActualReportNoList());
        }
        if (Func.isNotEmpty(reportIdReq.getLabId())) {
            wrapper.eq(ReportPO::getLabId, reportIdReq.getLabId());
        }
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }

    @Override
    public BaseResponse<List<ReportPO>> queryV1(ReportQueryReq reportQueryReq) {
        if (Func.isEmpty(reportQueryReq.getLab())) {
            reportQueryReq.setLab(SystemContextHolder.getLab());
        }
        //
        if(Func.isAnyNotEmpty(reportQueryReq.getReportId(),reportQueryReq.getReportNo(),reportQueryReq.getReportIdList(),reportQueryReq.getReportNoList())){
            reportQueryReq.setUseAccurateQuery(true);
        }
        Assert.isTrue(Func.isNotEmpty(reportQueryReq.getLab()), "common.param.miss", new Object[]{Constants.TERM.LAB.getCode()});
        return BaseResponse.newSuccessInstance(baseMapper.select(reportQueryReq));
    }

    @Override
    public String queryJobValidateByReportId(String reportId) {
        return baseMapper.getJobValidateByReportId(reportId);
    }

    @Override
    public List<ReportBO> convertToBO(Collection<ReportPO> poList) {
        return null;
    }

    @Override
    public List<ReportPO> convertToPO(Collection<ReportBO> boList) {
        if (Func.isEmpty(boList)) {
            return Collections.emptyList();
        }
        Date now = new Date();
        List<ReportPO> reportPOList = Lists.newArrayList();
        boList.forEach(bo -> {
            ReportPO reportPO = new ReportPO();
            reportPO.setId(bo.getId().getReportId());
            reportPO.setCertificateId(bo.getHeader().getCertificateId());
            reportPO.setCertificateFileCloudKey(bo.getHeader().getCertificateFileCloudKey());
            reportPO.setCertificateName(bo.getHeader().getCertificateName());
            reportPO.setModifiedBy(Func.isEmpty(bo.getModifiedBy()) ? SystemContextHolder.getUserInfoFillSystem().getRegionAccount() : bo.getModifiedBy());
            reportPO.setModifiedDate(Func.isEmpty(bo.getModifiedDate()) ? now : bo.getModifiedDate());
            reportPOList.add(reportPO);
        });
        return reportPOList;
    }

    @Override
    public LambdaQueryWrapper<ReportPO> createWrapper(ReportQueryReq queryReq) {
        return null;
    }

    @Override
    public BaseResponse<String> getReportTestMatrixMode(ReportTestMatrixModelGetReq reportTestMatrixModelGetReq) {
        ReportContext<ReportTestMatrixModelGetReq> reportContext = new ReportContext<>();
        // 生成报告的入参
        reportContext.setParam(reportTestMatrixModelGetReq);
        // 上下文基本信息
        reportContext.setUserInfo(SystemContextHolder.getUserInfoFillSystem());
        reportContext.setLab(SystemContextHolder.getLab());
        if (Func.isNotEmpty(reportTestMatrixModelGetReq.getProductLineCode())) {
            reportContext.setProductLineCode(reportTestMatrixModelGetReq.getProductLineCode());
        } else {
            reportContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        }
        reportContext.setToken(SystemContextHolder.getSgsToken());
        return BaseExecutor.start(ReportTestMatrixMergeModeBizCMD.class, reportContext);
    }

    @Override
    public BaseResponse<Boolean> updateReportTestResultStatus(ReportTestResultStatusUpdateReq reportTestResultStatusUpdateReq) {
        ReportUpdateContext<ReportTestResultStatusUpdateReq> context = new ReportUpdateContext<>();
        // 生成报告的入参
        context.setParam(reportTestResultStatusUpdateReq);
        // 上下文基本信息
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        context.setLab(SystemContextHolder.getLab());
        context.setToken(reportTestResultStatusUpdateReq.getToken());
        if(Func.isEmpty(reportTestResultStatusUpdateReq.getToken())){
            context.setToken(SystemContextHolder.getSgsToken());
        }
        if (Func.isNotEmpty(reportTestResultStatusUpdateReq.getProductLineCode())) {
            context.setProductLineCode(reportTestResultStatusUpdateReq.getProductLineCode());
        } else {
            context.setProductLineCode(reportTestResultStatusUpdateReq.getProductLineCode());
        }
        ExecutorService executorService = ThreadUtil.newExecutor(5);
        executorService.execute(() -> {
            // 执行任务
            try {
                BaseExecutor.start(ReportTestResultStatusUpdateCMD.class, context);
                executorService.shutdown();
                if (!executorService.awaitTermination(90, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
            }
        });
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse<Boolean> updateReportEntryMode(ReportEntryModeUpdateReq reportEntryModeUpdateReq) {
        if (Func.isEmpty(reportEntryModeUpdateReq) || Func.isAnyEmpty(reportEntryModeUpdateReq.getReportIdList(),reportEntryModeUpdateReq.getEntryMode())) {
            return BaseResponse.newFailInstance("Param  missing.");
        }
        String regionAccount = SecurityContextHolder.getUserInfoFillSystem().getRegionAccount();
        reportEntryModeUpdateReq.setModifiedBy(regionAccount);
        reportEntryModeUpdateReq.setModifiedDate(new Date());
        int i = baseMapper.updateReportEntryMode(reportEntryModeUpdateReq);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public List<ReportPO> queryReferReportList(ReferenceReportQueryReq req) {
        if(Func.isEmpty(req) || Func.isEmpty(req.getTestSampleId())){
            return Lists.newArrayList();
        }
        return baseMapper.queryReferReportList(req);
    }

    @Override
    public PageBO<TrackingToolDataRsp> selectTrackingToolData(TrackingToolDataReq trackingToolDataReq, Integer page, Integer rows) {
        IPage<TrackingToolDataRsp> iPage = baseMapper.selectTrackingToolData(new Page(page,rows),trackingToolDataReq);
        PageBO<TrackingToolDataRsp> toolDataRspPageBO = Func.copy(iPage, PageBO.class);
        return toolDataRspPageBO;
    }

    @Override
    public Boolean updateReportAccreditation(ReportAccreditationUpdateReq reportAccreditationUpdateReq) {
        if (Func.isEmpty(reportAccreditationUpdateReq) || Func.isEmpty(reportAccreditationUpdateReq.getReportIdList())) {
            return false;
        }
        int i = baseMapper.updateReportCertificate(reportAccreditationUpdateReq);
        log.info("updateReportCertificate:count:{},{}",i,Func.toJson(reportAccreditationUpdateReq));
        return i>0;
    }

    @Override
    public List<ReportPO> queryReportBySubReport(ReportQueryReq reportQueryReq) {
        if(Func.isEmpty(reportQueryReq.getSubReportId())){
            return null;
        }
        return baseMapper.queryReportBySubReportId(reportQueryReq.getSubReportId());
    }
}
