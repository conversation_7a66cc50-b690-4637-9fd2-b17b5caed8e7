package com.sgs.gpo.domain.service.otsnotes.status.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.status.GpnStatusLogMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.status.GpnStatusLogPO;
import com.sgs.gpo.domain.service.otsnotes.status.IGpnStatusLogService;
import com.sgs.gpo.facade.model.status.req.StatusLogReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;



@Service
@Slf4j
public class GpnStatusLogServiceImpl extends ServiceImpl<GpnStatusLogMapper, GpnStatusLogPO> implements IGpnStatusLogService {

    @Override
    public int delete(StatusLogReq statusLogReq) {
        if(Func.isEmpty(statusLogReq.getObjectType()) || Func.isEmpty(statusLogReq.getReportId())){
            return 0;
        }
        LambdaQueryWrapper<GpnStatusLogPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GpnStatusLogPO::getObjectType,statusLogReq.getObjectType());
        wrapper.eq(GpnStatusLogPO::getReportId,statusLogReq.getReportId());
        return baseMapper.delete(wrapper);
    }
}
