package com.sgs.gpo.domain.service.otsnotes.report.subdomain.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.report.ReportFileHistoryMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportFileHistoryPO;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportFileHistoryService;
import com.sgs.gpo.facade.model.report.req.ReportFileQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service
@Slf4j
public class ReportFileHistoryServiceImpl extends ServiceImpl<ReportFileHistoryMapper, ReportFileHistoryPO> implements IReportFileHistoryService {


    @Override
    public List<ReportFileHistoryPO> query(ReportFileQueryReq req) {
        List<ReportFileHistoryPO> list = new ArrayList<>();
        if(Func.isEmpty(req) || (Func.isEmpty(req.getReportFileIdList()) && Func.isEmpty(req.getReportIdList()))){
            return list;
        }
        LambdaQueryWrapper<ReportFileHistoryPO> wrapper = Wrappers.lambdaQuery();
        if(Func.isNotEmpty(req.getReportFileIdList())){
            wrapper.in(ReportFileHistoryPO::getReportFileId, req.getReportFileIdList());
        }
        if(Func.isNotEmpty(req.getReportIdList())){
            wrapper.in(ReportFileHistoryPO::getReportId, req.getReportIdList());
        }
        wrapper.orderByDesc(ReportFileHistoryPO::getCreatedDate);
        return baseMapper.selectList(wrapper);
    }
}
