package com.sgs.gpo.domain.service.otsnotes.job.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.PageBO;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.JobStatus;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.TestExecutionType;
import com.sgs.framework.model.order.v2.OrderIdRelBO;
import com.sgs.framework.model.test.execution.v2.job.*;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.test.testmatrix.TestMatrixBO;
import com.sgs.framework.open.platform.base.service.impl.AbstractBaseService;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.job.JobMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.job.JobPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.order.GeneralOrderInstancePO;
import com.sgs.gpo.domain.service.otsnotes.job.subdomain.IJobService;
import com.sgs.gpo.domain.service.otsnotes.job.subdomain.IJobTestLineService;
import com.sgs.gpo.domain.service.otsnotes.order.IGeneralOrderInstanceService;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.ITestLineService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixService;
import com.sgs.gpo.facade.model.job.req.JobDeleteReq;
import com.sgs.gpo.facade.model.job.req.JobQueryReq;
import com.sgs.gpo.facade.model.otsnotes.job.req.JobAssignOwnerReq;
import com.sgs.gpo.facade.model.otsnotes.job.req.JobLabInReq;
import com.sgs.gpo.facade.model.otsnotes.job.req.JobLabOutReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineLabInReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.otsnotes.facade.model.enums.OrderStatus;
import com.sgs.otsnotes.facade.model.enums.PreOrderStatus;
import com.sgs.preorder.facade.OrderFacade;
import com.sgs.preorder.facade.model.req.OrderNosReq;
import com.sgs.preorder.facade.model.req.SysStatusReq;
import com.sgs.preorder.facade.model.rsp.OrderStatusRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/7 12:00
 */
@Service
@Slf4j
public class JobServiceImpl
        extends AbstractBaseService<JobBO,JobPO,JobIdBO,JobMapper, JobQueryReq>
        implements IJobService {

    @Autowired
    private ITestMatrixService testMatrixService;
    @Autowired
    private OrderFacade orderFacade;
    @Autowired
    private IGeneralOrderInstanceService generalOrderInstanceService;
    @Autowired
    private IJobTestLineService jobTestLineService;
    @Autowired
    private ITestLineService testLineService;

    @Override
    public List<JobPO> query(JobQueryReq jobQueryReq) {
        if(Func.isEmpty(jobQueryReq.getLab())) {
            jobQueryReq.setLab(SystemContextHolder.getLab());
        }
        Assert.isTrue(Func.isNotEmpty(jobQueryReq.getLab()),"common.param.miss",new Object[]{Constants.TERM.LAB.getCode()});
        // 判断BU为SL，orderIndex为空。不需要执行Tops逻辑
        if(Func.equalsSafe(ProductLineContextHolder.getProductLineCode(), ProductLineType.SL.getProductLineAbbr())){
            jobQueryReq.setBlockTops(ProductLineType.SL.getProductLineAbbr());
            if(Func.isAnyNotEmpty(jobQueryReq.getOrderNoList(), jobQueryReq.getOrderNo(),jobQueryReq.getJobNoList(), jobQueryReq.getJobNo(),jobQueryReq.getJobIdList())){
                jobQueryReq.setLab(null);
            }
        }
        return baseMapper.selectList(jobQueryReq);
    }

    @Override
    public List<JobBO> convertToBO(Collection<JobPO> jobPOList) {
        List<JobBO> jobBOList = Lists.newArrayList();
        if(Func.isNotEmpty(jobPOList)){
            jobPOList.forEach(jobPO -> {
                JobBO jobBO = new JobBO();
                // ID
                JobIdBO jobIdBO = new JobIdBO();
                jobIdBO.setJobId(jobPO.getId());
                jobIdBO.setJobNo(jobPO.getJobNo());
                jobBO.setId(jobIdBO);

                // Relationship
                JobRelationshipBO jobRelationshipBO = new JobRelationshipBO();
                JobParentBO jobParentBO = new JobParentBO();
                OrderIdRelBO orderIdBO = new OrderIdRelBO();
                orderIdBO.setOrderId(jobPO.getGeneralOrderInstanceId());
                orderIdBO.setOrderNo(jobPO.getOrderNo());
                jobParentBO.setOrder(orderIdBO);
                jobRelationshipBO.setParent(jobParentBO);
                jobBO.setRelationship(jobRelationshipBO);

                // Header
                JobHeaderBO jobHeaderBO = new JobHeaderBO();
                jobHeaderBO.setJobStatus(jobPO.getJobStatus());
                jobBO.setHeader(jobHeaderBO);
                jobBOList.add(jobBO);
            });
        }
        return jobBOList;
    }

    @Override
    public List<JobPO> convertToPO(Collection<JobBO> jobPOList) {
        //TODO
        return null;
    }

    @Override
    public LambdaQueryWrapper<JobPO> createWrapper(JobQueryReq queryReq) {
        return null;
    }

    @Override
    public PageBO<JobPO> page(JobQueryReq jobQueryReq, Integer page, Integer rows) {
        IPage<JobPO> iPage = baseMapper.selectPage(new Page(page,rows),jobQueryReq);
        PageBO<JobPO> jobPOPage = Func.copy(iPage,PageBO.class);
        return jobPOPage;
    }

    @Override
    public BaseResponse<Boolean> validateRel(List<TestLineBO> testLineList,boolean protocolFlag) {

        List<TestMatrixBO> testMatrixList;
        //排除分包出去的TL
        testLineList = testLineList.stream().filter(i -> {
            return (Func.isEmpty(i.getTestExecutionList()) || !TestExecutionType.check(i.getTestExecutionList().get(0).getTestExecutionType(), TestExecutionType.SUBCONTRACT));
        }).collect(Collectors.toList());
        //排除Pending掉的TestLine
        testLineList = testLineList.stream().filter(item -> Func.isEmpty(item.getPendingFlag()) || !item.getPendingFlag()).collect(Collectors.toList());
        // 不能pending
        /*List<TestLineBO> pendingTestLineList = testLineList.stream().filter(testline->{return Func.isNotEmpty(testline.getPendingFlag()) && testline.getPendingFlag();}).collect(Collectors.toList());
        if(Func.isNotEmpty(pendingTestLineList)){
            String pendingTestLine = String.format("%s %s ", Constants.OBJECT.TEST_LINE,pendingTestLineList.stream().map(TestLineBO::getTestLineId).collect(Collectors.toSet()).toString());
            return BaseResponse.newFailInstance("common.pending.disable.action",new Object[]{pendingTestLine,"create job"});
        }*/

        // 必须有lab section
        List<TestLineBO> noLabSectionList = testLineList.stream().filter(testline -> {
            return Func.isEmpty(testline.getLabSectionList());
        }).collect(Collectors.toList());
//        TPSL 不验证必须有lab section
        if (Func.equals("TP SL",SystemContextHolder.getLabCode())&&Func.isNotEmpty(noLabSectionList)&&noLabSectionList.size()!=testLineList.size()) {
            testLineList = testLineList.stream().filter(testline->Func.isNotEmpty((testline.getLabSectionList()))).collect(Collectors.toList());
        }else
        if (Func.isNotEmpty(noLabSectionList)) {

            String unLabSection = String.format("%s %s", Constants.OBJECT.TEST_LINE.OBJECT_CODE, noLabSectionList.stream().map(TestLineBO::getTestLineId).collect(Collectors.toSet()).toString());
            return BaseResponse.newFailInstance("test.line.not.lab.section", new Object[]{unLabSection});
        }
        if(protocolFlag && Func.isEmpty(testLineList)){
            return BaseResponse.newSuccessInstance(true);
        }
        // 查询TestMatrix -> 校验test line 是否 assign sample;
        Set<String> testLineInstanceIdList = testLineList.stream().map(TestLineBO::getTestLineInstanceId).collect(Collectors.toSet());
        TestMatrixQueryReq testMatrixQueryReq = new TestMatrixQueryReq();
        testMatrixQueryReq.setTestLineInstanceIdList(testLineInstanceIdList);

        BaseResponse<List<TestMatrixBO>> testMatrixResponse = testMatrixService.queryV1(testMatrixQueryReq);
        if (testMatrixResponse.isFail()) {
            return BaseResponse.newFailInstance(testMatrixResponse.getMessage());
        }
        testMatrixList = testMatrixResponse.getData();
        if (Func.isEmpty(testMatrixList)) {
            testMatrixList = Lists.newArrayList();
        }

        // TestLine 已经Assign Sample
        Set<String> testMatrixTestLineInstanceIdList = testMatrixList.stream().map(TestMatrixBO::getTestLineInstanceId).collect(Collectors.toSet());
        if (testMatrixTestLineInstanceIdList.size() != testLineInstanceIdList.size()) {
            Set<String> unAssignIdList = testLineInstanceIdList.stream().filter(id -> {
                return !testMatrixTestLineInstanceIdList.contains(id);
            }).collect(Collectors.toSet());
            Set<TestLineBO> unAssignSampleList = testLineList.stream().filter(testLineBO -> {
                return unAssignIdList.contains(testLineBO.getTestLineInstanceId());
            }).collect(Collectors.toSet());
            String key = unAssignSampleList.stream().map(TestLineBO::getTestLineId).collect(Collectors.toSet()).toString();
            return BaseResponse.newFailInstance("test.line.not.assign.sample", new Object[]{key});
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse<Boolean> updateJobOwner(JobAssignOwnerReq jobAssignOwnerReq) {
        if (Func.isEmpty(jobAssignOwnerReq) || Func.isEmpty(jobAssignOwnerReq.getJobOwner()) || Func.isEmpty(jobAssignOwnerReq.getJobNoList())) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"JobNoList"});
        }
        if (Func.isEmpty(jobAssignOwnerReq.getJobOwner())) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"JobOwner"});
        }
        JobPO jobPO = new JobPO();
        jobPO.setJobOwner(jobAssignOwnerReq.getJobOwner());
        if(Func.isNotEmpty(jobAssignOwnerReq.getSecondJobOwner())){
            jobPO.setSecondJobOwner(jobAssignOwnerReq.getSecondJobOwner());
        } else {
            jobPO.setSecondJobOwner("");
        }
        if(Func.isNotEmpty(jobAssignOwnerReq.getThirdJobOwner())){
            jobPO.setThirdJobOwner(jobAssignOwnerReq.getThirdJobOwner());
        } else{
            jobPO.setThirdJobOwner("");
        }
        UpdateWrapper<JobPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in(JobPO.COLUMN.JobNo, jobAssignOwnerReq.getJobNoList());
        baseMapper.update(jobPO, updateWrapper);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse<Boolean> updateJobForLabOut(JobLabOutReq jobLabOutReq) {
        if (Func.isEmpty(jobLabOutReq) || Func.isEmpty(jobLabOutReq.getJobNoList())) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"JobNoList"});
        }
        //更新LabOutDate、labOutBy，JobStatus=Closed
        JobQueryReq jobQueryReq = new JobQueryReq();
        jobQueryReq.setJobNoList(jobLabOutReq.getJobNoList());
        List<JobPO> jobPOList = this.query(jobQueryReq);
        UserInfo user = SystemContextHolder.getUserInfoFillSystem();
        String regionAccount = user.getRegionAccount();
        if (Func.isNotEmpty(jobPOList)) {
            UpdateWrapper<JobPO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.in(JobPO.COLUMN.JobNo, jobLabOutReq.getJobNoList());
            JobPO jobPO = new JobPO();
            jobPO.setLabOutDate(new Date());
            jobPO.setJobStatus(JobStatus.Closed.getStatus());
            jobPO.setModifiedBy(regionAccount);
            jobPO.setModifiedDate(new Date());
            baseMapper.update(jobPO, updateWrapper);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse<List<JobPO>> jobLabIn(JobLabInReq jobLabInReq) {

        QueryWrapper<JobPO> queryWrapper = new QueryWrapper<JobPO>();
        queryWrapper.in(JobPO.COLUMN.JobNo, jobLabInReq.getJobNos());
        List<JobPO> jobLabInList = baseMapper.selectList(queryWrapper);

        if (Func.isNull(jobLabInList)) {
            return BaseResponse.newFailInstance("search job fail");
        }

        // Check ，有效的Job Status（new状态）
        List<JobPO> errorJobs = jobLabInList.stream().filter(jobPo -> {
            return !com.sgs.otsnotes.facade.model.enums.JobStatus.check(jobPo.getJobStatus(), com.sgs.otsnotes.facade.model.enums.JobStatus.New);
        }).collect(Collectors.toList());

        if (Func.isNotEmpty(errorJobs)) {
            StringBuilder errorMsg = new StringBuilder();
            errorMsg.append("[");
            errorMsg.append(errorJobs.stream().map(JobPO::getJobNo).collect(Collectors.joining(",")));
            errorMsg.append("] job status is not new,can't lab in!");
            return BaseResponse.newFailInstance(errorMsg.toString());
        }

        List<String> orderNos = jobLabInList.stream().map(JobPO::getOrderNo).collect(Collectors.toList());
        OrderNosReq orderNosReq = new OrderNosReq();
        orderNosReq.setOrderNos(orderNos);
        orderNosReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        BaseResponse<List<OrderStatusRsp>> orderStatusResponse = orderFacade.getOrderStatusByOrderNoList(orderNosReq);

        if (!orderStatusResponse.isSuccess() || Func.isEmpty(orderStatusResponse.getData())) {
            return BaseResponse.newFailInstance("订单信息查询失败！");
        }
        List<OrderStatusRsp> orderStatusRspList = orderStatusResponse.getData();
        //check 无效的订单状态
        List<OrderStatusRsp> errorLabInOrders = orderStatusRspList.stream().filter(orderStatusRsp -> {
            Integer orderStatus = Func.toInt(orderStatusRsp.getOrderStatus());
            return Func.isEmpty(orderStatus)
                    || com.sgs.preorder.facade.model.enums.OrderStatus.checkStatus(orderStatus,
                    com.sgs.preorder.facade.model.enums.OrderStatus.Pending,
                    com.sgs.preorder.facade.model.enums.OrderStatus.Completed,
                    com.sgs.preorder.facade.model.enums.OrderStatus.Closed,
                    com.sgs.preorder.facade.model.enums.OrderStatus.Cancelled);
        }).collect(Collectors.toList());

        if (Func.isNotEmpty(errorLabInOrders)) {
            StringBuilder errorMsg = new StringBuilder();
            errorMsg.append("orders,[");
            errorLabInOrders.stream().forEach(errorLabInOrder -> {
                errorMsg.append(errorLabInOrder.getOrderNo()).append(":").append(com.sgs.preorder.facade.model.enums.OrderStatus.getOrderStatus(Func.toInt(errorLabInOrder.getOrderStatus()))).append(";");
            });
            errorMsg.append("],can't lab in!");
            return BaseResponse.newFailInstance(errorMsg.toString());
        }

        List<JobPO> needLabInJobs = null;
        List<String> labNoLabIns = null;
        //筛选符合条件的job，进行labIn
        needLabInJobs = jobLabInList.stream().filter(jobInfoPO -> {
            return Func.isEmpty(jobInfoPO.getLabInDate()) && com.sgs.otsnotes.facade.model.enums.JobStatus.check(jobInfoPO.getJobStatus(), com.sgs.otsnotes.facade.model.enums.JobStatus.New);
        }).collect(Collectors.toList());

        if (Func.isEmpty(needLabInJobs)) {
            return BaseResponse.newFailInstance("没有可以LabIn的Job！");
        }

        //只获取labId
        labNoLabIns = needLabInJobs.stream().map(JobPO::getId).collect(Collectors.toList());

        //更新job的状态
        if (Func.isNotEmpty(labNoLabIns)) {
            //根据jobno关联的订单更新订单状态
            int updateOrderQty = updateGenOrder(orderStatusRspList);
            if (updateOrderQty < 1) {
                return BaseResponse.newFailInstance("labIn关联订单更新状态失败");
            }
            UserInfo user = SecurityUtil.getUser();
            Date labInDate = DateUtil.now();
            JobPO jobLabInPo = new JobPO();
            jobLabInPo.setLabInDate(labInDate);
            jobLabInPo.setJobStatus(com.sgs.otsnotes.facade.model.enums.JobStatus.Testing.getStatus());
            jobLabInPo.setModifiedBy(user.getRegionAccount());
            jobLabInPo.setModifiedDate(labInDate);
            if (Func.isNotEmpty(jobLabInReq.getJobOwner())) {
                jobLabInPo.setJobOwner(jobLabInReq.getJobOwner());
            }
            UpdateWrapper<JobPO> updateWrapper = new UpdateWrapper<JobPO>();
            updateWrapper.in(JobPO.COLUMN.ID, labNoLabIns);
            Integer updateFlag = baseMapper.update(jobLabInPo, updateWrapper);
            if (updateFlag < 1) {
                throw new BizException("labIn 更新Job 状态失败");
            }
        }


        return BaseResponse.newSuccessInstance(needLabInJobs);
    }

    @Override
    public BaseResponse  labIn(JobLabInReq jobLabInReq){
        // 更新Job & Status
        JobPO jobPO = new JobPO();
        // 开始时间
        jobPO.setLabInDate(new Date());
        if(Func.isNotEmpty(jobLabInReq.getJobOwner())) {
            jobPO.setJobOwner(jobLabInReq.getJobOwner());
        }
        if(Func.isNotEmpty(jobLabInReq.getSecondJobOwner())) {
            jobPO.setSecondJobOwner(jobLabInReq.getSecondJobOwner());
        }
        if(Func.isNotEmpty(jobLabInReq.getThirdJobOwner())) {
            jobPO.setThirdJobOwner(jobLabInReq.getThirdJobOwner());
        }
        // 状态为测试中
        jobPO.setJobStatus(JobStatus.Testing.getStatus());

        LambdaUpdateWrapper <JobPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(JobPO::getJobNo,jobLabInReq.getJobNos());
        updateWrapper.eq(JobPO::getJobStatus,JobStatus.New.getStatus());

        return BaseResponse.newSuccessInstance(this.update(jobPO,updateWrapper));
    }

    @Override
    public BaseResponse<List<JobPO>> labOutClosed(JobLabOutReq jobLabOutReq) {
        //更新jobStatus和 LabOut时间
        JobPO jobPO = new JobPO();
        jobPO.setLabOutDate(new Date());
        //状态变为Close
        jobPO.setJobStatus(JobStatus.Closed.getStatus());
        jobPO.setModifiedBy(jobLabOutReq.getOperatedBy());
        LambdaUpdateWrapper <JobPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(JobPO::getJobNo,jobLabOutReq.getJobNoList());
        updateWrapper.eq(JobPO::getJobStatus,JobStatus.Completed.getStatus());
        this.update(jobPO,updateWrapper);
        //更新JobOwner
        //TODO 少了校验 修改更新语句 更新两句
        if(Func.isNotEmpty(jobLabOutReq.getJobOwner())){
            JobPO jobPOOwner = new JobPO();
            jobPOOwner.setJobOwner(jobLabOutReq.getJobOwner());
            if(Func.isNotEmpty(jobLabOutReq.getSecondJobOwner())) {
                jobPOOwner.setSecondJobOwner(jobLabOutReq.getSecondJobOwner());
            }
            if(Func.isNotEmpty(jobLabOutReq.getThirdJobOwner())) {
                jobPOOwner.setThirdJobOwner(jobLabOutReq.getThirdJobOwner());
            }
            LambdaUpdateWrapper <JobPO> updateWrapperOwner = new LambdaUpdateWrapper<>();
            updateWrapperOwner.in(JobPO::getJobNo,jobLabOutReq.getJobNoList());
            updateWrapperOwner.isNull(JobPO::getJobOwner);
            this.update(jobPOOwner,updateWrapperOwner);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse<List<JobPO>> labOutCompleted(JobLabOutReq jobLabOutReq) {
        JobPO jobPO = new JobPO();
        //状态变为Completed
        jobPO.setJobStatus(JobStatus.Completed.getStatus());
        jobPO.setModifiedBy(jobLabOutReq.getOperatedBy());
        LambdaUpdateWrapper <JobPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(JobPO::getJobNo,jobLabOutReq.getJobNoList());
        updateWrapper.eq(JobPO::getJobStatus,JobStatus.Testing.getStatus());
        return BaseResponse.newSuccessInstance(this.update(jobPO,updateWrapper));
    }

    @Override
    public BaseResponse<List<JobPO>> queryJobByTestLine(JobQueryReq jobQueryReq) {
        if(Func.isEmpty(jobQueryReq) || Func.isEmpty(jobQueryReq.getTestLineInstanceIdList())){
            return BaseResponse.newFailInstance("common.miss", new Object[]{"TestLineInstanceIdList"});

        }
        List<JobPO> jobPOList = baseMapper.selectJobByTlInstanceIdList(jobQueryReq);
        return BaseResponse.newSuccessInstance(jobPOList);
    }

    private int updateGenOrder(List<OrderStatusRsp> orderStatusRspList) {
        AtomicInteger updateQty = new AtomicInteger();
        orderStatusRspList.forEach(orderStatusRsp -> {
            String orderNo = orderStatusRsp.getOrderNo();
            int oldStatus = Integer.parseInt(orderStatusRsp.getOrderStatus());
            if (Func.equals(oldStatus, PreOrderStatus.Confirmed.getStatus()) || Func.equals(oldStatus, PreOrderStatus.New.getStatus())) {
                QueryWrapper<GeneralOrderInstancePO> queryWrapper = new QueryWrapper<GeneralOrderInstancePO>();
                queryWrapper.eq(GeneralOrderInstancePO.COLUMN.ORDER_NO, orderNo);
                GeneralOrderInstancePO generalOrderInstancePOs = generalOrderInstanceService.getOne(queryWrapper);
                if (Func.isEmpty(generalOrderInstancePOs)) {
                    throw new BizException("jobLabIn没有找到相应的订单" + orderNo);
                }
                GeneralOrderInstancePO generalOrderInstancePO = new GeneralOrderInstancePO();
                generalOrderInstancePO.setId(generalOrderInstancePOs.getId());
                generalOrderInstancePO.setOrderStatus(OrderStatus.Testing.getStatus());
                generalOrderInstancePO.setModifiedDate(new Date());
                generalOrderInstancePO.setModifiedBy(SecurityContextHolder.getUserInfo().getRegionAccount());
                boolean b = generalOrderInstanceService.updateById(generalOrderInstancePO);
                if (b) {
                    updateQty.getAndIncrement();
                }
                SysStatusReq sysStatusReq = new SysStatusReq();
                sysStatusReq.setObjectNo(orderNo);
                sysStatusReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                sysStatusReq.setToken(SecurityContextHolder.getSgsToken());
                // gpostatusFacade.orderTesting(sysStatusReq);
            }

        });
        return updateQty.get();
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse<Boolean> removeByJobAndTL(JobDeleteReq jobDeleteReq) {
        if(Func.isEmpty(jobDeleteReq) || Func.isEmpty(jobDeleteReq.getJobId()) || Func.isEmpty(jobDeleteReq.getTestLineInstanceIdList())){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"jobId/testLineInstanceIdList"});
        }
        //删除JOB和TestLine的关系
        BaseResponse<Integer> integerBaseResponse = jobTestLineService.removeByJobAndTL(jobDeleteReq.getJobId(), jobDeleteReq.getTestLineInstanceIdList());
        if(integerBaseResponse.isSuccess()){
//            清空TL的TestStartDate和Engineer
            TestLineLabInReq testLineLabInReq = new TestLineLabInReq();
            testLineLabInReq.setTestLineInstanceIdList(jobDeleteReq.getTestLineInstanceIdList());
            testLineService.rollTestLineBackLabIn(testLineLabInReq);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse<List<JobPO>> select(JobQueryReq jobQueryReq) {
        if(Func.isEmpty(jobQueryReq) || Func.isAllEmpty(jobQueryReq.getOrderNo(),jobQueryReq.getOrderNoList(),jobQueryReq.getJobNo(),jobQueryReq.getJobNoList()) ){
            return BaseResponse.newFailInstance("common.param.miss",new Object[]{"Job"});
        }
        return BaseResponse.newSuccessInstance(baseMapper.select(jobQueryReq));
    }
}
