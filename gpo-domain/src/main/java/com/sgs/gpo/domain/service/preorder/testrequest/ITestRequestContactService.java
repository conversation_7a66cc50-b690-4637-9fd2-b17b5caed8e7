package com.sgs.gpo.domain.service.preorder.testrequest;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.testrequest.TestRequestContactPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.testrequest.TestRequestPO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;

import java.util.List;

public interface ITestRequestContactService extends IService<TestRequestContactPO> {

    BaseResponse<List<TestRequestContactPO>> select(OrderIdReq orderIdReq);

}
