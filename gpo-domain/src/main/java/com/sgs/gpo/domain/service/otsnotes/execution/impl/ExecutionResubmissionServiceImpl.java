package com.sgs.gpo.domain.service.otsnotes.execution.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.execution.ExecutionResubmissionMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.execution.ExecutionResubmissionRecordPO;
import com.sgs.gpo.domain.service.otsnotes.execution.IExecutionResubmissionService;
import com.sgs.gpo.facade.model.otsnotes.execution.req.ResubmissionQueryReq;
import com.sgs.gpo.facade.model.otsnotes.execution.req.ResubmissionUpdateReq;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
@AllArgsConstructor
@Slf4j
public class ExecutionResubmissionServiceImpl extends ServiceImpl<ExecutionResubmissionMapper, ExecutionResubmissionRecordPO> implements IExecutionResubmissionService {
    @Override
    public BaseResponse<Boolean> batchUpdateStatusById(ResubmissionUpdateReq resubmissionUpdateReq) {
        return BaseResponse.newSuccessInstance(baseMapper.batchUpdateStatusById(resubmissionUpdateReq)>0);
    }

    @Override
    public BaseResponse<List<ExecutionResubmissionRecordPO>> select(ResubmissionQueryReq resubmissionQueryReq) {
        if (Func.isEmpty(resubmissionQueryReq) ||
                Func.isAllEmpty(resubmissionQueryReq.getResubmissionId(), resubmissionQueryReq.getExecutionNo(), resubmissionQueryReq.getExecutionNos())) {
            return BaseResponse.newFailInstance("Param  missing.");
        }
        LambdaQueryWrapper<ExecutionResubmissionRecordPO> wrapper = new LambdaQueryWrapper<>();
        if (Func.isNotEmpty(resubmissionQueryReq.getResubmissionId())) {
            wrapper.eq(ExecutionResubmissionRecordPO::getId, resubmissionQueryReq.getResubmissionId());
        }
        if (Func.isNotEmpty(resubmissionQueryReq.getExecutionNo())) {
            wrapper.eq(ExecutionResubmissionRecordPO::getExecutionNo, resubmissionQueryReq.getExecutionNo());
        }
        if (Func.isNotEmpty(resubmissionQueryReq.getExecutionNos())) {
            wrapper.in(ExecutionResubmissionRecordPO::getExecutionNo, resubmissionQueryReq.getExecutionNos());
        }
        if (Func.isNotEmpty(resubmissionQueryReq.getActiveIndicator())) {
            wrapper.eq(ExecutionResubmissionRecordPO::getActiveIndicator, resubmissionQueryReq.getActiveIndicator());
        }
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }
}
