package com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.order.v2.TestLineIdRelBO;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixBO;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixHeaderBO;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixIdBO;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixParentBO;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixRelationshipBO;
import com.sgs.framework.model.test.testsample.v2.TestSampleIdBO;
import com.sgs.framework.model.test.testscheme.TestSchemeIdBO;
import com.sgs.framework.open.platform.base.service.impl.AbstractBaseService;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testmatrix.TestMatrixMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.TestMatrixPO;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixService;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixProcessReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.rsp.TestMatrixSampleRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/3 10:52
 */
@Service
@Slf4j
public class TestMatrixServiceImpl
        extends AbstractBaseService<TestMatrixBO,TestMatrixPO, TestMatrixIdBO,TestMatrixMapper, TestMatrixQueryReq>
        implements ITestMatrixService {

    @Autowired
    private TestMatrixMapper testMatrixMapper;

    @Override
    public BaseResponse<List<com.sgs.framework.model.test.testmatrix.TestMatrixBO>> queryV1(TestMatrixQueryReq testMatrixQueryReq){
        if(Func.isEmpty(testMatrixQueryReq)){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        if(Func.isEmpty(testMatrixQueryReq.getTestMatrixIdList())
                && Func.isEmpty(testMatrixQueryReq.getTestItemNoList())
                && Func.isEmpty(testMatrixQueryReq.getTestLineInstanceIdList())
                && Func.isEmpty(testMatrixQueryReq.getOrderIdList())
                && Func.isEmpty(testMatrixQueryReq.getOrderNoList())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        return BaseResponse.newSuccessInstance(testMatrixMapper.select(testMatrixQueryReq));
    }
    @Override
    public BaseResponse<List<TestMatrixPO>> queryList(TestMatrixQueryReq testMatrixQueryReq){
        if(Func.isEmpty(testMatrixQueryReq)){
            return BaseResponse.newFailInstance("common.miss",null);
        }
        QueryWrapper<TestMatrixPO> queryWrapper = new QueryWrapper<>();
        if(Func.isNotEmpty(testMatrixQueryReq.getTestMatrixIdList())){
            queryWrapper.in(TestMatrixPO.COLUMN.ID,testMatrixQueryReq.getTestMatrixIdList());
        }
        if(Func.isNotEmpty(testMatrixQueryReq.getTestLineInstanceIdList())){
            queryWrapper.in(TestMatrixPO.COLUMN.TestLineInstanceID,testMatrixQueryReq.getTestLineInstanceIdList());
        }
        if(Func.isNotEmpty(testMatrixQueryReq.getOrderIdList())){
            queryWrapper.in(TestMatrixPO.COLUMN.GeneralOrderInstanceID,testMatrixQueryReq.getOrderIdList());
        }
        if(Func.isNotEmpty(testMatrixQueryReq.getTestSampleInstanceIdList())){
            queryWrapper.in(TestMatrixPO.COLUMN.TestSampleID,testMatrixQueryReq.getTestSampleInstanceIdList());
        }
        return BaseResponse.newSuccessInstance(baseMapper.selectList(queryWrapper));
    }
    @Override
    public BaseResponse<Boolean> updateMatrixStatus(TestMatrixProcessReq testMatrixProcessReq) {
        if(Func.isEmpty(testMatrixProcessReq) || Func.isAllEmpty(testMatrixProcessReq.getTestMatrixIdList(),testMatrixProcessReq.getTestLineInstanceIdList()) || Func.isEmpty(testMatrixProcessReq.getNewMatrixStatus())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        UpdateWrapper<TestMatrixPO> updateWrapper = new UpdateWrapper<>();
        TestMatrixPO testMatrixPO = new TestMatrixPO();
        testMatrixPO.setMatrixStatus(testMatrixProcessReq.getNewMatrixStatus());
        testMatrixPO.setModifiedDate(new Date());
        testMatrixPO.setModifiedBy(SecurityUtil.getUserAccount());
        if (Func.isNotEmpty(testMatrixProcessReq.getTestMatrixIdList())) {
            updateWrapper.in(TestMatrixPO.COLUMN.ID, testMatrixProcessReq.getTestMatrixIdList());
        }
        if (Func.isNotEmpty(testMatrixProcessReq.getTestLineInstanceIdList())) {
            updateWrapper.in(TestMatrixPO.COLUMN.TestLineInstanceID, testMatrixProcessReq.getTestLineInstanceIdList());
        }
        baseMapper.update(testMatrixPO,updateWrapper);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse<List<TestMatrixSampleRsp>> selectTestMatrixSample(TestMatrixQueryReq testMatrixQueryReq) {
        if (Func.isEmpty(testMatrixQueryReq) ||
                Func.isAllEmpty(testMatrixQueryReq.getOrderIdList(),testMatrixQueryReq.getOrderNoList())) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"orderTestMatrixReq"});
        }
        List<TestMatrixSampleRsp> list = testMatrixMapper.selectTestMatrixSample(testMatrixQueryReq);
        return BaseResponse.newSuccessInstance(list);
    }

    @Override
    public List<TestMatrixBO> convertToBO(Collection<TestMatrixPO> poList) {
        if(Func.isEmpty(poList)){
            return Collections.emptyList();
        }
        List<TestMatrixBO> testMatrixBOList = Lists.newArrayList();
        poList.forEach(po->{
            TestMatrixBO testMatrixBO = new TestMatrixBO();
            testMatrixBO.setProductAttributeList(Lists.newArrayList());
            testMatrixBO.setConditionList(Lists.newArrayList());
            testMatrixBO.setSpecimenList(Lists.newArrayList());
            testMatrixBO.setPositionList(Lists.newArrayList());
            testMatrixBO.setAttachmentList(Lists.newArrayList());
            TestMatrixIdBO testMatrixIdBO = new TestMatrixIdBO();
            testMatrixIdBO.setTestMatrixId(po.getId());
            testMatrixIdBO.setTestMatrixNo(po.getMatrixNo());
            testMatrixIdBO.setId(po.getId());
            testMatrixBO.setId(testMatrixIdBO);
            TestMatrixRelationshipBO testMatrixRelationshipBO = getTestMatrixRelationshipBO(po);
            testMatrixBO.setRelationship(testMatrixRelationshipBO);
            TestMatrixHeaderBO testMatrixHeaderBO = new TestMatrixHeaderBO();
            testMatrixHeaderBO.setMatrixConfirmDate(new Date());
            testMatrixHeaderBO.setApplicationFactorIds(Lists.newArrayList());
            testMatrixBO.setHeader(testMatrixHeaderBO);
            testMatrixBO.setActiveIndicator(po.getActiveIndicator());
            testMatrixBO.setModifiedBy(po.getModifiedBy());
            testMatrixBO.setModifiedDate(po.getModifiedDate());
            testMatrixBO.setCreateDate(po.getCreatedDate());
            testMatrixBO.setCreatedBy(po.getCreatedBy());
            testMatrixBOList.add(testMatrixBO);
        });
        return testMatrixBOList;
    }

    private TestMatrixRelationshipBO getTestMatrixRelationshipBO(TestMatrixPO po) {
        TestMatrixRelationshipBO testMatrixRelationshipBO = new TestMatrixRelationshipBO();
        // Parent 处理
        TestMatrixParentBO testMatrixParentBO = new TestMatrixParentBO();
        // TestLine
        TestLineIdRelBO testLineIdRelBO = new TestLineIdRelBO();
        testLineIdRelBO.setTestLineInstanceId(po.getTestLineInstanceID());
        testMatrixParentBO.setTestLine(testLineIdRelBO);
        // TestScheme
        TestSchemeIdBO testSchemeIdBO = new TestSchemeIdBO();
//        testSchemeIdBO.setTestSchemeId(po.getTestSchemeId());
        testMatrixParentBO.setTestScheme(testSchemeIdBO);
        //TestSample
        TestSampleIdBO testSampleIdBO = new TestSampleIdBO();
        testSampleIdBO.setTestSampleInstanceId(po.getTestSampleID());
        testMatrixParentBO.setTestSample(testSampleIdBO);

        testMatrixRelationshipBO.setParent(testMatrixParentBO);
        return testMatrixRelationshipBO;
    }

    @Override
    public List<TestMatrixPO> convertToPO(Collection<TestMatrixBO> boList) {
        return null;
    }

    @Override
    public LambdaQueryWrapper<TestMatrixPO> createWrapper(TestMatrixQueryReq queryReq) {
        return null;
    }
}
