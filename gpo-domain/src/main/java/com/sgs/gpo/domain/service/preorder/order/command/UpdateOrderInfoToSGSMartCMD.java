package com.sgs.gpo.domain.service.preorder.order.command;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.util.DateUtils;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.customer.CustomerPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderExtPO;
import com.sgs.gpo.domain.service.preorder.customer.subdomain.ICustomerService;
import com.sgs.gpo.domain.service.preorder.order.context.OrderContext;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IGeneralOrderService;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IOrderExtService;
import com.sgs.gpo.facade.model.preorder.order.dto.OrderExtFieldsDTO;
import com.sgs.gpo.facade.model.preorder.order.dto.OrderExtTrfTemplateDTO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderToSgsMartReq;
import com.sgs.priceengine.facade.model.enums.CustomerUsage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
public class UpdateOrderInfoToSGSMartCMD extends BaseCommand<OrderContext<OrderToSgsMartReq>> {

    @Autowired
    IGeneralOrderService generalOrderService;
    @Autowired
    IOrderExtService orderExtService;
    @Autowired
    ICustomerService customerService;

    @Override
    public BaseResponse validateParam(OrderContext<OrderToSgsMartReq> context) {
        OrderToSgsMartReq orderToSgsMartReq = context.getParam();
        Assert.isTrue(Func.isNotEmpty(orderToSgsMartReq.getOrderNo()), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(OrderContext<OrderToSgsMartReq> context) {
        //查询OrderId
        String orderNo = context.getParam().getOrderNo();
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setOrderNoList(Sets.newHashSet(orderNo));
        List<GeneralOrderPO> orderPOList = generalOrderService.query2(orderQueryReq).getData();
        if(Func.isNotEmpty(orderPOList)){
            GeneralOrderPO orderPO = orderPOList.stream().filter(e -> Func.equalsSafe(e.getOrderNo(),orderNo)).findAny().orElse(null);
            if(Func.isNotEmpty(orderPO)){
                context.setOrderPO(orderPO);
            }
        }
        return super.before(context);
    }

    @Override
    public BaseResponse execute(OrderContext<OrderToSgsMartReq> context) {
        GeneralOrderPO orderPO = context.getOrderPO();
        OrderToSgsMartReq orderToSgsMartReq = context.getParam();
        UserInfo userInfo = context.getUserInfo();
        //更新ToDMFlag kaCustomerDeptCode
        GeneralOrderPO order = new GeneralOrderPO();
        order.setToDMFlag(1);
        order.setKaCustomerDeptCode(orderToSgsMartReq.getKACustomerDeptCode());
        LambdaUpdateWrapper<GeneralOrderPO> orderPOLambdaWrapper = new LambdaUpdateWrapper<>();
        orderPOLambdaWrapper.eq(GeneralOrderPO::getId, orderPO.getId());
        generalOrderService.update(order, orderPOLambdaWrapper);
        //更新template
        if(Func.isNotEmpty(orderToSgsMartReq.getTemplateId()) && Func.isNotEmpty(orderToSgsMartReq.getTemplateName())){
            OrderExtFieldsDTO orderExtFieldsDTO = new OrderExtFieldsDTO();
            OrderExtTrfTemplateDTO trfTemplateDTO = new OrderExtTrfTemplateDTO();
            trfTemplateDTO.setTemplateId(orderToSgsMartReq.getTemplateId());
            trfTemplateDTO.setTemplateName(orderToSgsMartReq.getTemplateName());
            orderExtFieldsDTO.setTrfTemplate(trfTemplateDTO);
            //先查询
            LambdaQueryWrapper<OrderExtPO> orderExtPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orderExtPOLambdaQueryWrapper.eq(OrderExtPO::getOrderId, orderPO.getId());
            List<OrderExtPO> orderExtPOList = orderExtService.list(orderExtPOLambdaQueryWrapper);
            if(Func.isNotEmpty(orderExtPOList)){
                OrderExtPO orderExtPO = new OrderExtPO();
                orderExtPO.setExtFields(JSONObject.toJSONString(orderExtFieldsDTO));
                LambdaUpdateWrapper<OrderExtPO> orderExtPOLambdaWrapper = new LambdaUpdateWrapper<>();
                orderExtPOLambdaWrapper.eq(OrderExtPO::getOrderId, orderPO.getId());
                orderExtService.update(orderExtPO,orderExtPOLambdaWrapper);
            } else {
                OrderExtPO orderExtPO = new OrderExtPO();
                orderExtPO.setId(IdUtil.uuId());
                orderExtPO.setOrderId(orderPO.getId());
                orderExtPO.setActiveIndicator(1);
                orderExtPO.setCreatedBy(userInfo.getRegionAccount());
                orderExtPO.setCreatedDate(DateUtils.getNow());
                orderExtPO.setModifiedBy(userInfo.getRegionAccount());
                orderExtPO.setModifiedDate(DateUtils.getNow());
                orderExtPO.setExtFields(JSONObject.toJSONString(orderExtFieldsDTO));
                orderExtService.save(orderExtPO);
            }
        }
        //更新 SgsMartAccount
        if(Func.isNotEmpty(orderToSgsMartReq.getSgsMartAccount()) && Func.isNotEmpty(orderToSgsMartReq.getSgsMartUserId())){
            LambdaUpdateWrapper<CustomerPO> customerPOLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            customerPOLambdaUpdateWrapper.eq(CustomerPO::getGeneralOrderId,orderPO.getId()).eq(CustomerPO::getCustomerUsage, CustomerUsage.Applicant.getCode());
            CustomerPO customerPO = new CustomerPO();
            customerPO.setSgsMartUserId(orderToSgsMartReq.getSgsMartUserId());
            customerPO.setSgsMartAccount(orderToSgsMartReq.getSgsMartAccount());
            customerService.update(customerPO, customerPOLambdaUpdateWrapper);
        }
        return BaseResponse.newSuccessInstance(true);
    }
}
