package com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.subcontract.SubcontractTestLineMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractTestLinePO;
import com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.ISubcontractTestLineService;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractTestLineQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class SubcontractTestLineServiceImpl extends ServiceImpl<SubcontractTestLineMapper, SubcontractTestLinePO> implements ISubcontractTestLineService {
    @Override
    public BaseResponse<List<SubcontractTestLinePO>> query(SubcontractTestLineQueryReq req) {
        if(Func.isEmpty(req)||Func.isAllEmpty(req.getSubcontractIds(),req.getTestLineInstanceIdList())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        LambdaQueryWrapper<SubcontractTestLinePO> wrapper= Wrappers.<SubcontractTestLinePO>lambdaQuery();
        if(Func.isNotEmpty(req.getSubcontractIds())){
            wrapper.in(SubcontractTestLinePO::getSubContractId,req.getSubcontractIds());
        }
        if(Func.isNotEmpty(req.getTestLineInstanceIdList())){
            wrapper.in(SubcontractTestLinePO::getTestLineInstanceId,req.getTestLineInstanceIdList());
        }
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }
}
