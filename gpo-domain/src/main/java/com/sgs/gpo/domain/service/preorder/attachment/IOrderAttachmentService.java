package com.sgs.gpo.domain.service.preorder.attachment;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.attachment.OrderAttachmentPO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderAttachmentQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.UpdateAttachmentSeqReq;

import java.util.List;
import java.util.Set;


public interface IOrderAttachmentService extends IService<OrderAttachmentPO> {

    BaseResponse<List<OrderAttachmentPO>> queryTestLineAttachment(Set<String> testLineInstanceIdList, Set<String> testSampleIdList);

    BaseResponse<List<OrderAttachmentPO>> select(OrderAttachmentQueryReq orderAttachmentQueryReq);

    BaseResponse updateOrderAttachmentSeq(UpdateAttachmentSeqReq updateAttachmentSeqReq);

    List<OrderAttachmentPO> selectByInvoiceNo(Set<String> invoiceNoList);

}
