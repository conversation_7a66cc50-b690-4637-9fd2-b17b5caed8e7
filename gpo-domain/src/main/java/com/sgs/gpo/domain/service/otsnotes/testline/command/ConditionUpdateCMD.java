package com.sgs.gpo.domain.service.otsnotes.testline.command;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.domain.service.otsnotes.testline.context.ConditionUpdateContext;
import com.sgs.gpo.facade.model.otsnotes.testline.req.ConditionUpdateReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @title: ConditionUpdateCMD
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/28 15:32
 */
@Service
@Slf4j
public class ConditionUpdateCMD  extends BaseCommand<ConditionUpdateContext<ConditionUpdateReq>> {
    @Override
    public BaseResponse validateParam(ConditionUpdateContext<ConditionUpdateReq> context) {
        if (Func.isEmpty(context) || Func.isEmpty(context.getParam())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        ConditionUpdateReq conditionUpdateReq = context.getParam();
        if (Func.isEmpty(conditionUpdateReq.getTestLineInstanceId())) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"testLineInstanceId"});
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(ConditionUpdateContext<ConditionUpdateReq> context) {
        return null;
    }
}
