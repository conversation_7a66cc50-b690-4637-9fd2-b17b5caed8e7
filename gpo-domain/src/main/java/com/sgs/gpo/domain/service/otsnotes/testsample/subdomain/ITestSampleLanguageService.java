package com.sgs.gpo.domain.service.otsnotes.testsample.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.TestSampleLanguagePO;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleLanguageQueryReq;

import java.util.List;

public interface ITestSampleLanguageService extends IService<TestSampleLanguagePO> {

    BaseResponse<List<TestSampleLanguagePO>> query(TestSampleLanguageQueryReq req);

}
