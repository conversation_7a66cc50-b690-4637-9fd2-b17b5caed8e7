package com.sgs.gpo.domain.service.otsnotes.subreport;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subreport.SubReportPO;
import com.sgs.gpo.facade.model.otsnotes.subreport.req.SubReportQueryReq;

import java.util.List;

/**
 * <AUTHOR>
 * @title: ISubReportService
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/8 17:17
 */
public interface ISubReportService extends IService<SubReportPO> {

    BaseResponse<List<SubReportPO>> query(SubReportQueryReq subReportQueryReq);

    /**
     * 多种组合条件查询SubReportList
     * @param subReportQueryReq
     * @return
     */
    List<SubReportPO> select(SubReportQueryReq subReportQueryReq);
    /**
     *
     */
    List<SubReportPO> selectSubReportsNoMatrix(SubReportQueryReq subReportQueryReq);


}
