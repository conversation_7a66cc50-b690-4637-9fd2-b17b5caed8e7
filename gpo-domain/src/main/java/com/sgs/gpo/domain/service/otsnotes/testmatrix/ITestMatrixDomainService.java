package com.sgs.gpo.domain.service.otsnotes.testmatrix;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.common.print.OutPutDataBO;
import com.sgs.framework.model.test.execution.v2.job.JobBO;
import com.sgs.framework.model.test.execution.v2.job.JobIdBO;
import com.sgs.framework.model.test.testmatrix.TestLineWithSampleBO;
import com.sgs.framework.model.test.testmatrix.TestLineWithSampleMatrixBO;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixBO;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixIdBO;
import com.sgs.framework.open.platform.base.service.IDomainService;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.TestMatrixPO;
import com.sgs.gpo.facade.model.otsnotes.testline.TestLinePageBO;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixProcessReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;

import java.util.List;

/**
 * <AUTHOR>
 * @title: ITestMatrixDomainService
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/8/30 15:18
 */
public interface ITestMatrixDomainService extends IDomainService<TestMatrixBO,TestMatrixIdBO,TestMatrixQueryReq> {
    /**
     * Matrix 确认
     * @param orderIdReq
     * @return
     */
    BaseResponse confirmMatrix(OrderIdReq orderIdReq);

    /**
     * 查询TestMatirx
     * @param testMatrixQueryReq
     * @return
     */
    BaseResponse<List<TestLineWithSampleMatrixBO>> queryTestLineWithSampleMatrix(TestMatrixQueryReq testMatrixQueryReq);

    /**
     * 查询Test、Sample数据
     * @param testMatrixQueryReq
     * @return
     */
    BaseResponse<List<TestLineWithSampleBO>> queryTestLineWithSample(TestMatrixQueryReq testMatrixQueryReq);
    BaseResponse<List<TestLinePageBO>> queryTestLineWithSampleForTestData(TestMatrixQueryReq testMatrixQueryReq);

    BaseResponse updateMatrixStatus(TestMatrixProcessReq testMatrixProcessReq);

    BaseResponse<Boolean> buildMatrixCombinedConditionDesc(OutPutDataBO outPutDataBO);
}
