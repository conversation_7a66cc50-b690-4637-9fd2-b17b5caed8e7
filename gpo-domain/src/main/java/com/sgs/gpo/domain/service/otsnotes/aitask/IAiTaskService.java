package com.sgs.gpo.domain.service.otsnotes.aitask;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.aitask.AiTaskPO;
import com.sgs.gpo.facade.model.aitask.AiTaskQueryReq;

import java.util.List;

public interface IAiTaskService extends IService<AiTaskPO> {
    BaseResponse<List<AiTaskPO>> select(AiTaskQueryReq taskQueryReq);
}
