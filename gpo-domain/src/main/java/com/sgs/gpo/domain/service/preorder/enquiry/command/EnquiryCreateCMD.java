package com.sgs.gpo.domain.service.preorder.enquiry.command;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.constant.KafkaTopicConsts;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.i18n.util.MessageUtil;
import com.sgs.framework.model.common.customer.CustomerBO;
import com.sgs.framework.model.common.customer.CustomerContactBO;
import com.sgs.framework.model.common.customer.CustomerLanguageBO;
import com.sgs.framework.model.common.servicerequirement.ReportLanguageBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementReportBO;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.model.enums.CustomerType;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.OppBusinessTypeEnums;
import com.sgs.framework.model.enums.OrderPersonType;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.model.enums.TrfStatus;
import com.sgs.framework.model.order.enquiry.EnquiryBO;
import com.sgs.framework.model.order.enquiry.EnquiryFlagBO;
import com.sgs.framework.model.order.enquiry.EnquiryHeaderBO;
import com.sgs.framework.model.order.enquiry.EnquiryIdBO;
import com.sgs.framework.model.order.enquiry.EnquiryOthersBO;
import com.sgs.framework.model.order.enquiry.EnquiryRelationshipBO;
import com.sgs.framework.model.order.trf.TrfBO;
import com.sgs.framework.model.order.v2.OrderIdBO;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.AttachmentSourceEnums;
import com.sgs.gpo.core.enums.EnquiryStatus;
import com.sgs.gpo.core.enums.KafkaActionType;
import com.sgs.gpo.core.enums.TagObjectType;
import com.sgs.gpo.core.util.DateUtils;
import com.sgs.gpo.core.util.StandardCharPool;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.attachment.OrderAttachmentPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryCustomerPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryParcelPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryPersonPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryProductPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryReportReceiverPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryTestRequestContactsPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryTestRequestPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryTrfRelationshipPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.tag.ObjectTagPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.trf.TrfToDoPO;
import com.sgs.gpo.domain.service.extservice.sci.SciTrfService;
import com.sgs.gpo.domain.service.preorder.attachment.IOrderAttachmentService;
import com.sgs.gpo.domain.service.preorder.enquiry.context.EnquiryContext;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryCustomerService;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryParcelService;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryPersonService;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryProductService;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryReportReceiverService;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryService;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryTestRequestContactsService;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryTestRequestService;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryTrfRelationshipService;
import com.sgs.gpo.domain.service.preorder.tag.IObjectTagsService;
import com.sgs.gpo.domain.service.preorder.trf.ITrfToDoService;
import com.sgs.gpo.domain.service.user.SalesHostingService;
import com.sgs.gpo.facade.model.kafka.KafkaMessage;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.UserLabDTO;
import com.sgs.gpo.facade.model.preorder.trf.TrfToDoListReq;
import com.sgs.gpo.facade.model.salesHost.SalesHostingInfo;
import com.sgs.gpo.integration.framework.FrameworkClient;
import com.sgs.gpo.integration.framework.req.FileCopyReq;
import com.sgs.gpo.integration.framework.req.GenerateNumberReq;
import com.sgs.gpo.integration.framework.rsp.FileInfoRsp;
import com.sgs.gpo.integration.quotation.QuotationClient;
import com.sgs.preorder.core.common.EventType;
import com.sgs.preorder.core.common.PreEvent;
import com.sgs.preorder.core.common.StandardObjectType;
import com.sgs.priceengine.facade.QuotationFacade;
import com.sgs.priceengine.facade.QuotationInfoFacade;
import com.sgs.priceengine.facade.model.DTO.GenerateOrderResp;
import com.sgs.priceengine.facade.model.request.CopyOrderBatchRequest;
import com.sgs.priceengine.facade.model.request.CopyOrderPair;
import com.sgs.priceengine.facade.model.request.OrderCustomerInfo;
import com.sgs.priceengine.facade.model.request.OrderToEnquiry;
import com.sgs.priceengine.facade.model.request.TriggerNewVersionReq;
import com.sgs.priceengine.facade.model.response.quotationinfo.QuotationHeadInfoRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class EnquiryCreateCMD extends BaseCommand<EnquiryContext<EnquiryBO>> {

    @Resource
    private ITrfToDoService trfToDoService;
    @Resource
    private IEnquiryService enquiryService;
    @Resource
    private IEnquiryParcelService enquiryParcelService;
    @Resource
    private IEnquiryPersonService enquiryPersonService;
    @Resource
    private IEnquiryCustomerService enquiryCustomerService;
    @Resource
    private IEnquiryReportReceiverService enquiryReportReceiverService;
    @Resource
    private IEnquiryTestRequestService enquiryTestRequestService;
    @Resource
    private IObjectTagsService objectTagsService;
    @Resource
    private IEnquiryTrfRelationshipService enquiryTrfRelationshipService;
    @Resource
    private IEnquiryProductService enquiryProductService;
    @Resource
    private SciTrfService sciTrfService;
    @Resource
    private FrameworkClient frameworkClient;
    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;
    @Resource
    private QuotationClient quotationClient;
    @Autowired
    private QuotationFacade quotationFacade;
    @Autowired
    private QuotationInfoFacade quotationInfoFacade;
    @Autowired
    private MessageUtil messageUtil;
    @Autowired
    private SalesHostingService salesHostingService;
    @Autowired
    private IEnquiryTestRequestContactsService enquiryTestRequestContactsService;

@Resource
private IOrderAttachmentService orderAttachmentService;

    @Override
    public BaseResponse validateParam(EnquiryContext<EnquiryBO> context) {
        EnquiryBO enquiryCreateReq = context.getParam();
        Assert.isTrue(Func.isNotEmpty(enquiryCreateReq), "common.param.miss", new String[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(context.getLab()), "common.param.miss", new String[]{Constants.TERM.LAB.getCode()});
        EnquiryOthersBO others = enquiryCreateReq.getOthers();
        // TRF 状态验证
        if (Func.isNotEmpty(others) && Func.isNotEmpty(others.getTrfNo())) {
            // 查询TRF详细信息
            TrfToDoListReq trfToDoListReq = new TrfToDoListReq();
            trfToDoListReq.setTrfNoList(Sets.newHashSet(others.getTrfNo()));
            trfToDoListReq.setStatus(TrfStatus.TrfNoOrder.getStatus());
            List<TrfToDoPO> trfList = trfToDoService.select(trfToDoListReq);
            if (Func.isEmpty(trfList)) {
                return BaseResponse.newFailInstance("TRF:" + others.getTrfNo() + "无效，请刷新页面后再操作");
            }
        }
        // 区别新增编辑场景
//        context.setIsNew(Func.isEmpty(enquiryCreateReq.getId().getEnquiryId()));
        return BaseResponse.newSuccessInstance(true);
    }


    @Override
    public BaseResponse before(EnquiryContext<EnquiryBO> context) {
        // 组装需要保存的数据
        this.buildDomain(context);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse buildDomain(EnquiryContext<EnquiryBO> context) {
        Lab lab = context.getLab();
        // enquiry
        buildEnquiry(context);
        // Customer
        buildCustomer(context);
        // reportinfo
        buildReportInfo(context);
        // ServiceRequirement
        buildServiceRequirement(context);
        // Dff 需要再分析
        buildDff(context);
        // tag
        buildTag(context);
        // attachment
        buildAttachment(context);

        buildQuotation(context);
        //Trf AFL的商机是存到trf中
        buildTrf(context);
        return BaseResponse.newSuccessInstance(true);
    }

    private void buildReportInfo(EnquiryContext<EnquiryBO> context) {
        EnquiryBO enquiryBO = context.getParam();
        EnquiryReportReceiverPO enquiryReportReceiverPO = context.getEnquiryReportReceiver();
        UserInfo userInfo = context.getUserInfo();
        String enquiryId = context.getNewEnquiry().getId();
        if (Func.isNotEmpty(enquiryReportReceiverPO)) {
            enquiryReportReceiverPO.setEnquiryId(enquiryId);
            enquiryReportReceiverPO.setCreatedBy(userInfo.getRegionAccount());
            enquiryReportReceiverPO.setCreatedDate(DateUtil.now());
        }
    }


    private String generateEnquiryNumber(Lab lab, EnquiryContext<EnquiryBO> context) {
        GenerateNumberReq generateNumberReq = new GenerateNumberReq();
        generateNumberReq.setNumberRuleCode("GPOEnquiryNo");
        generateNumberReq.setBuId(lab.getBuId());
        generateNumberReq.setLocationId(lab.getLocationId());
        generateNumberReq.setPostfix("E");
        String prefix = String.format("%s%s", lab.getLocationCode(), lab.getProductLineCode());
        generateNumberReq.setPrefix(prefix);
        Integer locationId = lab.getLocationId();
        String labCode = lab.getLabCode();
        UserLabDTO saleUserLabDto = context.getSaleUserLabDTO();
        //AFL 逻辑调整，如果选择了bySalesLocationFlag 为1  则取sale对应的location信息建单
        if(Func.isNotEmpty(saleUserLabDto) && Func.isNotEmpty(saleUserLabDto.getLocationId())){
            locationId = saleUserLabDto.getLocationId();
            labCode = saleUserLabDto.getLabCode();
        }
        return frameworkClient.generateEnquiryNo(context.getToken(), lab.getBuId(), locationId, labCode, "E");
    }

    private void buildEnquiry(EnquiryContext<EnquiryBO> context) {
        EnquiryBO enquiryBO = context.getParam();
        EnquiryHeaderBO enquiryHeader = enquiryBO.getHeader();
        UserInfo userInfo = context.getUserInfo();
        Lab lab = context.getLab();

        String enquiryId = context.getIsNew() ? IdUtil.uuId() : enquiryBO.getId().getEnquiryId();

        String enquiryNo = enquiryBO.getId().getEnquiryNo();
        // Enquiry主信息
        EnquiryPO newEnquiry = new EnquiryPO();
        Func.copy(enquiryHeader, newEnquiry);
        newEnquiry.setId(enquiryId);
        // 设置是否是常规单
        newEnquiry.setServiceType(context.getRegular());
        // 设置FCM
        if (context.getCopyFcm().intValue() == 1) {
            newEnquiry.setPendingFlag(1);
        }
        // 设置sales person
        newEnquiry.setSalesPerson(context.getSalesPerson());
        if (context.getIsNew()) {
            String newEnquiryNo = generateEnquiryNumber(lab, context);
            Assert.isTrue(Func.isNotEmpty(newEnquiryNo), "enquiry.number.get.fail", null);
            newEnquiry.setSplitOrderFlag(calculateSplitOrderFlag(enquiryBO));
            newEnquiry.setEnquiryNo(newEnquiryNo);

            newEnquiry.setGroupId( enquiryBO.getId().getEnquiryId());

            Date currentDate = DateUtil.now();
            String currentUser = userInfo.getRegionAccount();
            newEnquiry.setReferenceEnquiryNo(enquiryNo);
            newEnquiry.setCreatedDate(currentDate);
            newEnquiry.setCreatedBy(currentUser);
            // TODO EnquiryHeaderBO里面没有该字段
//            newEnquiry.setExtField(enquiryBO.getExtField());
            // TODO isCopyFlag,toDmFlag,sameAsApplicantFlag,needToBossFlag
            EnquiryFlagBO enquiryFlag = enquiryBO.getFlags();
            if (Func.isNotEmpty(enquiryFlag)) {
                newEnquiry.setNeedToBossFlag(enquiryFlag.getNeedToBossFlag());
                newEnquiry.setToDmFlag(enquiryFlag.getToDmFlag());
                newEnquiry.setSameAsApplicantFlag(enquiryFlag.getSameAsApplicantFlag());
                newEnquiry.setIsCopyFlag(0);
            }
            // TODO 确认新增和copy时是否都要设置此字段
//            newEnquiry.setModifiedDate(currentDate);
//            newEnquiry.setModifiedBy(currentUser);
            // 根据enquiryNo 生成 流水号
            newEnquiry.setSuffixNum(enquiryNo.substring(9, 14));
            newEnquiry.setEnquiryStatus(EnquiryStatus.New.getType());
            newEnquiry.setActiveIndicator(ActiveType.Enable.getStatus());
            newEnquiry.setOrderId(IdUtil.uuId());
            // lab信息
            newEnquiry.setBuId(lab.getBuId());
            newEnquiry.setBuCode(lab.getBuCode());
            newEnquiry.setLocationId(lab.getLocationId());
            newEnquiry.setLocationCode(lab.getLocationCode());
            newEnquiry.setLabCode(lab.getLabCode());
        } else {
            newEnquiry.setModifiedDate(DateUtils.now());
            newEnquiry.setModifiedBy(userInfo.getRegionAccount());
            // Enquiry 上的OrderId是否还需要
//            newEnquiry.setOrderId(headers.getPreOrderId());
        }
        Integer templateFlag = Func.isEmpty(enquiryHeader.getTemplateFlag()) ? 0 : enquiryHeader.getTemplateFlag();
        newEnquiry.setTemplateFlag(templateFlag);
        context.setNewEnquiry(newEnquiry);
        // Parcel 信息单独存
        if (Func.isNotEmpty(enquiryHeader.getParcelNo())) {
            List<EnquiryParcelPO> enquiryParcelList =
                    enquiryHeader.getParcelNo().parallelStream().map(item -> {
                        EnquiryParcelPO enquiryParcelPO = new EnquiryParcelPO();
                        enquiryParcelPO.setId(IdUtil.uuId());
                        enquiryParcelPO.setEnquiryId(enquiryId);
                        enquiryParcelPO.setParcelNo(item);
                        enquiryParcelPO.setCreatedBy(userInfo.getRegionAccount());
                        enquiryParcelPO.setActiveIndicator(ActiveType.Enable.getStatus());
                        enquiryParcelPO.setCreatedDate(DateUtils.now());
                        return enquiryParcelPO;
                    }).collect(Collectors.toList());
            context.setNewEnquiryParcels(enquiryParcelList);
        }
        // Enquiry Person
        if (Func.isNotEmpty(enquiryBO.getContactPersonList())) {
            List<EnquiryPersonPO> enquiryPersonList = enquiryBO.getContactPersonList().parallelStream().map(item -> {
                EnquiryPersonPO enquiryPerson = new EnquiryPersonPO();
                enquiryPerson.setId(IdUtil.uuId());
                enquiryPerson.setEnquiryId(enquiryId);
                enquiryPerson.setRegionAccount(userInfo.getRegionAccount());
                enquiryPerson.setPersonType(OrderPersonType.CSA.getCode());
                enquiryPerson.setCreatedBy(userInfo.getRegionAccount());
                enquiryPerson.setActiveIndicator(ActiveType.Enable.getStatus());
                enquiryPerson.setCreatedDate(DateUtils.now());
                return enquiryPerson;
            }).collect(Collectors.toList());
            context.setNewEnquiryPersons(enquiryPersonList);
        }
        // 设置bySalesLocation
        setBySalesLocation(context, newEnquiry);
    }
    private void setBySalesLocation(EnquiryContext<EnquiryBO> context, EnquiryPO newEnquiry) {
        UserLabDTO userLabDTO = context.getSaleUserLabDTO();
        if (Func.isNotEmpty(userLabDTO)) {
            newEnquiry.setLabCode(userLabDTO.getLabCode());
            newEnquiry.setLocationId(userLabDTO.getLocationId());
            newEnquiry.setLocationCode(userLabDTO.getLocationCode());
            newEnquiry.setBySalesLocationFlag(1);
        }
    }

    private void buildCustomer(EnquiryContext<EnquiryBO> context) {
        EnquiryBO enquiryBO = context.getParam();
        String enquiryId = context.getNewEnquiry().getId();
        // customer
        List<CustomerBO> customerList = enquiryBO.getCustomerList();
        String salePerson = enquiryBO.getHeader().getSalesPerson();
        // 查询现有的customer数据
        List<String> customerDelIds = Lists.newArrayList();
        if (context.getIsNew()) {
            EnquiryIdReq enquiryIdReq = new EnquiryIdReq();
            enquiryIdReq.setEnquiryIdList(Sets.newHashSet(enquiryId));
            BaseResponse<List<EnquiryCustomerPO>> dbEnquiryListRes = enquiryCustomerService.select(enquiryIdReq);
            if (Func.isNotEmpty(dbEnquiryListRes) && Func.isNotEmpty(dbEnquiryListRes.getData())) {
                List<EnquiryCustomerPO> dbEnquiryList = dbEnquiryListRes.getData();
                customerDelIds = dbEnquiryList.stream().map(EnquiryCustomerPO::getId).collect(Collectors.toList());
            }
        }
        if (Func.isNotEmpty(customerList)) {
            List<String> finalCustomerDelIds = customerDelIds;
            List<EnquiryCustomerPO> enquiryCustomerList = new ArrayList<>();
            customerList.forEach(customerBO -> {
                EnquiryCustomerPO enquiryCustomerPO = Func.deepCopy(customerBO, EnquiryCustomerPO.class);
                enquiryCustomerPO.setId(context.getIsNew() ? IdUtil.uuId() : customerBO.getId());
                if (context.getIsNew() && finalCustomerDelIds.contains(enquiryCustomerPO.getId())) {
                    finalCustomerDelIds.remove(enquiryCustomerPO.getId());
                }
                enquiryCustomerPO.setEnquiryId(enquiryId);
                if (Func.isNotEmpty(customerBO.getLanguageList())) {
                    CustomerLanguageBO customerLanguageCn = customerBO.getLanguageList().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese)).findAny().orElse(null);
                    if (Func.isNotEmpty(customerLanguageCn)) {
                        enquiryCustomerPO.setCustomerAddressCn(customerLanguageCn.getCustomerAddress());
                        enquiryCustomerPO.setCustomerNameCn(customerLanguageCn.getCustomerName());
                    }
                    CustomerLanguageBO customerLanguageEn = customerBO.getLanguageList().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.English)).findAny().orElse(null);
                    if (Func.isNotEmpty(customerLanguageEn)) {
                        enquiryCustomerPO.setCustomerAddressEn(customerLanguageEn.getCustomerAddress());
                        enquiryCustomerPO.setCustomerNameEn(customerLanguageEn.getCustomerName());
                    }
                }
                // 联系人信息 TODO customerContactList是null
                if (Func.isNotEmpty(customerBO.getCustomerContactList())) {
                    // TODO 默认取第一个的是否合适
                    CustomerContactBO customerContactBO = customerBO.getCustomerContactList().get(0);
                    enquiryCustomerPO.setContactPersonEmail(customerContactBO.getContactEmail());
                    enquiryCustomerPO.setContactPersonFax(customerContactBO.getContactFAX());
                    enquiryCustomerPO.setContactPersonPhone1(customerContactBO.getContactTelephone());
                    enquiryCustomerPO.setContactPersonName(customerContactBO.getContactName());
                    enquiryCustomerPO.setContactPersonRemark(customerContactBO.getContactPersonRemark());
                    enquiryCustomerPO.setContactPersonPhone2(customerContactBO.getContactMobile());
                    enquiryCustomerPO.setContactAddressId(customerContactBO.getCustomerContactAddressId());
                    enquiryCustomerPO.setBossSiteUserId(customerContactBO.getBossSiteUseId());
                    enquiryCustomerPO.setBossContactId(customerContactBO.getBossContactId());
                    enquiryCustomerPO.setSgsMartAccount(customerContactBO.getContactSgsMartAccount());
                    enquiryCustomerPO.setSgsMartUserId(customerContactBO.getContactSgsMartUserId());
                }

                enquiryCustomerPO.setCustomerGroupId(customerBO.getCustomerGroupId());
                enquiryCustomerPO.setCustomerCredit(customerBO.getCustomerCredit());
                enquiryCustomerPO.setLogoCloudId(customerBO.getLogoCloudId());
                enquiryCustomerPO.setOrganizationName(customerBO.getOrganizationName());


                enquiryCustomerPO.setPrimaryFlag(customerBO.getPrimaryFlag());
                enquiryCustomerPO.setMonthlyPayment(customerBO.getMonthlyPayment());
                enquiryCustomerPO.setBossLocationCode(customerBO.getBossLocationCode());
                enquiryCustomerPO.setPaymentTermName(customerBO.getPaymentTerm());

                // customerUsage
                if (Func.isNotEmpty(customerBO.getCustomerUsage())) {
                    enquiryCustomerPO.setCustomerUsage(CustomerType.enumOf(customerBO.getCustomerUsage()));
                }
                // accountID
                enquiryCustomerPO.setAccountID(customerBO.getAccountId());
                enquiryCustomerPO.setBossNumber(customerBO.getBossNo());

                // createBy
                UserInfo userInfo = context.getUserInfo();
                if (context.getIsNew()) {
                    enquiryCustomerPO.setCreatedBy(userInfo.getRegionAccount());
                    enquiryCustomerPO.setCreatedDate(DateUtils.now());
                } else {
                    enquiryCustomerPO.setModifiedBy(userInfo.getRegionAccount());
                    enquiryCustomerPO.setModifiedDate(DateUtils.now());
                }

                // others TODO要看获取的数据里面有没有值 paymentTermName other为空
                if (Func.isNotEmpty(customerBO.getOthers())) {
                    enquiryCustomerPO.setBuyerGroup(customerBO.getOthers().getBuyerGroup());
                    enquiryCustomerPO.setBuyerGroupName(customerBO.getOthers().getBuyerGroupName());
                    enquiryCustomerPO.setSupplierNo(customerBO.getOthers().getSupplierNo());
                    enquiryCustomerPO.setIsAsApplicant(customerBO.getOthers().getIsAsApplicant());
                    enquiryCustomerPO.setReportDeliveredTo(customerBO.getOthers().getReportDeliveredTo());
                    enquiryCustomerPO.setFailedReportDeliveredTo(customerBO.getOthers().getFailedReportDeliveredTo());
                }
                // 根据选中的sales判断是否清空customer的信息
                clearCustomerInfo(salePerson, enquiryCustomerPO, context);
                enquiryCustomerList.add(enquiryCustomerPO);
            });
            context.setNewCustomerList(enquiryCustomerList);
        }
        context.setCustomerDelIds(customerDelIds);
    }

    private void clearCustomerInfo(String salePerson, EnquiryCustomerPO enquiryCustomerPO, EnquiryContext<EnquiryBO> context) {
        String salesPerson = context.getSalesPerson();
        String newSalePerson = context.getSalesPerson();
        String customerId = enquiryCustomerPO.getCustomerId();
        Long bossNumber = enquiryCustomerPO.getBossNumber();
        String customerUsage = enquiryCustomerPO.getCustomerUsage();
        if (Func.isNotEmpty(salePerson) && Func.isNotEmpty(newSalePerson) && !Func.equalsSafe(salePerson, newSalePerson) && Func.isNotEmpty(customerUsage) && Func.equalsSafe(customerUsage, CustomerType.Applicant.getCode())) {
            // 判断是否有原单子的客户权限
            SalesHostingInfo condition =new SalesHostingInfo();
            condition.setRegionAccount("apac\\" + newSalePerson);
            List<SalesHostingInfo> saleHostingInfo = salesHostingService.searchSalesHostings(condition);
            if (Func.isNotEmpty(saleHostingInfo) && Func.isNotEmpty(customerId)) {
                Optional<SalesHostingInfo> result = saleHostingInfo.stream()
                        .filter(salesHostingInfo -> String.valueOf(customerId).equals(salesHostingInfo.getHostingId()))
                        .findFirst();
                if (!result.isPresent()) {
                   enquiryCustomerPO.setCustomerId(null);
                   enquiryCustomerPO.setCustomerGroupId(null);
                   enquiryCustomerPO.setCustomerCredit(null);
                   enquiryCustomerPO.setCustomerAddressCn(null);
                   enquiryCustomerPO.setCustomerAddressEn(null);
                   enquiryCustomerPO.setCustomerNameCn(null);
                   enquiryCustomerPO.setCustomerNameEn(null);
                   enquiryCustomerPO.setBossNumber(null);
                   enquiryCustomerPO.setContactPersonEmail(null);
                   enquiryCustomerPO.setContactPersonFax(null);
                   enquiryCustomerPO.setContactPersonName(null);
                   enquiryCustomerPO.setContactPersonPhone1(null);
                   enquiryCustomerPO.setContactPersonPhone2(null);
                   enquiryCustomerPO.setContactPersonRemark(null);
                }
            }
        }
    }

    private void buildServiceRequirement(EnquiryContext<EnquiryBO> context) {
        EnquiryBO enquiryBO = context.getParam();
        UserInfo userInfo = context.getUserInfo();
        String enquiryId = context.getNewEnquiry().getId();
        if (Func.isNotEmpty(enquiryBO.getServiceRequirement())) {
            ServiceRequirementBO serviceRequirement = enquiryBO.getServiceRequirement();
            // testRequest
            EnquiryTestRequestPO enquiryTestRequest = Func.deepCopy(serviceRequirement, EnquiryTestRequestPO.class);
            enquiryTestRequest.setId(context.getIsNew() ? IdUtil.uuId() : serviceRequirement.getId());
            enquiryTestRequest.setEnquiryId(enquiryId);
            enquiryTestRequest.setOtherRequirements(serviceRequirement.getOtherRequestRemark());
            enquiryTestRequest.setReturnTestedSampleFlag(serviceRequirement.getReturnResidueSampleFlag());
            enquiryTestRequest.setReturnTestedSampleRemark(serviceRequirement.getReturnTestSampleRemark());
            enquiryTestRequest.setIsProformaInvoice(serviceRequirement.getProformaInvoice());
            enquiryTestRequest.setLiquidTestSample(serviceRequirement.getLiquidTestSample());
            enquiryTestRequest.setVatType(serviceRequirement.getVatType());
            enquiryTestRequest.setPackageIndicator(serviceRequirement.getPackageIndicator());
            enquiryTestRequest.setReturnResidueSampleFlag(serviceRequirement.getReturnResidueSampleFlag());
            enquiryTestRequest.setReturnResidueSampleRemark(serviceRequirement.getReturnResidueSampleRemark());
            enquiryTestRequest.setEvaluationBasis(serviceRequirement.getEvaluationBasis());
            enquiryTestRequest.setServiceType(serviceRequirement.getServiceType());
            enquiryTestRequest.setDisplaySupplierFlag(serviceRequirement.getDisplaySupplierFlag());
            enquiryTestRequest.setCommentFlag(serviceRequirement.getCommentFlag());
            enquiryTestRequest.setConfirmCoverPageFlag(serviceRequirement.getConfirmCoverPageFlag());
            enquiryTestRequest.setHtmlString(serviceRequirement.getHtmlString());
            enquiryTestRequest.setPaymentRemark(serviceRequirement.getPaymentRemark());
            enquiryTestRequest.setPhotoRequest(serviceRequirement.getPhotoRequest());
            enquiryTestRequest.setReportRequirement(serviceRequirement.getReportRequirement());
            enquiryTestRequest.setInvoiceDeliverWay(serviceRequirement.getInvoiceDeliverWay());
            enquiryTestRequest.setQrcodeFlag(serviceRequirement.getQrcodeFlag());
            enquiryTestRequest.setSampleSaveDuration(serviceRequirement.getSampleSaveDuration());
            enquiryTestRequest.setBusinessProjectType(serviceRequirement.getBusinessProjectType());


            // TODO
//            enquiryTestRequest.setReportDate(serviceRequirement.getReportDate());
//            enquiryTestRequest.setActiveIndicator(serviceRequirement.getActiveIndicator());

            if (Func.isNotEmpty(serviceRequirement.getReport())) {
                ServiceRequirementReportBO report = enquiryBO.getServiceRequirement().getReport();
                enquiryTestRequest.setQualification(report.getQualification());
                if (Func.isNotEmpty(report.getReportLanguage())) {
                    enquiryTestRequest.setReportLanguage(report.getReportLanguage().toString());
                }
                enquiryTestRequest.setReportManner(report.getReportManner());
                enquiryTestRequest.setReportType(report.getReportType());
                enquiryTestRequest.setResultJudgingFlag(report.getResultJudgingFlag());
                enquiryTestRequest.setHardCopyFlag(report.getHardCopyFlag());
                enquiryTestRequest.setChineseReportFlag(report.getChineseReportFlag());
                enquiryTestRequest.setTakePhotoFlag(report.getNeedPhoto());
                enquiryTestRequest.setTakePhotoRemark(report.getTakePhotoRemark());
                enquiryTestRequest.setReportAccreditationNeededFlag(report.getNeedAccreditation());
                enquiryTestRequest.setHardCopyToApplicantFlag(report.getHardCopyToApplicantFlag());
                enquiryTestRequest.setHardCopyToPayerFlag(report.getHardCopyToPayerFlag());
                enquiryTestRequest.setHardCopyToOther(report.getHardCopyToOther());
                enquiryTestRequest.setSoftCopyToOther(report.getSoftCopyToOther());
                enquiryTestRequest.setSoftCopyToApplicantFlag(report.getSoftCopyToApplicantFlag());
                enquiryTestRequest.setSoftCopyToPayerFlag(report.getSoftCopyToPayerFlag());
                enquiryTestRequest.setPdfReportSecurity(report.getPdfReportSecurity());
                enquiryTestRequest.setQualificationType(report.getQualificationType());
                enquiryTestRequest.setDraftReportRequired(report.getNeedDraft());
                enquiryTestRequest.setNeedConclusion(report.getNeedConclusion());
                enquiryTestRequest.setHardCopyReportDeliverWay(report.getHardCopyReportDeliverWay());
                enquiryTestRequest.setSealCode(report.getSealCode());
                enquiryTestRequest.setSealFlag(report.getSealFlag());

                //ReportReceiver
                EnquiryReportReceiverPO reportReceiverPO = new EnquiryReportReceiverPO();
                reportReceiverPO.setId(context.getIsNew() ? IdUtil.uuId() : report.getReceiverId());
                reportReceiverPO.setEnquiryId(enquiryId);
                reportReceiverPO.setReportLanguage(report.getReportLanguage());
                reportReceiverPO.setReportHeader(report.getReportHeader());
                reportReceiverPO.setReportDeliveredTo(report.getReportAddress());
                reportReceiverPO.setReceiverType(report.getReportType());
                reportReceiverPO.setCreatedBy(userInfo.getRegionAccount());
                reportReceiverPO.setCreatedDate(DateUtil.now());
                assembleReportReceiver(reportReceiverPO, report);
                context.setEnquiryReportReceiver(reportReceiverPO);
            }
            List<EnquiryTestRequestContactsPO> enquiryTestRequestContactsPOList = new ArrayList<>();
            Date now = DateUtil.now();
            if (Func.isNotEmpty(serviceRequirement.getDelivers())) {
                serviceRequirement.getDelivers().forEach(deliver -> {
                    EnquiryTestRequestContactsPO enquiryTestRequestContactsPO = new EnquiryTestRequestContactsPO();
                    enquiryTestRequestContactsPO.setId(IdUtil.uuId());
                    enquiryTestRequestContactsPO.setEnquiryId(enquiryId);
                    enquiryTestRequestContactsPO.setContactsType(deliver.getContactsType());
                    if (Func.isNotEmpty(deliver.getDeliveryTo())) {
                        enquiryTestRequestContactsPO.setDeliverTo(String.join(",", deliver.getDeliveryTo().stream().map(item -> item.toString()).collect(Collectors.toList())));
                    } else {
                        enquiryTestRequestContactsPO.setDeliverTo("");
                    }
                    enquiryTestRequestContactsPO.setDeliverOthers(deliver.getDeliveryOthers());
                    enquiryTestRequestContactsPO.setCreatedBy(userInfo.getRegionAccount());
                    enquiryTestRequestContactsPO.setCreatedDate(now);
                    enquiryTestRequestContactsPOList.add(enquiryTestRequestContactsPO);
                });
            }
            context.setEnquiryTestRequestContactsPOList(enquiryTestRequestContactsPOList);
            context.setEnquiryTestRequest(enquiryTestRequest);
        }
    }

    private void buildDff(EnquiryContext<EnquiryBO> context) {
        EnquiryBO enquiryBO = context.getParam();
        String enquiryId = context.getNewEnquiry().getId();
        // DFF 转换，支持两种形式的入参结构
        if (Func.isNotEmpty(enquiryBO.getDffAttrList())) {
            List<EnquiryProductPO> productList = Lists.newArrayList();
            enquiryBO.getDffAttrList().stream().forEach(dff -> {
                String cancelFlag = dff.getCancelFlag();
                dff.setCancelFlag(null);
                EnquiryProductPO enquiryProduct = Func.deepCopy(dff, EnquiryProductPO.class);
                String uuid = IdUtil.uuId();
                enquiryProduct.setId(uuid);
                enquiryProduct.setEnquiryId(enquiryId);
                enquiryProduct.setCreatedBy(SystemContextHolder.getRegionAccount());
                enquiryProduct.setCreatedDate(DateUtil.now());
                enquiryProduct.setCancelFlag("1".equals(cancelFlag));
                enquiryProduct.setRefSampleId(uuid);
                productList.add(enquiryProduct);
            });
            // 处理多语言情况下的refSampleId
            handlerRefSampleId(productList);
            context.setProductList(productList);
        }
    }

    private void handlerRefSampleId(List<EnquiryProductPO> list) {
        if (Func.isEmpty(list)) {
            return;
        }

        String productRefSampleId = null;
        Map<String, List<EnquiryProductPO>> sampleRefSampleIdMap = Collections.emptyMap();

        List<EnquiryProductPO> productList = new ArrayList<>();
        List<EnquiryProductPO> sampleList = new ArrayList<>();

        // 一次性分类，避免多次 stream 遍历
        for (EnquiryProductPO item : list) {
            if (Func.isEmpty(item.getHeaderId())) {
                productList.add(item);
            } else {
                sampleList.add(item);
            }
        }

        // 设置 productRefSampleId
        if (productList.size() > 1) {
            EnquiryProductPO tempProduct = productList.stream()
                    .filter(item -> Func.isEmpty(item.getHeaderId()) &&
                            item.getLanguageId().equals(LanguageType.English.getLanguageId()))
                    .findFirst()
                    .orElse(null);
            productRefSampleId = tempProduct != null ? tempProduct.getRefSampleId() : null;
        }

        // 设置 sampleRefSampleIdMap
        if (!sampleList.isEmpty() && sampleList.size() > 2) {
            sampleRefSampleIdMap = sampleList.stream()
                    .filter(item -> Func.isNotEmpty(item.getSampleId()) &&
                            item.getLanguageId().equals(LanguageType.English.getLanguageId()))
                    .collect(Collectors.groupingBy(EnquiryProductPO::getHeaderId));
        }

        // 更新 RefSampleId
        for (EnquiryProductPO item : list) {
            if (Func.isEmpty(item.getHeaderId()) && productRefSampleId != null &&
                    !item.getLanguageId().equals(LanguageType.English.getLanguageId())) {
                item.setRefSampleId(productRefSampleId);
            }

            if (Func.isNotEmpty(item.getHeaderId()) && !sampleRefSampleIdMap.isEmpty() &&
                    !item.getLanguageId().equals(LanguageType.English.getLanguageId())) {
                List<EnquiryProductPO> tempList = sampleRefSampleIdMap.get(item.getSampleId());
                if (tempList != null && !tempList.isEmpty()) {
                    EnquiryProductPO tempItem = tempList.get(0);
                    if (tempItem != null) {
                        item.setRefSampleId(tempItem.getRefSampleId());
                    }
                }
            }
        }
    }


    private void buildTag(EnquiryContext<EnquiryBO> context) {
        EnquiryBO enquiryBO = context.getParam();
        String enquiryId = context.getNewEnquiry().getId();
        // tag
        if (Func.isNotEmpty(enquiryBO.getOthers()) && Func.isNotEmpty(enquiryBO.getOthers().getTags())) {
            ObjectTagPO enquiryTag = new ObjectTagPO();
            enquiryTag.setId(IdUtil.uuId());
            enquiryTag.setData(enquiryBO.getOthers().getTags());
            enquiryTag.setObjectId(enquiryId);
            enquiryTag.setObject(TagObjectType.Enquiry.getValue());
            enquiryTag.setActiveIndicator(ActiveType.Enable.getStatus());
            enquiryTag.setCreatedDate(DateUtils.now());
            enquiryTag.setCreatedBy(SystemContextHolder.getRegionAccount());
            context.setEnquiryTag(enquiryTag);
        }
    }

    private void buildAttachment(EnquiryContext<EnquiryBO> context) {
        EnquiryBO enquiryBO = context.getParam();
        String enquiryId = context.getNewEnquiry().getId();
        // attachment
        if (Func.isNotEmpty(enquiryBO.getAttachmentList())) {
            List<OrderAttachmentPO> orderAttachmentList = Lists.newArrayList();
            FileCopyReq fileCopyReq = new FileCopyReq();
            enquiryBO.getAttachmentList().stream().forEach(attachment -> {
                OrderAttachmentPO orderAttachment = Func.deepCopy(attachment, OrderAttachmentPO.class);
                orderAttachment.setId(IdUtil.uuId());
                orderAttachment.setGeneralOrderID(enquiryId);
                orderAttachment.setObjectID(null);
                orderAttachment.setCreatedBy(SystemContextHolder.getRegionAccount());
                orderAttachment.setCreatedDate(DateUtils.getNow());
                orderAttachment.setModifiedDate(DateUtils.getNow());
                orderAttachment.setSource(AttachmentSourceEnums.ENQUIRY.getStatus());
//                orderAttachment.setToCp();
                if (Func.isNotEmpty(attachment.getCloudId())) {
                    fileCopyReq.setCloudId(attachment.getCloudId());
                    fileCopyReq.setObjectId(enquiryId);
                } else {
                    fileCopyReq.setFileId(attachment.getId());
                }
                FileInfoRsp fileInfoRsp = frameworkClient.copyFile(fileCopyReq);
                if (Func.isEmpty(fileInfoRsp) || Func.isEmpty(fileInfoRsp.getCloudID())) {
                    return;
                }
                orderAttachment.setAttachmentName(fileInfoRsp.getAttachmentName());
                orderAttachment.setCloudID(fileInfoRsp.getCloudID());
                orderAttachment.setFileID(fileInfoRsp.getId());
                orderAttachmentList.add(orderAttachment);
            });
            context.setOrderAttachmentList(orderAttachmentList);
        }
    }

    private void buildTrf(EnquiryContext<EnquiryBO> context) {
        EnquiryBO enquiryBO = context.getParam();
        String enquiryId = context.getNewEnquiry().getId();
        List<EnquiryTrfRelationshipPO> oldRefList = Lists.newArrayList();
        List<Long> trfDelIds = Lists.newArrayList();
        if (context.getIsNew()) {
            // 查询DB中现存的TRF数据
            EnquiryIdReq enquiryIdReq = new EnquiryIdReq();
            enquiryIdReq.setEnquiryIdList(Sets.newHashSet(enquiryId));
            BaseResponse<List<EnquiryTrfRelationshipPO>> enquiryTrfRes = enquiryTrfRelationshipService.select(enquiryIdReq);
            if (Func.isNotEmpty(enquiryTrfRes.getData())) {
                oldRefList = enquiryTrfRes.getData();
                trfDelIds = oldRefList.stream().map(EnquiryTrfRelationshipPO::getId).collect(Collectors.toList());
            }
        }
        if (Func.isNotEmpty(enquiryBO.getRelationship().getParent()) && Func.isNotEmpty(enquiryBO.getRelationship().getParent().getTrfList())) {
            List<TrfBO> trfList = enquiryBO.getRelationship().getParent().getTrfList();
            List<EnquiryTrfRelationshipPO> newEnquiryTrfList = Lists.newArrayList();
            List<EnquiryTrfRelationshipPO> finalOldRefList = oldRefList;
            List<Long> finalTrfDelIds = trfDelIds;
            trfList.stream().forEach(trfBO -> {
                // 判断DB中是否绑定对应系统
                EnquiryTrfRelationshipPO oldTrf = finalOldRefList.stream().filter(item -> Func.equalsSafe(item.getRefSystemId(), trfBO.getRefSystemId())).findAny().orElse(null);
                if(Func.isNotEmpty(oldTrf)){
                    finalTrfDelIds.remove(oldTrf.getId());
                }
                EnquiryTrfRelationshipPO enquiryTrfRelationshipPO = new EnquiryTrfRelationshipPO();
                enquiryTrfRelationshipPO.setId(Func.isNotEmpty(oldTrf)?oldTrf.getId():null);
                enquiryTrfRelationshipPO.setEnquiryId(enquiryId);
                enquiryTrfRelationshipPO.setRefNo(trfBO.getTrfNo());
                enquiryTrfRelationshipPO.setExternalOrderNo(trfBO.getExternalOrderNo());
                enquiryTrfRelationshipPO.setRefSystemId(trfBO.getRefSystemId());
                enquiryTrfRelationshipPO.setCreateType(trfBO.getCreateType());
                enquiryTrfRelationshipPO.setExtObjectId(trfBO.getExtObjectId());
                enquiryTrfRelationshipPO.setExtData(trfBO.getExtData());
                if(context.getIsNew()){
                    enquiryTrfRelationshipPO.setCreatedDate(DateUtils.now());
                    enquiryTrfRelationshipPO.setCreatedBy(SystemContextHolder.getRegionAccount());
                }else {
                    enquiryTrfRelationshipPO.setModifiedDate(DateUtils.now());
                    enquiryTrfRelationshipPO.setModifiedBy(SystemContextHolder.getRegionAccount());
                }
                // 判断是否是快速商机或者简易商机
                if (checkOpportunityType(enquiryTrfRelationshipPO)) {
                    newEnquiryTrfList.add(enquiryTrfRelationshipPO);
                }
            });
            context.setEnquiryTrfList(newEnquiryTrfList);
        }
        context.setTrfDelIds(trfDelIds);
    }

    private boolean checkOpportunityType(EnquiryTrfRelationshipPO enquiryTrfRelationshipPO) {
        if (Func.isEmpty(enquiryTrfRelationshipPO)) {
            return true;
        }
        Integer systemId = enquiryTrfRelationshipPO.getRefSystemId();
        String extData = enquiryTrfRelationshipPO.getExtData();
        String oppType = null;
        if(Func.isNotEmpty(extData)){
            JSONObject extDataObject =  JSON.parseObject(extData);
            if(Func.isNotEmpty(extDataObject)){
                oppType = extDataObject.getString("oppType");
            }
        }
        return !(RefSystemIdEnum.CRM.getRefSystemId() == systemId && Func.isNotEmpty(oppType) && (OppBusinessTypeEnums.FastBusiness.getValue().equals(oppType) || OppBusinessTypeEnums.SimpleBusiness.getValue().equals(oppType)) );
    }

    private void buildQuotation(EnquiryContext<EnquiryBO> context) {
        if (!context.isCopyQuotationFlag()) {
            context.setCopyOrderBatchRequest(null);
            context.setOrderToEnquiry(null);
            return;
        }
        List<CopyOrderPair> copyOrderPairs= new ArrayList();
        Lab lab = context.getLab();
        EnquiryBO enquiryBo = context.getParam();
        OrderIdBO orderIdBO = context.getOrderIdBO();
        EnquiryIdBO enquiryIdBo = enquiryBo.getId();
        EnquiryPO newEnquiryInfo = context.getNewEnquiry();
        if (Func.isNotEmpty(orderIdBO) && Func.isNotEmpty(orderIdBO.getOrderId())) {
            // order copy to enquiry
            OrderToEnquiry orderToEnquiry = new OrderToEnquiry();
            orderToEnquiry.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            orderToEnquiry.setSgsToken(SystemContextHolder.getSgsToken());
            orderToEnquiry.setSystemId(SgsSystem.GPO.getSgsSystemId());
            orderToEnquiry.setLabCode(lab.getLabCode());
            orderToEnquiry.setSourceProductLineCode(ProductLineContextHolder.getProductLineCode());
            List<OrderToEnquiry.OrderToEnquiryPair> orderToEnquiryPairList = new ArrayList();
            OrderToEnquiry.OrderToEnquiryPair orderToEnquiryPair = new OrderToEnquiry.OrderToEnquiryPair();
            orderToEnquiryPair.setOldOrderId(orderIdBO.getOrderId());
            orderToEnquiryPair.setOldOrderNo(orderIdBO.getOrderNo());
            orderToEnquiryPair.setNewEnquiryId(newEnquiryInfo.getId());
            orderToEnquiryPair.setNewEnquiryNo(newEnquiryInfo.getEnquiryNo());
            orderToEnquiryPairList.add(orderToEnquiryPair);
            orderToEnquiry.setOrderToEnquiryPairList(orderToEnquiryPairList);
            context.setOrderToEnquiry(orderToEnquiry);
        } else {
            CopyOrderPair objCopyOrderPair=new CopyOrderPair();
            objCopyOrderPair.setNewId(newEnquiryInfo.getId());
            objCopyOrderPair.setOldId(enquiryIdBo.getEnquiryId());
            objCopyOrderPair.setNewOrderNo(newEnquiryInfo.getEnquiryNo());
            objCopyOrderPair.setOldOrderNo(enquiryIdBo.getEnquiryNo());
            copyOrderPairs.add(objCopyOrderPair);
            CopyOrderBatchRequest objCopyOrderBatchRequest=new CopyOrderBatchRequest();
            objCopyOrderBatchRequest.setCopyType(1);
            objCopyOrderBatchRequest.setCopyOrderPairs(copyOrderPairs);
            objCopyOrderBatchRequest.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            objCopyOrderBatchRequest.setSystemId(SgsSystem.GPO.getSgsSystemId());
            objCopyOrderBatchRequest.setSgsToken(SystemContextHolder.getSgsToken());
            objCopyOrderBatchRequest.setAutoCloseQuotation(false);
            context.setCopyOrderBatchRequest(objCopyOrderBatchRequest);
        }
    }

    /**
     * ReportHeader ReportAddress中英文拼接
     *
     * @return
     */
    private void assembleReportReceiver(EnquiryReportReceiverPO reportReceiverPO, ServiceRequirementReportBO serviceRequirement) {
        if (Func.isEmpty(serviceRequirement)) {
            return;
        }
        if (Func.isEmpty(serviceRequirement.getLanguageList())) {
            reportReceiverPO.setReportHeader(serviceRequirement.getReportHeader());
            reportReceiverPO.setReportDeliveredTo(serviceRequirement.getReportAddress());
        }
        List<ReportLanguageBO> languageList = serviceRequirement.getLanguageList();
        if (Func.isEmpty(languageList)) {
            return;
        }
        ReportLanguageBO reportLanguageEN = serviceRequirement.getLanguageList().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.English))
                .findAny().orElse(null);
        ReportLanguageBO reportLanguageCN = serviceRequirement.getLanguageList().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese))
                .findAny().orElse(null);
        List<String> reportHeaderList = Lists.newArrayList();
        List<String> reportAddressList = Lists.newArrayList();
        if (Func.isNotEmpty(reportLanguageCN)) {
            reportHeaderList.add(reportLanguageCN.getReportHeader());
            reportAddressList.add(reportLanguageCN.getReportAddress());
        }
        if (Func.isNotEmpty(reportLanguageEN)) {
            reportHeaderList.add(reportLanguageEN.getReportHeader());
            reportAddressList.add(reportLanguageEN.getReportAddress());
        }
        if (Func.isNotEmpty(reportHeaderList)) {
            reportReceiverPO.setReportHeader(reportHeaderList.stream().collect(Collectors.joining(StandardCharPool.GPO_SEPARATOR)));
        }
        if (Func.isNotEmpty(reportAddressList)) {
            reportReceiverPO.setReportDeliveredTo(reportAddressList.stream().collect(Collectors.joining(StandardCharPool.GPO_SEPARATOR)));
        }
    }

    private Integer calculateSplitOrderFlag(EnquiryBO enquiryInfo) {
        //基本参数验证
        if (Func.isEmpty(enquiryInfo)) {
            return null;
        }
        if (Func.isEmpty(enquiryInfo.getHeader())) {
            return null;
        }
        EnquiryRelationshipBO relationship = enquiryInfo.getRelationship();
        if (Func.isEmpty(relationship.getParent()) || Func.isEmpty(relationship.getParent().getTrfList())) {
            return null;
        }
        Integer splitOrderFlag;

        boolean splitFlag = relationship.getParent().getTrfList().parallelStream()
                .anyMatch(trf -> sciTrfService.checkSciTrf(trf.getRefSystemId()));

        if (splitFlag) {
            splitOrderFlag = 0;
        } else {
            splitOrderFlag = 1;
        }
        return splitOrderFlag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse execute(EnquiryContext<EnquiryBO> context) {
        // 编辑的场景下部分数据可以删除重新插入
        if (!context.getIsNew()) {
            // 删除parcel
            LambdaQueryWrapper<EnquiryParcelPO> parcelWrapper = Wrappers.lambdaQuery();
            parcelWrapper.eq(EnquiryParcelPO::getEnquiryId, context.getNewEnquiry().getId());
            enquiryParcelService.remove(parcelWrapper);
            // 删除person信息
            LambdaQueryWrapper<EnquiryPersonPO> personWrapper = Wrappers.lambdaQuery();
            personWrapper.eq(EnquiryPersonPO::getEnquiryId, context.getNewEnquiry().getId());
            enquiryPersonService.remove(personWrapper);
            // 删除客户
            if (Func.isNotEmpty(context.getCustomerDelIds())) {
                enquiryCustomerService.removeByIds(context.getCustomerDelIds());
            }
            // 删除Tag数据
            LambdaQueryWrapper<ObjectTagPO> tagWrapper = Wrappers.lambdaQuery();
            tagWrapper.eq(ObjectTagPO::getObjectId, context.getNewEnquiry().getId());
            objectTagsService.remove(tagWrapper);
            // 删除TRF数据
            if(Func.isNotEmpty(context.getTrfDelIds())){
                enquiryTrfRelationshipService.removeByIds(context.getTrfDelIds());
            }
        }
        // 更新DB数据
        if (Func.isNotEmpty(context.getNewEnquiry())) {
            enquiryService.saveOrUpdate(context.getNewEnquiry());
        }
        if (Func.isNotEmpty(context.getNewEnquiryParcels())) {
            enquiryParcelService.saveBatch(context.getNewEnquiryParcels());
        }
        if (Func.isNotEmpty(context.getNewEnquiryPersons())) {
            enquiryPersonService.saveBatch(context.getNewEnquiryPersons());
        }
        if (Func.isNotEmpty(context.getNewCustomerList())) {
            enquiryCustomerService.saveOrUpdateBatch(context.getNewCustomerList());
        }
        if (Func.isNotEmpty(context.getEnquiryReportReceiver())) {
            enquiryReportReceiverService.saveOrUpdate(context.getEnquiryReportReceiver());
        }
        if (Func.isNotEmpty(context.getEnquiryTestRequest())) {
            enquiryTestRequestService.saveOrUpdate(context.getEnquiryTestRequest());
        }
        if (Func.isNotEmpty(context.getEnquiryTestRequestContactsPOList())) {
            enquiryTestRequestContactsService.saveBatch(context.getEnquiryTestRequestContactsPOList());
        }
        if (Func.isNotEmpty(context.getEnquiryTag())) {
            objectTagsService.save(context.getEnquiryTag());
        }
        // 判断是否需要copy quotation
        if (Func.isNotEmpty(context.getCopyOrderBatchRequest()) || Func.isNotEmpty(context.getOrderToEnquiry())) {
            if (Func.isNotEmpty(context.getCopyOrderBatchRequest())) {
                copyQuotation(context.getCopyOrderBatchRequest());
            }
            if (Func.isNotEmpty(context.getOrderToEnquiry())) {
                orderToEnquiry(context.getOrderToEnquiry());
            }
        }
//        if (Func.isNotEmpty(context.getOrderAttachmentList())) {
//            orderAttachmentService.saveBatch(context.getOrderAttachmentList());
//        }
        if (Func.isNotEmpty(context.getEnquiryTrfList())) {
            enquiryTrfRelationshipService.saveBatch(context.getEnquiryTrfList());
        }
        if (Func.isNotEmpty(context.getProductList())) {
            enquiryProductService.saveBatch(context.getProductList());
        }
        if (Func.isNotEmpty(context.getOrderAttachmentList())) {
            orderAttachmentService.saveBatch(context.getOrderAttachmentList());
        }
        // 事务提交后再发送消息出去，防止消息被提前消费
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                //Enquiry第一次保存时发布Enquiry已创建事件
                PreEvent preEvent = new PreEvent();
                preEvent.setEventSource(StandardObjectType.Enquiry.getName());
                preEvent.setEventType(EventType.EnquiryNew.getTypeName());
                preEvent.setToken(SystemContextHolder.getSgsToken());
                preEvent.setEventSourceNo(context.getNewEnquiry().getEnquiryNo());
                preEvent.setEventSourceId(context.getNewEnquiry().getId());
                preEvent.setOrderId(context.getNewEnquiry().getId());
                preEvent.setOrderNo(context.getNewEnquiry().getEnquiryNo());
                preEvent.setProductLineCode(SystemContextHolder.getBuCode());
                preEvent.setEventSourceStatus(EventType.EnquiryNew.getType());
                KafkaMessage<PreEvent> message = new KafkaMessage();
                message.setAction(KafkaActionType.event.getCode());
                message.setSgsToken(preEvent.getToken());
                message.setProductLineCode(preEvent.getProductLineCode());
                message.setLabCode(SystemContextHolder.getLabCode());
                message.setData(preEvent);
                kafkaTemplate.send(KafkaTopicConsts.TOPIC_EVENT, context.getNewEnquiry().getEnquiryNo(), JSON.toJSONString(message));
            }
        });
        return BaseResponse.newSuccessInstance(context.getNewEnquiry());
    }

    @Override
    public BaseResponse after(EnquiryContext<EnquiryBO> context) {
        // 编辑时triggerQuotationNewVersion
        if(!context.getIsNew()){
            TriggerNewVersionReq objTriggerNewVersionReq=new TriggerNewVersionReq();
            objTriggerNewVersionReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
            objTriggerNewVersionReq.setSystemId(SgsSystem.GPO.getSgsSystemId());
            objTriggerNewVersionReq.setOrderId(context.getNewEnquiry().getId());
            objTriggerNewVersionReq.setTriggerType(3);
            objTriggerNewVersionReq.setSalesPerson(context.getNewEnquiry().getSalesPerson());
            objTriggerNewVersionReq.setSgsToken(SystemContextHolder.getSgsToken());
            objTriggerNewVersionReq.setOrderType(context.getNewEnquiry().getEnquiryType());
            objTriggerNewVersionReq.setCustomerInfoList(JSON.parseArray(JSON.toJSONString(context.getNewCustomerList()), OrderCustomerInfo.class));
            log.info("Call Quotation.triggerNewVersion,req:{}",JSON.toJSONString(objTriggerNewVersionReq));
            BaseResponse objBaseResponse=quotationClient.triggerNewVersion(objTriggerNewVersionReq);
            log.info("Call Quotation.triggerNewVersion,rsp:{}",JSON.toJSONString(objBaseResponse));
        }
        //Enquiry_Operation_History

        return BaseResponse.newSuccessInstance(true);
    }

    private void copyQuotation(CopyOrderBatchRequest objCopyOrderBatchRequest) {
        if (Func.isNotEmpty(objCopyOrderBatchRequest)) {
            try {
                log.info("copyEnquiry,req:{}",JSON.toJSONString(objCopyOrderBatchRequest));
                com.sgs.framework.core.base.BaseResponse<GenerateOrderResp> copyResult = quotationFacade.copyQuotationForBatch(objCopyOrderBatchRequest);
                if (copyResult.getStatus() != 200) {
                    throw new BizException(Func.isEmpty(copyResult.getMessage()) ? messageUtil.get("enquiry.quotation.copy.fail") : copyResult.getMessage());
                } else {
                    if (copyResult.getData() != null && Func.isNotEmpty(copyResult.getData().getCopyQuotationFailedItemList())) {
                        log.info("无效的ServiceItem,res:{}", JSON.toJSONString(copyResult.getData()));
                    }
                }
            } catch (Exception e) {
                log.error("copyQuotation,error:{}", e);
            }
        }
    }
    private void orderToEnquiry(OrderToEnquiry orderToEnquiry) {
        if (Func.isNotEmpty(orderToEnquiry)) {
            try {
                System.out.println("orderToEnquiry,req:{}"+JSON.toJSONString(orderToEnquiry));
                BaseResponse<List<QuotationHeadInfoRsp>> copyResult = quotationInfoFacade.orderToEnquiry(orderToEnquiry);
                if (copyResult.getStatus() != 200) {
                    throw new BizException(Func.isEmpty(copyResult.getMessage()) ? messageUtil.get("enquiry.quotation.copy.fail") : copyResult.getMessage());
                } else {
                    if (copyResult.getData() != null && Func.isNotEmpty(copyResult.getData())) {
                        log.info("无效的ServiceItem,res:{}", JSON.toJSONString(copyResult.getData()));
                    }
                }
            } catch (Exception e) {
                log.error("orderToEnquiry,error:{}", e);
            }
        }
    }
}
