package com.sgs.gpo.domain.service.setting.notifyconfig.command;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.StringPool;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.util.DateUtils;
import com.sgs.gpo.dbstorages.mybatis.model.notify.NotifyConfigPO;
import com.sgs.gpo.domain.service.setting.notifyconfig.INotifyConfigProcessService;
import com.sgs.gpo.domain.service.setting.notifyconfig.context.NotifyConfigContext;
import com.sgs.gpo.domain.service.setting.notifyconfig.subdomain.INotifyConfigService;
import com.sgs.gpo.domain.service.setting.buparam.IBUParam;
import com.sgs.gpo.facade.model.buparam.PendingNotifyConfigRsp;
import com.sgs.gpo.facade.model.notifyconfig.req.NotifyConfigAddReq;
import com.sgs.gpo.integration.framework.FrameworkClient;
import com.sgs.otsnotes.facade.model.enums.JobStatus;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import com.sgs.preorder.facade.model.dto.holiday.HolidayDTO;
import com.sgs.preorder.facade.model.info.HolidayDateInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class NotifyConfigAddCMD extends BaseCommand<NotifyConfigContext<NotifyConfigAddReq>>{
    @Autowired
    private INotifyConfigService  notifyConfigService;
    @Autowired
    private INotifyConfigProcessService notifyConfigProcessService;
    @Autowired
    private FrameworkClient frameworkClient;
    @Autowired
    private IBUParam buParam;

    @Override
    public BaseResponse validateParam(NotifyConfigContext<NotifyConfigAddReq> context) {
        Assert.isTrue(Func.isNotEmpty(context.getParam()), "common.miss", new Object[]{"param"});
        Assert.isTrue(Func.isNotEmpty(context.getUserInfo()), "common.miss", new Object[]{"UserInfo"});
        NotifyConfigAddReq notifyConfigAddReq = context.getParam();
        Assert.isTrue(Func.isNotEmpty(notifyConfigAddReq.getObjectNo()), "common.miss", new Object[]{"ObjectNo"});
        Assert.isTrue(Func.isNotEmpty(notifyConfigAddReq.getObjectType()), "common.miss", new Object[]{"ObjectType"});
        Assert.isTrue(Func.isNotEmpty(notifyConfigAddReq.getBuId()), "common.miss", new Object[]{"BuId"});
        Assert.isTrue(Func.isNotEmpty(notifyConfigAddReq.getBuCode()), "common.miss", new Object[]{"BuCode"});
        Assert.isTrue(Func.isNotEmpty(notifyConfigAddReq.getLocationId()), "common.miss", new Object[]{"LocationId"});
        Assert.isTrue(Func.isNotEmpty(notifyConfigAddReq.getLocationCode()), "common.miss", new Object[]{"LocationCode"});
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    @Transactional
    @DS(Constants.DB.COMMON)
    public BaseResponse execute(NotifyConfigContext<NotifyConfigAddReq> context) {
        NotifyConfigAddReq notifyConfigAddReq = context.getParam();
        String regionAccount = context.getUserInfo().getRegionAccount();
        Date currDate = new Date();
        //更新可能之前存在的数据
        NotifyConfigPO notifyPO = new NotifyConfigPO();
        LambdaQueryWrapper<NotifyConfigPO> notifyPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        notifyPOLambdaQueryWrapper.eq(NotifyConfigPO::getObjectNo, notifyConfigAddReq.getObjectNo());
        notifyPOLambdaQueryWrapper.eq(NotifyConfigPO::getObjectType, notifyConfigAddReq.getObjectType());
        notifyPOLambdaQueryWrapper.eq(NotifyConfigPO::getEventType, notifyConfigAddReq.getEventType());
        notifyPOLambdaQueryWrapper.eq(NotifyConfigPO::getActiveIndicator, ActiveType.Enable.getStatus());
        notifyPO.setActiveIndicator(ActiveType.Disable.getStatus());
        notifyPO.setModifiedBy(regionAccount);
        notifyPO.setModifiedDate(currDate);
        notifyConfigService.update(notifyPO, notifyPOLambdaQueryWrapper);

        //插入新数据
        NotifyConfigPO notifyConfigPO = new NotifyConfigPO();
        BeanUtils.copyProperties(notifyConfigAddReq, notifyConfigPO);
        notifyConfigPO.setId(IdUtil.uuId());
        notifyConfigPO.setNotifyCount(0);
        notifyConfigPO.setCreatedBy(regionAccount);
        notifyConfigPO.setCreatedDate(currDate);
        notifyConfigPO.setModifiedBy(regionAccount);
        notifyConfigPO.setModifiedDate(currDate);
        notifyConfigService.save(notifyConfigPO);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(NotifyConfigContext<NotifyConfigAddReq> context) {
        NotifyConfigAddReq notifyConfigAddReq = context.getParam();
        notifyConfigAddReq.setActiveIndicator(ActiveType.Enable.getStatus());
        if (!notifyConfigAddReq.getIsCalculateNotifyDate()){
            return BaseResponse.newSuccessInstance(true);
        }
        //计算下次的通知时间
        if((Func.equalsSafe(notifyConfigAddReq.getObjectType(), Constants.BUSINESS_TYPE.REPORT) && Func.equalsSafe(notifyConfigAddReq.getEventType(), ReportStatus.Pending.getMessage())) ||
                (Func.equalsSafe(notifyConfigAddReq.getObjectType(), Constants.BUSINESS_TYPE.JOB) && Func.equalsSafe(notifyConfigAddReq.getEventType(), JobStatus.PENDING.getMessage()))){
            BaseResponse<PendingNotifyConfigRsp> notifyConfigParamRes = buParam.getNotifyConfig(notifyConfigAddReq.getBuCode(), notifyConfigAddReq.getLocationCode());
            if (notifyConfigParamRes.isFail()){
                notifyConfigAddReq.setActiveIndicator(ActiveType.Disable.getStatus());
                notifyConfigAddReq.setRemark("Bu param invalid");
                return BaseResponse.newSuccessInstance(true);
            }
            PendingNotifyConfigRsp notifyConfigParam = notifyConfigParamRes.getData();
            //没有查询到配置或者Report/Job表没查询到数据，只更新状态
            if(Func.isEmpty(notifyConfigParam) || !notifyConfigParam.getIsNotify() || Func.equalsSafe(notifyConfigParam.getIntervalHours(),0)){
                notifyConfigAddReq.setActiveIndicator(ActiveType.Disable.getStatus());
                notifyConfigAddReq.setRemark("Bu param invalid");
                return BaseResponse.newSuccessInstance(true);
            }
            //获取节假日
            BaseResponse<String> holidayIdRes = frameworkClient.queryHolidayId(Func.toStr(notifyConfigAddReq.getBuId()), Func.toStr(notifyConfigAddReq.getLocationId()));
            if (holidayIdRes.isFail() || Func.isEmpty(holidayIdRes.getData())) {
                notifyConfigAddReq.setActiveIndicator(ActiveType.Disable.getStatus());
                notifyConfigAddReq.setRemark("Holiday not found");
                return BaseResponse.newSuccessInstance(true);
            }
            // 根据holidayId查询holidayDetail
            String holidayId = holidayIdRes.getData();
            List<HolidayDateInfo> holidayDateList = Lists.newArrayList();
            String[] holidayIdArray = holidayId.split(StringPool.COMMA);
            for(String id : holidayIdArray){
                BaseResponse<HolidayDTO> holidayRes = frameworkClient.queryHolidayDetail(id);
                if (holidayRes.isSuccess() && Func.isNotEmpty(holidayRes.getData())) {
                    holidayDateList.addAll(holidayRes.getData().getHolidayDateList());
                }
            }
            List<Date> holidayDateInfos = holidayDateList.stream().map(HolidayDateInfo::getHolidayDate).collect(Collectors.toList());
            //计算下次发送时间，排除节假日
            Integer intervalHours = notifyConfigParam.getIntervalHours();
            Date nextNotifyDate = notifyConfigProcessService.getNextNotifyDate(DateUtils.now(),intervalHours,holidayDateInfos).getData();
            notifyConfigAddReq.setNextNotifyDate(nextNotifyDate);
        }
        return BaseResponse.newSuccessInstance(true);
    }
}
