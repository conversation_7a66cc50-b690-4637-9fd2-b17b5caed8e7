package com.sgs.gpo.domain.service.preorder.order.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.PageBO;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.payment.paidup.PaidUpMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.PaidUpPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.PaidUpVO;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IPaidUpService;
import com.sgs.gpo.facade.model.payment.paiduplist.req.PaidUpQueryReq;
import com.sgs.gpo.facade.model.payment.paiduplist.rsp.PaidUpListRsp;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Set;

@Service
public class PaidUpServiceImpl extends ServiceImpl<PaidUpMapper,PaidUpPO> implements IPaidUpService{


    @Override
    public List<PaidUpPO> getPaidUpByOrderNo(String orderNo) {
        return baseMapper.getPaidUpByOrderNo(orderNo);
    }

    @Override
    public List<String> getOrderByPaidUpIds(List<String> paidUpIds) {
        return baseMapper.getOrderByPaidUpIds(paidUpIds);
    }

    @Override
    public PageBO<PaidUpListRsp> page(PaidUpQueryReq req) {
        IPage<PaidUpListRsp> iPage = baseMapper.selectPage(new Page(req.getPage(),req.getRows()),req);
        PageBO<PaidUpListRsp> paidUpPage = Func.copy(iPage,PageBO.class);
        return paidUpPage;
    }

    @Override
    public void inactiveByIdList(Set<String> idList) {
        if(Func.isEmpty(idList)){
            return;
        }
        LambdaUpdateWrapper<PaidUpPO> updateWrapper = new LambdaUpdateWrapper<>();
        PaidUpPO paidUpPO = new PaidUpPO();
        paidUpPO.setActiveIndicator(ActiveType.Disable.getStatus());
        updateWrapper.in(PaidUpPO::getId,idList);
        baseMapper.update(paidUpPO,updateWrapper);
    }

    @Override
    public List<PaidUpVO> select(PaidUpQueryReq paidUpQueryReq) {
        return baseMapper.select(paidUpQueryReq);
    }




}
