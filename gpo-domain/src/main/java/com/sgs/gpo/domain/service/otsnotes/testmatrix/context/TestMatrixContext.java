package com.sgs.gpo.domain.service.otsnotes.testmatrix.context;


import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.condition.TestConditionInstanceLanguagePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.condition.TestConditionInstancePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.ProductAttributeInstancePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.TestMatrixExtPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.TestMatrixPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.TestSamplePO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/3 14:25
 */
@Data
public class TestMatrixContext<Input, Domain> extends BaseContext<Input, Domain> {


    private List<TestMatrixPO> testMatrixPOList;
    private List<ProductAttributeInstancePO> productAttributePOList;
    private List<TestConditionInstancePO> testConditionInstancePOList;
    private List<TestConditionInstanceLanguagePO> testConditionInstanceLanguagePOList;
    private List<TestSamplePO> testSampleList;
    private List<TestMatrixExtPO> testMatrixExtList;
}
