package com.sgs.gpo.domain.service.preorder.order.command;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.model.enums.PaymentStatus;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.BizLogConstant;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.*;
import com.sgs.gpo.domain.service.preorder.order.context.OrderUpdatePaidStatusContext;
import com.sgs.gpo.domain.service.preorder.order.subdomain.*;
import com.sgs.gpo.facade.model.enums.CaseType;
import com.sgs.gpo.facade.model.enums.EnableDelivery;
import com.sgs.gpo.facade.model.enums.ProductLines;
import com.sgs.gpo.facade.model.preorder.order.req.CalcPaymentStatusReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderUpdatePaidStatusReq;
import com.sgs.gpo.integration.framework.FrameworkClient;
import com.sgs.gpo.integration.framework.rsp.UserLabRsp;
import com.sgs.gpo.integration.quotation.QuotationClient;
import com.sgs.gpo.integration.quotation.req.SwitchNewQuotationConfigReq;
import com.sgs.gpo.integration.quotation.rsp.SwitchNewQuotationConfigRsp;
import com.sgs.gpo.integration.sodapreorder.SodaPreorderClient;
import com.sgs.gpo.integration.sodapreorder.req.SodaPreOrderAmountReq;
import com.sgs.gpo.integration.sodapreorder.rsp.SodaPreOrderAmountRsp;
import com.sgs.gpo.integration.usermanagement.UserManagementClient;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.info.BizLogInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
@Scope(value = "prototype")
public class OrderUpdatePaidStatusCMD extends BaseCommand<OrderUpdatePaidStatusContext> {

    @Autowired
    ISLOrderService slOrderService;
    @Autowired
    IGeneralOrderService generalOrderService;
    @Autowired
    IPaidUpService paidUpService;
    @Autowired
    FrameworkClient frameworkClient;
    @Autowired
    private UserManagementClient userManagementClient;
    @Autowired
    IBossOrderInvoiceService bossOrderInvoiceService;
    @Autowired
    IOrderPaidUpService orderPaidUpService;
    @Autowired
    SodaPreorderClient sodaPreorderClient;
    @Autowired
    BizLogClient bizLogClient;
    @Autowired
    QuotationClient quotationClient;

    @Override
    public BaseResponse validateParam(OrderUpdatePaidStatusContext context) {
        if (Func.isEmpty(context) || Func.isEmpty(context.getParam()) || Func.isEmpty(context.getParam().getOrderNo())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        if(Func.isEmpty(context.getParam().getAction())){
            return BaseResponse.newFailInstance("action不能为空！");
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(OrderUpdatePaidStatusContext context) {
        OrderUpdatePaidStatusReq req = context.getParam();
        String orderNo = req.getOrderNo();
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setOrderNoList(Sets.newHashSet(orderNo));
        BaseResponse<List<GeneralOrderPO>> orderInfoBaseRes = generalOrderService.query2(orderQueryReq);
        if(Func.isNotEmpty(orderInfoBaseRes.getData())){
            GeneralOrderPO orderPO = orderInfoBaseRes.getData().stream().filter(e -> e.getOrderNo().equals(orderNo)).findAny().orElse(null);
            log.info("预认款查询出来的Order信息:{}",orderPO);
            if(Func.isNotEmpty(orderPO)){
                //首先处理水单
                this.saveOrUpdatePaidUp(orderPO,context);
                OrderIdReq orderIdReq = new OrderIdReq();
                orderIdReq.setOrderIdList(Sets.newHashSet(orderPO.getId()));
                BaseResponse<List<SLOrderPO>> slOrderPORsp = slOrderService.select(orderIdReq);
                if(Func.isNotEmpty(slOrderPORsp) && Func.isNotEmpty(slOrderPORsp.getData())) {
                    Integer oldPaymentStatus = orderPO.getPayStatus();
                    SLOrderPO slOrderInfo = slOrderPORsp.getData().stream().filter(e -> e.getGeneralOrderID().equals(orderPO.getId())).findAny().orElse(null);
                    CalcPaymentStatusReq calcPaymentStatusReq = new CalcPaymentStatusReq();
                    calcPaymentStatusReq.setOrderNo(orderPO.getOrderNo());
                    calcPaymentStatusReq.setCaseType(slOrderInfo.getCaseType());
                    calcPaymentStatusReq.setProductLineCode(req.getProductLineCode());
                    PaymentStatus paymentStatus = this.calcPaymentStatus(calcPaymentStatusReq, orderPO, slOrderInfo);
                    orderPO.setPayStatus(paymentStatus.getType());

                    GeneralOrderPO newOrder = new GeneralOrderPO();
                    newOrder.setPayStatus(paymentStatus.getType());
                    newOrder.setEnableDelivery(orderPO.getEnableDelivery());
                    LambdaUpdateWrapper<GeneralOrderPO> wrapper = new LambdaUpdateWrapper();
                    wrapper.eq(GeneralOrderPO::getId,orderPO.getId());
                    generalOrderService.update(newOrder,wrapper);

                    SLOrderPO newSLOrder = new SLOrderPO();
                    newSLOrder.setPaymentStatus(paymentStatus.getType());
                    LambdaUpdateWrapper<SLOrderPO> wrapper2 = new LambdaUpdateWrapper<>();
                    wrapper2.eq(SLOrderPO::getId,slOrderInfo.getId());
                    slOrderService.update(newSLOrder,wrapper2);

                    if (!NumberUtil.equals(oldPaymentStatus, paymentStatus.getType())) {
                        UserInfo userInfo = context.getUserInfo();
                        BizLogInfo bizLog = new BizLogInfo();
                        bizLog.setBu(Func.isNotEmpty(orderPO.getBuCode()) ? orderPO.getBuCode() : ProductLineContextHolder.getProductLineCode());
                        bizLog.setLab(orderPO.getLocationCode());
                        bizLog.setOpUser("System");
                        if(Func.isNotEmpty(userInfo)){
                            bizLog.setOpUser(userInfo.getRegionAccount());
                        }
                        bizLog.setBizId(orderPO.getOrderNo());
                        bizLog.setOpType("预认款更新paymentStatus");
                        if (Func.isNotEmpty(orderPO) && Func.isNotEmpty(orderPO.getPayStatus())) {
                            if (Func.isNotEmpty(oldPaymentStatus)) {
                                String oldPaymentCode = PaymentStatus.getType(oldPaymentStatus).getCode();
                                bizLog.setOriginalVal(oldPaymentCode);
                            }
                        }
                        if (Func.isNotEmpty(paymentStatus)) {
                            String newPaymentCode = PaymentStatus.getType(paymentStatus.getType()).getCode();
                            bizLog.setNewVal(newPaymentCode);
                        }
                        bizLog.setBizOpType(BizLogConstant.ORDER_OPERATION_HISTORY);
                        log.info("bizLog param: {}", JSON.toJSONString(bizLog));
                        bizLogClient.doSend(bizLog);
                    }
                }
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }

    public PaymentStatus calcPaymentStatus(CalcPaymentStatusReq calcPaymentStatusReq, GeneralOrderPO orderPO, SLOrderPO slOrderInfo) {
        if(Func.isEmpty(calcPaymentStatusReq) || Func.isEmpty(calcPaymentStatusReq.getOrderNo()) || Func.isEmpty(calcPaymentStatusReq.getCaseType())){
            log.warn("calc paymentStatus fail, param miss");
            return PaymentStatus.NA;
        }
        String productLineCode = calcPaymentStatusReq.getProductLineCode();
        if(Func.isEmpty(productLineCode)){
            productLineCode = ProductLineContextHolder.getProductLineCode();
        }
        String orderType = calcPaymentStatusReq.getCaseType();
        String orderNo = calcPaymentStatusReq.getOrderNo();
        BigDecimal orderTotalAmount = null;

        //没有水单,取Order的总金额
        //有水单,根据Order先查询到Paid Up,再查询Paid Up下关联的所有的Order的totalPrice和币种
        //GPO2-15033
        SwitchNewQuotationConfigReq switchNewQuotationConfigReq = new SwitchNewQuotationConfigReq();
        String locationCode = orderPO.getLocationCode();
        switchNewQuotationConfigReq.setLocationCode(locationCode);
        SwitchNewQuotationConfigRsp rsp = quotationClient.getSwitchNewQuotationConfig(switchNewQuotationConfigReq);
        boolean isNewQuotation = false;
        if(Func.isNotEmpty(rsp)){
            if(Func.equalsSafe(rsp.getLocationCode(),locationCode) && Func.equalsSafe(1,rsp.getNewQuotationSwitch())){
                isNewQuotation = true;
            }
        }
        String orderCurrency = "";
        if(!(StringUtils.equalsIgnoreCase(productLineCode, ProductLines.SOFTLINE.getCode())) || (isNewQuotation && StringUtils.equalsIgnoreCase(productLineCode,ProductLines.SOFTLINE.getCode()))) {
            orderTotalAmount = generalOrderService.getAllTotalAmountByPaidUp(orderNo);
            if(Func.isNotEmpty(slOrderInfo)){
                orderCurrency = slOrderInfo.getQuoteCurrencyID();
            }
        }
        List<PaidUpPO> paidUpPOList = paidUpService.getPaidUpByOrderNo(orderNo);
        BigDecimal paidUpAmount = BigDecimal.ZERO;
        if(Func.isNotEmpty(paidUpPOList)){
            //SL处理逻辑，先查询出order对应的paidUp,再查询出paidUp下所有的Order,根据OrderNos调用接口获取totalAmount和Currency
            if(StringUtils.equalsIgnoreCase(productLineCode,ProductLines.SOFTLINE.getCode()) && !isNewQuotation){
                List<String> paidUpIdList = paidUpPOList.stream().map(PaidUpPO::getId).collect(Collectors.toList());
                List<String> orderNoList = paidUpService.getOrderByPaidUpIds(paidUpIdList);
                if(Func.isNotEmpty(orderNoList)){
                    BaseResponse<List<SodaPreOrderAmountRsp>> sodaPreOrderAmountRes = sodaPreorderClient.queryOrderAmount(new SodaPreOrderAmountReq(orderNoList));
                    if(Func.isNotEmpty(sodaPreOrderAmountRes) && Func.isNotEmpty(sodaPreOrderAmountRes.getData())){
                        List<SodaPreOrderAmountRsp> preOrderAmountRspList = sodaPreOrderAmountRes.getData();
                        //计算OrderTotalAmount、获取当前Order的Currency
                        SodaPreOrderAmountRsp sodaPreOrderAmountRsp = preOrderAmountRspList.stream().filter(i -> Func.equals(i.getOrderNo(), orderNo)).findAny().orElse(null);
                        if(Func.isNotEmpty(sodaPreOrderAmountRsp)){
                            orderCurrency = sodaPreOrderAmountRsp.getQuoteCurrency();
                        }
                        orderTotalAmount = preOrderAmountRspList.stream().map(SodaPreOrderAmountRsp::getAmount).filter(Func::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_DOWN);
                    }
                }
            }
            //找出与Order的币种不一致的水单
            //调用接口查询实时汇率，为避免重复调用，按水单币种分组
            Map<String, List<PaidUpPO>> currencyPaidUpMap = paidUpPOList
                    .parallelStream().collect(Collectors.groupingBy(PaidUpPO::getCurrencyCode));
            String conversionDate = DateUtil.format(new Date(),DateUtil.PATTERN_DATE);
            for(Map.Entry<String, List<PaidUpPO>> paidUpEntry:currencyPaidUpMap.entrySet()){
                String paidUpCurrency = paidUpEntry.getKey();
                List<PaidUpPO> paidUpPOS = paidUpEntry.getValue();
                BigDecimal sameCurrencyTotalPaidUpAmount = paidUpPOS.stream().map(PaidUpPO::getRemittanceAmount).filter(Func::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_DOWN);
                if(Func.isNotEmpty(paidUpCurrency) && Func.isNotEmpty(orderCurrency) && !StringUtils.equalsIgnoreCase(paidUpCurrency,orderCurrency)){
                    //水单币种与Order币种不一致，调用接口查询实时汇率
                    BigDecimal currencyExchangeRate = frameworkClient.getCurrencyExchangeRate(paidUpCurrency, orderCurrency, conversionDate);
                    BigDecimal afterRatePaidUpAmount = sameCurrencyTotalPaidUpAmount.multiply(currencyExchangeRate).setScale(2,BigDecimal.ROUND_HALF_UP);
                    paidUpAmount = paidUpAmount.add(afterRatePaidUpAmount);
                }else{
                    paidUpAmount = paidUpAmount.add(sameCurrencyTotalPaidUpAmount);
                }
            }
        }
        else if(StringUtils.equalsIgnoreCase(productLineCode,ProductLines.SOFTLINE.getCode()) && !isNewQuotation){
           BaseResponse<List<SodaPreOrderAmountRsp>> sodaPreOrderAmountRes = sodaPreorderClient.queryOrderAmount(new SodaPreOrderAmountReq(Lists.newArrayList(orderNo)));
            if(Func.isEmpty(sodaPreOrderAmountRes) && Func.isNotEmpty(sodaPreOrderAmountRes.getData())){
                SodaPreOrderAmountRsp sodaPreOrderAmountRsp = sodaPreOrderAmountRes.getData().get(0);
                if(Func.isNotEmpty(sodaPreOrderAmountRsp)){
                    orderTotalAmount = sodaPreOrderAmountRsp.getAmount();
                }
            }
        }
        if(Func.isEmpty(orderTotalAmount)){
            orderTotalAmount = BigDecimal.ZERO;
        }
//        log.info("{},calcPaymentStatus total amount:{}",orderNo,totalAmount);
        if (CaseType.check(orderType, CaseType.IDB, CaseType.IDN, CaseType.IDNTJ)) {
            return PaymentStatus.NA;
        } else if (CaseType.check(orderType, CaseType.Local, CaseType.OverseaPayment,CaseType.HKPayment) && (Func.isEmpty(orderTotalAmount) || Func.equalsSafe(orderTotalAmount, BigDecimal.ZERO))) {
            if(StringUtils.equalsIgnoreCase(productLineCode,ProductLines.SOFTLINE.getCode())) {
                return PaymentStatus.UN_PAID;
            }else{
                return PaymentStatus.NA;
            }
        }
        // 查询Order 下所有的Invoice,排除冲抵的Invoice
        BaseResponse<List<BossOrderInvoicePO>> orderInvoiceRes = bossOrderInvoiceService.getInvoiceByOrderNo(orderNo);
        List<BossOrderInvoicePO> invoiceList = orderInvoiceRes.getData();
        //查询Order下所有的PaidUp
        List<PaidUpPO> paidUpList = paidUpService.getPaidUpByOrderNo(orderNo);
        BigDecimal invoiceAmount = null;
        BigDecimal invoiceBalanceAmount = null;
        if (Func.isNotEmpty(invoiceList)) {
            // sum(invoiceAmount)
            invoiceAmount = invoiceList.stream().map(BossOrderInvoicePO::getInvoiceAmount).filter(Func::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
            // sum(invoiceBalanceAmount)
            invoiceBalanceAmount = invoiceList.stream().map(BossOrderInvoicePO::getBalanceAmount).filter(Func::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
        }


        log.info("【syncPayment】sync orderNo[{}] payment info : orderTotalAmt={},invoiceAmt={},invoiceBalanceAmount={},PaidUpAmount={}",
                orderNo,
                orderTotalAmount,
                invoiceAmount,
                invoiceBalanceAmount,
                paidUpAmount
        );
        if (Func.isNotEmpty(invoiceList) && isALLQuotationInvoiceAndPayment(orderNo, invoiceBalanceAmount)) {
            return PaymentStatus.PAID;
        } else if (Func.isNotEmpty(paidUpList) && NumberUtil.isGreaterOrEqual(paidUpAmount, orderTotalAmount)
                && (Func.isEmpty(invoiceList) || Func.equalsSafe(invoiceBalanceAmount, invoiceAmount))) {
            if(StringUtils.equalsIgnoreCase(productLineCode,ProductLines.SOFTLINE.getCode())){
                return PaymentStatus.PAID;
            }else{
                return PaymentStatus.PRE_PAID;
            }
        } else if ((Func.isNotEmpty(invoiceList) && !Func.equalsSafe(invoiceAmount, invoiceBalanceAmount))
                || (Func.isNotEmpty(paidUpList) && NumberUtil.isLess(paidUpAmount, orderTotalAmount))) {
            return PaymentStatus.PART_PAID;
        } else {
            return PaymentStatus.UN_PAID;
        }
    }
    private boolean isALLQuotationInvoiceAndPayment(String orderNo,BigDecimal invoiceBalanceAmount){
        // 查询不存在Invoice或Invoice被冲抵的Quotation数量
        Long noInvoiceQuotationCount = bossOrderInvoiceService.getNoInvoiceQuotationCount(orderNo);
        if((Func.isEmpty(noInvoiceQuotationCount) || noInvoiceQuotationCount==0) && invoiceBalanceAmount.compareTo(BigDecimal.ZERO) == 0){
            return true;
        }else{
            return false;
        }
    }

    private void saveOrUpdatePaidUp(GeneralOrderPO orderPO, OrderUpdatePaidStatusContext context){
        OrderUpdatePaidStatusReq req = context.getParam();
        String action = req.getAction();
        String orderId = orderPO.getId();
        if(action.equals("PrePayment")){
            log.info("预认款处理水单开始：{}",orderPO.getOrderNo());
            //保存水单 逻辑-查找是否已经存在水单，存在的话建立关联关系，没有的话-创建水单记录并建立关系
            String remittanceNo = req.getReceiptsNo();
            LambdaQueryWrapper<PaidUpPO> wrapper= Wrappers.<PaidUpPO>lambdaQuery()
                    .eq(PaidUpPO::getRemittanceNo,remittanceNo).eq(PaidUpPO::getActiveIndicator,1).eq(PaidUpPO::getFCode,req.getOrgCode());
            List<PaidUpPO> paidUpPOList = paidUpService.list(wrapper);
            PaidUpPO paidUpPO = null;
            if(Func.isNotEmpty(paidUpPOList)){
                paidUpPO = paidUpPOList.stream().filter(
                        e -> Func.isNotEmpty(e.getRemittanceNo()) && e.getRemittanceNo().equals(remittanceNo)).findAny().orElse(null);
            }
            if(Func.isNotEmpty(paidUpPO)){
                //存在 - 只建立关系
                OrderPaidUpPO orderPaidUpPO = new OrderPaidUpPO();
                orderPaidUpPO.setOrderId(orderId);
                orderPaidUpPO.setPaidUpId(paidUpPO.getId());
                orderPaidUpPO.setCreatedDate(new Date());
                orderPaidUpPO.setRemark(req.getRemark());
                orderPaidUpPO.setCreatedBy("System");
                orderPaidUpPO.setModifiedBy("System");
                orderPaidUpPO.setModifiedDate(new Date());
                orderPaidUpPO.setActiveIndicator(1);
                orderPaidUpService.save(orderPaidUpPO);
                log.info("已存在水单，建立关系,orderPaidUpPO:{}",orderPaidUpPO);
            }else {
                //不存在 - 新建水单、同时建立关系
                //获取用户信息
                UserLabRsp userLabRsp = userManagementClient.getUserLabBuInfo(context.getToken());
                log.info("从token获取的userLabBuInfo：{}", userLabRsp);
                //组装水单
                PaidUpPO newPaidUpPO = new PaidUpPO();
                newPaidUpPO.setId(UUID.randomUUID().toString());
                if(Func.isNotEmpty(userLabRsp)){
                    newPaidUpPO.setLabCode(userLabRsp.getLabCode());
                    newPaidUpPO.setBuId(userLabRsp.getProductLineId());
                    newPaidUpPO.setBuCode(userLabRsp.getProductLineCode());
                    newPaidUpPO.setLocationId(userLabRsp.getLocationId());
                    newPaidUpPO.setLocationCode(userLabRsp.getLocationCode());
                }
                newPaidUpPO.setPaidUpNo(orderPO.getOrderNo());
                if(Func.isNotEmpty(userLabRsp.getLabCode())){
                    String labCode = userLabRsp.getLabCode().replace(" ","");
                    BaseResponse<String> paidUpNoRsp = frameworkClient.getPaidUpNo(userLabRsp.getProductLineId(), userLabRsp.getLocationId(),labCode);
                    log.info("获取的PaidUpNoRsp：{}",paidUpNoRsp);
                    if(paidUpNoRsp.isSuccess() && Func.isNotEmpty(paidUpNoRsp.getData())){
                        newPaidUpPO.setPaidUpNo(paidUpNoRsp.getData());
                    }
                }
                newPaidUpPO.setFCode(req.getOrgCode());
                newPaidUpPO.setPaidBy(req.getPaymentCustomerName());
                newPaidUpPO.setRemittanceNo(req.getReceiptsNo());
                newPaidUpPO.setRemittanceAmount(req.getPaymentAmount());
                newPaidUpPO.setCurrencyCode(req.getPaymentCurrency());
                newPaidUpPO.setRemittanceReceivedDate(req.getReceiptsDate());
                newPaidUpPO.setPaidDate(req.getReceiptsDate());
                newPaidUpPO.setPaymentMethod("3");
                newPaidUpPO.setCreatedDate(new Date());
                //newPaidUpPO.setRemark(req.getRemark());
                newPaidUpPO.setCreatedBy("System");
                newPaidUpPO.setModifiedBy("System");
                newPaidUpPO.setModifiedDate(new Date());
                newPaidUpPO.setActiveIndicator(1);
                //组装关联信息
                OrderPaidUpPO orderPaidUpPO = new OrderPaidUpPO();
                orderPaidUpPO.setOrderId(orderId);
                orderPaidUpPO.setPaidUpId(newPaidUpPO.getId());
                orderPaidUpPO.setRemark(req.getRemark());
                orderPaidUpPO.setCreatedBy("System");
                orderPaidUpPO.setModifiedBy("System");
                orderPaidUpPO.setCreatedDate(new Date());
                orderPaidUpPO.setModifiedDate(new Date());
                orderPaidUpPO.setActiveIndicator(1);
                orderPaidUpService.save(orderPaidUpPO);
                paidUpService.save(newPaidUpPO);
                log.info("不存在水单，建立水单,PaidUpPO:{}",newPaidUpPO);
                log.info("不存在水单，建立关系,orderPaidUpPO:{}",orderPaidUpPO);
            }
            if(Func.isNotEmpty(orderPO.getEnableDelivery())){
                orderPO.setEnableDelivery(orderPO.getEnableDelivery() | EnableDelivery.UPLOAD_PAID_UP.getStatus());
            } else{
                orderPO.setEnableDelivery(EnableDelivery.UPLOAD_PAID_UP.getStatus());
            }

        } else if(action.equals("UnPrePayment")){
            //更新水单的有效性为失效
            //查询关联关系表
            //查找水单表 - 找出水单记录
            log.info("取消预认款处理水单开始:{}",orderPO.getOrderNo());
            String remittanceNo = req.getReceiptsNo();
            LambdaQueryWrapper<PaidUpPO> wrapperPaidUp= Wrappers.<PaidUpPO>lambdaQuery()
                    .eq(PaidUpPO::getRemittanceNo,remittanceNo).eq(PaidUpPO::getActiveIndicator,1).eq(PaidUpPO::getFCode,req.getOrgCode());
            List<PaidUpPO> paidUpPOList = paidUpService.list(wrapperPaidUp);
            PaidUpPO paidUpPO = paidUpPOList.stream().filter(
                    e -> Func.isNotEmpty(e.getRemittanceNo()) && e.getRemittanceNo().equals(remittanceNo)).findAny().orElse(null);
            if(Func.isNotEmpty(paidUpPO)){
                LambdaQueryWrapper<OrderPaidUpPO> wrapperOrderPaidUp= Wrappers.<OrderPaidUpPO>lambdaQuery()
                        .eq(OrderPaidUpPO::getOrderId,orderId).eq(OrderPaidUpPO::getPaidUpId,paidUpPO.getId()).eq(OrderPaidUpPO::getActiveIndicator,1);
                List<OrderPaidUpPO> orderPaidUpPOList = orderPaidUpService.list(wrapperOrderPaidUp);
                OrderPaidUpPO orderPaidUpPO = orderPaidUpPOList.stream().filter(
                        e -> Func.isNotEmpty(e.getOrderId()) && Func.isNotEmpty(e.getPaidUpId()) && e.getOrderId().equals(orderId) && e.getPaidUpId().equals(paidUpPO.getId())).findAny().orElse(null);
                if(Func.isNotEmpty(orderPaidUpPO)){
                    //orderPaidUpPO.setActiveIndicator(0);
                    //orderPaidUpService.saveOrUpdate(orderPaidUpPO);
                    orderPaidUpService.removeById(orderPaidUpPO);
                    log.info("存在水单,取消关系,OrderPaidUpPO:{}",orderPaidUpPO);
                }
            }
            //检查订单是否还存在水单关联 如果没有的话，enableDelivery设置为0(减去4)
            LambdaQueryWrapper<OrderPaidUpPO> wrapperOrderPaidUp= Wrappers.<OrderPaidUpPO>lambdaQuery()
                    .eq(OrderPaidUpPO::getOrderId,orderId).eq(OrderPaidUpPO::getActiveIndicator,1);
            List<OrderPaidUpPO> orderPaidUpPOList = orderPaidUpService.list(wrapperOrderPaidUp);
            log.info("order:{}关联的水单信息：{}",orderPO.getOrderNo(),orderPaidUpPOList);
            if(Func.isEmpty(orderPaidUpPOList)){
                orderPO.setEnableDelivery(orderPO.getEnableDelivery() ^ EnableDelivery.UPLOAD_PAID_UP.getStatus());
            }

        }
    }
}
