package com.sgs.gpo.domain.service.otsnotes.subcontract.command;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.common.customer.CustomerBO;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.SubContractStatus;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractPO;
import com.sgs.gpo.domain.service.otsnotes.subcontract.context.SubcontractContext;
import com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.ISubcontractService;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.facade.model.emailmodel.EmailAddressDTO;
import com.sgs.gpo.facade.model.emailmodel.EmailAttachmentDTO;
import com.sgs.gpo.facade.model.emailmodel.EmailModelDTO;
import com.sgs.gpo.facade.model.emailmodel.SendEmailModelContext;
import com.sgs.gpo.facade.model.otsnotes.subcontract.req.SubContractPageReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractQueryReq;
import com.sgs.priceengine.facade.model.enums.CustomerUsage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
@Scope(value = "prototype")
public class SubContractSendEmailCMD extends BaseCommand<SubcontractContext<SubContractPageReq>> {
    @Autowired
    private IOrderDomainService orderDomainService;
    @Autowired
    ISubcontractService subcontractService;


    @Override
    public BaseResponse validateParam(SubcontractContext<SubContractPageReq> context) {

        Assert.isTrue(Func.isNotEmpty(context.getParam()), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(context.getParam().getSubcontractNo()), "common.param.miss", new Object[]{"subcontractNo"});
        Assert.isTrue(Func.isNotEmpty(context.getParam().getOrderNo()), "common.param.miss", new Object[]{"orderNo"});

        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(SubcontractContext<SubContractPageReq> context) {
        //查找order是否存在
        Set<String> setOrderReq = new HashSet<>();
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        setOrderReq.add(context.getParam().getOrderNo());
        orderQueryReq.setOrderNoList(setOrderReq);
        BaseResponse<List<OrderBO>> orderBORes = orderDomainService.queryBO(orderQueryReq);
        if (Func.isNull(orderBORes) || Func.isEmpty(orderBORes.getData())) {
            return BaseResponse.newFailInstance("没有找到相应的订单信息");
        }
        OrderBO orderBO=orderBORes.getData().get(0);
        //检查分包单是否存在
        SubcontractQueryReq subcontractQueryReq = new SubcontractQueryReq();
        Set<String> setSubcontractReq = new HashSet<>();
        setSubcontractReq.add(context.getParam().getSubcontractNo());
        subcontractQueryReq.setSubcontractNoList(setSubcontractReq);
        List<SubcontractPO> subcontractResponse = subcontractService.query(subcontractQueryReq);
        if (Func.isEmpty(subcontractResponse)) {
            return BaseResponse.newFailInstance("未查询到分包单信息！");
        }


        if (SubContractStatus.check(subcontractResponse.get(0).getStatus(), SubContractStatus.PENDING) || SubContractStatus.check(subcontractResponse.get(0).getStatus(), SubContractStatus.CANCELLED) ||
                SubContractStatus.check(subcontractResponse.get(0).getStatus(), SubContractStatus.REPORT_COMPLETED)) {
            return BaseResponse.newFailInstance("分包单状态为pending,Completed,Cancelled不可发送邮件！");
        }
        return BaseResponse.newSuccessInstance(this.buildSendEmailModel(context,orderBO));
    }


    public EmailModelDTO buildSendEmailModel(SubcontractContext<SubContractPageReq> context, OrderBO orderBO) {
        EmailModelDTO emailModelDTO = new EmailModelDTO();
        emailModelDTO.setSendEmailModelContext(this.buildEmailContext(orderBO, context));
        emailModelDTO.setTemplateType(Constants.EMAIL.TEMPLATE_TYPE.SUBCONTRACT);
        emailModelDTO.setSystemId(Func.toStr(SgsSystem.GPO.getSgsSystemId()));
        emailModelDTO.setProductLineCode(context.getProductLineCode());
        return emailModelDTO;
    }

    public SendEmailModelContext buildEmailContext(OrderBO orderBO, SubcontractContext<SubContractPageReq> context) {
        SendEmailModelContext emailContext = new SendEmailModelContext();
        emailContext.setTo(orderBO.getHeader().getCsEmail());
        emailContext.setSystemId(Func.toStr(SgsSystem.GPO.getSgsSystemId()));
        emailContext.setSgsToken(SystemContextHolder.getSgsToken());
        emailContext.setFrom(context.getUserInfo().getEmail());
        emailContext.setSender(context.getUserInfo().getEmail());
        emailContext.setRespCs(context.getUserInfo().getEmail());
        emailContext.setRespCsName(orderBO.getHeader().getCsName());
        emailContext.setCc(Func.toStr(SecurityContextHolder.getUserInfoFillSystem().getEmail()));
        // 3、获取DffFields
        Map<String, Object> dffFields = new HashMap<>();
        emailContext.setDffFields(dffFields);
        EmailAddressDTO emailAddressDTO = new EmailAddressDTO();
        emailAddressDTO.setCustomized_cc(context.getUserInfo().getEmail());
        emailAddressDTO.setCustomized_to(orderBO.getHeader().getCsEmail());
        emailContext.setEmailAddress(emailAddressDTO);
        emailContext.setGeneralFields(this.buildEmailGeneralField(context, orderBO));
        List<EmailAttachmentDTO> attachmentList = new ArrayList<EmailAttachmentDTO>();
        emailContext.setAttachmentList(attachmentList);
        return emailContext;
    }

    public Map<String, String> buildEmailGeneralField(SubcontractContext<SubContractPageReq> context, OrderBO orderBO) {

        Map<String, String> generalFields = new HashMap<String, String>();
        CustomerBO applicant = orderBO.getCustomerList().stream()
                .filter(customerBO -> Func.equals(customerBO.getCustomerUsage(), CustomerUsage.Applicant.getStatus()))
                .findFirst().orElse(new CustomerBO());
        generalFields.put("${OrderNo}", context.getParam().getOrderNo());
        generalFields.put("${SubcontractNo}", context.getParam().getSubcontractNo());
        generalFields.put("${ApplicantNameEN}", applicant.getCustomerName());
        generalFields.put("${ResponsibleCSName}",orderBO.getHeader().getCsName());
        return generalFields;
    }
}
