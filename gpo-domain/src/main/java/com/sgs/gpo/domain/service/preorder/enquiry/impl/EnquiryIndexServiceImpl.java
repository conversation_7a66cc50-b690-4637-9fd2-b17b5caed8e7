package com.sgs.gpo.domain.service.preorder.enquiry.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.order.enquiry.EnquiryIdBO;
import com.sgs.framework.redis.utils.RedisUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.DBConstants;
import com.sgs.gpo.core.enums.ObjectType;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.enquiry.EnquiryIndexMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryIndexPO;
import com.sgs.gpo.domain.service.common.index.IIndexService;
import com.sgs.gpo.domain.service.common.index.Index;
import com.sgs.gpo.facade.model.kafka.MaxWellMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import java.util.List;



@Service
@Index(object = ObjectType.Enquiry)
@Slf4j
@Primary
public class EnquiryIndexServiceImpl extends ServiceImpl<EnquiryIndexMapper, EnquiryIndexPO> implements IIndexService<EnquiryIndexPO, EnquiryIdBO> {

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public List<EnquiryIndexPO> searchFromSource(EnquiryIndexPO index) {
        List<EnquiryIndexPO> list = baseMapper.selectSourceList(index);
        return list;
    }

    @Override
    public boolean saveOrUpdate(EnquiryIdBO enquiryId) {
        // 获取enquiryId / enquiryNo
        Assert.isTrue(Func.isNotEmpty(enquiryId) && (Func.isNotEmpty(enquiryId.getEnquiryId() ) || Func.isNotEmpty(enquiryId.getEnquiryNo())), ResponseCode.PARAM_MISS,"Message invalid (not id information)");

        // 读取DB数据
        EnquiryIndexPO dbEnquiryIndex = getByIdFromSource(enquiryId);
        if (Func.isEmpty(dbEnquiryIndex)) {
            log.error("EnquiryIndex[not found]:{}", enquiryId);
            return false;
        }
        dbEnquiryIndex.setIndexId(dbEnquiryIndex.getEnquiryId());

        //补充DB中的EnquiryId
        enquiryId.setEnquiryId(dbEnquiryIndex.getEnquiryId());

        // 保存/更新订单索引
        if (!hasIndex(enquiryId)) {
            log.info("EnquiryIndex[create]:{}",dbEnquiryIndex.getEnquiryNo());
            return save(dbEnquiryIndex);
        } else {
            log.info("EnquiryIndex[update]:{}",dbEnquiryIndex.getEnquiryNo());
            return updateById(dbEnquiryIndex);
        }
    }

    @Override
    public boolean saveOrUpdate(MaxWellMessage maxWellMessage) {

        Assert.notNull(maxWellMessage,"Message is null");

        // 获取enquiryId / enquiryNo
        EnquiryIdBO enquiryId = getId(maxWellMessage);
        Assert.notNull(enquiryId,String.format("Message invalid (not id information), Message:%s",Func.toStr(maxWellMessage)));

        Long xid = maxWellMessage.getXid();
        Long xoffset = maxWellMessage.getXoffset();
        log.info("EnquiryIndex[receive],enquiryId:{},XID{},XOffSet{}",enquiryId,xid,xoffset);

        // 60s内忽略相同DB事务多次更新的消息
        String key = String.format("DB_XID_%s_%s_%s", maxWellMessage.getDatabase(),xid,enquiryId.hashCode());
        if(redisUtil.hasKey(key)){
            log.debug("EnquiryIndex[receive repeat]:{}",maxWellMessage);
            return false;
        }
        redisUtil.set(key,xid,60l);

        return saveOrUpdate(enquiryId);
    }

    @Override
    public EnquiryIdBO getId(MaxWellMessage maxWellMessage) {
        if(Func.isEmpty(maxWellMessage)){
            return null;
        }
        Object enquiryId = maxWellMessage.getData().get(DBConstants.DATA_BASE.PREORDER.ENQUIRY_CUSTOMER.enquiryId);
        Object enquiryNo = maxWellMessage.getData().get(DBConstants.DATA_BASE.PREORDER.ENQUIRY.enquiryNo);
        if (Func.isEmpty(enquiryId)) {
            enquiryId = maxWellMessage.getData().get(DBConstants.DATA_BASE.PREORDER.ENQUIRY.id);
        }

        EnquiryIdBO enquiryIdBO = new EnquiryIdBO();
        enquiryIdBO.setEnquiryId(Func.toStr(enquiryId) );
        enquiryIdBO.setEnquiryNo(Func.toStr(enquiryNo) );
        return enquiryIdBO;
    }

    @Override
    public boolean hasIndex(EnquiryIdBO id) {
        return baseMapper.hasIndex(id.getEnquiryId())>0;
    }

    @Override
    public EnquiryIndexPO idToIndex(EnquiryIdBO enquiryId){
        if(Func.isEmpty(enquiryId)){
            return null;
        }
        EnquiryIndexPO enquiryIndex = new EnquiryIndexPO();
        enquiryIndex.setEnquiryId(enquiryId.getEnquiryId());
        enquiryIndex.setEnquiryNo(enquiryId.getEnquiryNo());
        return enquiryIndex;
    }

}
