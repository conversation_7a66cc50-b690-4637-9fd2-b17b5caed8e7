package com.sgs.gpo.domain.service.otsnotes.status.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.enums.GpnStatusObjectType;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.status.GpnStatusMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.status.GpnStatusPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.status.GpoStatusPO;
import com.sgs.gpo.domain.service.otsnotes.status.IGpnStatusService;
import com.sgs.gpo.facade.model.preorder.order.req.GpnStatusReq;
import com.sgs.gpo.facade.model.report.req.ReportIdReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @title: GpnStatusServiceImpl
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/26 16:36
 */
@Service
@Slf4j
public class GpnStatusServiceImpl extends ServiceImpl<GpnStatusMapper, GpnStatusPO> implements IGpnStatusService {

    @Override
    public BaseResponse<List<GpnStatusPO>> queryReportStatus(ReportIdReq reportIdReq) {
        if(Func.isEmpty(reportIdReq) || Func.isEmpty(reportIdReq.getReportNoList())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        QueryWrapper<GpnStatusPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(GpnStatusPO.COLUMN.OBJECT_NO, reportIdReq.getReportNoList());
        queryWrapper.eq(GpnStatusPO.COLUMN.OBJECT_TYPE, GpnStatusObjectType.REPORT.getCode());
        List<GpnStatusPO> gpoStatusPOS = baseMapper.selectList(queryWrapper);
        return BaseResponse.newSuccessInstance(gpoStatusPOS);
    }

    @Override
    public List<GpnStatusPO> query(GpnStatusReq gpnStatusReq) {
        if (Func.isEmpty(gpnStatusReq) || Func.isEmpty(gpnStatusReq.getObjectNo())) {
            return null;
        }
        LambdaQueryWrapper<GpnStatusPO> wrapper= Wrappers.<GpnStatusPO>lambdaQuery()
                .eq(GpnStatusPO::getObjectNo, gpnStatusReq.getObjectNo());
        if(Func.isNotEmpty(gpnStatusReq.getOperationType())){
            wrapper.eq(GpnStatusPO::getOperationType,gpnStatusReq.getOperationType());
        }
        wrapper.orderByDesc(GpnStatusPO::getCreatedDate);
        return baseMapper.selectList(wrapper);
    }
}
