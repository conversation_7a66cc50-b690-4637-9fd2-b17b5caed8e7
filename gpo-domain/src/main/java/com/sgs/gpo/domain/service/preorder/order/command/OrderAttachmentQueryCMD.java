package com.sgs.gpo.domain.service.preorder.order.command;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.model.common.attachment.AttachmentBO;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.attachment.OrderAttachmentPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.domain.service.preorder.attachment.IOrderAttachmentService;
import com.sgs.gpo.domain.service.preorder.order.context.OrderContext;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IGeneralOrderService;
import com.sgs.gpo.facade.model.preorder.order.req.OrderAttachmentQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Scope(value = "prototype")
@Service
public class OrderAttachmentQueryCMD extends BaseCommand<OrderContext<OrderAttachmentQueryReq>> {

    @Autowired
    IGeneralOrderService generalOrderService;
    @Autowired
    IOrderAttachmentService orderAttachmentService;

    @Override
    public BaseResponse validateParam(OrderContext<OrderAttachmentQueryReq> context) {
        OrderAttachmentQueryReq req = context.getParam();
        if(Func.isAllEmpty(req.getOrderIdList(),req.getOrderNoList())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(OrderContext<OrderAttachmentQueryReq> context) {
        OrderAttachmentQueryReq req = context.getParam();
        if(Func.isEmpty(req.getOrderIdList()) && Func.isNotEmpty(req.getOrderNoList())){
            OrderQueryReq orderQueryReq = new OrderQueryReq();
            orderQueryReq.setOrderNoList(req.getOrderNoList());
            BaseResponse<List<GeneralOrderPO>> orderListRsp = generalOrderService.query2(orderQueryReq);
            if(orderListRsp.isSuccess() && Func.isNotEmpty(orderListRsp.getData())){
                List<GeneralOrderPO> orderPOList = orderListRsp.getData();
                Set<String> orderIdList = orderPOList.stream().map(e -> e.getId()).collect(Collectors.toSet());
                if(Func.isNotEmpty(orderIdList)){
                    req.setOrderIdList(orderIdList);
                }
            }
        }
        if(Func.isAnyNotEmpty(req.getOrderIdList())){
            //查询OrderAttachment
            BaseResponse<List<OrderAttachmentPO>> attachmentPOListRsp = orderAttachmentService.select(req);
            if(attachmentPOListRsp.isSuccess() && Func.isNotEmpty(attachmentPOListRsp)){
                context.setOrderAttachmentList(attachmentPOListRsp.getData());
            }
        }
        return super.before(context);
    }

    @Override
    public BaseResponse execute(OrderContext<OrderAttachmentQueryReq> context){
        //转BO
        List<AttachmentBO> attachmentList = new ArrayList<>();
        List<OrderAttachmentPO> attachmentPOList = context.getOrderAttachmentList();
        if(Func.isNotEmpty(attachmentPOList)){
            attachmentPOList.stream().forEach(attachment ->{
                AttachmentBO attachmentBO = new AttachmentBO();
                Func.copy(attachment,attachmentBO);
                attachmentBO.setId(attachment.getId());
                attachmentBO.setCloudId(attachment.getCloudID());
                attachmentBO.setFileName(attachment.getAttachmentName());
                attachmentBO.setFileType(attachment.getFileType());
                attachmentBO.setSampleNo(attachment.getSampleNo());
                attachmentBO.setReportNo(attachment.getReportNo());
                attachmentBO.setJobNo(attachment.getJobNo());
                attachmentBO.setBusinessType(attachment.getBusinessType());
                attachmentBO.setDescription(attachment.getDescription());
                attachmentList.add(attachmentBO);
            });
        }
        return BaseResponse.newSuccessInstance(attachmentList);
    }
}
