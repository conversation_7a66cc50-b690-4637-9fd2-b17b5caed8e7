package com.sgs.gpo.domain.service.otsnotes.testmatrix.command;

import com.sgs.framework.core.base.BaseBO;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.context.TestMatrixContext;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.ITestLineService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixService;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.ITestSampleService;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/7/3 14:25
 */
@Service
@Slf4j
@Scope(value = "prototype")
public class TestMatrixConfirmCMD extends BaseCommand<TestMatrixContext<OrderIdReq, BaseBO>> {

    @Autowired
    private ITestMatrixService testMatrixService;
    @Autowired
    private ITestLineService testLineService;
    @Autowired
    private ITestSampleService testSampleService;

//    private OrderIdReq orderIdReq;
//    private boolean firstConfirm;
//    private String testMatrixMode;

    @Override
    public BaseResponse validateParam(TestMatrixContext<OrderIdReq, BaseBO> context) {
        // 校验入参不能为空
        if(Func.isEmpty(context)||Func.isEmpty(context.getParam())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        OrderIdReq orderIdReq = context.getParam();
        // 校验入参不为空
        if(Func.isEmpty(orderIdReq.getOrderIdList())||Func.isEmpty(orderIdReq.getOrderNoList())){
            return BaseResponse.newFailInstance("common.miss", new Object[]{"orderId / orderNo"});
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(TestMatrixContext<OrderIdReq, BaseBO> context) {
        //1、获取order简要信息
        // orderService.query()
        //2、gpo quotation -> test,order.quotationMode = byEnquiry / byOrder
        // quotationService.toTest()
        //3、判断是否第一次confirm order
        // order.confirmDate!=null
        boolean firstConfirm = true;
        //4、获取TestMatrixModel = TestMatrix / TestData
        // buParamService.getTestMatrixModel()
        //4.1、testData
            //获取 analyte list
            // analyteService.query()
        return super.before(context);
    }

    @Override
    public BaseResponse validateDomain(TestMatrixContext<OrderIdReq, BaseBO> context) {
        //----------TestMatrixService.validateAttr();
            //1、校验Order必填项
            // orderService.validateAttr()

        //----------TestMatrixService.validateRel();
            //2、校验sample 关系(mix)，testSample.type=mix
            // testSample.validateRel()
            //3、校验sample都被assign到tl
            //4、校验testline都assign sample
            //5、校验testmatrix condition confirmed；
            // testMatrixService.validateRel()
            //6、存在Conclusion，校验Conclusion,  testSample.type=mix & TestMatrix.conclusion=F & customer.config.testLIne.SplitMixFail=Y
            // conclusionService.validateRel()
        return super.validateDomain(context);
    }

    @Override
    public BaseResponse execute(TestMatrixContext<OrderIdReq, BaseBO> context) {
        //1、first confirm，更新订单状态 = confirmed
        // orderProcessService.confirm()
        //2、testMatrixMode = testData
        //2.1、save analyte
        // analyteService.save()
        //2.2、save condition group
        // conditionService.save()
        //3、updateTestLineSeq
        // testMatrixService.updateTestLineSeq()
        //4、updateMatrixNo
        // testMatrixService.updateMatrixNo()
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse after(TestMatrixContext<OrderIdReq, BaseBO> context) {
        //1、发送SR到CP
        // cpClient.sendSR()
        //2、发送消息到SCI
        // sciClient.confirm()
        //3、发送tracking
        // trackingClient.send()
        //4、记录bizlog
        // bizlogClient.send()
        return super.after(context);
    }


}
