package com.sgs.gpo.domain.service.otsnotes.report.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.report.ReportExtMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportExtPO;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportExtService;
import com.sgs.gpo.facade.model.report.req.ReportIdReq;
import com.sgs.gpo.facade.model.report.req.ReportExtForTLUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportTLCompletedFlagUpdateReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @title: ReportExtServiceImpl
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2024/2/29 8:55
 */
@Service
@Slf4j
public class ReportExtServiceImpl extends ServiceImpl<ReportExtMapper, ReportExtPO> implements IReportExtService {
    @Override
    public BaseResponse<List<ReportExtPO>> query(ReportQueryReq reportQueryReq) {
        Assert.isTrue(Func.isNotEmpty(reportQueryReq), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(reportQueryReq.getReportIdList()), "common.param.miss", new Object[]{"ReportIdList"});
        LambdaQueryWrapper<ReportExtPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ReportExtPO::getReportId, reportQueryReq.getReportIdList());
        List<ReportExtPO> list = this.list(wrapper);
        return BaseResponse.newSuccessInstance(list);
    }

    @Override
    public BaseResponse delete(ReportQueryReq reportQueryReq) {
        Assert.isTrue(Func.isNotEmpty(reportQueryReq), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(reportQueryReq.getReportIdList()), "common.param.miss", new Object[]{"ReportIdList"});
        LambdaQueryWrapper<ReportExtPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ReportExtPO::getReportId, reportQueryReq.getReportIdList());
        return BaseResponse.newSuccessInstance(baseMapper.delete(wrapper));
    }

    /*@Override
    public BaseResponse<Boolean> clearReportLabSection(ReportIdReq reportIdReq) {
        Assert.isTrue(Func.isNotEmpty(reportIdReq), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(reportIdReq.getReportIdList()), "common.param.miss", new Object[]{"ReportIdList"});
        baseMapper.clearReportLabSection(reportIdReq.getReportIdList(), SecurityUtil.getUserAccount());
        return BaseResponse.newSuccessInstance(true);
    }*/
   /* @Override
    public BaseResponse<Boolean> updateReportLabSection(ReportExtForTLUpdateReq reportExtForTLUpdateReq) {
        Assert.isTrue(Func.isNotEmpty(reportExtForTLUpdateReq), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(reportExtForTLUpdateReq.getReportIdList()), "common.param.miss", new Object[]{"ReportIdList"});
        baseMapper.updateReportLabSection(reportExtForTLUpdateReq);
        return BaseResponse.newSuccessInstance(true);
    }*/

}
