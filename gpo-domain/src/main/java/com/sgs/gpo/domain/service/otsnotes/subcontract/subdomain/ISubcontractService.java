package com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractBO;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractIdBO;
import com.sgs.framework.open.platform.base.service.IBaseService;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractPO;
import com.sgs.gpo.facade.model.otsnotes.subcontract.req.SubContractPageReq;
import com.sgs.gpo.facade.model.otsnotes.subcontract.vo.SubContractPageVO;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractQueryReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/7 12:00
 */
public interface ISubcontractService extends IBaseService<SubcontractBO,SubcontractPO, SubcontractIdBO,SubcontractQueryReq> {

    BaseResponse<List<SubcontractPO>> select(SubcontractQueryReq subcontractQueryReq);

    BaseResponse<IPage<SubContractPageVO>> page(IPage<SubContractPageVO> page, SubContractPageReq subContractPageReq);
    BaseResponse<Long> pageCount(SubContractPageReq subContractPageReq);

    BaseResponse<List<SubcontractPO>> query2(SubcontractQueryReq subcontractQueryReq);

    SubcontractPO queryOrderIsSubOrder(String orderNo);

    BaseResponse<List<SubcontractPO>> querySubcontractByTestLine(SubcontractQueryReq subcontractQueryReq);
}
