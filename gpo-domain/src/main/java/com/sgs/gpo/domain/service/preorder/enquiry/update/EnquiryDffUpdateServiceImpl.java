package com.sgs.gpo.domain.service.preorder.enquiry.update;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.order.enquiry.EnquiryBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.annotation.Section;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryProductPO;
import com.sgs.gpo.domain.convertor.ProductConvert;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryProductService;
import com.sgs.gpo.domain.service.preorder.factory.update.UpdateBaseService;
import com.sgs.gpo.domain.service.preorder.factory.update.context.UpdateContext;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Section(order = 0, attribute = "EnquiryDFF")
@AllArgsConstructor
@Service("enquiryDffUpdateServiceImpl")
public class EnquiryDffUpdateServiceImpl extends UpdateBaseService {
    private IEnquiryProductService enquiryProductService;

    @Override
    public BaseResponse update(UpdateContext updateContext) {
        //入参标准统一为BO
        Assert.notNull(updateContext);
        EnquiryBO enquiryBO = updateContext.getEnquiryBO();
        List<EnquiryProductPO> allEnquiryProductPOList = new ArrayList<>();
        if(Func.isNotEmpty(enquiryBO) ){
            if(Func.isNotEmpty(enquiryBO.getProduct())){
                List<EnquiryProductPO> enquiryProductPOList = ProductConvert.convertProductBOToEnquiryProductPO(enquiryBO.getProduct());
                if (Func.isNotEmpty(enquiryProductPOList)) {
                    allEnquiryProductPOList.addAll(enquiryProductPOList);
                }
            }
            if(Func.isNotEmpty(enquiryBO.getSampleList())){
                List<EnquiryProductPO> enquiryProductPOList = ProductConvert.convertSampleBOToEnquiryProductPO(enquiryBO.getSampleList());
                if (Func.isNotEmpty(enquiryProductPOList)) {
                    allEnquiryProductPOList.addAll(enquiryProductPOList);
                }
            }
            allEnquiryProductPOList.forEach(enquiryProductPO -> {
                if(Func.isEmpty(enquiryProductPO.getEnquiryId())){
                    enquiryProductPO.setEnquiryId(enquiryBO.getId().getEnquiryId());
                }
            });
        }
        if(Func.isNotEmpty(allEnquiryProductPOList)){
            enquiryProductService.saveOrUpdateBatch(allEnquiryProductPOList);
        }
        return BaseResponse.newSuccessInstance(true);
    }
}
