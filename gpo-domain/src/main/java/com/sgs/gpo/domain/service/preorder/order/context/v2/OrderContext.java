package com.sgs.gpo.domain.service.preorder.order.context.v2;

import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.common.productsample.CareLabelBO;
import com.sgs.framework.model.common.productsample.ProductSampleBO;
import com.sgs.framework.model.order.trf.TrfBO;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.order.GeneralOrderInstancePO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.attachment.OrderAttachmentPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.customer.CustomerPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.*;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.testrequest.TestRequestContactPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.testrequest.TestRequestPO;
import com.sgs.gpo.facade.model.preorder.externalno.bo.ExternalNoBO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/5 15:34
 */
@Data
public class OrderContext<Input> extends BaseContext<Input, OrderBO> {

    private List<GeneralOrderPO> generalOrderList;
    private List<LabInstancePO> labList;
    private List<SLOrderPO> slOrderList;
    private List<GeneralOrderInstancePO> generalOrderInstanceList;
    private List<OrderPersonPO> orderPersonList;
    private List<CustomerPO> customerList;
    private List<TestRequestPO> testRequestList;
    private List<TestRequestContactPO> testRequestContactList;
    private List<OrderReportReceiverPO> orderReportReceiverList;
    private List<OrderAttachmentPO> orderAttachmentList;
    private List<OrderCrossLabPO> orderCrossLabList;
    private List<ExternalNoBO> externalNoBOList;
    private List<CareLabelBO> careLabelList;
    private List<PaidUpVO> paidUpVOList;
    private List<OrderParcelPO> orderParcelList;
    private List<TrfBO> trfList;
    private List<OrderExtPO> orderExtPOList;
    private List<ProductSampleBO> productSampleBOList;

    private List<OrderBO> orderList;
    private Integer buLanguageId;
}
