package com.sgs.gpo.domain.service.otsnotes.subcontract;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractBO;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractIdBO;
import com.sgs.framework.open.platform.base.service.IDomainService;
import com.sgs.gpo.facade.model.otsnotes.subcontract.req.SubContractPageReq;
import com.sgs.gpo.facade.model.otsnotes.subcontract.req.SubcontractBatchToStarLimsReq;
import com.sgs.gpo.facade.model.otsnotes.subcontract.vo.SubContractPageVO;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractQueryReq;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractReportMatrixQueryReq;

/**
 * <AUTHOR>
 * @title: ISubContractBizService
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/7 17:52
 */
public interface ISubContractDomainService extends IDomainService<SubcontractBO, SubcontractIdBO, SubcontractQueryReq> {
    BaseResponse<IPage<SubContractPageVO>> querySubContractPage(SubContractPageReq subContractPageReq, Integer page, Integer rows);

    /**
     * 根据分包单ID查询ReportMatrix信息
     */
    BaseResponse getReportMatrixBySubcontract(SubcontractReportMatrixQueryReq subcontractReportMatrixQueryReq);

    /**
     * 根据订单号查询分包关系，判断订单是否是分包创建的订单
     */

    /**
     * 获取需要发送邮件的信息封装
     * @param
     * @return
     */
    BaseResponse  getSendEmailModel(SubContractPageReq subContractPageReq);
    /**
     * 获取StarLimsFolders信息
     * @param
     * @return
     */
    BaseResponse getStarLimsFolders(SubContractPageReq subContractPageReq);

    BaseResponse checkStarLimsFolders(SubContractPageReq subContractPageReq);

    /**
     * 批量toStarLims
     */
    BaseResponse batchToStarLims(SubcontractBatchToStarLimsReq subcontractBatchToStarLimsReq);
}
