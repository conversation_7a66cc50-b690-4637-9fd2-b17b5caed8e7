package com.sgs.gpo.domain.service.preorder.order.command;


import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.common.dff.DFFParallelAttrBO;
import com.sgs.framework.model.common.lab.LabBO;
import com.sgs.framework.model.common.object.ObjectIdBO;
import com.sgs.framework.model.common.object.busetting.attribute.ObjectTemplateAttributeBO;
import com.sgs.framework.model.common.object.busetting.attribute.ObjectTemplateAttributeHeaderBO;
import com.sgs.framework.model.common.object.busetting.template.ObjectTemplateBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementReportBO;
import com.sgs.framework.model.order.enquiry.EnquiryBO;
import com.sgs.framework.model.order.enquiry.EnquiryChildrenBO;
import com.sgs.framework.model.order.enquiry.EnquiryFlagBO;
import com.sgs.framework.model.order.enquiry.EnquiryHeaderBO;
import com.sgs.framework.model.order.enquiry.EnquiryIdBO;
import com.sgs.framework.model.order.enquiry.EnquiryMatrixBO;
import com.sgs.framework.model.order.enquiry.EnquiryOthersBO;
import com.sgs.framework.model.order.enquiry.EnquiryParentBO;
import com.sgs.framework.model.order.enquiry.EnquiryRelationshipBO;
import com.sgs.framework.model.order.order.OrderFlagBO;
import com.sgs.framework.model.order.order.OrderOthersBO;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.model.order.v2.OrderChildrenBO;
import com.sgs.framework.model.order.v2.OrderHeaderBO;
import com.sgs.framework.model.order.v2.OrderIdBO;
import com.sgs.framework.model.order.v2.OrderParentBO;
import com.sgs.framework.model.order.v2.OrderRelationshipBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryReportReceiverPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.productinstance.ProductInstancePO;
import com.sgs.gpo.domain.service.preorder.enquiry.command.EnquiryCreateCMD;
import com.sgs.gpo.domain.service.preorder.enquiry.context.EnquiryContext;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.domain.service.preorder.order.context.OrderContext;
import com.sgs.gpo.domain.service.preorder.productinstance.IProductInstanceService;
import com.sgs.gpo.domain.service.setting.object.IObjectTemplateDomainService;
import com.sgs.gpo.facade.model.enums.EnquirySectionTypeEnum;
import com.sgs.gpo.facade.model.preorder.order.req.OrderCopyToEnquiryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.preorder.productsample.req.ProductSampleQueryReq;
import com.sgs.gpo.facade.model.setting.object.req.ObjectBuSettingReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

@Service
@Slf4j
public class OrderCopyToEnquiryCMD extends BaseCommand<OrderContext<OrderCopyToEnquiryReq>> {

    @Autowired
    private IObjectTemplateDomainService objectTemplateDomainService;

    @Autowired
    private IOrderDomainService orderDomainService;

    @Autowired
    private IProductInstanceService productInstanceService;


    @Override
    public BaseResponse validateParam(OrderContext<OrderCopyToEnquiryReq> context) {
        OrderCopyToEnquiryReq copyReq = context.getParam();
        Assert.isTrue(Func.isNotEmpty(copyReq), "common.param.miss", new String[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(context.getLab()), "common.param.miss", new String[]{Constants.TERM.LAB.getCode()});
        // enquiryId必填
        Assert.isTrue(Func.isNotEmpty(copyReq.getOrderId()), "common.param.miss", new String[]{"orderId"});
        Assert.isTrue(Func.isNotEmpty(copyReq.getObject()), "common.param.miss", new String[]{"orderNo"});
        Assert.isTrue(Func.isNotEmpty(copyReq.getObject().getOrderNo()), "common.param.miss", new String[]{"orderNo"});
        // copyNum必须是数字，且大于等于1
        Assert.isTrue(Func.isNotEmpty(copyReq.getNum()), "common.param.miss", new String[]{"copyNum"});
        Assert.isTrue(Func.isNotEmpty(copyReq.getSectionTypeList()), "common.param.miss", new String[]{"sectionTypeList"});
        int copyNum = copyReq.getNum();
        if (copyNum < 1 || copyNum > 100) {
            Assert.isTrue(copyNum >= 1 &&  copyNum <= 100, "common.param.invalid", new String[]{"copyNum"});
        }
        // 查询order的详情，然后转为EnquiryBO的对象
        String orderNo = copyReq.getObject().getOrderNo();
        EnquiryBO originalEnquiry = this.queryOrderAndCovertEnquiry(orderNo);
        if (Func.isEmpty(originalEnquiry)) {
            return BaseResponse.newFailInstance("common.param.invalid", new String[]{"orderId"});
        }
        context.setCopyNum(copyNum);
        context.setOriginalEnquiry(originalEnquiry);
        // lab切换校验
        //判断当前用户是否切换了BU
        // 当前登陆用户的Lab信息
        Integer userLocationId = context.getLab().getLocationId();
        Integer userBuId = context.getLab().getBuId();
        // 当前单据的Lab信息
        Integer enquiryLocationId = originalEnquiry.getLab().getLocationId();
        Integer enquiryBuId = originalEnquiry.getLab().getProductLineId();
//        if (!Func.equalsSafe(userLocationId, enquiryLocationId) || !Func.equalsSafe(userBuId, enquiryBuId)) {
//            return BaseResponse.newFailInstance("BU已经切换，请刷新页面后切换到正确BU下再操作！");
//        }
        // 校验黑名单,HL要求黑名单不允许开单
        // 统一设计查询Object Setting中配置的必填字段
        return BaseResponse.newSuccessInstance(true);

    }

    @Override
    public BaseResponse before(OrderContext<OrderCopyToEnquiryReq> context) {
        // 获取前端传来的item列表
        List<String> sectionTypeList = Optional.ofNullable(context.getParam().getSectionTypeList()).orElse(Lists.newArrayList());

        // 获取enquiry对象下配置的列表
        ObjectBuSettingReq objectBuSettingReq = new ObjectBuSettingReq();
        ObjectIdBO objectId = new ObjectIdBO();
        objectId.setObjectCode(Constants.OBJECT.ORDER.OBJECT_CODE);
        objectBuSettingReq.setObject(objectId);
        objectBuSettingReq.setProductLineCode(context.getLab().getBuCode());
        objectBuSettingReq.setLabCode(context.getLab().getLabCode());

        BaseResponse<ObjectTemplateBO> objectTemplateRes = objectTemplateDomainService.getBuSetting(objectBuSettingReq);


        if (objectTemplateRes == null || objectTemplateRes.getData() == null || Func.isEmpty(objectTemplateRes.getData().getAttributeList())) {
            return BaseResponse.newSuccessInstance(true);
        }
        List<ObjectTemplateAttributeBO> attributeList = objectTemplateRes.getData().getAttributeList();
        // 因为attachment不是配置出来的，手动添加一下
        ObjectTemplateAttributeBO objectTemplateAttributeBO = new ObjectTemplateAttributeBO();
        ObjectTemplateAttributeHeaderBO objectTemplateAttributeHeaderBO = new ObjectTemplateAttributeHeaderBO();
        objectTemplateAttributeHeaderBO.setAttributeCode(EnquirySectionTypeEnum.ATTACHMENT.getCode());
        objectTemplateAttributeBO.setHeader(objectTemplateAttributeHeaderBO);
        attributeList.add(objectTemplateAttributeBO);

        // 提取配置的section类型列表
        List<String> configSectionTypeEnumList = extractSectionTypeEnumList(attributeList);

        // 设置是否copy quotation
        setCopyQuotationFlag(sectionTypeList, context);

        // 比较并找出需要情况的选项
        Set<String> onlyInSectionTypeList = findSectionTypeDifference(sectionTypeList, configSectionTypeEnumList);

        // 根据差异项清空对应的字段
        clearEnquiryFields(context.getOriginalEnquiry(), onlyInSectionTypeList, context);

        return BaseResponse.newSuccessInstance(true);
    }

    private void setCopyQuotationFlag(List<String> sectionTypeList, OrderContext<OrderCopyToEnquiryReq> context) {
        if (Func.isNotEmpty(sectionTypeList) && !sectionTypeList.contains(EnquirySectionTypeEnum.SERVICE_ITEMS.getCode())) {
            context.setCopyQuotationFlag(false);
            return;
        }
        context.setCopyQuotationFlag(true);
    }

/**
 * 提取属性列表中的section类型枚举
 */
private List<String> extractSectionTypeEnumList(List<ObjectTemplateAttributeBO> attributeList) {
    List<String> result = Lists.newArrayList();
    for (ObjectTemplateAttributeBO attribute : attributeList) {
        if (attribute != null && attribute.getHeader() != null && Func.isNotEmpty(attribute.getHeader().getAttributeCode())) {
            EnquirySectionTypeEnum sectionTypeEnum = EnquirySectionTypeEnum.findCode(attribute.getHeader().getAttributeCode());
            if (Func.isNotEmpty(sectionTypeEnum)) {
                result.add(sectionTypeEnum.getCode());
            }
        }
    }
    return result;
}

/**
 * 找出两个集合的差异项
 * 如果list1集合为空或者是传了serviceItem选项，则返回空，默认全部的都要copy
 */
private Set<String> findSectionTypeDifference(List<String> list1, List<String> list2) {
    Set<String> set1 = new HashSet<>(list1);
    Set<String> set2 = new HashSet<>(list2);

    // 检查 set1 是否包含 SERVICE_ITEM
//    if (set1.isEmpty() || set1.contains(EnquirySectionTypeEnum.SERVICE_ITEMS.getCode())) {
//        return Collections.emptySet();
//    }

    // 检查set1中是否包含head，不包含则新增。默认head必须要copy
    if (!set1.contains(EnquirySectionTypeEnum.HEAD.getCode())) {
        set1.add(EnquirySectionTypeEnum.HEAD.getCode());
    }


    // 找出 set1 中有但 set2 中没有的元素
    Set<String> onlyInSet1 = new HashSet<>(set1);
    onlyInSet1.removeAll(set2);

    // 找出 set2 中有但 set1 中没有的元素
    Set<String> onlyInSet2 = new HashSet<>(set2);
    onlyInSet2.removeAll(set1);

    // 合并两个集合
    Set<String> result = new HashSet<>(onlyInSet1);
    result.addAll(onlyInSet2);

    return result;
}


/**
 * 根据差异项清空对应的字段，传入的值是需要清空的数据
 */
private static final Runnable NO_OP = () -> {};

/**
 * 根据差异项清空对应的字段，传入的值是需要清空的数据
 */
private void clearEnquiryFields(EnquiryBO enquiryBo, Set<String> sectionTypesToClear, OrderContext<OrderCopyToEnquiryReq> context) {
    if (enquiryBo == null || Func.isEmpty(sectionTypesToClear)) {
        return;
    }

    ServiceRequirementBO serviceRequirementBo = enquiryBo.getServiceRequirement();
    ServiceRequirementReportBO serviceRequirementReportBo;

    EnquiryReportReceiverPO reportReceiverPO = new EnquiryReportReceiverPO();
    if (Func.isNotEmpty(serviceRequirementBo)) {
        serviceRequirementReportBo = serviceRequirementBo.getReport();
        reportReceiverPO.setId(IdUtil.uuId());
        reportReceiverPO.setReportLanguage(serviceRequirementReportBo != null ? serviceRequirementReportBo.getReportLanguage() : null);
        reportReceiverPO.setReportHeader(serviceRequirementReportBo != null ? serviceRequirementReportBo.getReportHeader() : null);
        reportReceiverPO.setReportDeliveredTo(serviceRequirementReportBo != null ? serviceRequirementReportBo.getReportAddress() : null);
        reportReceiverPO.setReceiverType(serviceRequirementReportBo != null ? serviceRequirementReportBo.getReportType() : null);
    } else {
        serviceRequirementReportBo = null;
    }
    context.setEnquiryReportReceiver(reportReceiverPO);

    Map<String, Runnable> fieldClearMap = new HashMap<>();

    fieldClearMap.put(EnquirySectionTypeEnum.SERVICE_REQUIREMENT.getCode(), () -> {
        if (!sectionTypesToClear.contains(EnquirySectionTypeEnum.PRODUCT_SAMPLE.getCode())) {
            if (Func.isNotEmpty(serviceRequirementBo)) {
                ServiceRequirementReportBO newReport = new ServiceRequirementReportBO();
                newReport.setReportLanguage(reportReceiverPO.getReportLanguage());
                serviceRequirementBo.setReport(newReport);
            }
            return;
        }
        enquiryBo.setServiceRequirement(null);
    });

    fieldClearMap.put(EnquirySectionTypeEnum.PRODUCT_SAMPLE.getCode(), () -> {
        enquiryBo.setSampleList(null);
        enquiryBo.setProduct(null);
        enquiryBo.setDffAttrList(null);
    });

    fieldClearMap.put(EnquirySectionTypeEnum.ATTACHMENT.getCode(), () -> enquiryBo.setAttachmentList(null));

    fieldClearMap.put(EnquirySectionTypeEnum.REPORT_INFO.getCode(), () -> {
        if (!sectionTypesToClear.contains(EnquirySectionTypeEnum.PRODUCT_SAMPLE.getCode())) {
            if (Func.isNotEmpty(reportReceiverPO)) {
                reportReceiverPO.setReportHeader(null);
                reportReceiverPO.setReportDeliveredTo(null);
                reportReceiverPO.setReceiverType(null);
                return;
            }
        }
        if (serviceRequirementBo != null && serviceRequirementBo.getReport() != null) {
            serviceRequirementBo.setReport(null);
        }
        context.setEnquiryReportReceiver(null);
    });

    fieldClearMap.put(EnquirySectionTypeEnum.CUSTOMER.getCode(), () -> {
        // 客户不copy
        enquiryBo.setCustomerList(null);

        // 不copy商机
        Optional.ofNullable(enquiryBo.getRelationship())
                .map(item -> item.getParent())
                .ifPresent(parent -> {
                    List<?> trfList = parent.getTrfList();
                    if (Func.isNotEmpty(trfList)) {
                        trfList.clear();
                    }
                });

        // service requirement中的不开票信息清空
        if (serviceRequirementBo != null) {
            serviceRequirementBo.setIssueInvoiceFlag("1");
        }

        // 报告的抬头不copy
        if (Func.isNotEmpty(serviceRequirementReportBo)) {
            serviceRequirementReportBo.setReportHeader(null);
            serviceRequirementReportBo.setReportAddress(null);
        }
    });

    for (String sectionTypeCode : sectionTypesToClear) {
        fieldClearMap.getOrDefault(sectionTypeCode, NO_OP).run();
    }
}



    @Override
    public BaseResponse execute(OrderContext<OrderCopyToEnquiryReq> context) {
        // groupId更新
        EnquiryContext<EnquiryBO> createContext = new EnquiryContext<>();
        createContext.setParam(context.getOriginalEnquiry());
        createContext.setLab(context.getLab());
        createContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        createContext.setIsNew(context.getIsNew());
        createContext.setUserInfo(SystemContextHolder.getUserInfo());
        createContext.setCopyNum(context.getCopyNum());
        createContext.setRegular(context.getParam().getRegular());
        createContext.setCopyFcm(context.getParam().getCopyFcm());
        createContext.setSalesPerson(context.getParam().getSalesPerson());
        createContext.setSaleUserLabDTO(context.getParam().getSaleUserLabDTO());
        createContext.setEnquiryReportReceiver(context.getEnquiryReportReceiver());
        createContext.setOrderIdBO(context.getParam().getObject());
        createContext.setCopyQuotationFlag(context.isCopyQuotationFlag());
        EnquiryPO newEnquiry = null;
        for(int i = 0; i < context.getCopyNum(); i++) {
            EnquiryPO resEnquiry = (EnquiryPO) BaseExecutor.start(EnquiryCreateCMD.class, createContext).getData();
            if (Func.isEmpty(newEnquiry)) {
                newEnquiry = resEnquiry;
            }
        }
        return BaseResponse.newSuccessInstance(newEnquiry);
    }

    /**
     * 查询订单详情 and 订单详情转换成Enquiry
     * @param orderNo
     * @return
     */
    private EnquiryBO queryOrderAndCovertEnquiry(String orderNo) {
        return covertOrderToEnquiry(queryOrder(orderNo));
    }

    private OrderBO queryOrder(String orderNo) {
        if (Func.isEmpty(orderNo)) {
            return null;
        }
        Set<String> orderNos = new HashSet<>();
        orderNos.add(orderNo);
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setOrderNoList(orderNos);
        List<com.sgs.framework.model.order.v2.OrderBO> orderBOList = orderDomainService.queryBO(orderQueryReq).getData();
        if (Func.isNotEmpty(orderBOList)) {
            return orderBOList.get(0);
        }
        return null;
    }

    private EnquiryBO covertOrderToEnquiry(OrderBO orderBo) {
        return this.buildDomain(orderBo);
    }

    public EnquiryBO buildDomain(OrderBO orderBo) {
        EnquiryBO enquiryBo =new EnquiryBO();
        if (Func.isEmpty(orderBo)) {
           return enquiryBo;
        }
        assembleEnquiryHeader(orderBo, enquiryBo);
        assembleEnquiryLab(orderBo, enquiryBo);
        assembleEnquiryFlags(orderBo, enquiryBo);
        assembleEnquiryCustomer(orderBo, enquiryBo);
        assembleEnquiryContacts(orderBo, enquiryBo);
        // other中有tag
        assembleOthers(orderBo, enquiryBo);
        // relation中有trfno
        assembleRelationShip(orderBo, enquiryBo);
        assembleTestRequest(orderBo, enquiryBo);
        // reportinfo 跟着testrequest一块了
//        assembleReportInfo(orderBo, enquiryBo);
        assembleEnquiryMatrix(orderBo, enquiryBo);
        assembleEnquiryDFF(orderBo, enquiryBo);
        assembleEnquiryAttachment(orderBo, enquiryBo);
        return enquiryBo;
    }

    private void assembleEnquiryHeader(OrderBO orderBo, EnquiryBO enquiryBo) {
        EnquiryHeaderBO enquiryHeader = new EnquiryHeaderBO();
        // EnquiryId
        EnquiryIdBO enquiryId = new EnquiryIdBO();
        if (Func.isNotEmpty(orderBo)) {
            if (Func.isNotEmpty(orderBo.getHeader())) {
                OrderHeaderBO orderHeader = orderBo.getHeader();
//                enquiryId.setEnquiryId(orderHeader.getEnquiryId());
//                enquiryId.setEnquiryNo(orderHeader.getEnquiryNo());

                // 0是新增
                enquiryHeader.setEnquiryStatus(0);
                enquiryHeader.setEnquiryType(orderHeader.getCaseType());
                // groupid赋值copy的order的id
                enquiryHeader.setGroupId(orderHeader.getOrderId());
                enquiryHeader.setCsName(orderHeader.getCsName());
                enquiryHeader.setCsContact(orderHeader.getCsContact());
                enquiryHeader.setCsEmail(orderHeader.getCsEmail());
                enquiryHeader.setSalesPerson(orderHeader.getCsName());
                enquiryHeader.setProductCategory(orderHeader.getProductCategory());
                enquiryHeader.setProductSubCategory(orderHeader.getProductSubCategory());
                // 0 未付款
                enquiryHeader.setPayStatus(0);
                enquiryHeader.setExpectedOrderDueDate(null);
                enquiryHeader.setVatType(null);
                enquiryHeader.setSampleConfirmDate(null);
                enquiryHeader.setSampleReceivingDate(orderHeader.getSampleReceiveDate());
                // orderHeardBo对象上没有对应值
                enquiryHeader.setServiceType(null);
                enquiryHeader.setCertificateProgram(null);
                enquiryHeader.setKaCustomerDeptCode(null);
                enquiryHeader.setNetAmount(null);
                enquiryHeader.setAfterTaxAmount(null);
                enquiryHeader.setFinalAmount(null);
                enquiryHeader.setCurrencyCode(null);
                enquiryHeader.setSubcontractFeeCurrency(null);
                enquiryHeader.setResponsibleTeamCode(null);
                enquiryHeader.setOrganizationName(null);
                enquiryHeader.setTat(orderHeader.getTat());
                enquiryHeader.setTemplateFlag(0);
                enquiryHeader.setParcelNo(null);
            }
            enquiryBo.setHeader(enquiryHeader);
            if (Func.isNotEmpty(orderBo.getId())) {
                OrderIdBO orderId = orderBo.getId();
                // 这里放order的值
                enquiryId.setEnquiryId(orderId.getOrderId());
                enquiryId.setEnquiryNo(orderId.getOrderNo());
            }
        }

        enquiryBo.setId(enquiryId);
    }
    private void assembleEnquiryLab(OrderBO orderBo, EnquiryBO enquiryBo) {
        //lab
        LabBO lab = new LabBO();
        if (Func.isNotEmpty(orderBo)) {
            if (Func.isNotEmpty(orderBo.getLab())) {
                LabBO orderLab = orderBo.getLab();
                Func.copy(orderLab, lab);
            }
        }
        enquiryBo.setLab(lab);
    }
    private void assembleEnquiryFlags(OrderBO orderBo, EnquiryBO enquiryBo) {
        //flags
        EnquiryFlagBO flags = new EnquiryFlagBO();
        if (Func.isNotEmpty(orderBo)) {
            if (Func.isNotEmpty(orderBo.getFlags())) {
                OrderFlagBO orderFlag = orderBo.getFlags();
                Func.copy(orderFlag, flags);
                flags.setToDmFlag(orderFlag.getToDMFlag());
                flags.setNeedToBossFlag(orderFlag.getToBossFlag());
                flags.setSplitOrderFlag(null);
            }
        }
        enquiryBo.setFlags(flags);
    }
    private void assembleEnquiryCustomer(OrderBO orderBo, EnquiryBO enquiryBo) {
        if (Func.isNotEmpty(orderBo) && Func.isNotEmpty(orderBo.getCustomerList())) {
            enquiryBo.setCustomerList(orderBo.getCustomerList());
        }
    }
    private void assembleEnquiryContacts(OrderBO orderBo, EnquiryBO enquiryBo) {
        if (Func.isNotEmpty(orderBo) && Func.isNotEmpty(orderBo.getContactPersonList())) {
            enquiryBo.setContactPersonList(orderBo.getContactPersonList());
        }
    }
    private void assembleRelationShip(OrderBO orderBo, EnquiryBO enquiryBo) {
        EnquiryRelationshipBO relationshipBO = new EnquiryRelationshipBO();
        if (Func.isNotEmpty(orderBo) && Func.isNotEmpty(orderBo.getRelationship())) {
            OrderRelationshipBO orderRelationship = orderBo.getRelationship();
            // TODO
            OrderChildrenBO orderChilder = orderRelationship.getChildren();
            OrderParentBO orderParent = orderRelationship.getParent();
            EnquiryChildrenBO enquiryChildren = new EnquiryChildrenBO();
            relationshipBO.setChildren(enquiryChildren);

            EnquiryParentBO enquiryParent = new EnquiryParentBO();
            enquiryParent.setTrfList(orderParent.getTrfList());
            relationshipBO.setParent(enquiryParent);
        }
        enquiryBo.setRelationship(relationshipBO);
    }
    private void assembleOthers(OrderBO orderBo, EnquiryBO enquiryBo) {
        EnquiryOthersBO others = new EnquiryOthersBO();
        if (Func.isNotEmpty(orderBo) && Func.isNotEmpty(orderBo.getOthers())) {
            OrderOthersBO orderOther = orderBo.getOthers();
            // TODO
            others.setRemark(orderOther.getOrderRemark());
            others.setTrfNo(null);
            others.setTrfSubmissionDate(null);
            others.setTags(orderOther.getTags());
        }
        enquiryBo.setOthers(others);
    }
    private void assembleTestRequest(OrderBO orderBo, EnquiryBO enquiryBo) {
        if (Func.isNotEmpty(orderBo) && Func.isNotEmpty(orderBo.getServiceRequirement())) {
            ServiceRequirementBO orderServiceRequirement = orderBo.getServiceRequirement();
            enquiryBo.setServiceRequirement(orderServiceRequirement);
        }
    }
    private void assembleEnquiryMatrix(OrderBO orderBo, EnquiryBO enquiryBo) {
        List<EnquiryMatrixBO> enquiryMatrixList  = new ArrayList<>();
        // TODO
        enquiryBo.setEnquiryMatrixList(enquiryMatrixList);
    }
    private void assembleEnquiryDFF(OrderBO orderBo, EnquiryBO enquiryBo) {
        List<DFFParallelAttrBO> dffParallelAttrList = new ArrayList<>();
        // dff单独处理，重新查询后再赋值
        if (Func.isNotEmpty(orderBo.getId()) && Func.isNotEmpty(orderBo.getId().getOrderId())) {
            List<ProductInstancePO> productInfstancePOList = listProductInstance(orderBo.getId().getOrderId());
            if (Func.isNotEmpty(productInfstancePOList)) {
                productInfstancePOList.stream().forEach(item -> {
                    DFFParallelAttrBO dFFParallelAttrBO = Func.deepCopy(item, DFFParallelAttrBO.class);
                    dffParallelAttrList.add(dFFParallelAttrBO);
                });
            }
        }
        enquiryBo.setDffAttrList(dffParallelAttrList);
    }

    private void assembleEnquiryAttachment(OrderBO orderBo, EnquiryBO enquiryBo) {
        if (Func.isNotEmpty(orderBo) && Func.isNotEmpty(orderBo.getAttachmentList())) {
            enquiryBo.setAttachmentList(orderBo.getAttachmentList());
        }
    }

    private List<ProductInstancePO> listProductInstance(String orderId) {
        // 查询现有订单的DFF信息
        ProductSampleQueryReq productSampleQueryReq = new ProductSampleQueryReq();
                productSampleQueryReq.setOrderIdList(Sets.newHashSet(orderId));
        BaseResponse<List<ProductInstancePO>> queryOrderProductSampleResponse = productInstanceService.queryOrderProductSample(productSampleQueryReq);
        if (queryOrderProductSampleResponse.isFail()) {
            return null;
        }
        return queryOrderProductSampleResponse.getData();
    }
}
