package com.sgs.gpo.domain.service.otsnotes.testline.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.common.object.ObjectBO;
import com.sgs.framework.model.common.object.ObjectHeaderBO;
import com.sgs.framework.model.common.object.ObjectIdBO;
import com.sgs.framework.model.enums.DataEntryMode;
import com.sgs.framework.model.enums.JobStatus;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.TestLineStatus;
import com.sgs.framework.model.test.citation.CitationBO;
import com.sgs.framework.model.test.testline.v2.TestLineBO;
import com.sgs.framework.model.test.testline.v2.TestLineHeaderBO;
import com.sgs.framework.model.test.testline.v2.TestLineIdBO;
import com.sgs.framework.open.platform.base.service.impl.AbstractBaseService;
import com.sgs.framework.security.annotation.AccessRule;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.framework.tool.utils.StringPool;
import com.sgs.gpo.core.constants.BizLogConstant;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.MatrixActionEnums;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testline.TestLineInstanceMapper;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testline.TestLineMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.job.JobPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.job.JobTestLinePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.order.GeneralOrderInstancePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testline.TestLineInstancePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.TestMatrixPO;
import com.sgs.gpo.dbstorages.mybatis.model.setting.object.ObjectPO;
import com.sgs.gpo.domain.service.otsnotes.job.subdomain.IJobService;
import com.sgs.gpo.domain.service.otsnotes.job.subdomain.IJobTestLineService;
import com.sgs.gpo.domain.service.otsnotes.order.IGeneralOrderInstanceService;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.ITestLineService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixService;
import com.sgs.gpo.facade.model.job.req.JobQueryReq;
import com.sgs.gpo.facade.model.otsnotes.job.req.JobAssignOwnerReq;
import com.sgs.gpo.facade.model.otsnotes.order.OrderInstanceQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testline.dto.PPTestLineDTO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.QueryJobTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineBatchUpdateReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineExpectDueDateReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineLabInReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineLabOutReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineNameReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineRetestReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineUpdateDRFlagReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineUpdateStatusReq;
import com.sgs.gpo.facade.model.otsnotes.testline.rsp.TestLineNameNewRsp;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixProcessReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.gpo.integration.dataentry.DataEntryClient;
import com.sgs.gpo.integration.dataentry.req.DataEntryRetestReq;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.info.BizLogInfo;
import com.sgs.preorder.facade.OrderFacade;
import com.sgs.preorder.facade.model.dto.order.OrderInfoDto;
import com.sgs.preorder.facade.model.req.OrderNosReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/7 12:00
 */
@Service
@Slf4j
public class TestLineServiceImpl
        extends AbstractBaseService<TestLineBO,TestLineInstancePO, TestLineIdBO,TestLineInstanceMapper, OrderTestLineReq>
        implements ITestLineService {
    @Autowired
    private IJobTestLineService jobTestLineService;
    @Autowired
    private TestLineMapper testLineMapper;
    @Autowired
    private BizLogClient bizLogClient;
    @Autowired
    private IGeneralOrderInstanceService generalOrderInstanceService;
    @Autowired
    private OrderFacade orderFacade;
    @Autowired
    private DataEntryClient dataEntryClient;
    @Autowired
    private IJobService jobService;
    @Autowired
    private ITestMatrixService testMatrixService;


    @Override
    public BaseResponse<Integer> updateExpectDueDate(List<TestLineExpectDueDateReq> expectDueDateList) {
        if (Func.isEmpty(expectDueDateList)) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        return BaseResponse.newSuccessInstance(baseMapper.updateExpectDueDate(expectDueDateList));
    }

    @Override
    public BaseResponse<Integer> updateTestLineEngineerByJob(JobAssignOwnerReq jobAssignOwnerReq) {
        if (Func.isEmpty(jobAssignOwnerReq) || Func.isEmpty(jobAssignOwnerReq.getJobNoList())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        //更新TestLineEngineer为空的数据
        return BaseResponse.newSuccessInstance(baseMapper.updateEmptyTestLineEngineerByJob(jobAssignOwnerReq.getJobOwner(), jobAssignOwnerReq.getJobNoList()));
    }

    @Override
    public BaseResponse<List<com.sgs.framework.model.test.testline.TestLineBO>> selectPPTestLine(OrderTestLineReq orderTestLineReq) {
        if (Func.isEmpty(orderTestLineReq)) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        List<com.sgs.framework.model.test.testline.TestLineBO> testLineBOList = testLineMapper.selectPPTestLine(null, orderTestLineReq);
        return BaseResponse.newSuccessInstance(testLineBOList);
    }

    @Override
    @AccessRule
    public BaseResponse<List<TestLineNameNewRsp>> queryTestLineNameList(TestLineNameReq testLineNameReq) {
        if (Func.isEmpty(testLineNameReq) || Func.isEmpty(testLineNameReq.getTestLineNameKey())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        UserInfo userInfo = SecurityContextHolder.getUserInfo();
        if (Func.isEmpty(userInfo)) {
            return BaseResponse.newFailInstance(ResponseCode.TokenExpire);
        }
        testLineNameReq.setLabCode(userInfo.getCurrentLabCode());
        if (Func.isEmpty(testLineNameReq.getLimit())) {
            testLineNameReq.setLimit(20);
        }
        List<TestLineNameNewRsp> testLineNameRsps = baseMapper.queryUsageRecordTestLineNameList(testLineNameReq);
        return BaseResponse.newSuccessInstance(testLineNameRsps);
    }

    @Override
    public BaseResponse<Boolean> updateTestLineForLabOut(TestLineLabOutReq testLineLabOutReq) {
        if (Func.isEmpty(testLineLabOutReq) || Func.isEmpty(testLineLabOutReq.getTestLineInstanceIdList())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        UserInfo user = SystemContextHolder.getUserInfoFillSystem();
        String regionAccount = user.getRegionAccount();
        Set<String> testLineInstanceIdList = testLineLabOutReq.getTestLineInstanceIdList();
        //更新TL：TestEndDate=NOW、TestStatus=Completed、ModifiedBy=currentUser、ModifiedDate=NOW
        TestLineInstancePO testLineInstancePO = new TestLineInstancePO();
        testLineInstancePO.setTestEndDate(new Date());
        testLineInstancePO.setTestLineStatus(TestLineStatus.Completed.getStatus());
        testLineInstancePO.setModifiedDate(new Date());
        testLineInstancePO.setModifiedBy(regionAccount);
        UpdateWrapper<TestLineInstancePO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in(TestLineInstancePO.COLUMN.ID, testLineInstanceIdList);
        baseMapper.update(testLineInstancePO, updateWrapper);
        //更新TL ValidateBy 为空的数据=currentUser
        updateWrapper.clear();
        updateWrapper.in(TestLineInstancePO.COLUMN.ID, testLineInstanceIdList);
        updateWrapper.and(wq -> { wq.isNull(TestLineInstancePO.COLUMN.VALIDATE_BY).or().eq(TestLineInstancePO.COLUMN.VALIDATE_BY, "");
        });
        testLineInstancePO = new TestLineInstancePO();
        testLineInstancePO.setValidateBy(regionAccount);
        baseMapper.update(testLineInstancePO, updateWrapper);
        //更新TL Engineer为空的数据
        if (Func.isNotEmpty(testLineLabOutReq.getEngineer())) {
            updateWrapper.clear();
            updateWrapper.in(TestLineInstancePO.COLUMN.ID, testLineInstanceIdList);
            updateWrapper.and(wq -> {
                wq.isNull(TestLineInstancePO.COLUMN.ENGINEER).or().eq(TestLineInstancePO.COLUMN.ENGINEER, "");
            });
            testLineInstancePO = new TestLineInstancePO();
            testLineInstancePO.setEngineer(testLineLabOutReq.getEngineer());
            baseMapper.update(testLineInstancePO, updateWrapper);
        }
        TestMatrixProcessReq testMatrixProcessReq = new TestMatrixProcessReq();
        testMatrixProcessReq.setNewMatrixStatus(TestLineStatus.Completed.getStatus());
        testMatrixProcessReq.setTestLineInstanceIdList(testLineInstanceIdList);
        testMatrixService.updateMatrixStatus(testMatrixProcessReq);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse<List<TestLineInstancePO>> queryTestLineBaseByJob(QueryJobTestLineReq jobTestLineReq) {
        if(Func.isEmpty(jobTestLineReq) || Func.isEmpty(jobTestLineReq.getJobIdList())){
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        List<TestLineInstancePO>  testLineInstancePOList = new ArrayList<>();
        List<JobTestLinePO> jobTestLinePOList = jobTestLineService.query(jobTestLineReq).getData();
        if(Func.isNotEmpty(jobTestLinePOList)){
            Set<String> testLineInstanceIdList = jobTestLinePOList.stream().map(JobTestLinePO::getTestLineInstanceId).collect(Collectors.toSet());
            testLineInstancePOList = this.queryTestLineBase(testLineInstanceIdList).getData();
        }
        return BaseResponse.newSuccessInstance(testLineInstancePOList);
    }
    public BaseResponse<List<TestLineInstancePO>> queryTestLineBase(Set<String> testLineInstanceIdList){
        if(Func.isEmpty(testLineInstanceIdList) ){
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        QueryWrapper<TestLineInstancePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(TestLineInstancePO.COLUMN.ID,testLineInstanceIdList);
        List<TestLineInstancePO> testLineInstancePOS = baseMapper.selectList(queryWrapper);
        return BaseResponse.newSuccessInstance(testLineInstancePOS);
    }
    public BaseResponse<List<TestLineInstancePO>> queryTestLineBaseByOrderId(Set<String> orderInstanceIdList){
        if(Func.isEmpty(orderInstanceIdList) ){
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        QueryWrapper<TestLineInstancePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(TestLineInstancePO.COLUMN.GENERAL_ORDER_INSTANCE_ID,orderInstanceIdList);
        List<TestLineInstancePO> testLineInstancePOS = baseMapper.selectList(queryWrapper);
        return BaseResponse.newSuccessInstance(testLineInstancePOS);
    }

    @Override
    public BaseResponse<List<TestLineInstancePO>> batchUpdateTestLineStatus(TestLineUpdateStatusReq testLineUpdateStatusReq) {
        if(Func.isEmpty(testLineUpdateStatusReq) || Func.isEmpty(testLineUpdateStatusReq.getTestLineInstanceIdList()) || Func.isEmpty(testLineUpdateStatusReq.getTestLineStatus())){
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        TestLineInstancePO testLineInstancePO = new TestLineInstancePO();
        testLineInstancePO.setTestLineStatus(testLineUpdateStatusReq.getTestLineStatus());
        testLineInstancePO.setModifiedDate(new Date());
        testLineInstancePO.setModifiedBy(SecurityUtil.getUserAccount());
        String action = testLineUpdateStatusReq.getAction();
        MatrixActionEnums matrixActionEnum = MatrixActionEnums.getAction(action);
        if(Func.isNotEmpty(matrixActionEnum) && Func.equalsSafe(SystemContextHolder.getBuCode(), ProductLineType.MR.getProductLineAbbr())){
            switch (matrixActionEnum) {
                case Validate:
                    testLineInstancePO.setTestEndDate(new Date());
                    break;
            }
        }
        if(Func.isNotEmpty(testLineUpdateStatusReq.getTestLineInstanceIdList())){
            log.info("批量更新TL状态，action={},testLineInstancePO={},TestLineInstanceId={}",action, JSON.toJSONString(testLineInstancePO),testLineUpdateStatusReq.getTestLineInstanceIdList());
            testLineMapper.batchUpdateTestLineForProcess(testLineInstancePO,testLineUpdateStatusReq.getTestLineInstanceIdList());
        }
        //记录TestLine变化Bizlog
        List<GeneralOrderInstancePO> generalOrderInstancePOList = new ArrayList<>();
        try{
            Set<String> testLineInstanceIdList = testLineUpdateStatusReq.getTestLineInstanceIdList();
            BaseResponse<List<TestLineInstancePO>> queryTestLineBase = this.queryTestLineBase(testLineInstanceIdList);
            if(queryTestLineBase.isSuccess() && Func.isNotEmpty(queryTestLineBase.getData())){
                List<TestLineInstancePO> testLineInstancePOList = queryTestLineBase.getData();
                Set<String> gpnOrderIdList = testLineInstancePOList.stream().map(TestLineInstancePO::getGeneralOrderInstanceId).collect(Collectors.toSet());
                if(Func.isNotEmpty(generalOrderInstanceService)){
                    OrderInstanceQueryReq orderInstanceQueryReq = new OrderInstanceQueryReq();
                    orderInstanceQueryReq.setOrderIdList(gpnOrderIdList);
                    BaseResponse<List<GeneralOrderInstancePO>> orderRes = generalOrderInstanceService.select(orderInstanceQueryReq);
                    if(orderRes.isSuccess() && Func.isNotEmpty(orderRes.getData())){
                        generalOrderInstancePOList = orderRes.getData();
                    }
                }
                BizLogInfo bizLog = null;
                String bizNewVal = "";
                if(Func.isNotEmpty(generalOrderInstancePOList)){
                    List<String> orderNos = generalOrderInstancePOList.stream().map(GeneralOrderInstancePO::getOrderNo).collect(Collectors.toList());
                    // 查询generalOrder信息
                    OrderNosReq orderNosReq = new OrderNosReq();
                    orderNosReq.setOrderNos(orderNos);
                    BaseResponse<List<OrderInfoDto>> rspResult = orderFacade.getOrderListByOrderNos(orderNosReq);
                    List<OrderInfoDto> orderInfoDtos = null;
                    if (Func.isNotEmpty(rspResult) && Func.isNotEmpty(rspResult.getData())){
                        orderInfoDtos = rspResult.getData();
                    }
                    for (GeneralOrderInstancePO generalOrderInstancePO : generalOrderInstancePOList) {
                        List<TestLineInstancePO> collect = testLineInstancePOList.stream().filter(item -> Func.equalsSafe(item.getGeneralOrderInstanceId(), generalOrderInstancePO.getId())).collect(Collectors.toList());
                        String testItemNos = collect.stream().map(TestLineInstancePO::getTestItemNo).distinct().collect(Collectors.joining(","));
                        OrderInfoDto orderInfoDto = null;
                        if(Func.isNotEmpty(orderInfoDtos)) {
                            orderInfoDto = orderInfoDtos.stream().filter(e -> Func.equals(e.getOrderNo(), generalOrderInstancePO.getOrderNo())).findFirst().orElse(null);
                        }
                        //OperationBizLog
                        bizLog = new BizLogInfo();
                        bizLog.setBizOpType(BizLogConstant.TEST_HISTORY);
                        bizLog.setBu((Func.isNotEmpty(orderInfoDto) && Func.isNotEmpty(orderInfoDto.getBUCode())) ? orderInfoDto.getBUCode() : SecurityUtil.getProductLine());
                        bizLog.setLab((Func.isNotEmpty(orderInfoDto) && Func.isNotEmpty(orderInfoDto.getLocationCode())) ? orderInfoDto.getLocationCode() : SecurityUtil.getLocation());
                        bizLog.setOpUser(SecurityContextHolder.getUserInfoFillSystem().getRegionAccount());
                        bizLog.setBizId(generalOrderInstancePO.getOrderNo());
                        String opType = testLineUpdateStatusReq.getAction();
                        if(Func.isNotEmpty(testLineUpdateStatusReq.getTrigger())){
                            opType = testLineUpdateStatusReq.getTrigger();
                        }
                        bizLog.setOpType(opType);
                        bizNewVal = "TestItemNo["+testItemNos+"]";
                        String remark = testLineUpdateStatusReq.getRemark();
                        if(Func.isNotEmpty(remark)){
                            bizNewVal += ",Remark["+remark+"]";
                        }
                        bizLog.setNewVal(bizNewVal);
                        bizLogClient.doSend(bizLog);
                    }
                }


                //Status Change BizLog
                for (TestLineInstancePO lineInstancePO : testLineInstancePOList) {
                    bizLog = new BizLogInfo();
                    bizLog.setBizOpType(BizLogConstant.TEST_LINE_STATUS_CHANGE_HISTORY);
                    bizLog.setBu(SecurityUtil.getProductLine());
                    bizLog.setLab(SecurityUtil.getLocation());
                    bizLog.setOpUser(SecurityContextHolder.getUserInfoFillSystem().getRegionAccount());
                    bizLog.setBizId(lineInstancePO.getTestItemNo());
                    bizLog.setOpType("TestLine Status Change");
                    bizNewVal = com.sgs.otsnotes.facade.model.enums.TestLineStatus.getMessage(testLineUpdateStatusReq.getTestLineStatus());
                    String originVal = com.sgs.otsnotes.facade.model.enums.TestLineStatus.getMessage(lineInstancePO.getTestLineStatus());
                    bizLog.setNewVal(bizNewVal);
                    bizLog.setOriginalVal(originVal);
                    bizLogClient.doSend(bizLog);
                }
            }

        }catch (Exception e){
            log.error("Record TestLine Status BizLog Error:{}",e);
        }


        return BaseResponse.newSuccessInstance(true);
    }



    @Override
    public BaseResponse<List<String>> labIn(TestLineLabInReq testLineLabInReq) {
        List<TestLineInstancePO> testLineLabInList=null;
        if (Func.isNotEmpty(testLineLabInReq.getTestLineInstanceIdList())) {
            QueryWrapper<TestLineInstancePO> queryWrapper = new QueryWrapper<>();
            queryWrapper.in(TestLineInstancePO.COLUMN.ID,testLineLabInReq.getTestLineInstanceIdList());
            testLineLabInList = baseMapper.selectList(queryWrapper);
        }
        //封装需要更新labin的job列表
        List<TestLineInstancePO> needLabInTestLines=null;
        //封装TL的错误信息
        List<TestLineInstancePO> errorTestLines=null;
        if (Func.isNotEmpty(testLineLabInList)) {
            errorTestLines = testLineLabInList.stream().filter(testLineInstancePO -> {
                return testLineInstancePO.getPendingFlag();
            }).collect(Collectors.toList());
        }

        if (Func.isNotEmpty(errorTestLines)) {
            StringBuilder  errorMsg = new StringBuilder();
            errorMsg.append("test line [");
            errorTestLines.stream().forEach(testLineInstance -> {
                errorMsg.append(testLineInstance.getTestItemNo()).append(";");
            });
            errorMsg.append("] is pending, can't lab in!");
            return BaseResponse.newFailInstance(errorMsg.toString());
        }

        needLabInTestLines = testLineLabInList.stream().filter(testLineInstancePO -> {
            return Func.isEmpty(testLineInstancePO.getTestStartDate());
        }).collect(Collectors.toList());

        List<String> tlInstanceLabIns=null;
        if (Func.isNotEmpty(needLabInTestLines)) {
            tlInstanceLabIns = needLabInTestLines.stream().map(TestLineInstancePO::getId).collect(Collectors.toList());
        }

        if (Func.isNotEmpty(tlInstanceLabIns)) {
            UserInfo user = SystemContextHolder.getUserInfo();
            Date nowDate = DateUtil.now();
            Date testStartDate = testLineLabInReq.getTestStartDate();
            if(Func.isEmpty(testStartDate)){
                testStartDate = DateUtil.now();
            }
            TestLineInstancePO testLineLabInPo = new TestLineInstancePO();
            testLineLabInPo.setTestStartDate(testStartDate);
            if (Func.isNotEmpty(testLineLabInReq.getEngineer())) {
                testLineLabInPo.setEngineer(testLineLabInReq.getEngineer());
            }
            testLineLabInPo.setModifiedBy(user.getRegionAccount());
            testLineLabInPo.setModifiedDate(nowDate);
            UpdateWrapper<TestLineInstancePO> updateWrapper = new UpdateWrapper<TestLineInstancePO>();
            updateWrapper.in(TestLineInstancePO.COLUMN.ID, tlInstanceLabIns);
            int updateFlag= baseMapper.update(testLineLabInPo, updateWrapper);
            if (updateFlag<1) {
                throw new BizException("tl labIn 更新Job 状态失败");
            }
        }

        return  BaseResponse.newSuccessInstance(tlInstanceLabIns);
    }

    @Override
    public BaseResponse<Integer> rollTestLineBackLabIn(TestLineLabInReq testLineLabInReq) {
        if (Func.isEmpty(testLineLabInReq) || Func.isEmpty(testLineLabInReq.getTestLineInstanceIdList())) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"testLineInstanceIdList"});
        }
        int i = baseMapper.updateTestStartDateAndEngineer(testLineLabInReq);
        return BaseResponse.newSuccessInstance(i);
    }

    @Override
    public BaseResponse<List<String>> labOut(TestLineLabOutReq testLineLabOutReq) {
        List<TestLineInstancePO> testLineLabOutList = Lists.newArrayList();
        if(Func.isNotEmpty(testLineLabOutReq.getTestItemNoList()) || Func.isNotEmpty(testLineLabOutReq.getTestLineInstanceIdList())){
            LambdaQueryWrapper<TestLineInstancePO> queryWrapper = new LambdaQueryWrapper<>();
            if(Func.isNotEmpty(testLineLabOutReq.getTestItemNoList())){
                queryWrapper.in(TestLineInstancePO::getTestItemNo,testLineLabOutReq.getTestItemNoList());
            }
            if(Func.isNotEmpty(testLineLabOutReq.getTestLineInstanceIdList())){
                queryWrapper.in(TestLineInstancePO::getId, testLineLabOutReq.getTestLineInstanceIdList());
            }
            testLineLabOutList = baseMapper.selectList(queryWrapper);
        }
        //需要更新LabOut的TestLineList
        List<TestLineInstancePO> needLabOutTestLines = Lists.newArrayList();
        //不允许LabOut的TestLineList
        List<TestLineInstancePO> errorTestLines = Lists.newArrayList();
        if(Func.isNotEmpty(testLineLabOutList)){
            errorTestLines = testLineLabOutList.stream().filter(e -> {
                return e.getPendingFlag();
            }).collect(Collectors.toList());
        }
        if(Func.isNotEmpty(errorTestLines)){
            StringBuilder  errorMsg = new StringBuilder();
            errorMsg.append("test line [");
            errorTestLines.stream().forEach(testLineInstance -> {
                errorMsg.append(testLineInstance.getTestItemNo()).append(";");
            });
            errorMsg.append("] is pending, can't lab out!");
            return BaseResponse.newFailInstance(errorMsg.toString());
        }

        return null;
    }

    /**
     * 更新TestLine Document ReviewFlag
     *
     * @param testLineUpdateDRFlagReq
     * @return
     */
    @Override
    public BaseResponse<Integer> updateDRFlag(TestLineUpdateDRFlagReq testLineUpdateDRFlagReq) {
        Assert.isTrue(Func.isNotEmpty(testLineUpdateDRFlagReq) && Func.isNotEmpty(testLineUpdateDRFlagReq.getTestLineInstanceId()), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        TestLineInstancePO testLineInstancePO = new TestLineInstancePO();
        testLineInstancePO.setId(testLineUpdateDRFlagReq.getTestLineInstanceId());
        testLineInstancePO.setDocumentReviewFlag(testLineUpdateDRFlagReq.getDocumentReviewFlag());
        testLineInstancePO.setModifiedDate(new Date());
        testLineInstancePO.setModifiedBy(SecurityContextHolder.getUserInfoFillSystem().getRegionAccount());
        int i = baseMapper.updateById(testLineInstancePO);
        return BaseResponse.newSuccessInstance(i);
    }
    /**
     * 同步TestLine使用记录
     * @return BaseResponse 返回同步操作的结果信息
     */
    @Override
    public BaseResponse syncTestLineUsageRecord() {
        int result = baseMapper.syncTestLineUsageRecord();
        return BaseResponse.newSuccessInstance(result);
    }

    @Override
    public BaseResponse<List<PPTestLineDTO>> selectBasePPTestLine(OrderTestLineReq orderTestLineReq) {
        if (Func.isEmpty(orderTestLineReq) ||
                Func.isAllEmpty(orderTestLineReq.getOrderId(),orderTestLineReq.getOrderIdList(),
                        orderTestLineReq.getOrderNoList(),orderTestLineReq.getTestLineInstanceIdList())) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"orderTestLineReq"});
        }
        List<PPTestLineDTO> list = testLineMapper.selectBasePPTestLine(orderTestLineReq);
        return BaseResponse.newSuccessInstance(list);
    }

    @Override
    public BaseResponse testLineRetest(TestLineRetestReq testLineRetestReq) {
        if (Func.isEmpty(testLineRetestReq) || Func.isEmpty(testLineRetestReq.getTestLineInstanceIdList())) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"testLineRetestReq"});
        }
        Set<String> testLineInstanceIdList = testLineRetestReq.getTestLineInstanceIdList();
        //查询TestLine对应的job
        JobQueryReq jobQueryReq = new JobQueryReq();
        jobQueryReq.setTestLineInstanceIdList(testLineInstanceIdList);
        BaseResponse<List<JobPO>> jobRes = jobService.queryJobByTestLine(jobQueryReq);
        if (Func.isEmpty(jobRes) || Func.isEmpty(jobRes.getData())){
            return BaseResponse.newFailInstance("job.not.find");
        }
        //查询job, 如果job状态不是Closed，则不允许操作
        List<String> jobNoList = jobRes.getData().stream().filter(jobPO -> !JobStatus.check(jobPO.getJobStatus(), JobStatus.Closed)).map(JobPO::getJobNo).collect(Collectors.toList());
        if (Func.isNotEmpty(jobNoList)){
            String jobNos = Func.join(jobNoList, StringPool.COMMA);
            return BaseResponse.newFailInstance("job.status.not.match",new Object[]{jobNos,"Closed"});
        }
        //查询TestLineInstanceId对应的matrix
        TestMatrixQueryReq testMatrixQueryReq = new TestMatrixQueryReq();
        testMatrixQueryReq.setTestLineInstanceIdList(testLineInstanceIdList);
        BaseResponse<List<TestMatrixPO>> testMatrixResponse = testMatrixService.queryList(testMatrixQueryReq);
        if (Func.isEmpty(testMatrixResponse) || Func.isEmpty(testMatrixResponse.getData())) {
            return BaseResponse.newFailInstance("test.matrix.not.exist");
        }
        Set<String> testMatrixIdList = testMatrixResponse.getData().stream().map(TestMatrixPO::getId).collect(Collectors.toSet());
        //调用DataEntry接口
        DataEntryRetestReq dataEntryRetestReq = new DataEntryRetestReq();
        dataEntryRetestReq.setDataEntryMode(DataEntryMode.ProtocolResultEntry.getMode());
        if(Func.isNotEmpty(testLineRetestReq.getProductLineCode())){
            dataEntryRetestReq.setProductLineCode(testLineRetestReq.getProductLineCode());
        }else{
            dataEntryRetestReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        }
        dataEntryRetestReq.setTestLineInstanceIds(testLineInstanceIdList);
        dataEntryRetestReq.setTestMatrixIdList(testMatrixIdList);
        return dataEntryClient.retest(dataEntryRetestReq);
    }

    @Override
    public BaseResponse updateTestLineRemark(TestLineBatchUpdateReq testLineBatchUpdateReq) {
        if(Func.isEmpty(testLineBatchUpdateReq) || Func.isEmpty(testLineBatchUpdateReq.getTestLineInstanceIdList())){
            return BaseResponse.newFailInstance("common.param.miss", new Object[]{"TestLineInstanceId"});
        }
        TestLineInstancePO testLineInstancePO = new TestLineInstancePO();
        testLineInstancePO.setOrderTestLineRemark(Func.toStr(testLineBatchUpdateReq.getOrderTestLineRemark()));
        testLineInstancePO.setModifiedBy(SecurityContextHolder.getUserInfoFillSystem().getRegionAccount());
        testLineInstancePO.setModifiedDate(new Date());
        UpdateWrapper<TestLineInstancePO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in(TestLineInstancePO.COLUMN.ID,testLineBatchUpdateReq.getTestLineInstanceIdList());
        baseMapper.update(testLineInstancePO,updateWrapper);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public List<TestLineBO> convertToBO(Collection<TestLineInstancePO> poList) {
        List<TestLineBO> testLineBOList = Lists.newArrayList();
        if(Func.isNotEmpty(poList)){
            poList.forEach(testLinePO -> {
                TestLineBO testLineBO = new TestLineBO();
                //ID
                TestLineIdBO testLineIdBO = new TestLineIdBO();
                testLineIdBO.setId(testLinePO.getId());
                testLineIdBO.setTestItemNo(testLinePO.getTestItemNo());
                testLineIdBO.setTestLineInstanceId(testLinePO.getId());
                testLineIdBO.setTestLineId(testLinePO.getTestLineId());
                testLineIdBO.setTestLineVersionId(testLinePO.getTestLineVersionId());
                testLineIdBO.setTestLineBaseId(testLinePO.getTestLineBaseId());
                testLineBO.setId(testLineIdBO);
                //Relationship 因表中没关系不需要处理
                //Header
                TestLineHeaderBO testLineHeaderBO = new TestLineHeaderBO();
                testLineHeaderBO.setTestLineStatus(testLinePO.getTestLineStatus());
                testLineHeaderBO.setEngineer(testLinePO.getEngineer());
                testLineHeaderBO.setTestLineRemark(testLinePO.getOrderTestLineRemark());
                testLineHeaderBO.setTestLineSeq(testLinePO.getTestLineSeq());
                testLineHeaderBO.setTestLineType(testLinePO.getTestLineType());
                testLineHeaderBO.setProductLineAbbr(testLinePO.getProductLineAbbr());
                testLineHeaderBO.setPendingFlag(testLinePO.getPendingFlag());
                testLineHeaderBO.setEngineer(testLinePO.getEngineer());
                testLineHeaderBO.setLabTeam(testLinePO.getLabTeamCode());
                //TODO 修改对象类型
                testLineHeaderBO.setSampleSegegrationWIID(testLinePO.getSampleSegegrationWIId()+"");
                testLineHeaderBO.setSampleSegegrationWIText(testLinePO.getSampleSegegrationWIText());
                testLineHeaderBO.setCreatedBy(testLinePO.getCreatedBy());
                testLineHeaderBO.setCreatedDate(testLinePO.getCreatedDate());
                testLineBO.setHeader(testLineHeaderBO);

                //Citation
                CitationBO citationBO = new CitationBO();
                citationBO.setCitationBaseId(testLinePO.getCitationBaseId());
                citationBO.setCitationId(testLinePO.getCitationId());
                citationBO.setCitationVersionId(testLinePO.getCitationVersionId());
                citationBO.setCitationType(testLinePO.getCitationTypeId());
                testLineBO.setCitation(citationBO);

                testLineBOList.add(testLineBO);
            });
        }
        return testLineBOList;
    }

    @Override
    public List<TestLineInstancePO> convertToPO(Collection<TestLineBO> boList) {
        List<TestLineInstancePO> testLineInstancePOList = Lists.newArrayList();
        if(Func.isNotEmpty(boList)){
            UserInfo userInfo = SystemContextHolder.getUserInfo();
            boList.forEach(testLineBO -> {
                //TODO 补充其它字段
                TestLineInstancePO testLineInstancePO = new TestLineInstancePO();
                //ID
                TestLineIdBO testLineIdBO = testLineBO.getId();
                String testLineInstanceId = testLineIdBO.getTestLineInstanceId();
                if(Func.isNotEmpty(testLineBO.getId())&&Func.isNotEmpty(testLineInstanceId)){
                    testLineInstancePO.setId(testLineInstanceId);
                    testLineInstancePO.setModifiedDate(new Date());
                    testLineInstancePO.setModifiedBy(userInfo.getRegionAccount());
                }else{
                    testLineInstancePO.setId(IdUtil.uuId());
                    //TODO 生成TestItemNo
                    testLineInstancePO.setCreatedDate(new Date());
                    testLineInstancePO.setCreatedBy(userInfo.getRegionAccount());
                    testLineInstancePO.setTestLineStatus(TestLineStatus.Typing.getStatus());
                }
                //TestLine
                testLineInstancePO.setTestLineId(testLineIdBO.getTestLineId());
                testLineInstancePO.setTestLineVersionId(testLineIdBO.getTestLineVersionId());
                testLineInstancePO.setTestLineBaseId(testLineIdBO.getTestLineBaseId());
                //TODO 多语言
                TestLineHeaderBO testLineHeaderBO = testLineBO.getHeader();
                if(Func.isNotEmpty(testLineHeaderBO)) {
                    testLineInstancePO.setCustomerTestLineName(testLineHeaderBO.getEvaluationAlias());
                    testLineInstancePO.setCustomerTestLineNameCN("");

                    //WI
                    testLineInstancePO.setSampleSegegrationWIId(Func.toLong(testLineHeaderBO.getSampleSegegrationWIID()));
                    testLineInstancePO.setSampleSegegrationWIText(testLineHeaderBO.getSampleSegegrationWIText());
                }

                //Citation
                CitationBO citationBO = testLineBO.getCitation();
                Assert.notNull(citationBO);
                testLineInstancePO.setCitationBaseId(citationBO.getCitationBaseId());
                testLineInstancePO.setCitationVersionId(citationBO.getCitationVersionId());
                testLineInstancePO.setCitationId(citationBO.getCitationId());
                testLineInstancePO.setCitationTypeId(citationBO.getCitationType());


                testLineInstancePOList.add(testLineInstancePO);
            });
        }
        return testLineInstancePOList;
    }

    @Override
    public LambdaQueryWrapper<TestLineInstancePO> createWrapper(OrderTestLineReq queryReq) {
        Assert.isTrue(Func.isNotEmpty(queryReq),"common.param.miss",new Object[]{Constants.TERM.REQUEST.getCode()});
        LambdaQueryWrapper<TestLineInstancePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        Assert.notNull(queryReq.getIdList(),"common.param.miss",new Object[]{Constants.TERM.REQUEST.getCode()});

        if(Func.isNotEmpty(queryReq.getIdList())) {
            Set<String> idList = queryReq.getIdList().stream().map(TestLineIdBO::getTestLineInstanceId).filter(Objects::nonNull).collect(Collectors.toSet());
            if(Func.isNotEmpty(idList)) {
                lambdaQueryWrapper.in(TestLineInstancePO::getId, idList);
            }
        }

        return lambdaQueryWrapper;
    }
}
