package com.sgs.gpo.domain.service.otsnotes.testline;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.test.testline.v2.TestLineBO;
import com.sgs.framework.model.test.testline.v2.TestLineIdBO;
import com.sgs.framework.open.platform.base.service.IProcessService;
import com.sgs.gpo.facade.model.otsnotes.common.rsp.StatusControlRsp;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineProcessReq;

/**
 * <AUTHOR>
 * @date 2023/7/6 12:29
 */
public interface ITestLineProcessService extends IProcessService<TestLineBO, TestLineIdBO> {
    BaseResponse<StatusControlRsp> toNa(TestLineProcessReq testLineProcessReq);
}
