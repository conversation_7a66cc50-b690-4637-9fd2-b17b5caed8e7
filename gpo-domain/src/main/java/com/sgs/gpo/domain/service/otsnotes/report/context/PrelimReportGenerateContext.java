package com.sgs.gpo.domain.service.otsnotes.report.context;


import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.order.order.OrderBO;
import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.PrelimResultPO;
import com.sgs.gpo.facade.model.digital.dto.GenerateReportTemplateDTO;
import com.sgs.gpo.integration.digital.req.v2.DigitalReportReq;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class PrelimReportGenerateContext<Input> extends BaseContext<Input, ReportBO> {

    /**
     * 生成报告使用的上下文参数
     */
    private PrelimResultPO prelimResult;
    private GenerateReportTemplateDTO template;
    private List<DigitalReportReq> digitalReportReqList;

    // Report关联的TestLine的集合
    private Set<String> testLineInsIdList;
    // Report 关联的Matrix集合
    private Set<String> testMatrixIds;
    private String objectAttachmentId;
    private OrderBO orderBO;
}
