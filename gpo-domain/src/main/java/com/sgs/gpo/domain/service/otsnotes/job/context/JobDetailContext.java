package com.sgs.gpo.domain.service.otsnotes.job.context;

import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.order.order.OrderBO;
import com.sgs.framework.model.test.execution.JobBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.job.JobPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.job.JobTestLinePO;
import com.sgs.priceengine.facade.model.DTO.TLAmountDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/3 09:02
 */
@Data
public class JobDetailContext<Input> extends BaseContext<Input, JobBO> {
    private String testLineGroupModel = Constants.OBJECT.JOB.TESTLINE_GROUP_MODE.LAB_SECTION;
    /**
     * 是否创建新Job
     */
    private boolean newJobFlag;
    /**
     * Job关联的Order信息
     */
    private OrderBO orderBO;
    /**
     * 持久化Job对象
     */
    private JobPO jobPO;
    /**
     * 订单下测试项集合
     */
    private List<TestLineBO> orderTestLineList;
    /**
     * 持久化Job关联的测试项集合
     */
    private List<JobTestLinePO> jobTestLineList;
    /**
     * 测试项金额集合
     */
    private List<TLAmountDTO> tlAmountList;
}
