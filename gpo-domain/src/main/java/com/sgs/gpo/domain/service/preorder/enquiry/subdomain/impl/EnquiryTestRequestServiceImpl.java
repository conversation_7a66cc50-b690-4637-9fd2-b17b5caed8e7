package com.sgs.gpo.domain.service.preorder.enquiry.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.enquiry.EnquiryTestRequestMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryTestRequestPO;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryTestRequestService;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class EnquiryTestRequestServiceImpl extends ServiceImpl<EnquiryTestRequestMapper, EnquiryTestRequestPO>
        implements IEnquiryTestRequestService {
    @Override
    public List<EnquiryTestRequestPO> select(EnquiryIdReq enquiryIdReq) {
        if (Func.isEmpty(enquiryIdReq) || Func.isEmpty(enquiryIdReq.getEnquiryIdList())) {
            return null;
        }
        LambdaQueryWrapper<EnquiryTestRequestPO> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EnquiryTestRequestPO::getEnquiryId, enquiryIdReq.getEnquiryIdList());
        return this.baseMapper.selectList(wrapper);
    }
}
