package com.sgs.gpo.domain.service.otsnotes.report.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.report.ReportCertificateMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportCertificatePO;
import com.sgs.gpo.dbstorages.mybatis.model.setting.object.process.ObjectProcessPO;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportCertificateService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.facade.model.enums.ReportCertificateStatus;
import com.sgs.gpo.facade.model.otsnotes.report.req.ReportCertificateQueryReq;
import com.sgs.gpo.facade.model.otsnotes.report.req.ReportCertificateSaveReq;
import com.sgs.gpo.facade.model.otsnotes.report.req.ReportCertificateUpdateReq;
import com.sgs.gpo.facade.model.otsnotes.report.rsp.ReportCertificateRsp;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.gpo.integration.framework.FrameworkClient;
import com.sgs.gpo.integration.framework.req.GenerateNumberReq;
import com.sgs.gpo.integration.framework.rsp.CertificateTypeRsp;
import com.sgs.preorder.facade.model.info.DataDictInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class ReportCertificateServiceImpl extends ServiceImpl<ReportCertificateMapper, ReportCertificatePO> implements IReportCertificateService {
    @Autowired
    private FrameworkClient frameworkClient;
    @Autowired
    private IReportService reportService;
    @Autowired
    private IOrderDomainService orderDomainService;

    @Override
    public BaseResponse<List<ReportCertificatePO>> select(ReportQueryReq reportQueryReq) {
        if(Func.isEmpty(reportQueryReq) || Func.isAllEmpty(reportQueryReq.getReportIdList(),reportQueryReq.getReportId())){
            return BaseResponse.newFailInstance("Param  missing.");
        }
        LambdaQueryWrapper<ReportCertificatePO> wrapper= Wrappers.lambdaQuery();
        if(Func.isNotEmpty(reportQueryReq.getReportId())){
            wrapper.eq(ReportCertificatePO::getReportId,reportQueryReq.getReportId());
        }
        if(Func.isNotEmpty(reportQueryReq.getReportIdList())){
            wrapper.in(ReportCertificatePO::getReportId,reportQueryReq.getReportIdList());
        }
        wrapper.orderByDesc(ReportCertificatePO::getCreatedDate);
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }

    @Override
    public BaseResponse<List<ReportCertificateRsp>> reportCertificateList(ReportQueryReq reportQueryReq) {
        List<ReportCertificatePO> reportCertificatePOS = this.select(reportQueryReq).getData();
        List<ReportCertificateRsp> resultList = new ArrayList<>();
        if(Func.isNotEmpty(reportCertificatePOS)){
            Lab lab = SystemContextHolder.getLab();
            BaseResponse<List<CertificateTypeRsp>> queryCertificateTypeResponse = frameworkClient.queryCertificateType(lab.getLocationId(), lab.getBuId());
            if(queryCertificateTypeResponse.isFail()){
                throw new BizException(queryCertificateTypeResponse.getMessage());
            }
            List<CertificateTypeRsp> certificateTypeRspList = queryCertificateTypeResponse.getData();
            for (ReportCertificatePO reportCertificatePO : reportCertificatePOS) {
                ReportCertificateRsp reportCertificateRsp = Func.deepCopy(reportCertificatePO, ReportCertificateRsp.class);
                if(Func.isNotEmpty(certificateTypeRspList)){
                    CertificateTypeRsp certificateTypeRsp = certificateTypeRspList.stream().filter(item -> Func.equalsSafe(item.getCertificateTypeCode(), reportCertificatePO.getCertificateType())).findAny().orElse(null);
                    if(Func.isNotEmpty(certificateTypeRsp)){
                        reportCertificateRsp.setCertificateTypeDisplay(certificateTypeRsp.getCertificateTypeName());
                    }
                }
                reportCertificateRsp.setStatusDisplay(ReportCertificateStatus.getName(reportCertificateRsp.getStatus()));
                resultList.add(reportCertificateRsp);
            }
        }
        return BaseResponse.newSuccessInstance(resultList);
    }

    @Override
    @Transactional
    public BaseResponse<Boolean> reportCertificateSave(ReportCertificateSaveReq reportCertificateSaveReq) {
        Assert.isTrue(Func.isNotEmpty(reportCertificateSaveReq), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(reportCertificateSaveReq.getCertificateType()), "common.select.required",new Object[]{"Certificate Type"});
        ReportCertificatePO reportCertificatePO = Func.deepCopy(reportCertificateSaveReq,ReportCertificatePO.class);
        Lab lab = SystemContextHolder.getLab();
        if(Func.isEmpty(reportCertificatePO.getId())){
            reportCertificatePO.setId(IdUtil.uuId());
            GenerateNumberReq generateNumberReq = new GenerateNumberReq();
            generateNumberReq.setNumberRuleCode("GPOCoc");
            generateNumberReq.setBuId(lab.getBuId());
            generateNumberReq.setLocationId(lab.getLocationId());
            generateNumberReq.setPostfix(reportCertificateSaveReq.getOrderProductCategory());
            String prefix = reportCertificateSaveReq.getPrefix();
            generateNumberReq.setPrefix(prefix);
            String certificateNo = frameworkClient.generateNumber(generateNumberReq).getData();
            reportCertificatePO.setStatus(1);
            reportCertificatePO.setCertificateNo(certificateNo);
            reportCertificatePO.setCreatedDate(new Date());
            reportCertificatePO.setCreatedBy(SystemContextHolder.getRegionAccount());
            reportCertificatePO.setActiveIndicator(ActiveType.Enable.getStatus());
        }else{
            ReportCertificateQueryReq reportCertificateQueryReq = new ReportCertificateQueryReq();
            reportCertificateQueryReq.setId(reportCertificatePO.getId());
            BaseResponse<ReportCertificateRsp> reportCertificateRspBaseResponse = this.reportCertificateDetail(reportCertificateQueryReq);
            if(reportCertificateRspBaseResponse.isSuccess() && Func.isNotEmpty(reportCertificateRspBaseResponse.getData())){
                ReportCertificateRsp reportCertificateRsp = reportCertificateRspBaseResponse.getData();
                if(ReportCertificateStatus.check(reportCertificateRsp.getStatus(),ReportCertificateStatus.CANCELED)){
                    return BaseResponse.newFailInstance("common.process.status.not.allowed",new Object[]{"Certificate Cancelled"});
                }
            }
        }

        reportCertificatePO.setModifiedDate(new Date());
        reportCertificatePO.setModifiedBy(SystemContextHolder.getRegionAccount());
        boolean b = this.saveOrUpdate(reportCertificatePO);
        return BaseResponse.newSuccessInstance(b);
    }

    @Override
    @Transactional
    public BaseResponse<Boolean> reportCertificateCancel(ReportCertificateUpdateReq reportCertificateUpdateReq) {
        Assert.isTrue(Func.isNotEmpty(reportCertificateUpdateReq), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(reportCertificateUpdateReq.getId()), "common.param.miss", new Object[]{"ID"});
        ReportCertificateQueryReq reportCertificateQueryReq = new ReportCertificateQueryReq();
        reportCertificateQueryReq.setId(reportCertificateUpdateReq.getId());
        BaseResponse<ReportCertificateRsp> reportCertificateRspBaseResponse = this.reportCertificateDetail(reportCertificateQueryReq);
        if(reportCertificateRspBaseResponse.isSuccess() && Func.isNotEmpty(reportCertificateRspBaseResponse.getData())){
            ReportCertificateRsp reportCertificateRsp = reportCertificateRspBaseResponse.getData();
            if(ReportCertificateStatus.check(reportCertificateRsp.getStatus(),ReportCertificateStatus.CANCELED)){
                return BaseResponse.newFailInstance("common.process.status.not.allowed",new Object[]{"Certificate Cancelled"});
            }
        }

        ReportCertificatePO cancelPO = new ReportCertificatePO();
        cancelPO.setId(reportCertificateUpdateReq.getId());
        cancelPO.setStatus(9);
        cancelPO.setModifiedDate(new Date());
        cancelPO.setModifiedBy(SystemContextHolder.getRegionAccount());
        int i = baseMapper.updateById(cancelPO);
        return BaseResponse.newSuccessInstance(i>0);
    }

    @Override
    public BaseResponse<ReportCertificateRsp> reportCertificateDetail(ReportCertificateQueryReq reportCertificateQueryReq) {
        Assert.isTrue(Func.isNotEmpty(reportCertificateQueryReq), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(reportCertificateQueryReq.getId()), "common.param.miss", new Object[]{"ID"});
        ReportCertificatePO reportCertificatePO = this.getById(reportCertificateQueryReq.getId());
        ReportCertificateRsp reportCertificateRsp = Func.deepCopy(reportCertificatePO, ReportCertificateRsp.class);
        Lab lab = SystemContextHolder.getLab();
        Integer buId = null;
        if(Func.isNotEmpty(lab)){
            buId = lab.getBuId();
        }
        List<DataDictInfo> dataDictList = frameworkClient.getDataDictList(Constants.SYS_KEY_GROUP.CERTIFICATE_TYPE, buId);
        ReportCertificatePO finalReportCertificatePO = reportCertificatePO;
        DataDictInfo dataDictInfo = dataDictList.stream().filter(item -> Func.equalsSafe(item.getSysKey(), finalReportCertificatePO.getCertificateType())).findAny().orElse(null);
        if(Func.isNotEmpty(dataDictInfo)){
            reportCertificateRsp.setCertificateTypeDisplay( dataDictInfo.getSysValue());
        }
        reportCertificateRsp.setStatusDisplay(ReportCertificateStatus.getName(reportCertificateRsp.getStatus()));
        return BaseResponse.newSuccessInstance(reportCertificateRsp);
    }
}
