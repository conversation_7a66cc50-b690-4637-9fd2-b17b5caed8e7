package com.sgs.gpo.domain.service.otsnotes.testline.command;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.order.order.OrderBO;
import com.sgs.framework.model.test.pp.pptestline.PPTestLineRelBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.test.testsample.TestSampleBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineDomainService;
import com.sgs.gpo.domain.service.otsnotes.testline.context.TestLineEditContext;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testline.TestLineLabTeamDefaultRelationshipPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.ordercitation.OrderCitationPO;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.ITestLineLabTeamDefaultRelService;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.ITestSampleService;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.domain.service.otsnotes.ordercitation.IOrderCitationService;
import com.sgs.gpo.domain.service.util.CitationUtil;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineLabTeamSearchReq;
import com.sgs.gpo.facade.model.otsnotes.testline.rsp.*;
import com.sgs.gpo.facade.model.otsnotes.testline.vo.TestLineEditVO;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.integration.framework.rsp.UserLabRsp;
import com.sgs.gpo.integration.trims.TrimsClient;
import com.sgs.gpo.integration.usermanagement.UserManagementClient;
import com.sgs.otsnotes.facade.model.enums.CategoryEnums;
import com.sgs.trimslocal.facade.model.analyte.req.QueryTestLineAnalyteReq;
import com.sgs.trimslocal.facade.model.analyte.req.TestLineAnalyteReq;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestAnalyteLangReq;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestLineAnalyteRsp;
import com.sgs.trimslocal.facade.model.citation.req.GetCitationListByTestLineVersionIdReq;
import com.sgs.trimslocal.facade.model.citation.rsp.CitationLanItem;
import com.sgs.trimslocal.facade.model.citation.rsp.CitationListRsp;
import com.sgs.trimslocal.facade.model.condition.req.GetConditionListItemReq;
import com.sgs.trimslocal.facade.model.condition.req.GetTestLineConditionListReq;
import com.sgs.trimslocal.facade.model.condition.rsp.TestConditionItemRsp;
import com.sgs.trimslocal.facade.model.condition.rsp.TestConditionLangRsp;
import com.sgs.trimslocal.facade.model.condition.rsp.TestConditionRsp;
import com.sgs.trimslocal.facade.model.condition.rsp.TestConditionTypeLanguageRsp;
import com.sgs.trimslocal.facade.model.labsection.rsp.GetLabSectionBaseInfoRsp;
import com.sgs.trimslocal.facade.model.workinginstruction.Dto.TestLineCitationDto;
import com.sgs.trimslocal.facade.model.workinginstruction.req.WorkingInstructionReq;
import com.sgs.trimslocal.facade.model.workinginstruction.rsp.WorkingInstructionLanRsp;
import com.sgs.trimslocal.facade.model.workinginstruction.rsp.WorkingInstructionRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: TestLineEditCMD
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/26 9:53
 */
@Service
@Slf4j
public class TestLineEditCMD extends BaseCommand<TestLineEditContext<OrderTestLineReq>> {
    @Autowired
    private ITestLineDomainService testLineDomainService;
    @Autowired
    private TrimsClient trimsClient;
    @Autowired
    private IOrderCitationService orderCitationService;
    @Autowired
    private UserManagementClient userManagementClient;
    @Autowired
    private ITestLineLabTeamDefaultRelService testLineLabTeamDefaultRelService;
    @Autowired
    private CitationUtil citationUtil;
    @Autowired
    private ITestSampleService testSampleService;
    @Autowired
    private IOrderDomainService orderDomainService;
    @Autowired
    private IReportService reportService;

    @Override
    public BaseResponse validateParam(TestLineEditContext<OrderTestLineReq> context) {
        if (Func.isEmpty(context) || Func.isEmpty(context.getParam())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        OrderTestLineReq orderTestLineReq = context.getParam();
        if (Func.isEmpty(orderTestLineReq.getTestLineInstanceIdList())) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"orderNoList/TestLineInstanceIdList"});
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(TestLineEditContext<OrderTestLineReq> context) {
        TestLineEditVO testLineEditVO  = new TestLineEditVO();
        buildTestLineInfo(context,testLineEditVO);

        buildCitationList(context,testLineEditVO);
        buildConditionList(context,testLineEditVO);
        buildAnalyteList(context,testLineEditVO);
        buildWIForSampleList(context,testLineEditVO);
        buildLabSectionList(context,testLineEditVO);
        buildLabTeamList(context,testLineEditVO);
        buildSampleList(context,testLineEditVO);

        buildButtonDisabled(context,testLineEditVO);
        return BaseResponse.newSuccessInstance(testLineEditVO);
    }

    private void buildTestLineInfo(TestLineEditContext<OrderTestLineReq> context, TestLineEditVO testLineEditVO) {
        /*private List<Integer> selectedAnalyteIdList;
        private List<Integer> selectedLabSectionBaseId;
        private Long selectedCitationVersionId;
        private List<String> selectedTestSampleIdList;
        private String labTeamCode;
        private Long sampleSegegrationWIID;
        private String sampleSegegrationWIText;*/




    }

    private void buildButtonDisabled(TestLineEditContext<OrderTestLineReq> context,TestLineEditVO testLineEditVO){
//        reportIsActive: 默认0，订单下所有的New、Draft、Combined状态的报告的数量>0?0:1    存在New、Draft、Combined状态的报告?
//        orderStatus:
//        reportStatus:根据orderNo查询订单，按CreatedDate倒序取第一个 ORDER BY CreatedDate DESC LIMIT 1

        //按钮编辑状态：订单状态5,6,7,10或translationOrder(order.OperationType=7 TranslationReport) 不可编辑
        //按钮展示状态：
        testLineEditVO.setSaveBtnDisabled(false);
        //按钮编辑状态：订单状态6/7不可编辑
        //按钮展示状态：tlStatus in (701,702,703, 704, 705, 708,799) and orderStatus in (1,3,8,9) and reportStatus in (201,204,206) and reportIsActive=0 and 非内部分包
        testLineEditVO.setCancelBtnDisabled(false);
        //按钮编辑状态：订单状态6,7不可编辑
        //按钮展示状态：tlStatus in (701,707,708,799) and orderStatus in (1,3,8,9) and report Status in (201,204,206) and reportIsActive=0 and 非内部分包
        testLineEditVO.setDelBtnDisabled(false);
        //按钮编辑状态：
        //按钮展示状态：
        testLineEditVO.setPendingBtnDisabled(false);
        //按钮编辑状态：订单状态6,7不可编辑
        //按钮展示状态：tlStatus in (701,705) and orderStatus in (1,3,8,9) and report Status in (201,204,206) and reportIsActive=0 and 非内部分包 and assign过Sample
        testLineEditVO.setCopyBtnDisabled(false);
        //按钮编辑状态：
        //按钮展示状态：
        testLineEditVO.setSeqBtnDisabled(false);
        
        //NC按钮
        //按钮编辑状态：订单状态6,7不可编辑
        //按钮展示状态：非Pending and tlStatus in (701) and orderStatus in (1,3,8,9) and report Status in (201,204,206) and reportIsActive=0 and 非内部分包 and 没有assign过sample


        //Change按钮
        //按钮编辑状态：订单状态6,7不可编辑
        //按钮展示状态：非Pending and tlStatus in (703,704,705) and orderStatus in (1,3,8,9,10) and report Status in (201,204,206) and reportIsActive=0
    }

    private void buildCitationList(TestLineEditContext<OrderTestLineReq> context, TestLineEditVO testLineEditVO) {
        List<CitationListRsp> citationListRspList = context.getCitationListRspList();
        boolean chineseFlag = context.isChineseFlag();
        if(Func.isEmpty(citationListRspList)){
            return;
        }
        List<TestLineEditCitationRsp> testLineEditCitationList = new ArrayList<>();
        for (CitationListRsp citationListRsp : citationListRspList) {
            TestLineEditCitationRsp testLineEditCitationRsp = new TestLineEditCitationRsp();
            Func.copy(citationListRsp,testLineEditCitationRsp);
            if(chineseFlag && Func.isNotEmpty(citationListRsp.getOtherLanguageItems())){
                CitationLanItem citationLanItem = citationListRsp.getOtherLanguageItems().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese)).findAny().orElse(null);
                if(Func.isNotEmpty(citationLanItem)){
                    if(Func.isNotEmpty(citationLanItem.getCitationName())){
                        testLineEditCitationRsp.setCitationName(citationLanItem.getCitationName());
                    }
                    if(Func.isNotEmpty(citationLanItem.getCitationFullName())){
                        testLineEditCitationRsp.setCitationFullName(citationLanItem.getCitationFullName());
                    }
                }
            }
            //是否是客户提供Citation
            testLineEditCitationRsp.setClientSpecifiedFlag(citationUtil.isProvideByClientStandard(null,Func.toLong(citationListRsp.getCitationId()),Func.toLong(citationListRsp.getCitationType())));
            testLineEditCitationList.add(testLineEditCitationRsp);
        }
        testLineEditVO.setTestLineEditCitationList(testLineEditCitationList);
    }

    private void buildConditionList(TestLineEditContext<OrderTestLineReq> context, TestLineEditVO testLineEditVO) {
        List<TestConditionRsp> testConditionRspList = context.getTestConditionRspList();
        boolean chineseFlag = context.isChineseFlag();
        if(Func.isEmpty(testConditionRspList)){
            return;
        }
        List<TestLineEditConditionRsp> testLineEditConditionList = new ArrayList<>();
        for (TestConditionRsp conditionRsp : testConditionRspList) {
            TestLineEditConditionRsp testLineEditConditionRsp = new TestLineEditConditionRsp();
            Func.copy(conditionRsp,testLineEditConditionRsp);
            if(chineseFlag && Func.isNotEmpty(conditionRsp.getLanguages())){
                TestConditionTypeLanguageRsp testConditionTypeLanguageRsp = conditionRsp.getLanguages().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese)).findAny().orElse(null);
                if(Func.isNotEmpty(testConditionTypeLanguageRsp) && Func.isNotEmpty(testConditionTypeLanguageRsp.getTestConditionTypeName())){
                    testLineEditConditionRsp.setTestConditionTypeName(testConditionTypeLanguageRsp.getTestConditionTypeName());
                }
            }
            testLineEditConditionRsp.setTestConditionTypeBlockLevel(conditionRsp.getSequence());
            testLineEditConditionRsp.setConditionTypeBlock(conditionRsp.getBlock());
            testLineEditConditionRsp.setProcedureCondition(conditionRsp.getFixed());
            List<TestConditionItemRsp> testConditionItemRspList = conditionRsp.getTestConditions();
            if(Func.isNotEmpty(testConditionItemRspList)){
                List<TestLineEditConditionItemRsp> conditionItemList = new ArrayList<>();
                for (TestConditionItemRsp testConditionItemRsp : testConditionItemRspList) {
                    TestLineEditConditionItemRsp testLineEditConditionItemRsp = new TestLineEditConditionItemRsp();
                    Func.copy(testConditionItemRsp,testLineEditConditionItemRsp);
                    if(chineseFlag && Func.isNotEmpty(testConditionItemRsp.getLanguages())){
                        TestConditionLangRsp testConditionLangRsp = testConditionItemRsp.getLanguages().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese)).findAny().orElse(null);
                        if(Func.isNotEmpty(testConditionLangRsp)){
                            if(Func.isNotEmpty(testConditionLangRsp.getTestConditionName())){
                                testLineEditConditionItemRsp.setTestConditionName(testConditionLangRsp.getTestConditionName());
                            }
                            if(Func.isNotEmpty(testConditionLangRsp.getTestConditionDesc())){
                                testLineEditConditionItemRsp.setTestConditionDesc(testConditionLangRsp.getTestConditionDesc());
                            }
                        }
                    }
                    testLineEditConditionItemRsp.setProductCondition(testConditionItemRsp.getProductCondition());
                    testLineEditConditionItemRsp.setRecommend(testConditionItemRsp.getDefaultCondition()&&
                            (Func.equals(Integer.valueOf(0),testConditionItemRsp.getProductCondition()))&&
                            (Func.equals(Integer.valueOf(1),testConditionItemRsp.getDefaultBy()))
                    );
                    testLineEditConditionItemRsp.setSelected(false);
                    conditionItemList.add(testLineEditConditionItemRsp);
                }
                testLineEditConditionRsp.setConditionItemList(conditionItemList);
            }
            testLineEditConditionList.add(testLineEditConditionRsp);
        }
        testLineEditVO.setTestLineEditConditionList(testLineEditConditionList);
    }

    private void buildAnalyteList(TestLineEditContext<OrderTestLineReq> context, TestLineEditVO testLineEditVO) {
        List<TestLineAnalyteRsp> testLineAnalyteRspList = context.getTestLineAnalyteRspList();
        if(Func.isEmpty(testLineAnalyteRspList)){
            return;
        }
        boolean chineseFlag = context.isChineseFlag();
        List<TestLineEditAnalyteRsp> testLineEditAnalyteRspList = new ArrayList<>();
        for (TestLineAnalyteRsp testLineAnalyteRsp : testLineAnalyteRspList) {
            TestLineEditAnalyteRsp testLineEditAnalyteRsp = new TestLineEditAnalyteRsp();
            Func.copy(testLineAnalyteRsp,testLineEditAnalyteRsp);
            if(chineseFlag && Func.isNotEmpty(testLineAnalyteRsp.getLanguages())){
                TestAnalyteLangReq testAnalyteLangRsp = testLineAnalyteRsp.getLanguages().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese)).findAny().orElse(null);
                if(Func.isNotEmpty(testAnalyteLangRsp) && Func.isNotEmpty(testAnalyteLangRsp.getTestAnalyteDesc())){
                    testLineEditAnalyteRsp.setTestAnalyteDesc(testAnalyteLangRsp.getTestAnalyteDesc());
                }
            }
            testLineEditAnalyteRspList.add(testLineEditAnalyteRsp);
        }
        testLineEditVO.setTestLineEditAnalyteList(testLineEditAnalyteRspList);
    }

    private void buildWIForSampleList(TestLineEditContext<OrderTestLineReq> context, TestLineEditVO testLineEditVO) {
        List<WorkingInstructionRsp> workingInstructionRspList = context.getWorkingInstructionRspList();
        if(Func.isEmpty(workingInstructionRspList)){
            return;
        }
        workingInstructionRspList = workingInstructionRspList.parallelStream().filter(wi -> CategoryEnums.checkCode(wi.getCategoryId(), CategoryEnums.SamplePreparation_Cutting, CategoryEnums.SamplePreparation_Injection, CategoryEnums.SamplePreparation_Tableting)).collect(Collectors.toList());
        if(Func.isEmpty(workingInstructionRspList)){
            return;
        }
        boolean chineseFlag = context.isChineseFlag();
        List<TestLineEditWIRsp> testLineEditWIList = new ArrayList<>();
        for (WorkingInstructionRsp workingInstructionRsp : workingInstructionRspList) {
            TestLineEditWIRsp testLineEditWIRsp = new TestLineEditWIRsp();
            Func.copy(workingInstructionRsp,testLineEditWIRsp);
            if(chineseFlag && Func.isNotEmpty(workingInstructionRsp.getLanguages())){
                WorkingInstructionLanRsp workingInstructionLanRsp = workingInstructionRsp.getLanguages().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese)).findAny().orElse(null);
                if(Func.isNotEmpty(workingInstructionLanRsp) && Func.isNotEmpty(workingInstructionLanRsp.getWorkingInstructionText())){
                    workingInstructionRsp.setWorkingInstructionText(workingInstructionLanRsp.getWorkingInstructionText());
                }
            }
            testLineEditWIList.add(testLineEditWIRsp);
        }
        testLineEditVO.setTestLineEditWIForSampleList(testLineEditWIList);

    }

    private void buildLabSectionList(TestLineEditContext<OrderTestLineReq> context, TestLineEditVO testLineEditVO) {
        List<GetLabSectionBaseInfoRsp> labSectionBaseInfoRspList = context.getLabSectionBaseInfoRspList();
        if(Func.isEmpty(labSectionBaseInfoRspList)){
            return;
        }
        List<TestLineEditLabSectionRsp> testLineEditLabSectionList = new ArrayList<>();
        for (GetLabSectionBaseInfoRsp labSectionBaseInfoRsp : labSectionBaseInfoRspList) {
            TestLineEditLabSectionRsp testLineEditLabSectionRsp = new TestLineEditLabSectionRsp();
            Func.copy(labSectionBaseInfoRsp,testLineEditLabSectionRsp);
            testLineEditLabSectionList.add(testLineEditLabSectionRsp);
        }
        testLineEditVO.setTestLineEditLabSectionList(testLineEditLabSectionList);
    }
    private void buildLabTeamList(TestLineEditContext<OrderTestLineReq> context, TestLineEditVO testLineEditVO) {
        List<TestLineLabTeamDefaultRelationshipPO> labTeamDefaultRelationshipPOList = context.getLabTeamDefaultRelationshipPOList();
        if(Func.isEmpty(labTeamDefaultRelationshipPOList)){
            return;
        }
        List<TestLineEditLabTeamRsp> testLineEditLabTeamList = new ArrayList<>();
        for (TestLineLabTeamDefaultRelationshipPO testLineLabTeamDefaultRelationshipPO : labTeamDefaultRelationshipPOList) {
            TestLineEditLabTeamRsp testLineEditLabTeamRsp = new TestLineEditLabTeamRsp();
            testLineEditLabTeamRsp.setLabCode(testLineLabTeamDefaultRelationshipPO.getLabCode());
            testLineEditLabTeamRsp.setLabTeamCode(testLineLabTeamDefaultRelationshipPO.getLabTeamCode());
            testLineEditLabTeamList.add(testLineEditLabTeamRsp);
        }
        testLineEditVO.setTestLineEditLabTeamList(testLineEditLabTeamList);
    }
    private void buildSampleList(TestLineEditContext<OrderTestLineReq> context, TestLineEditVO testLineEditVO) {
        List<TestSampleBO> testSampleList = context.getTestSampleList();
        if(Func.isEmpty(testSampleList)){
            return;
        }
        List<TestLineEditSampleRsp> testLineEditSampleList = new ArrayList<>();
        TestLineEditSampleRsp allTestLineEditSampleRsp = new TestLineEditSampleRsp();
        allTestLineEditSampleRsp.setSampleId("ALL_SELECT");
        allTestLineEditSampleRsp.setSampleNo("全部");
        testLineEditSampleList.add(allTestLineEditSampleRsp);
        for (TestSampleBO testSampleBO : testSampleList) {
            TestLineEditSampleRsp testLineEditSampleRsp = new TestLineEditSampleRsp();
            testLineEditSampleRsp.setSampleId(testSampleBO.getTestSampleInstanceId());
            testLineEditSampleRsp.setSampleNo(testSampleBO.getTestSampleNo());
            testLineEditSampleRsp.setSampleType(testSampleBO.getTestSampleType());
            testLineEditSampleRsp.setSampleSeq(testSampleBO.getTestSampleSeq());
            testLineEditSampleList.add(testLineEditSampleRsp);
        }
        testLineEditVO.setTestLineEditSampleList(testLineEditSampleList);
    }

    @Override
    public BaseResponse buildDomain(TestLineEditContext<OrderTestLineReq> context) {
        return super.buildDomain(context);
    }

    @Override
    public BaseResponse before(TestLineEditContext<OrderTestLineReq> context) {
        String defaultLanguage = LanguageType.English.getCode();
        if (Func.isNotEmpty(context.getUserInfo())) {
            defaultLanguage = context.getUserInfo().getDefaultLanguageCode();
        }
        boolean chineseFlag = LanguageType.check(LanguageType.findCode(defaultLanguage).getLanguageId(), LanguageType.Chinese);
        context.setChineseFlag(chineseFlag);

        OrderTestLineReq orderTestLineReq = context.getParam();
        BaseResponse<List<TestLineBO>> testLineBaseResponse = testLineDomainService.queryPPTestLine(orderTestLineReq);
        if (testLineBaseResponse.isFail() || Func.isEmpty(testLineBaseResponse.getData())) {
            return testLineBaseResponse;
        }
        List<TestLineBO> testLineBOList = testLineBaseResponse.getData();
        context.setEditTestLineBOList(testLineBOList);
        if (Func.isEmpty(testLineBOList)) {
            return BaseResponse.newFailInstance("NotFound", new Object[]{"TestLine"});
        }
        String orderNo = testLineBOList.get(0).getOrderNo();
        if(Func.isNotEmpty(orderNo)){
            OrderQueryReq orderQueryReq = new OrderQueryReq();
            orderQueryReq.setOrderNoList(Sets.newHashSet(orderNo));
            BaseResponse<List<OrderBO>> orderBaseResponse = orderDomainService.queryV1(orderQueryReq);
            if(orderBaseResponse.isSuccess() && Func.isNotEmpty(orderBaseResponse.getData())){
                OrderBO orderBO = orderBaseResponse.getData().get(0);
                if(Func.isNotEmpty(orderBO.getServiceRequirement()) && Func.isNotEmpty(orderBO.getServiceRequirement().getReport())){
                    context.setReportLanguage(orderBO.getServiceRequirement().getReport().getReportLanguage());
                }
            }
        }

        queryPPCitation(context);
        querySampleList(context);

        queryTrimsCitation(context);
        queryTrimsCondition(context);
        queryTrimsAnalyte(context);
        queryTrimsWI(context);
        queryLabTeam(context);
        queryTrimsLabSection(context);

        return BaseResponse.newSuccessInstance(true);
    }

    private void queryLabTeam(TestLineEditContext<OrderTestLineReq> context) {
        List<TestLineBO> testLineBOList = context.getEditTestLineBOList();
        Set<Integer> testLineIdList = testLineBOList.stream().map(TestLineBO::getTestLineId).collect(Collectors.toSet());
        TestLineLabTeamSearchReq testLineLabTeamSearchReq = new TestLineLabTeamSearchReq();
        testLineLabTeamSearchReq.setTestLineIdList(testLineIdList);
        testLineLabTeamSearchReq.setLabCode(context.getUserInfo().getCurrentLabCode());
        BaseResponse<List<TestLineLabTeamDefaultRelationshipPO>> labTeamResponse = testLineLabTeamDefaultRelService.select(testLineLabTeamSearchReq);
        if (labTeamResponse.isSuccess()) {
            context.setLabTeamDefaultRelationshipPOList(labTeamResponse.getData());
        }
    }

    private void queryTrimsCitation(TestLineEditContext<OrderTestLineReq> context) {
        List<TestLineBO> testLineBOList = context.getEditTestLineBOList();
        Integer testLineVersionId = testLineBOList.get(0).getTestLineVersionId();
        //Integer testLineType = testLineBOList.get(0).getTestLineType();
        GetCitationListByTestLineVersionIdReq citationListByTestLineVersionIdReq = new GetCitationListByTestLineVersionIdReq();
        citationListByTestLineVersionIdReq.setCallerBU(context.getProductLineCode());
        citationListByTestLineVersionIdReq.setTestLineVersionId(testLineVersionId);
        //不是testLine的,是CSPP 的 需要加上artifactType:1
        /*if(TestLineType.check(testLineType,TestLineType.CSPP) || TestLineType.check(testLineType,TestLineType.CSPP_OPEN)){
            citationListByTestLineVersionIdReq.setArtifactType(ArtifactType.PP.getType());
        }*/
        List<CitationListRsp> citationListRspList = trimsClient.getCitationInfoByCitationVersionId(citationListByTestLineVersionIdReq).getData();
        context.setCitationListRspList(citationListRspList);
    }

    private void queryTrimsCondition(TestLineEditContext<OrderTestLineReq> context) {
        List<TestLineBO> testLineBOList = context.getEditTestLineBOList();
        Integer testLineVersionId = testLineBOList.get(0).getTestLineVersionId();
        //查询Condition
        Map<String, GetConditionListItemReq> conditionMaps = Maps.newHashMap();
        GetConditionListItemReq conditionItemReq;

        List<PPTestLineRelBO> ppTestLineRelBOList = testLineBOList.stream().map(TestLineBO::getPpTestLineRel).filter(Func::isNotEmpty).collect(Collectors.toList());
        List<Integer> aidList = null;
        if (Func.isNotEmpty(ppTestLineRelBOList)) {
            aidList = ppTestLineRelBOList.stream().map(PPTestLineRelBO::getAid).collect(Collectors.toList());
        }

        if (Func.isEmpty(aidList)) {
            conditionItemReq = new GetConditionListItemReq();
            conditionItemReq.setArtifactId(0L);
            conditionItemReq.setTestLineVersionId(testLineVersionId);
            conditionMaps.put(String.format("%s_%s", 0, testLineVersionId), conditionItemReq);
        } else {
            // 不同PP 添加的同一TestLine 取第一条的 默认值
            for (Integer aid : aidList) {
                String conditionKey = String.format("%s_%s", aid, testLineVersionId);
                if (conditionMaps.containsKey(conditionKey)) {
                    continue;
                }
                conditionItemReq = new GetConditionListItemReq();
                conditionItemReq.setArtifactId(Func.toLong(aid));
                conditionItemReq.setTestLineVersionId(testLineVersionId);
                conditionMaps.put(conditionKey, conditionItemReq);
                break;
            }
        }
        GetTestLineConditionListReq testLineConditionListReq = new GetTestLineConditionListReq();
        testLineConditionListReq.setPpTestLineReqs(Lists.newArrayList(conditionMaps.values()));
        testLineConditionListReq.setCallerBU(context.getProductLineCode());

        testLineConditionListReq.setLanguageId(context.isChineseFlag() ? LanguageType.Chinese.getLanguageId() : LanguageType.English.getLanguageId());
        BaseResponse<List<TestConditionRsp>> conditionResponse = trimsClient.getTestLineConditionList(testLineConditionListReq);
        if (conditionResponse.isSuccess()) {
            context.setTestConditionRspList(conditionResponse.getData());
        }
    }

    private void queryTrimsAnalyte(TestLineEditContext<OrderTestLineReq> context) {
        List<TestLineBO> testLineBOList = context.getEditTestLineBOList();
        TestLineBO testLineBO = testLineBOList.get(0);

        QueryTestLineAnalyteReq queryTestLineAnalyteReq = new QueryTestLineAnalyteReq();
        TestLineAnalyteReq testLineAnalyteReq = new TestLineAnalyteReq();
        testLineAnalyteReq.setTestLineVersionId(testLineBO.getTestLineVersionId());
        if (Func.isNotEmpty(testLineBO.getCitation())) {
            testLineAnalyteReq.setStandardVersionIds(Sets.newHashSet(testLineBO.getCitation().getCitationVersionId()));
        }
        queryTestLineAnalyteReq.setCallerBU(context.getProductLineCode());
        List<PPTestLineRelBO> ppTestLineRelList = testLineBO.getPpTestLineRelList();
        Long artifactId = 0L;
        if(Func.isNotEmpty(ppTestLineRelList)){
            artifactId = ppTestLineRelList.stream().map(PPTestLineRelBO::getAid).map(Func::toLong).findAny().orElse(null);
        }
        List<TestLineAnalyteReq> analyteReqList = new ArrayList<>();
        testLineAnalyteReq.setArtifactId(artifactId);
        analyteReqList.add(testLineAnalyteReq);
        queryTestLineAnalyteReq.setAnalytes(analyteReqList);
        BaseResponse<List<TestLineAnalyteRsp>> clientTestLineAnalyteResponse = trimsClient.getTestLineAnalyteList(queryTestLineAnalyteReq);
        if (clientTestLineAnalyteResponse.isSuccess()) {
            context.setTestLineAnalyteRspList(clientTestLineAnalyteResponse.getData());
        }
    }

    private void queryTrimsWI(TestLineEditContext<OrderTestLineReq> context) {
        List<TestLineBO> testLineList = context.getEditTestLineBOList();
        List<OrderCitationPO> orderCitationList = context.getOrderCitationList();
        //WIForSample
        WorkingInstructionReq workingInstructionReq = new WorkingInstructionReq();
        List<TestLineCitationDto> testLineCitations = new ArrayList<>();
        //优先使用PPCitationBaseId
        for (TestLineBO testLineBO : testLineList) {
            TestLineCitationDto testLineCitationDto = new TestLineCitationDto();
            testLineCitationDto.setTestLineVersionId(testLineBO.getTestLineVersionId());
            if (Func.isNotEmpty(testLineBO.getCitation())) {
                testLineCitationDto.setCitationBaseId(testLineBO.getCitation().getCitationBaseId());
            }
            if (Func.isNotEmpty(testLineBO.getPpTestLineRel()) && Func.isNotEmpty(testLineBO.getPpTestLineRel().getPpBaseId()) && Func.isNotEmpty(orderCitationList)) {
                Long ppBaseId = testLineBO.getPpTestLineRel().getPpBaseId();
                Set<OrderCitationPO> ppCitationList = orderCitationList.stream().filter(item -> {
                    return Func.equalsSafe(ppBaseId, item.getPpBaseId());
                }).collect(Collectors.toSet());
                Long ppCitationBaseId = ppCitationList.stream().map(OrderCitationPO::getCitationBaseId).findAny().orElse(null);
                if (Func.isNotEmpty(ppCitationBaseId)) {
                    testLineCitationDto.setCitationBaseId(ppCitationBaseId);
                }
            }
            testLineCitations.add(testLineCitationDto);
        }
        Set<Integer> wiCategoryIdSets = Sets.newHashSet();
        wiCategoryIdSets.add(CategoryEnums.WIForCS.getCode());
        wiCategoryIdSets.add(CategoryEnums.WIForTesting.getCode());
        wiCategoryIdSets.add(CategoryEnums.SamplePreparation_Cutting.getCode());
        wiCategoryIdSets.add(CategoryEnums.SamplePreparation_Injection.getCode());
        wiCategoryIdSets.add(CategoryEnums.SamplePreparation_Tableting.getCode());
        /*wiCategoryIdSets.add(CategoryEnums.SamplePreparation_Injection.getCode());
        wiCategoryIdSets.add(CategoryEnums.SamplePreparation_Tableting.getCode());*/

        workingInstructionReq.setTestLineCitations(testLineCitations);
        workingInstructionReq.setCallerBU(context.getProductLineCode());
        workingInstructionReq.setCategoryIds(wiCategoryIdSets);
        BaseResponse<List<WorkingInstructionRsp>> workingInstructionResponse = trimsClient.getWorkingInstructionList(workingInstructionReq);
        if (workingInstructionResponse.isSuccess()) {
            List<WorkingInstructionRsp> workingInstructionRspList = workingInstructionResponse.getData();
            context.setWorkingInstructionRspList(workingInstructionRspList);
        }
    }

    private void queryTrimsLabSection(TestLineEditContext<OrderTestLineReq> context) {
        UserLabRsp userLabRsp = userManagementClient.getUserLabBuInfo(context.getToken());
        if (Func.isNotEmpty(userLabRsp) && Func.isNotEmpty(userLabRsp.getLabId())) {
            BaseResponse<List<GetLabSectionBaseInfoRsp>> labSectionResponse = trimsClient.getLabSectionByLabId(Sets.newHashSet(Func.toInteger(userLabRsp.getLabId())));
            if (labSectionResponse.isSuccess()) {
                context.setLabSectionBaseInfoRspList(labSectionResponse.getData());
            }
        }
    }

    private void queryPPCitation(TestLineEditContext<OrderTestLineReq> context) {
        List<TestLineBO> testLineList = context.getEditTestLineBOList();
        String orderId = testLineList.get(0).getOrderId();
        BaseResponse<List<OrderCitationPO>> orderCitationResponse = orderCitationService.queryByOrderId(Sets.newHashSet(orderId));
        List<OrderCitationPO> orderCitationList = orderCitationResponse.getData();
        log.info(Func.toJson(orderCitationResponse));
        context.setOrderCitationList(orderCitationList);
    }
    private void querySampleList(TestLineEditContext<OrderTestLineReq> context) {
        List<TestLineBO> testLineList = context.getEditTestLineBOList();
        String orderId = testLineList.get(0).getOrderId();
        TestSampleQueryReq sampleQueryReq = new TestSampleQueryReq();
        sampleQueryReq.setOrderId(orderId);
        BaseResponse<List<TestSampleBO>> sampleBaseResponse = testSampleService.queryV1(sampleQueryReq);
        if(sampleBaseResponse.isSuccess()){
            context.setTestSampleList(sampleBaseResponse.getData());
        }
    }
}
