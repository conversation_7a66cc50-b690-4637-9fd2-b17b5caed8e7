package com.sgs.gpo.domain.service.preorder.enquiry.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.enquiry.EnquiryPersonMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryPersonPO;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryPersonService;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class EnquiryPersonServiceImpl extends ServiceImpl<EnquiryPersonMapper, EnquiryPersonPO>
        implements IEnquiryPersonService {


    @Override
    public List<EnquiryPersonPO> select(EnquiryIdReq enquiryIdReq) {
        if (Func.isEmpty(enquiryIdReq) || Func.isEmpty(enquiryIdReq.getEnquiryIdList())) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<EnquiryPersonPO> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EnquiryPersonPO::getEnquiryId, enquiryIdReq.getEnquiryIdList());
        return baseMapper.selectList(wrapper);
    }
}
