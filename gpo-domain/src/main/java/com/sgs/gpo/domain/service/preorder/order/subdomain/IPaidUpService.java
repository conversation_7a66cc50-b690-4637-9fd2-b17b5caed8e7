package com.sgs.gpo.domain.service.preorder.order.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.PageBO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.PaidUpPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.PaidUpVO;
import com.sgs.gpo.facade.model.payment.paiduplist.req.PaidUpQueryReq;
import com.sgs.gpo.facade.model.payment.paiduplist.rsp.PaidUpListRsp;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;


public interface IPaidUpService extends IService<PaidUpPO> {

    List<PaidUpPO> getPaidUpByOrderNo(String orderNo);

    List<String> getOrderByPaidUpIds(@Param("paidUpIds") List<String> paidUpIds);

    List<PaidUpVO> select(PaidUpQueryReq paidUpQueryReq);

    PageBO<PaidUpListRsp> page(PaidUpQueryReq req);

    void inactiveByIdList(Set<String> idList);

}
