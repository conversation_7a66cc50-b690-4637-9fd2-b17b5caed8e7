package com.sgs.gpo.domain.service.otsnotes.testline.command;

import cn.hutool.core.util.NumberUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.util.StringPool;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.TestExecutionType;
import com.sgs.framework.model.enums.TestLineType;
import com.sgs.framework.model.test.analyte.AnalyteBO;
import com.sgs.framework.model.test.analyte.AnalyteLanguageBO;
import com.sgs.framework.model.test.citation.CitationBO;
import com.sgs.framework.model.test.citation.CitationLanguageBO;
import com.sgs.framework.model.test.condition.conditiongroup.ConditionGroupBO;
import com.sgs.framework.model.test.condition.conditiongroup.ConditionGroupLanguageBO;
import com.sgs.framework.model.test.condition.conditiongroup.PPConditionGroupBO;
import com.sgs.framework.model.test.pp.PPLanguageBO;
import com.sgs.framework.model.test.pp.pptestline.PPTestLineRelBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.test.testline.TestLineExecutionBO;
import com.sgs.framework.model.test.testline.TestLineLanguageBO;
import com.sgs.framework.model.test.wi.WorkInstructBO;
import com.sgs.framework.model.test.wi.v2.WorkingInstructionBO;
import com.sgs.framework.model.test.wi.v2.WorkingInstructionLanBO;
import com.sgs.framework.model.trims.labsection.LabSectionBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.config.StandardConfig;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.LangTypeEnum;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testline.TestLineMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.analyte.AnalytePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.condition.TestConditionGroupLanguagePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.condition.TestConditionGroupPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.condition.TestPpConditionGroupPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testline.TestLineLabSectionRelationshipPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.ordercitation.OrderCitationPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.orderlanguage.OrderLanguagePO;
import com.sgs.gpo.domain.otsnotes.common.service.analyte.ITestAnalyteService;
import com.sgs.gpo.domain.otsnotes.common.subdomain.ITestConditionGroupLanguageService;
import com.sgs.gpo.domain.otsnotes.common.subdomain.ITestConditionGroupService;
import com.sgs.gpo.domain.otsnotes.common.subdomain.ITestPpConditionGroupService;
import com.sgs.gpo.domain.service.otsnotes.ordercitation.IOrderCitationService;
import com.sgs.gpo.domain.service.otsnotes.orderlanguage.IOrderLanguageService;
import com.sgs.gpo.domain.service.otsnotes.testline.context.TestLineQueryContext;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.ITestLineLabSectionService;
import com.sgs.gpo.domain.service.setting.buparam.IBUParam;
import com.sgs.gpo.domain.service.trims.labsection.ILabSectionService;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.QueryTestConditionGroupReq;
import com.sgs.gpo.facade.model.trims.labsection.req.LabSectionReq;
import com.sgs.gpo.integration.trims.TrimsClient;
import com.sgs.otsnotes.facade.model.enums.CategoryEnums;
import com.sgs.priceengine.facade.QuotationFacade;
import com.sgs.priceengine.facade.model.DTO.TLAmountDTO;
import com.sgs.priceengine.facade.model.request.QueryTLAmountRequest;
import com.sgs.trimslocal.facade.model.analyte.req.QueryTestLineAnalyteReq;
import com.sgs.trimslocal.facade.model.analyte.req.TestLineAnalyteReq;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestAnalyteLangReq;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestLineAnalyteRsp;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestLineAnalyteUnitRsp;
import com.sgs.trimslocal.facade.model.language.rsp.GetPpLanguageRsp;
import com.sgs.trimslocal.facade.model.pp.req.GetPpInfoReq;
import com.sgs.trimslocal.facade.model.pp.rsp.GetPpBaseInfoRsp;
import com.sgs.trimslocal.facade.model.testline.info.TestLineSimplifyInfo;
import com.sgs.trimslocal.facade.model.testline.req.GetCitationBaseInfoReq;
import com.sgs.trimslocal.facade.model.testline.req.TestLineSimplifyInfoReq;
import com.sgs.trimslocal.facade.model.testline.rsp.CitationBaseInfoLanguagesRsp;
import com.sgs.trimslocal.facade.model.testline.rsp.GetCitationBaseInfoRsp;
import com.sgs.trimslocal.facade.model.testline.rsp.TestLineSimplifyInfoRsp;
import com.sgs.trimslocal.facade.model.testline.rsp.TestLineSimplifyLangInfoRsp;
import com.sgs.trimslocal.facade.model.workinginstruction.Dto.TestLineCitationDto;
import com.sgs.trimslocal.facade.model.workinginstruction.req.WorkingInstructionReq;
import com.sgs.trimslocal.facade.model.workinginstruction.rsp.WorkingInstructionLanRsp;
import com.sgs.trimslocal.facade.model.workinginstruction.rsp.WorkingInstructionRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/29 13:33
 */
@Service
@Slf4j
public class TestLineQueryBaseCMD extends BaseCommand<TestLineQueryContext<OrderTestLineReq, TestLineBO>> {

    @Autowired
    protected TestLineMapper testLineMapper;
    @Autowired
    protected ITestLineLabSectionService testLineLabSectionService;
    @Autowired
    private ITestAnalyteService analyteService;
    @Autowired
    protected IOrderLanguageService orderLanguageService;
    @Autowired
    protected IOrderCitationService orderCitationService;
    @Autowired
    protected ILabSectionService labSectionService;
    @Autowired
    protected TrimsClient trimsClient;
    @Autowired
    protected StandardConfig standardConfig;
    @Autowired
    private IBUParam buParam;
    @Autowired
    private ITestConditionGroupService testConditionGroupService;
    @Autowired
    private ITestConditionGroupLanguageService testConditionGroupLanguageService;
    @Autowired
    private ITestPpConditionGroupService testPpConditionGroupService;
    @Autowired
    private QuotationFacade quotationFacade;
    @Autowired
    private IBUParam ibuParam;


    @Override
    public BaseResponse validateParam(TestLineQueryContext<OrderTestLineReq, TestLineBO> context) {
        if(Func.isEmpty(context)||Func.isEmpty(context.getParam())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        OrderTestLineReq orderTestLineReq = context.getParam();
        if( Func.isEmpty(orderTestLineReq.getTestLineInstanceIdList())
                && Func.isEmpty(orderTestLineReq.getTestItemNoList())
                && Func.isEmpty(orderTestLineReq.getOrderNoList())
                && Func.isEmpty(orderTestLineReq.getOrderId())
                && Func.isEmpty(orderTestLineReq.getOrderNo())
                && Func.isEmpty(orderTestLineReq.getJobNo())
                && Func.isEmpty(orderTestLineReq.getJobNoList())
                && Func.isEmpty(orderTestLineReq.getSubcontractNo())
                && Func.isEmpty(orderTestLineReq.getSubcontractNoList())){
            return BaseResponse.newFailInstance("common.miss", new Object[]{"orderId / testLine"});
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse buildDomain(TestLineQueryContext<OrderTestLineReq, TestLineBO> context) {
        List<TestLineBO> testLineList = context.getTestLineList();
        List<LabSectionBO> labSectionList = context.getLabSectionList();
        List<GetCitationBaseInfoRsp> citationBaseInfoRspList = context.getCitationBaseInfoRspList();
        boolean chineseFlag = context.isChineseFlag();
        List<TestLineSimplifyInfoRsp> testLineSimplifyInfoRspList = context.getTestLineSimplifyInfoRspList();
        List<GetPpBaseInfoRsp> ppBaseInfoRspList = context.getPpBaseInfoRspList();
        // 拼装数据
        for(TestLineBO testLineBO : testLineList){
            String originCitationName = "";
            String originCitationNameCn = "";
            if(Func.isNotEmpty(testLineBO.getCitation())){
                originCitationName = testLineBO.getCitation().getCitationName();
                if(Func.isNotEmpty(testLineBO.getCitation().getLanguageList())){
                    List<CitationLanguageBO> languageList = testLineBO.getCitation().getLanguageList();
                    originCitationNameCn = languageList.get(0).getCitationName();
                }
            }
            // 组装LabSection
            List<LabSectionBO> currentTLLabSectionList = testLineBO.getLabSectionList();
            if(Func.isNotEmpty(currentTLLabSectionList) && Func.isNotEmpty(labSectionList)) {
                Set<Integer>  currentTLLabSectionIdList = currentTLLabSectionList.stream().map(LabSectionBO::getLabSectionId).filter(Func::isNotEmpty).collect(Collectors.toSet());
                Set<Long>  currentTLLabSectionBaseIdList = currentTLLabSectionList.stream().map(LabSectionBO::getLabSectionBaseId).filter(Func::isNotEmpty).collect(Collectors.toSet());
                currentTLLabSectionList = labSectionList.stream().filter(item->{return currentTLLabSectionIdList.contains(item.getLabSectionId());}).collect(Collectors.toList());
                if(Func.isEmpty(currentTLLabSectionList)){
                    currentTLLabSectionList = labSectionList.stream().filter(item->{return currentTLLabSectionBaseIdList.contains(item.getLabSectionBaseId());}).collect(Collectors.toList());
                }
                if(Func.isNotEmpty(currentTLLabSectionList)) {
                    testLineBO.setLabSectionList(currentTLLabSectionList);
                }
            }
            // 组织Test Execution
            this.buildTestExecutionList(testLineBO,context);
            // 组装名称
            Long ppArtifactRelId = Func.isEmpty(testLineBO.getPpTestLineRel())?0l:testLineBO.getPpTestLineRel().getPpArtifactRelId();
            TestLineSimplifyInfoRsp testLineSimplifyInfoDTO = null;
            if(Func.isNotEmpty(testLineSimplifyInfoRspList)){
                if(Func.isEmpty(ppArtifactRelId) || ppArtifactRelId.equals(0L)){
                    testLineSimplifyInfoDTO = testLineSimplifyInfoRspList.stream().filter(temp->{
                        return (Func.isEmpty(temp.getPpArtifactRelId()) || NumberUtil.equals(temp.getPpArtifactRelId().longValue(),0l)) && Func.equals(testLineBO.getTestLineBaseId(),temp.getTestLineBaseId())
                                &&Func.equals(testLineBO.getCitation().getCitationBaseId(),temp.getCitationBaseId());
                    }).findFirst().orElse(null);
                }else{
                    testLineSimplifyInfoDTO = testLineSimplifyInfoRspList.stream().filter(temp->{
                        return Func.equals(testLineBO.getTestLineBaseId(),temp.getTestLineBaseId())
                                &&Func.equals(testLineBO.getCitation().getCitationBaseId(),temp.getCitationBaseId()) && ppArtifactRelId.equals(temp.getPpArtifactRelId());
                    }).findFirst().orElse(null);
                }
            }
            List<TestLineLanguageBO> testLineLanguageListTemp = new ArrayList<>();
            List<CitationLanguageBO> citationLanguageListTemp = new ArrayList<>();
            String citationName = null;
            String citationFullName = null;
            if(Func.isNotEmpty(testLineSimplifyInfoDTO)){
                List<TestLineSimplifyLangInfoRsp> languages = testLineSimplifyInfoDTO.getLanguages();
                testLineBO.setEvaluationName(testLineSimplifyInfoDTO.getEvaluationAlias());
                //暂时先取testLineSimplifyInfo中的labSectionName
//                if(Func.isNotEmpty(testLineBO.getLabSectionList())){
//                    testLineBO.getLabSectionList().get(0).setLabSectionName(testLineSimplifyInfoDTO.getLabSectionName());
//                }else if(Func.isNotEmpty(testLineSimplifyInfoDTO.getLabSectionName())){
//                    LabSectionBO labSectionBO = new LabSectionBO();
//                    labSectionBO.setLabSectionName(testLineSimplifyInfoDTO.getLabSectionName());
//                    List<LabSectionBO> labSectionBOS = new ArrayList<>();
//                    labSectionBOS.add(labSectionBO);
//                    testLineBO.setLabSectionList(labSectionBOS);
//                }
                // 默认添加英文
                TestLineLanguageBO testLineLanguageEN = new TestLineLanguageBO();
                testLineLanguageEN.setLanguageId(LanguageType.English.getLanguageId());
                testLineLanguageEN.setEvaluationName(testLineSimplifyInfoDTO.getEvaluationAlias());
                testLineLanguageListTemp.add(testLineLanguageEN);
                citationName = testLineSimplifyInfoDTO.getCitationFullName();
                citationFullName = testLineSimplifyInfoDTO.getCitationFullName();
//                testLineBO.getCitation().setCitationName(testLineSimplifyInfoDTO.getCitationFullName());
//                testLineBO.getCitation().setCitationFullName(testLineSimplifyInfoDTO.getCitationFullName());
                // 默认添加英文
                CitationLanguageBO citationLanguageEN = new CitationLanguageBO();
                citationLanguageEN.setLanguageId(LanguageType.English.getLanguageId());
                citationLanguageEN.setCitationName(testLineSimplifyInfoDTO.getCitationFullName());
                citationLanguageEN.setCitationFullName(testLineSimplifyInfoDTO.getCitationFullName());
                citationLanguageListTemp.add(citationLanguageEN);

                if(Func.isNotEmpty(languages)){
                    for(TestLineSimplifyLangInfoRsp language : languages){
                        // 设置TL名称
                        TestLineLanguageBO testLineLanguage = new TestLineLanguageBO();
                        testLineLanguage.setLanguageId(language.getLanguageId());
                        testLineLanguage.setEvaluationName(language.getEvaluationAlias());
                        if(chineseFlag && LanguageType.check(language.getLanguageId(),LanguageType.Chinese) && Func.isNotEmpty(language.getEvaluationAlias())){
                            testLineBO.setEvaluationName(language.getEvaluationAlias());
                        }
                        testLineLanguageListTemp.add(testLineLanguage);
                        // 设置Citation名称
                        if(Func.isNotEmpty(language.getCitationFullName())){
                            CitationLanguageBO citationLanguageBO = new CitationLanguageBO();
                            citationLanguageBO.setLanguageId(language.getLanguageId());
                            citationLanguageBO.setCitationName(language.getCitationFullName());
                            citationLanguageBO.setCitationFullName(language.getCitationFullName());
                            citationLanguageListTemp.add(citationLanguageBO);
                        }
                        if(chineseFlag && LanguageType.check(language.getLanguageId(),LanguageType.Chinese) && Func.isNotEmpty(language.getCitationFullName())){
                            citationName = language.getCitationFullName();
                            citationFullName = language.getCitationFullName();
//                            testLineBO.getCitation().setCitationName(language.getCitationFullName());
//                            testLineBO.getCitation().setCitationFullName(language.getCitationFullName());
                        }
                    }
                }
//                testLineBO.setLanguageList(testLineLanguageListTemp);
//                testLineBO.getCitation().setLanguageList(citationLanguageListTemp);
            }
            //处理OOB逻辑
            Integer testLineType = testLineBO.getTestLineType();
            if (Func.isNotEmpty(testLineType) && (testLineType & TestLineType.OOB_TEST.getType()) == TestLineType.OOB_TEST.getType()) {
                if(Func.isNotEmpty(testLineBO.getCustomerTestLineName())){
                    testLineBO.setEvaluationName(testLineBO.getCustomerTestLineName());
                    // 默认添加英文
                    testLineLanguageListTemp = new ArrayList<>();
                    TestLineLanguageBO testLineLanguageEN = new TestLineLanguageBO();
                    testLineLanguageEN.setLanguageId(LanguageType.English.getLanguageId());
                    testLineLanguageEN.setEvaluationName(testLineBO.getCustomerTestLineName());
                    testLineLanguageListTemp.add(testLineLanguageEN);
                }
                if(Func.isNotEmpty(testLineBO.getCustomerTestLineNameCN())){
                    testLineLanguageListTemp.removeIf(item->{
                        return LanguageType.check(item.getLanguageId(),LanguageType.Chinese);
                    });
                    TestLineLanguageBO testLineLanguageCN = new TestLineLanguageBO();
                    testLineLanguageCN.setLanguageId(LanguageType.Chinese.getLanguageId());
                    testLineLanguageCN.setEvaluationName(testLineBO.getCustomerTestLineNameCN());
                    testLineLanguageListTemp.add(testLineLanguageCN);
                    if(chineseFlag ){
                        testLineBO.setEvaluationName(testLineBO.getCustomerTestLineNameCN());
                    }
                }
            }
            testLineBO.setLanguageList(testLineLanguageListTemp);
            //处理ProvideByClientStandard
            if(isProvideByClientStandard(Func.isEmpty(testLineBO.getPpTestLineRel())?null:testLineBO.getPpTestLineRel().getPpBaseId(),testLineBO.getCitation().getCitationId())){
                citationLanguageListTemp = new ArrayList<>();
                String formatCitationName = formatProvideByClientStandard(originCitationName, true);
                citationName = formatCitationName;
                citationFullName = formatCitationName;
                if(Func.isNotEmpty(testLineBO.getCitation().getLanguageList())){
                    List<CitationLanguageBO> languageList = testLineBO.getCitation().getLanguageList();
                    CitationLanguageBO citationLanguageEN = new CitationLanguageBO();
                    citationLanguageEN.setCitationName(formatCitationName);
                    citationLanguageEN.setCitationFullName(formatCitationName);
                    citationLanguageEN.setLanguageId(LanguageType.English.getLanguageId());
                    citationLanguageListTemp.add(citationLanguageEN);
                    String formatCitationNameLang = formatProvideByClientStandard(originCitationNameCn, true);
                    for (CitationLanguageBO citationLanguageBO : languageList) {
                        citationLanguageBO.setCitationName(formatCitationNameLang);
                        citationLanguageBO.setCitationFullName(formatCitationNameLang);
                        citationLanguageListTemp.add(citationLanguageBO);
                    }
                    if(chineseFlag ){
                        citationName =  formatCitationNameLang;
                        citationFullName = formatCitationNameLang;
                    }
                }
            }
            testLineBO.getCitation().setCitationName(citationName);
            if (Func.isNotEmpty(citationBaseInfoRspList)){
                GetCitationBaseInfoRsp getCitationBaseInfoRsp = citationBaseInfoRspList.stream().filter(item -> Func.equalsSafe(item.getCitationBaseId(), testLineBO.getCitation().getCitationBaseId())).findAny().orElse(null);
                if(Func.isNotEmpty(getCitationBaseInfoRsp)){
                    testLineBO.getCitation().setMethodDesc(getCitationBaseInfoRsp.getMethodDesc());
                    testLineBO.getCitation().setCitationType(getCitationBaseInfoRsp.getCitationType());
                    List<CitationBaseInfoLanguagesRsp> citationBaseInfoLanguagesRspList = getCitationBaseInfoRsp.getLanguages();
                    if(Func.isNoneEmpty(citationLanguageListTemp,citationBaseInfoLanguagesRspList) ){
                        for (CitationLanguageBO citationLanguageBO : citationLanguageListTemp) {
                            CitationBaseInfoLanguagesRsp citationBaseInfoLanguagesRsp = citationBaseInfoLanguagesRspList.stream().filter(item -> Func.equalsSafe(item.getLanguageId(), citationLanguageBO.getLanguageId())).findAny().orElse(null);
                            if(Func.isNotEmpty(citationBaseInfoLanguagesRsp)){
                                citationLanguageBO.setMethodDesc(citationBaseInfoLanguagesRsp.getMethodDesc());
                            }else{
                                citationLanguageBO.setMethodDesc(getCitationBaseInfoRsp.getMethodDesc());
                            }
                        }
                    }
                }
            }
            testLineBO.getCitation().setCitationFullName(citationFullName);
            testLineBO.getCitation().setLanguageList(citationLanguageListTemp);
            //组装pp名称
            if(Func.isNotEmpty(ppBaseInfoRspList) && Func.isNotEmpty(testLineBO.getPpTestLineRel())){
                GetPpBaseInfoRsp localTrimsPPBaseInfo = ppBaseInfoRspList.stream().filter(ppBaseInfo -> {
                    return 0==Long.compare(testLineBO.getPpTestLineRel().getPpBaseId(),ppBaseInfo.getPpBaseId());
                }).findFirst().orElse(null);
                GetPpBaseInfoRsp localTrimsRootPPBaseInfo = ppBaseInfoRspList.stream().filter(ppBaseInfo -> {
                    return 0==Long.compare(testLineBO.getPpTestLineRel().getRootPPBaseId(),ppBaseInfo.getPpBaseId());
                }).findFirst().orElse(null);
                if(Func.isNotEmpty(localTrimsPPBaseInfo)){
                    testLineBO.getPpTestLineRel().setPpNo(localTrimsPPBaseInfo.getPpNo());
                    testLineBO.getPpTestLineRel().setPpVersionId(localTrimsPPBaseInfo.getPpVersionId());
                    testLineBO.getPpTestLineRel().setPpName(localTrimsPPBaseInfo.getPpName());
                }
                if(Func.isNotEmpty(localTrimsRootPPBaseInfo)){
                    testLineBO.getPpTestLineRel().setRootPPNo(localTrimsRootPPBaseInfo.getPpNo());
                }

                List<PPTestLineRelBO> ppTestLineRelList = testLineBO.getPpTestLineRelList();
                for (PPTestLineRelBO ppTestLineRelBO : ppTestLineRelList) {
                    GetPpBaseInfoRsp ppBaseInfoRsp = ppBaseInfoRspList.stream().filter(ppBaseInfo -> {
                        return 0==Long.compare(ppTestLineRelBO.getPpBaseId(),ppBaseInfo.getPpBaseId());
                    }).findFirst().orElse(null);
                    GetPpBaseInfoRsp rootPpBaseInfoRsp = ppBaseInfoRspList.stream().filter(ppBaseInfo -> {
                        return 0==Long.compare(ppTestLineRelBO.getRootPPBaseId(),ppBaseInfo.getPpBaseId());
                    }).findFirst().orElse(null);

                    List<PPLanguageBO> ppLanguageBOList = ppTestLineRelBO.getLanguageList();
                    PPLanguageBO ppLanguageEn = new PPLanguageBO();
                    ppLanguageEn.setPpNotes(ppTestLineRelBO.getPpNotes());
                    ppLanguageEn.setLanguageId(LanguageType.English.getLanguageId());
                    if(Func.isNotEmpty(ppBaseInfoRsp)){
                        ppLanguageEn.setPpName(ppBaseInfoRsp.getPpName());
                        ppLanguageEn.setPpDescription(ppBaseInfoRsp.getProtocolPackageDescription());
                        ppTestLineRelBO.setPpName(ppBaseInfoRsp.getPpName());
                        ppTestLineRelBO.setPpNo(ppBaseInfoRsp.getPpNo());
                        ppTestLineRelBO.setPpVersionId(ppBaseInfoRsp.getPpVersionId());
                        ppTestLineRelBO.setPpDescription(ppBaseInfoRsp.getProtocolPackageDescription());
                        List<GetPpLanguageRsp> ppBaseInfoRspLanguages = ppBaseInfoRsp.getLanguages();
                        if(Func.isNotEmpty(ppLanguageBOList)){
                            for (PPLanguageBO ppLanguageBO : ppLanguageBOList) {
                                GetPpLanguageRsp getPpLanguageRsp = ppBaseInfoRspLanguages.stream().filter(item -> NumberUtil.equals(item.getLanguageId(), ppLanguageBO.getLanguageId())).findAny().orElse(null);
                                if(Func.isNotEmpty(getPpLanguageRsp)){
                                    ppLanguageBO.setPpName(getPpLanguageRsp.getPpName());
                                    ppLanguageBO.setPpDescription(getPpLanguageRsp.getProtocolPackageDescription());
                                }
                                if(chineseFlag && LanguageType.check(ppLanguageBO.getLanguageId(),LanguageType.Chinese) && Func.isNotEmpty(ppLanguageBO.getPpName())){
                                    ppTestLineRelBO.setPpName(ppLanguageBO.getPpName());
                                    ppTestLineRelBO.setPpDescription(ppLanguageBO.getPpDescription());
                                }
                            }
                        }
                    }
                    if(Func.isNotEmpty(rootPpBaseInfoRsp)){
                        ppTestLineRelBO.setRootPPNo(rootPpBaseInfoRsp.getPpNo());
                    }
                    if(Func.isNotEmpty(ppLanguageEn.getPpName()) || Func.isNotEmpty(ppLanguageEn.getPpNotes())){
                        ppLanguageBOList.add(ppLanguageEn);
                    }
                    ppTestLineRelBO.setLanguageList(ppLanguageBOList);
                }
            }
            buildTestAnalyteList(testLineBO,context);
            buildWI(testLineBO,context);
            buildTestConditionGroup(testLineBO,context);
            buildTlAmount(testLineBO,context);
            //TODO BuildTestScheme
            //TODO BuildTestCondition
        }
        context.setTestLineList(testLineList);
        return BaseResponse.newSuccessInstance(true);
    }
    private void buildTlAmount(TestLineBO testLineBO,TestLineQueryContext<OrderTestLineReq, TestLineBO> context) {
        if (Func.isEmpty(testLineBO)) {
            return;
        }
        List<TLAmountDTO> tlAmountList = context.getTlAmountList();
        testLineBO.setAmountText(Constants.COMMON.UNABLE_TO_CALCULATE);
        String ppTlRelId = "";
        if (Func.isNotEmpty(testLineBO.getPpTestLineRel())) {
            ppTlRelId = testLineBO.getPpTestLineRel().getPpTlRelId();
        }
        if (Func.isNotEmpty(tlAmountList)) {
            String finalPpTlRelId = ppTlRelId;
            List<TLAmountDTO> tlOrderAmountDTOList = tlAmountList.stream().filter(e ->
                    {
                        return Func.equalsSafe(e.getPpTlRelId(), finalPpTlRelId);
                    }
            ).collect(Collectors.toList());
            BigDecimal tlBigAmount = null;
            if (Func.isNotEmpty(tlOrderAmountDTOList)) {
                Double d = tlOrderAmountDTOList.stream().mapToDouble(e -> e.getAmount() == null ? 0 : e.getAmount().doubleValue()).sum();
                tlBigAmount = BigDecimal.valueOf(d).setScale(2, BigDecimal.ROUND_HALF_UP);
                testLineBO.setCurrency(tlOrderAmountDTOList.get(0).getCurrencyCode());
            }
            String amountText = tlBigAmount == null || tlBigAmount.compareTo(BigDecimal.ZERO) == 0 ? "0.00" : Func.toStr(tlBigAmount);
            testLineBO.setAmountText(Func.isEmpty(tlBigAmount)?Constants.COMMON.UNABLE_TO_CALCULATE: amountText );
        }
    }


    private void queryTLAmount(TestLineQueryContext<OrderTestLineReq, TestLineBO> context) {
        List<TestLineBO> testLineList = context.getTestLineList();
        List<String> preOrderIdList = testLineList.stream().map(TestLineBO::getPreOrderId).collect(Collectors.toList());
        if (Func.isEmpty(preOrderIdList)) {
            return;
        }
        //查询tl amount
        QueryTLAmountRequest objQueryTLAmountRequest = new QueryTLAmountRequest();
        objQueryTLAmountRequest.setSystemId(15);
        objQueryTLAmountRequest.setProductLineCode(SystemContextHolder.getBuCode());
        objQueryTLAmountRequest.setOrderIdList(preOrderIdList);
        BaseResponse<List<TLAmountDTO>> tlAmountReponse = quotationFacade.queryTLAmount(objQueryTLAmountRequest);
        List<TLAmountDTO> tlAmountDTOList = com.google.common.collect.Lists.newArrayList();
        if (tlAmountReponse.getStatus() == 200 && Func.isNotEmpty(tlAmountReponse.getData())) {
            tlAmountDTOList = tlAmountReponse.getData();
        }
        context.setTlAmountList(tlAmountDTOList);
    }
    protected void buildTestConditionGroup(TestLineBO testLineBO, TestLineQueryContext<OrderTestLineReq, TestLineBO> context){
        boolean chineseFlag = context.isChineseFlag();
        List<TestConditionGroupPO> testConditionGroupPOList = context.getTestConditionGroupList();
        List<TestConditionGroupLanguagePO> allTestConditionGroupLanguagePOList = context.getTestConditionGroupLanguageList();
        List<TestPpConditionGroupPO> allTestPpConditionGroupList = context.getTestPpConditionGroupList();
        List<ConditionGroupBO> conditionGroupBOList = new ArrayList<>();
        if(Func.isNotEmpty(testConditionGroupPOList)){
            List<TestConditionGroupPO> testConditionInstancePOS = testConditionGroupPOList.stream().filter(item -> Func.equalsSafe(item.getTestLineInstanceId(), testLineBO.getTestLineInstanceId())).collect(Collectors.toList());
            if(Func.isNotEmpty(testConditionInstancePOS)){
                for (TestConditionGroupPO testConditionGroupPO : testConditionInstancePOS) {
                    List<TestConditionGroupLanguagePO> testConditionGroupLanguagePOList =
                            allTestConditionGroupLanguagePOList.stream().filter(item -> Func.equalsSafe(item.getTestConditionGroupId(), testConditionGroupPO.getId())).collect(Collectors.toList());
                    ConditionGroupBO conditionGroupBO =  new ConditionGroupBO();
                    conditionGroupBO.setConditionGroupId(testConditionGroupPO.getId());
                    conditionGroupBO.setCombinedConditionDescription(testConditionGroupPO.getCombinedConditionDescription());
//                    conditionGroupBO.setRequirement(testConditionGroupPO);
                    conditionGroupBO.setTestLineInstanceId(testConditionGroupPO.getTestLineInstanceId());

                    List<ConditionGroupLanguageBO> languageList = new ArrayList<>();
                    ConditionGroupLanguageBO conditionGroupBOEn =  new ConditionGroupLanguageBO();
                    conditionGroupBOEn.setCombinedConditionDescription(testConditionGroupPO.getCombinedConditionDescription());
//                    conditionGroupBOEn.setConditionGroupName(testConditionGroupPO);
                    conditionGroupBOEn.setLanguageId(LanguageType.English.getLanguageId());
                    languageList.add(conditionGroupBOEn);

                    if(Func.isNotEmpty(testConditionGroupLanguagePOList)){
                        for (TestConditionGroupLanguagePO testConditionGroupLanguagePO : testConditionGroupLanguagePOList) {
                            if(chineseFlag &&  LanguageType.check(testConditionGroupLanguagePO.getLanguageId(),LanguageType.Chinese) && Func.isNotEmpty(testConditionGroupLanguagePO.getCombinedConditionDescription())) {
                                conditionGroupBO.setCombinedConditionDescription(testConditionGroupLanguagePO.getCombinedConditionDescription());
                            }
                            ConditionGroupLanguageBO conditionGroupBOCn =  new ConditionGroupLanguageBO();
                            conditionGroupBOCn.setCombinedConditionDescription(testConditionGroupLanguagePO.getCombinedConditionDescription());
                            //conditionGroupBOCn.setConditionGroupName(testConditionGroupPO);
                            conditionGroupBOCn.setLanguageId(testConditionGroupLanguagePO.getLanguageId());
                            languageList.add(conditionGroupBOCn);
                        }
                    }
                    conditionGroupBO.setLanguageList(languageList);
                    List<PPConditionGroupBO> ppConditionGroupList = new ArrayList<>();
                    if(Func.isNotEmpty(allTestPpConditionGroupList)){
                        List<TestPpConditionGroupPO> testPpConditionGroupPOList =
                                allTestPpConditionGroupList.stream().filter(item -> Func.equalsSafe(item.getConditionGroupId(), testConditionGroupPO.getId())).collect(Collectors.toList());
                        for (TestPpConditionGroupPO testPpConditionGroupPO : testPpConditionGroupPOList) {
                            PPConditionGroupBO ppConditionGroupBO = new PPConditionGroupBO();
                            ppConditionGroupBO.setId(testPpConditionGroupPO.getId());
                            ppConditionGroupBO.setConditionGroupId(testPpConditionGroupPO.getConditionGroupId());
                            ppConditionGroupBO.setPpTestLineRelId(testPpConditionGroupPO.getPpTestLineRelId());
                            ppConditionGroupBO.setLanguageType(testPpConditionGroupPO.getLanguageType());
                            ppConditionGroupBO.setGroupFootNotes(testPpConditionGroupPO.getGroupFootNotes());
                            if(Func.isNotEmpty(testLineBO.getPpTestLineRel())){
                                ppConditionGroupBO.setPpNo(testLineBO.getPpTestLineRel().getPpNo());
                                ppConditionGroupBO.setPpName(testLineBO.getPpTestLineRel().getPpName());
                            }
//                            ppConditionGroupBO.setSampleNos("");
                            ppConditionGroupList.add(ppConditionGroupBO);
                        }
                    }
                    conditionGroupBO.setPpConditionGroupList(ppConditionGroupList);
                    conditionGroupBOList.add(conditionGroupBO);
                }
            }
        }
        testLineBO.setConditionGroupList(conditionGroupBOList);
    }
    protected void buildWI(TestLineBO testLineBO, TestLineQueryContext<OrderTestLineReq, TestLineBO> context){
        List<WorkingInstructionRsp> workingInstructionRspList = context.getWorkingInstructionRspList();
        List<OrderCitationPO> orderCitationList = context.getOrderCitationList();

        WorkInstructBO wi = testLineBO.getWi();
        if(Func.isEmpty(wi)){
            wi = new WorkInstructBO();
        }
        List<WorkingInstructionBO> wiList = new ArrayList<>();
//        orderCitationList
        Long ppCitationBaseId = null;
        Long ppBaseId = Func.isNotEmpty(testLineBO.getPpTestLineRel())?testLineBO.getPpTestLineRel().getPpBaseId():null;
        if(Func.isNotEmpty(ppBaseId)) {
            if(Func.isNotEmpty(orderCitationList)) {
                Set<OrderCitationPO> ppCitationList = orderCitationList.stream().filter(item -> {
                    return NumberUtil.equals(ppBaseId,item.getPpBaseId());
                }).collect(Collectors.toSet());
                if (Func.isNotEmpty(ppCitationList)) {
                    ppCitationBaseId = ppCitationList.stream().map(OrderCitationPO::getCitationBaseId).findAny().orElse(null);
                }
            }
        }
        Long citationBaseId = null;
        if(Func.isNotEmpty(testLineBO.getCitation()) && Func.isNotEmpty(testLineBO.getCitation().getCitationBaseId())){
            citationBaseId = testLineBO.getCitation().getCitationBaseId();
        }
//        ppCitationBaseIdList.contains();
        Long finalCitationBaseId = citationBaseId;
        Long finalPpCitationBaseId = ppCitationBaseId;
        List<WorkingInstructionRsp> currentTLWIList  = workingInstructionRspList.stream().filter(workingInstructionRsp ->
                NumberUtil.equals(testLineBO.getTestLineVersionId(),workingInstructionRsp.getTestLineVersionId())
                        &&  NumberUtil.equals(Func.isEmpty(finalPpCitationBaseId)? finalCitationBaseId : finalPpCitationBaseId,workingInstructionRsp.getReqCitationBaseId())).collect(Collectors.toList());

        //WIForCS
        WorkingInstructionRsp workingInstructionForCSRsp = currentTLWIList.stream().filter(item -> CategoryEnums.checkCode(item.getCategoryId(), CategoryEnums.WIForCS)).findAny().orElse(null);
        if(Func.isNotEmpty(workingInstructionForCSRsp)){
            wi.setWiForCS(workingInstructionForCSRsp.getWorkingInstructionText());
            WorkingInstructionBO workingInstructionForCS = new WorkingInstructionBO();
            workingInstructionForCS.setWorkInstructionId(workingInstructionForCSRsp.getWorkInstructionId());
            workingInstructionForCS.setWorkingInstructionText(workingInstructionForCSRsp.getWorkingInstructionText());
            workingInstructionForCS.setCategoryId(workingInstructionForCSRsp.getCategoryId());
            workingInstructionForCS.setCategoryName(workingInstructionForCSRsp.getCategoryName());
            List<WorkingInstructionLanBO> languages = new ArrayList<>();
            if(Func.isNotEmpty(workingInstructionForCSRsp.getLanguages())){
                List<WorkingInstructionLanRsp> workingInstructionLanRspList = workingInstructionForCSRsp.getLanguages();
                for (WorkingInstructionLanRsp workingInstructionLanRsp : workingInstructionLanRspList) {
                    if(Func.isEmpty(workingInstructionLanRsp.getWorkingInstructionText())){
                        continue;
                    }
                    if(LanguageType.check(workingInstructionLanRsp.getLanguageId(),LanguageType.Chinese)){
                        wi.setWiForCS(workingInstructionLanRsp.getWorkingInstructionText());
                        workingInstructionForCS.setCategoryName(workingInstructionLanRsp.getCategoryName());
                    }
                    WorkingInstructionLanBO workingInstructionLanBO = new WorkingInstructionLanBO();
                    workingInstructionLanBO.setCategoryName(workingInstructionLanRsp.getCategoryName());
                    workingInstructionLanBO.setWorkingInstructionText(workingInstructionLanRsp.getWorkingInstructionText());
                    workingInstructionLanBO.setLanguageId(workingInstructionLanRsp.getLanguageId());
                    languages.add(workingInstructionLanBO);
                }
            }
            WorkingInstructionLanBO workingInstructionLanEn = new WorkingInstructionLanBO();
            workingInstructionLanEn.setCategoryName(workingInstructionForCSRsp.getCategoryName());
            workingInstructionLanEn.setWorkingInstructionText(workingInstructionForCSRsp.getWorkingInstructionText());
            workingInstructionLanEn.setLanguageId(LanguageType.English.getLanguageId());
            languages.add(workingInstructionLanEn);
            workingInstructionForCS.setLanguageList(languages);
            wiList.add(workingInstructionForCS);
        }
        //WIForTest
        WorkingInstructionRsp workingInstructionForTestingRsp = currentTLWIList.stream().filter(item -> CategoryEnums.checkCode(item.getCategoryId(), CategoryEnums.WIForTesting)).findAny().orElse(null);
        if(Func.isNotEmpty(workingInstructionForTestingRsp)){
            wi.setWiForTest(workingInstructionForTestingRsp.getWorkingInstructionText());
            WorkingInstructionBO workingInstructionForTest = new WorkingInstructionBO();
            workingInstructionForTest.setWorkInstructionId(workingInstructionForTestingRsp.getWorkInstructionId());
            workingInstructionForTest.setWorkingInstructionText(workingInstructionForTestingRsp.getWorkingInstructionText());
            workingInstructionForTest.setCategoryId(workingInstructionForTestingRsp.getCategoryId());
            workingInstructionForTest.setCategoryName(workingInstructionForTestingRsp.getCategoryName());
            List<WorkingInstructionLanBO> languages = new ArrayList<>();
            if(Func.isNotEmpty(workingInstructionForTestingRsp.getLanguages())){
                List<WorkingInstructionLanRsp> workingInstructionLanRspList = workingInstructionForTestingRsp.getLanguages();
                for (WorkingInstructionLanRsp workingInstructionLanRsp : workingInstructionLanRspList) {
                    if(Func.isEmpty(workingInstructionLanRsp.getWorkingInstructionText())){
                        continue;
                    }
                    if(LanguageType.check(workingInstructionLanRsp.getLanguageId(),LanguageType.Chinese)){
                        wi.setWiForTest(workingInstructionLanRsp.getWorkingInstructionText());
                        workingInstructionForTest.setCategoryName(workingInstructionLanRsp.getCategoryName());
                    }
                    WorkingInstructionLanBO workingInstructionLanBO = new WorkingInstructionLanBO();
                    workingInstructionLanBO.setCategoryName(workingInstructionLanRsp.getCategoryName());
                    workingInstructionLanBO.setWorkingInstructionText(workingInstructionLanRsp.getWorkingInstructionText());
                    workingInstructionLanBO.setLanguageId(workingInstructionLanRsp.getLanguageId());
                    languages.add(workingInstructionLanBO);
                }
            }
            WorkingInstructionLanBO workingInstructionLanEn = new WorkingInstructionLanBO();
            workingInstructionLanEn.setCategoryName(workingInstructionForTestingRsp.getCategoryName());
            workingInstructionLanEn.setWorkingInstructionText(workingInstructionForTestingRsp.getWorkingInstructionText());
            workingInstructionLanEn.setLanguageId(LanguageType.English.getLanguageId());
            languages.add(workingInstructionLanEn);
            workingInstructionForTest.setLanguageList(languages);
            wiList.add(workingInstructionForTest);
        }

        if(Func.isNotEmpty(testLineBO.getSampleSegegrationWIID())){
            WorkingInstructionRsp workingInstructionForSampleCuttingRsp = currentTLWIList.stream().filter(item -> Func.equalsSafe(item.getWorkInstructionId(),testLineBO.getSampleSegegrationWIID())).findAny().orElse(null);
            if(Func.isNotEmpty(workingInstructionForSampleCuttingRsp)){
                WorkingInstructionBO workingInstructionForSampleCutting = new WorkingInstructionBO();
                workingInstructionForSampleCutting.setWorkInstructionId(workingInstructionForSampleCuttingRsp.getWorkInstructionId());
                workingInstructionForSampleCutting.setWorkingInstructionText(testLineBO.getSampleSegegrationWIText());
                workingInstructionForSampleCutting.setCategoryId(workingInstructionForSampleCuttingRsp.getCategoryId());
                workingInstructionForSampleCutting.setCategoryName(workingInstructionForSampleCuttingRsp.getCategoryName());
                List<WorkingInstructionLanBO> languages = new ArrayList<>();
                WorkingInstructionLanBO workingInstructionLanEn = new WorkingInstructionLanBO();
                workingInstructionLanEn.setCategoryName(workingInstructionForSampleCuttingRsp.getCategoryName());
                workingInstructionLanEn.setWorkingInstructionText(testLineBO.getSampleSegegrationWIText());
                workingInstructionLanEn.setLanguageId(LanguageType.English.getLanguageId());
                languages.add(workingInstructionLanEn);
                List<WorkingInstructionLanRsp> workingInstructionLanRspList = workingInstructionForSampleCuttingRsp.getLanguages();
                if(Func.isNotEmpty(workingInstructionLanRspList)){
                    WorkingInstructionLanRsp workingInstructionCnRsp = workingInstructionLanRspList.stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese)).findAny().orElse(null);
                    WorkingInstructionLanBO workingInstructionLanCn = new WorkingInstructionLanBO();
                    workingInstructionLanCn.setLanguageId(LanguageType.Chinese.getLanguageId());
                    workingInstructionLanCn.setCategoryName(workingInstructionForSampleCuttingRsp.getCategoryName());
                    if(Func.isNotEmpty(workingInstructionCnRsp)){
                        workingInstructionForSampleCutting.setCategoryName(workingInstructionCnRsp.getCategoryName());
                        workingInstructionLanCn.setCategoryName(workingInstructionCnRsp.getCategoryName());
                    }
                    workingInstructionLanCn.setWorkingInstructionText(testLineBO.getSampleSegegrationWIText());
                    languages.add(workingInstructionLanCn);
                }else{
                    WorkingInstructionLanBO workingInstructionLanCn = new WorkingInstructionLanBO();
                    workingInstructionLanCn.setCategoryName(workingInstructionForSampleCuttingRsp.getCategoryName());
                    workingInstructionLanCn.setWorkingInstructionText(testLineBO.getSampleSegegrationWIText());
                    workingInstructionLanCn.setLanguageId(LanguageType.Chinese.getLanguageId());
                    languages.add(workingInstructionLanCn);
                }
                workingInstructionForSampleCutting.setLanguageList(languages);
                wiList.add(workingInstructionForSampleCutting);
            }
        }else{
            WorkingInstructionRsp workingInstructionForSampleCuttingRsp = currentTLWIList.stream().filter(item -> CategoryEnums.checkCode(item.getCategoryId(), CategoryEnums.SamplePreparation_Cutting)).findAny().orElse(null);
            if(Func.isNotEmpty(workingInstructionForSampleCuttingRsp)){
                WorkingInstructionBO workingInstructionForSampleCutting = new WorkingInstructionBO();
                workingInstructionForSampleCutting.setWorkInstructionId(workingInstructionForSampleCuttingRsp.getWorkInstructionId());
                workingInstructionForSampleCutting.setWorkingInstructionText(workingInstructionForSampleCuttingRsp.getWorkingInstructionText());
                workingInstructionForSampleCutting.setCategoryId(workingInstructionForSampleCuttingRsp.getCategoryId());
                workingInstructionForSampleCutting.setCategoryName(workingInstructionForSampleCuttingRsp.getCategoryName());
                List<WorkingInstructionLanBO> languages = new ArrayList<>();
                if(Func.isNotEmpty(workingInstructionForSampleCuttingRsp.getLanguages())){
                    List<WorkingInstructionLanRsp> workingInstructionLanRspList = workingInstructionForSampleCuttingRsp.getLanguages();
                    for (WorkingInstructionLanRsp workingInstructionLanRsp : workingInstructionLanRspList) {
                        if(Func.isEmpty(workingInstructionLanRsp.getWorkingInstructionText())){
                            continue;
                        }
                        if(LanguageType.check(workingInstructionLanRsp.getLanguageId(),LanguageType.Chinese)){
                            workingInstructionForSampleCutting.setCategoryName(workingInstructionLanRsp.getCategoryName());
                        }
                        WorkingInstructionLanBO workingInstructionLanBO = new WorkingInstructionLanBO();
                        workingInstructionLanBO.setCategoryName(workingInstructionLanRsp.getCategoryName());
                        workingInstructionLanBO.setWorkingInstructionText(workingInstructionLanRsp.getWorkingInstructionText());
                        workingInstructionLanBO.setLanguageId(workingInstructionLanRsp.getLanguageId());
                        languages.add(workingInstructionLanBO);
                    }
                }
                WorkingInstructionLanBO workingInstructionLanEn = new WorkingInstructionLanBO();
                workingInstructionLanEn.setCategoryName(workingInstructionForSampleCuttingRsp.getCategoryName());
                workingInstructionLanEn.setWorkingInstructionText(workingInstructionForSampleCuttingRsp.getWorkingInstructionText());
                workingInstructionLanEn.setLanguageId(LanguageType.English.getLanguageId());
                languages.add(workingInstructionLanEn);
                workingInstructionForSampleCutting.setLanguageList(languages);
                wiList.add(workingInstructionForSampleCutting);
            }
        }
        testLineBO.setWiList(wiList);
    }
    protected void buildTestAnalyteList(TestLineBO testLineBO, TestLineQueryContext<OrderTestLineReq, TestLineBO> context){
        List<TestLineAnalyteRsp> testLineAnalyteRspList = context.getTestLineAnalyteRspList();
        List<AnalytePO> analytePOList = context.getAnalyteList();
        if(Func.isNotEmpty(analytePOList)){
            List<AnalytePO> analytePOS = analytePOList.stream().filter(item -> Func.equalsSafe(item.getTestLineInstanceId(), testLineBO.getTestLineInstanceId())).collect(Collectors.toList());
            List<AnalyteBO> analyteBOList = new ArrayList<>();
            if(Func.isNotEmpty(analytePOS)){
                for (AnalytePO analytePO : analytePOS) {
                    AnalyteBO analyteBO = new AnalyteBO();
                    analyteBO.setAnalyteInstanceId(analytePO.getId());
                    analyteBO.setAnalyteBaseId(analytePO.getAnalyteBaseId());
                    analyteBO.setAnalyteId(analytePO.getAnalyteId());
                    analyteBO.setAnalyteName(analytePO.getTestAnalyteName());
                    analyteBO.setAnalyteSeq(analytePO.getTestAnalyteSeq());
                    analyteBO.setUnitBaseId(analytePO.getUnitBaseId());
                    analyteBO.setReportUnit(analytePO.getReportUnit());
                    analyteBO.setCasNo(analytePO.getCasNo());
                    analyteBOList.add(analyteBO);
                }
            }
            testLineBO.setAnalyteList(analyteBOList);
        }
        boolean chineseFlag = context.isChineseFlag();

        if(Func.isNotEmpty(testLineBO.getAnalyteList()) && Func.isNotEmpty(testLineAnalyteRspList)){
            List<AnalyteBO> analyteList = testLineBO.getAnalyteList();
            testLineAnalyteRspList = testLineAnalyteRspList.stream().filter(item->Func.equalsSafe(item.getTestLineVersionId(),testLineBO.getTestLineVersionId())).collect(Collectors.toList());
            for (AnalyteBO analyteBO : analyteList) {
                TestLineAnalyteRsp testLineAnalyteRsp = testLineAnalyteRspList.stream().filter(item -> Func.equalsSafe(analyteBO.getAnalyteId(), item.getTestAnalyteId())).findAny().orElse(null);
                if(Func.isNotEmpty(testLineAnalyteRsp)){
//                        analyteBO.setAnalyteInstanceId("");
                    analyteBO.setAnalyteBaseId(testLineAnalyteRsp.getAnalyteBaseId());
                    //默认设置英文名称
                    analyteBO.setAnalyteName(testLineAnalyteRsp.getTestAnalyteDesc());
                    analyteBO.setAnalyteSeq(testLineAnalyteRsp.getTestAnalyteSeq());
                    analyteBO.setCasNo(testLineAnalyteRsp.getTestAnalyteCasNumber());
                    //处理AnalyteUnit
                    Long unitBaseId = analyteBO.getUnitBaseId();
                    List<TestLineAnalyteUnitRsp> units = testLineAnalyteRsp.getUnits();
                    TestLineAnalyteUnitRsp testLineAnalyteUnitRsp = null;
                    if(Func.isNotEmpty(units)){
                        testLineAnalyteUnitRsp = units.stream().filter(item -> Func.equalsSafe(item.getUnitBaseId(), unitBaseId)).findAny().orElse(null);
                        if(Func.isNotEmpty(testLineAnalyteUnitRsp) && Func.isNotEmpty(testLineAnalyteUnitRsp.getUnitShortDepiction())){
                            analyteBO.setReportUnit(testLineAnalyteUnitRsp.getUnitShortDepiction());
                        }
                    }
                    //处理Analyte Language
                    List<TestAnalyteLangReq> languages = testLineAnalyteRsp.getLanguages();
                    if(Func.isNotEmpty(languages)){
                        List<AnalyteLanguageBO> languageList = new ArrayList<>();
                        for (TestAnalyteLangReq language : languages) {
                            AnalyteLanguageBO analyteLanguageBOEn = new AnalyteLanguageBO();
                            analyteLanguageBOEn.setAnalyteName(testLineAnalyteRsp.getTestAnalyteDesc());
                            analyteLanguageBOEn.setReportUnit(analyteBO.getReportUnit());
                            if(Func.isNotEmpty(testLineAnalyteUnitRsp)){
                                analyteLanguageBOEn.setReportUnit(testLineAnalyteUnitRsp.getUnitShortDepiction());
                            }
                            analyteLanguageBOEn.setLanguageId(LanguageType.English.getLanguageId());
                            languageList.add(analyteLanguageBOEn);
                            if(chineseFlag && LanguageType.check(language.getLanguageId(),LanguageType.Chinese) && Func.isNotEmpty(language.getTestAnalyteDesc())){
                                analyteBO.setAnalyteName(language.getTestAnalyteDesc());
                            }
                            AnalyteLanguageBO analyteLanguageBOCn = new AnalyteLanguageBO();
                            analyteLanguageBOCn.setAnalyteName(language.getTestAnalyteDesc());
                            //处理Analyte Unit Language
                            if(Func.isNotEmpty(testLineAnalyteUnitRsp) && Func.isNotEmpty(testLineAnalyteUnitRsp.getLanguage()) && Func.equalsSafe(language.getLanguageId(),testLineAnalyteUnitRsp.getLanguage().getLanguageId())  && Func.isNotEmpty(testLineAnalyteUnitRsp.getLanguage())){
                                analyteLanguageBOCn.setReportUnit(testLineAnalyteUnitRsp.getLanguage().getUnitShortDepiction());
                                if(chineseFlag && LanguageType.check(testLineAnalyteUnitRsp.getLanguage().getLanguageId(),LanguageType.Chinese)){
                                    analyteBO.setReportUnit(testLineAnalyteUnitRsp.getLanguage().getUnitShortDepiction());
                                }
                            }else{
                                analyteLanguageBOCn.setReportUnit(analyteBO.getReportUnit());
                            }
                            analyteLanguageBOCn.setLanguageId(language.getLanguageId());
                            languageList.add(analyteLanguageBOCn);
                        }
                        analyteBO.setLanguageList(languageList);
                    }
                }
            }
            List<TestLineAnalyteRsp> finalTestLineAnalyteRspList = testLineAnalyteRspList;
//            GPO2-16071 Edit TL时保存的Analyte是无序的，此处处理需要按LocalTrims接口返回的Analyte顺序排序
            Collections.sort(analyteList, (s1, s2) -> {
                int index1 = getIndexByLocalTrimsAnalyteList(finalTestLineAnalyteRspList, s1.getAnalyteId());
                int index2 = getIndexByLocalTrimsAnalyteList(finalTestLineAnalyteRspList, s2.getAnalyteId());
                return Integer.compare(index1, index2);
            });
            testLineBO.setAnalyteList(analyteList);
        }
    }
    private  int getIndexByLocalTrimsAnalyteList(List<TestLineAnalyteRsp> testLineAnalyteRspList, Integer analyteId) {
        if(Func.isNotEmpty(testLineAnalyteRspList)){
            for (int i = 0; i < testLineAnalyteRspList.size(); i++) {
                if (Func.equalsSafe(analyteId,testLineAnalyteRspList.get(i).getTestAnalyteId())) {
                    return i;
                }
            }
        }
        return -1;
    }

    protected void buildTestExecutionList(TestLineBO testLineBO, TestLineQueryContext<OrderTestLineReq, TestLineBO> context){
        List<TestLineExecutionBO> testExecutionList = context.getTestExecutionList();
        if(Func.isNotEmpty(testExecutionList)) {
            if(Constants.OBJECT.JOB.TESTLINE_GROUP_MODE.ORDER.equals(context.getTestLineGroupModel())) {
                List<TestLineExecutionBO> currentTLExecutionList = testExecutionList.stream().filter(item -> {
                    return testLineBO.getTestLineInstanceId().equals(item.getTestLineInstanceId());
                }).collect(Collectors.toList());
                testLineBO.setTestExecutionList(currentTLExecutionList);
            }else{
                List<TestLineExecutionBO> currentTLExecutionList = testExecutionList.stream().filter(item -> {
                    return testLineBO.getTestLineInstanceId().equals(item.getTestLineInstanceId());
                }).collect(Collectors.toList());
                if(Func.isNotEmpty(currentTLExecutionList)){
                    List<TestLineExecutionBO> resultExecutionList = new ArrayList<>();
                    //分包
                    List<TestLineExecutionBO> subcontractExecutionBoList = currentTLExecutionList.stream().filter(item -> TestExecutionType.check(item.getTestExecutionType(), TestExecutionType.SUBCONTRACT)).collect(Collectors.toList());
                    if(Func.isNotEmpty(subcontractExecutionBoList)){
                        resultExecutionList.addAll(subcontractExecutionBoList);
                    }
                    //job
                    List<TestLineExecutionBO> jobExecutionBoList = currentTLExecutionList.stream().filter(item -> TestExecutionType.check(item.getTestExecutionType(), TestExecutionType.JOB)).collect(Collectors.toList());
                    if(Func.isNotEmpty(testLineBO.getLabSectionList())){
                        List<String> testLineLabSectionIdList = testLineBO.getLabSectionList().stream().map(LabSectionBO::getLabSectionId).map(Func::toStr).collect(Collectors.toList());
                        jobExecutionBoList = jobExecutionBoList.stream().filter(item -> {
                            return testLineLabSectionIdList.contains(item.getTestExecutionOrg());
                        }).collect(Collectors.toList());
                        if(Func.isNotEmpty(jobExecutionBoList)){
                            resultExecutionList.addAll(jobExecutionBoList);
                        }
                    }
                    testLineBO.setTestExecutionList(resultExecutionList);
                }
            }
        }
    }

    protected BaseResponse queryLabSection(TestLineQueryContext<OrderTestLineReq, TestLineBO> context){
        OrderTestLineReq orderTestLineRequest = new OrderTestLineReq();
        orderTestLineRequest.setOrderIdList(context.getOrderIdList());
        orderTestLineRequest.setOrderNoList(context.getParam().getOrderNoList());
        orderTestLineRequest.setTestLineInstanceIdList(context.getTestLineInstanceIdList());
        BaseResponse<List<TestLineLabSectionRelationshipPO>> baseResponse = testLineLabSectionService.queryByOrderTestLine(orderTestLineRequest);
        Set<Long> labSectionBaseIdList = null;
        if(Func.isNotEmpty(baseResponse) && Func.isNotEmpty(baseResponse.getData())){
            List<TestLineLabSectionRelationshipPO> testLineLabSectionRelationshipList = baseResponse.getData();
            labSectionBaseIdList = testLineLabSectionRelationshipList.stream().map(TestLineLabSectionRelationshipPO::getLabSectionBaseId).collect(Collectors.toSet());
        }else{
            List<LabSectionBO> testLineExecutionBOList = context.getTestLineList().stream().filter(testLineBO -> Func.isNotEmpty(testLineBO.getLabSectionList())).flatMap(tl -> tl.getLabSectionList().stream()).collect(Collectors.toList());
            if(Func.isNotEmpty(testLineExecutionBOList)){
                labSectionBaseIdList = testLineExecutionBOList.stream().map(LabSectionBO::getLabSectionBaseId).collect(Collectors.toSet());
            }
        }
        if(Func.isEmpty(labSectionBaseIdList)){
            return BaseResponse.newSuccessInstance(true);
        }
        LabSectionReq labSectionBaseIdReq = new LabSectionReq();
        labSectionBaseIdReq.setLabSectionBaseIdList(labSectionBaseIdList);
        BaseResponse<List<LabSectionBO>> labSectionResponse =labSectionService.queryLabSection(labSectionBaseIdReq);
        List<LabSectionBO> labSectionList = labSectionResponse.getData();
        context.setLabSectionList(labSectionList);
        return BaseResponse.newSuccessInstance(true);
    }

    protected BaseResponse queryTestLineAnalyte(TestLineQueryContext<OrderTestLineReq, TestLineBO> context){
        OrderTestLineReq orderTestLineRequest = new OrderTestLineReq();
        orderTestLineRequest.setTestLineInstanceIdList(context.getTestLineInstanceIdList());
        List<AnalytePO> analytePOList = analyteService.query(orderTestLineRequest);
        context.setAnalyteList(analytePOList);
        return BaseResponse.newSuccessInstance(true);
    }


    protected BaseResponse queryPPCitation(TestLineQueryContext<OrderTestLineReq, TestLineBO> context){
        Set<String> orderIdList = context.getOrderIdList();
        BaseResponse<List<OrderCitationPO>> orderCitationResponse = orderCitationService.queryByOrderId(orderIdList);
        List<OrderCitationPO> orderCitationList = orderCitationResponse.getData();
        log.info(Func.toJson(orderCitationResponse));
        context.setOrderCitationList(orderCitationList);
        return BaseResponse.newSuccessInstance(true);
    }

    protected BaseResponse queryPPBaseInfo(TestLineQueryContext<OrderTestLineReq, TestLineBO> context){
        List<TestLineBO> testLineList = context.getTestLineList();
        Set<Long> ppBaseIdSet = testLineList.stream().filter(Func::isNotEmpty).map(TestLineBO::getPpTestLineRel).filter(Func::isNotEmpty).map(PPTestLineRelBO::getPpBaseId).filter(Func::isNotEmpty).collect(Collectors.toSet());
        Set<Long> rootPpBaseIdSet = testLineList.stream().filter(Func::isNotEmpty).map(TestLineBO::getPpTestLineRel).filter(Func::isNotEmpty).map(PPTestLineRelBO::getRootPPBaseId).filter(Func::isNotEmpty).collect(Collectors.toSet());
        ppBaseIdSet.addAll(rootPpBaseIdSet);
        GetPpInfoReq ppInfoReq = new GetPpInfoReq();
        ppInfoReq.setCallerBU(context.getProductLineCode());
        ppInfoReq.setPpBaseIds(ppBaseIdSet);
        ppInfoReq.setLanguageIds(Lists.newArrayList(LanguageType.Chinese.getLanguageId()));
        List<GetPpBaseInfoRsp> ppBaseInfoRspList = trimsClient.getPpBaseInfo(ppInfoReq).getData();
        context.setPpBaseInfoRspList(ppBaseInfoRspList);
        return BaseResponse.newSuccessInstance(true);
    }

    protected BaseResponse querySimplifyName(TestLineQueryContext<OrderTestLineReq, TestLineBO> context){
        Set<String> orderIdList = context.getOrderIdList();
        BaseResponse<List<OrderLanguagePO>> orderLanguageResponse = orderLanguageService.queryByOrderId(orderIdList);
        List<OrderLanguagePO> orderLanguageList = orderLanguageResponse.getData();
        List<OrderLanguagePO> citationLanguageList  = null;
        List<OrderLanguagePO> ppCitationLanguageList = null;
        List<OrderLanguagePO> ppTestLineLanguageList = null;
        List<OrderCitationPO> orderCitationList = context.getOrderCitationList();
        if(Func.isNotEmpty(orderLanguageList)) {
            citationLanguageList = orderLanguageList.stream().filter(item -> {return LangTypeEnum.check(item.getLangType(),LangTypeEnum.Citation);}).collect(Collectors.toList());
            ppCitationLanguageList = orderLanguageList.stream().filter(item -> {return LangTypeEnum.check(item.getLangType(),LangTypeEnum.PpCitation);}).collect(Collectors.toList());
            ppTestLineLanguageList = orderLanguageList.stream().filter(item -> {return LangTypeEnum.check(item.getLangType(),LangTypeEnum.PpArtifactRel);}).collect(Collectors.toList());
        }
        log.info(Func.toJson(orderLanguageResponse));

        // 2.4、查询测试项目名称
        TestLineSimplifyInfoReq testLineSimplifyInfoReq = new TestLineSimplifyInfoReq();
        testLineSimplifyInfoReq.setCallerBU(context.getProductLineCode());
        List<TestLineSimplifyInfo> testLineSimplifyList = new ArrayList<>();
        for(TestLineBO testLine:context.getTestLineList()){
            TestLineSimplifyInfo testLineSimplifyInfo = new TestLineSimplifyInfo();
            if(Func.isNotEmpty(testLine.getLabSectionList()) && Func.isNotEmpty(testLine.getLabSectionList().get(0).getLabSectionBaseId())){
                testLineSimplifyInfo.setLabSectionBaseId(testLine.getLabSectionList().get(0).getLabSectionBaseId());
            }
            testLineSimplifyInfo.setTestLineBaseId(testLine.getTestLineBaseId());
            if(Func.isNotEmpty(testLine.getCitation())) {
                testLineSimplifyInfo.setCitationBaseId(testLine.getCitation().getCitationBaseId());
            }
            if(Func.isNotEmpty(citationLanguageList) && Func.isNotEmpty(testLine.getCitation())) {
                OrderLanguagePO citationLanguage = citationLanguageList.stream().filter(item -> {return Func.equals(testLine.getOrderId(),item.getOrderId()) && Func.equalsSafe(testLine.getCitation().getCitationBaseId(),item.getObjectBaseId());}).findFirst().orElse(null);
                if(Func.isNotEmpty(citationLanguage)){
                    testLineSimplifyInfo.setCitationLangBaseId(citationLanguage.getLangBaseId());
                }
            }
            //TODO Local Trims 修改为数组
            if(Func.isNotEmpty(testLine.getPpTestLineRel())) {
                testLineSimplifyInfo.setRootPpBaseId(testLine.getPpTestLineRel().getRootPPBaseId());
                testLineSimplifyInfo.setPpArtifactRelId(testLine.getPpTestLineRel().getPpArtifactRelId());
                Set<Long> ppBaseIdList = Sets.newHashSet(testLine.getPpTestLineRel().getPpBaseId());
                if(Func.isNotEmpty(ppBaseIdList)){
                    if(Func.isNotEmpty(context.getOrderCitationList())) {
                        Set<OrderCitationPO> ppCitationList = orderCitationList.stream().filter(item -> {
                            return ppBaseIdList.contains(item.getPpBaseId());
                        }).collect(Collectors.toSet());
                        if (Func.isNotEmpty(ppCitationList)) {
                            Set<Long> ppCitationBaseIdList = ppCitationList.stream().map(OrderCitationPO::getCitationBaseId).collect(Collectors.toSet());
                            testLineSimplifyInfo.setPpCitationBaseIds(ppCitationBaseIdList);
                        }
                    }
                    if(Func.isNotEmpty(ppTestLineLanguageList)){
                        Set<OrderLanguagePO> ppTestLineLangList = ppTestLineLanguageList.stream().filter(item -> {return Func.equals(testLine.getOrderId(),item.getOrderId()) && Func.equalsSafe(testLine.getPpTestLineRel().getPpArtifactRelId(),item.getObjectBaseId());}).collect(Collectors.toSet());
                        if(Func.isNotEmpty(ppTestLineLangList)){
                            Long ppArtifactLangBaseId = ppTestLineLangList.stream().mapToLong(OrderLanguagePO::getLangBaseId).max().getAsLong();
                            testLineSimplifyInfo.setPpArtifactLangBaseId(ppArtifactLangBaseId);
                        }
                    }
                    if(Func.isNotEmpty(ppCitationLanguageList)){
                        Set<OrderLanguagePO> ppCitationLangList = ppCitationLanguageList.stream().filter(item -> {return Func.equals(testLine.getOrderId(),item.getOrderId()) && ppBaseIdList.contains(item.getObjectBaseId());}).collect(Collectors.toSet());
                        if(Func.isNotEmpty(ppCitationLangList)){
                            testLineSimplifyInfo.setPpCitationLangBaseIds(ppCitationLangList.stream().map(OrderLanguagePO::getLangBaseId).collect(Collectors.toSet()));
                        }
                    }
                }
            }
            testLineSimplifyList.add(testLineSimplifyInfo);
        }
        testLineSimplifyInfoReq.setTestLines(testLineSimplifyList);
        BaseResponse<List<TestLineSimplifyInfoRsp>> trimsResponse = trimsClient.getTestLineSimplifyInfoList(testLineSimplifyInfoReq);
        List<TestLineSimplifyInfoRsp> testLineSimplifyInfoRspList = trimsResponse.getData();
        context.setTestLineSimplifyInfoRspList(testLineSimplifyInfoRspList);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse<List<TestLineBO>> execute(TestLineQueryContext<OrderTestLineReq, TestLineBO> context) {
        //2、对象转换
        BaseResponse buildResponse = buildDomain(context);
        if(buildResponse.isFail()){
            return buildResponse;
        }
        //3、对象验证
        BaseResponse validateResponse = this.validateDomain(context);
        if(validateResponse.isFail()){
            return validateResponse;
        }
        return BaseResponse.newSuccessInstance(context.getTestLineList());
    }

    @Override
    public BaseResponse before(TestLineQueryContext<OrderTestLineReq, TestLineBO> context) {
        context.setTestLineSimplifyInfoRspList(Lists.newArrayList());
        context.setPpBaseInfoRspList(Lists.newArrayList());
        context.setTestExecutionList(Lists.newArrayList());
        context.setCitationBaseInfoRspList(Lists.newArrayList());
        //1、查询TestLine列表
        List<TestLineBO> testLineList = context.getTestLineList();
        if(Func.isEmpty(context.getTestLineList())){
            return BaseResponse.newSuccessInstance(context.getTestLineList());
        }
        String testLineGroupModel = buParam.testLineGroupMode(context.getProductLineCode()).getData();
        context.setTestLineGroupModel(testLineGroupModel);
        Set<String> orderIdList = testLineList.stream().map(TestLineBO::getOrderId).collect(Collectors.toSet());
        Set<String> testLineInstanceIdList = testLineList.stream().map(TestLineBO::getTestLineInstanceId).collect(Collectors.toSet());
        context.setOrderIdList(orderIdList);
        context.setTestLineInstanceIdList(testLineInstanceIdList);
        if(Func.isNotEmpty(testLineInstanceIdList)) {
            List<TestLineExecutionBO> testExecutionList = testLineMapper.selectTestExecution(context.getTestLineInstanceIdList(), context.getTestLineGroupModel());
            context.setTestExecutionList(testExecutionList);
        }
        LanguageType primaryLanguage = LanguageType.English;
        BaseResponse<LanguageType> primaryLanguageRsp = ibuParam.getPrimaryLanguage(ProductLineContextHolder.getProductLineCode());
        List<BuParamValueRsp> buParamMainCurrencyList = null;
        if (Func.isNotEmpty(primaryLanguageRsp)) {
            primaryLanguage = primaryLanguageRsp.getData();
        }
        boolean isChinese = LanguageType.check(primaryLanguage.getLanguageId(), LanguageType.Chinese);
        context.setChineseFlag(isChinese);
        // Loading 数据
        queryLabSection(context);
        queryPPCitation(context);
        queryPPBaseInfo(context);
        queryTestLineAnalyte(context);
        queryConditionGroup(context);
        querySimplifyName(context);
        // SL Master打印不需要此部分数据
        if(!context.getParam().isBaseQuery()){
            queryTrimsAnalyte(context);
        }
        queryTrimsWI(context);
        queryTLAmount(context);
        getCitationBaseInfoList(context);
        return BaseResponse.newSuccessInstance(true);
    }

    public void getCitationBaseInfoList(TestLineQueryContext<OrderTestLineReq, TestLineBO> context){
        List<TestLineBO> testLineList = context.getTestLineList();
        if(Func.isEmpty(testLineList)){
            return;
        }
        Set<Long> citationBaseIdList = testLineList.stream().filter(Func::isNotEmpty).map(TestLineBO::getCitation).filter(Func::isNotEmpty).map(CitationBO::getCitationBaseId).filter(Func::isNotEmpty).collect(Collectors.toSet());
        GetCitationBaseInfoReq getCitationBaseInfoReq = new GetCitationBaseInfoReq();
        getCitationBaseInfoReq.setCitationBaseIds(citationBaseIdList);
        getCitationBaseInfoReq.setLanguageIds(Lists.newArrayList(LanguageType.English.getLanguageId(),LanguageType.Chinese.getLanguageId()));
        BaseResponse<List<GetCitationBaseInfoRsp>> citationBaseInfoRsp = trimsClient.getCitationBaseInfoList(getCitationBaseInfoReq);
        if(citationBaseInfoRsp.isSuccess()){
            context.setCitationBaseInfoRspList(citationBaseInfoRsp.getData());
        }
    }

    protected BaseResponse queryConditionGroup(TestLineQueryContext<OrderTestLineReq, TestLineBO> context){
        Set<String> testLineInstanceIdList = context.getTestLineInstanceIdList();
        QueryTestConditionGroupReq queryTestConditionGroupReq = new QueryTestConditionGroupReq();
        queryTestConditionGroupReq.setTestLineInstanceIdList(testLineInstanceIdList);
        BaseResponse<List<TestConditionGroupPO>> conditionGroupBaseResponse = testConditionGroupService.select(queryTestConditionGroupReq);
        if(conditionGroupBaseResponse.isSuccess() && Func.isNotEmpty(conditionGroupBaseResponse.getData())){
            List<TestConditionGroupPO> testConditionGroupPOList = conditionGroupBaseResponse.getData();
            context.setTestConditionGroupList(testConditionGroupPOList);
            Set<String> testConditionGroupIdList = testConditionGroupPOList.parallelStream().map(TestConditionGroupPO::getId).collect(Collectors.toSet());
            if(Func.isNotEmpty(testConditionGroupIdList)){
                queryTestConditionGroupReq.setTestConditionGroupIdList(testConditionGroupIdList);
                BaseResponse<List<TestConditionGroupLanguagePO>> testConditionGroupLanguageResponse = testConditionGroupLanguageService.select(queryTestConditionGroupReq);
                if(testConditionGroupLanguageResponse.isSuccess()){
                    List<TestConditionGroupLanguagePO> testConditionGroupLanguagePOList = testConditionGroupLanguageResponse.getData();
                    context.setTestConditionGroupLanguageList(testConditionGroupLanguagePOList);
                }
                BaseResponse<List<TestPpConditionGroupPO>> testPpConditionGroupBaseResponse = testPpConditionGroupService.select(queryTestConditionGroupReq);
                if(testPpConditionGroupBaseResponse.isSuccess()){
                    List<TestPpConditionGroupPO>  testPpConditionGroupPOList = testPpConditionGroupBaseResponse.getData();
                    context.setTestPpConditionGroupList(testPpConditionGroupPOList);
                }
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }


    private void queryTrimsWI(TestLineQueryContext<OrderTestLineReq, TestLineBO> context) {
        List<TestLineBO> testLineList = context.getTestLineList();
        List<OrderCitationPO> orderCitationList = context.getOrderCitationList();

        WorkingInstructionReq workingInstructionReq = new WorkingInstructionReq();
        List<TestLineCitationDto> testLineCitations = new ArrayList<>();
        //优先使用PPCitationBaseId
        for (TestLineBO testLineBO : testLineList) {
            TestLineCitationDto testLineCitationDto = new TestLineCitationDto();
            testLineCitationDto.setTestLineVersionId(testLineBO.getTestLineVersionId());
            if(Func.isNotEmpty(testLineBO.getCitation())){
                testLineCitationDto.setCitationBaseId(testLineBO.getCitation().getCitationBaseId());
            }
            if(Func.isNotEmpty(testLineBO.getPpTestLineRel()) && Func.isNotEmpty(testLineBO.getPpTestLineRel().getPpBaseId()) && Func.isNotEmpty(orderCitationList)){
                Long ppBaseId = testLineBO.getPpTestLineRel().getPpBaseId();
                Set<OrderCitationPO> ppCitationList = orderCitationList.stream().filter(item -> {
                            return NumberUtil.equals(ppBaseId,item.getPpBaseId());
                }).collect(Collectors.toSet());
                Long ppCitationBaseId = ppCitationList.stream().map(OrderCitationPO::getCitationBaseId).findAny().orElse(null);
                if(Func.isNotEmpty(ppCitationBaseId)){
                    testLineCitationDto.setCitationBaseId(ppCitationBaseId);
                }
            }
            testLineCitations.add(testLineCitationDto);
        }
        Set<Integer> wiCategoryIdSets = Sets.newHashSet();
        wiCategoryIdSets.add(CategoryEnums.WIForCS.getCode());
        wiCategoryIdSets.add(CategoryEnums.WIForTesting.getCode());
        wiCategoryIdSets.add(CategoryEnums.SamplePreparation_Cutting.getCode());
        wiCategoryIdSets.add(CategoryEnums.SamplePreparation_Injection.getCode());
        wiCategoryIdSets.add(CategoryEnums.SamplePreparation_Tableting.getCode());

        workingInstructionReq.setTestLineCitations(testLineCitations);
        workingInstructionReq.setCallerBU(context.getProductLineCode());
        workingInstructionReq.setCategoryIds(wiCategoryIdSets);
        BaseResponse<List<WorkingInstructionRsp>> workingInstructionRsp = trimsClient.getWorkingInstructionList(workingInstructionReq);
        if(workingInstructionRsp.isSuccess()){
            context.setWorkingInstructionRspList(workingInstructionRsp.getData());
        }
    }

    private void queryTrimsAnalyte(TestLineQueryContext<OrderTestLineReq, TestLineBO> context) {
        List<TestLineBO> testLineList = context.getTestLineList();
        QueryTestLineAnalyteReq queryTestLineAnalyteReq = new QueryTestLineAnalyteReq();
        List<TestLineAnalyteReq> analyteReqList = new ArrayList<>();
        for (TestLineBO testLineBO : testLineList) {
            int testLineVersionId = testLineBO.getTestLineVersionId().intValue();
            int standardVersionId = testLineBO.getCitation().getCitationVersionId().intValue();
            List<PPTestLineRelBO> ppTestLineRelList = testLineBO.getPpTestLineRelList();
            Long artifactId = 0L;
            if(Func.isNotEmpty(ppTestLineRelList)){
                artifactId = ppTestLineRelList.stream().map(PPTestLineRelBO::getAid).map(Func::toLong).findAny().orElse(null);
            }
            TestLineAnalyteReq testLineAnalyteReq = new TestLineAnalyteReq();
            testLineAnalyteReq.setTestLineVersionId(testLineVersionId);
            testLineAnalyteReq.setStandardVersionIds(Sets.newHashSet(standardVersionId));
            testLineAnalyteReq.setArtifactId(artifactId);
            analyteReqList.add(testLineAnalyteReq);
        }
        queryTestLineAnalyteReq.setAnalytes(analyteReqList);

        BaseResponse<List<TestLineAnalyteRsp>> testLineAnalyteResponse = trimsClient.getTestLineAnalyteList(queryTestLineAnalyteReq);
        if(testLineAnalyteResponse.isSuccess()){
            context.setTestLineAnalyteRspList(testLineAnalyteResponse.getData());
        }
    }

    /**
     *
     * @param citationName
     * @param showCitationStyle 是否展示HTML样式，编辑模式下不需要展示样式
     * @return
     */
    public String formatProvideByClientStandard(String citationName,boolean showCitationStyle){
        if(Func.isEmpty(citationName)){
            return StringPool.EMPTY;
        }else if (showCitationStyle){
            return "<span style='color:red'>" + citationName + "</span>";
        }else{
            return citationName;
        }
    }
    public boolean isProvideByClientStandard(Long ppBaseId,Integer citationId){
        boolean result = false;
        if(Func.isNotEmpty(ppBaseId) && ppBaseId!=0){
            return false;
        }
        if(Func.isEmpty(citationId)){
            result =  false;
        }
        else if (NumberUtil.equals(citationId,standardConfig.getProvideByClient())){
            result = true;
        }else{
            result = false;
        }
        return result;
    }

}
