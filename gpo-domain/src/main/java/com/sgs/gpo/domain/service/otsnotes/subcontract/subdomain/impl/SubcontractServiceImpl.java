package com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractBO;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractIdBO;
import com.sgs.framework.open.platform.base.service.impl.AbstractBaseService;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.subcontract.SubContractMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractPO;
import com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.ISubcontractService;
import com.sgs.gpo.facade.model.otsnotes.subcontract.req.SubContractPageReq;
import com.sgs.gpo.facade.model.otsnotes.subcontract.vo.SubContractPageVO;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/7 12:00
 */
@Service
@Slf4j
public class SubcontractServiceImpl
        extends AbstractBaseService<SubcontractBO,SubcontractPO, SubcontractIdBO,SubContractMapper, SubcontractQueryReq>
        implements ISubcontractService {
    @Override
    public BaseResponse<List<SubcontractPO>> select(SubcontractQueryReq subcontractQueryReq) {
        if(Func.isEmpty(subcontractQueryReq) || (Func.isEmpty(subcontractQueryReq.getOrderNo()) && Func.isEmpty(subcontractQueryReq.getSubcontractId())
                && Func.isEmpty(subcontractQueryReq.getSubcontractIdList()) && Func.isEmpty(subcontractQueryReq.getOrderNoList())) ){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        LambdaQueryWrapper<SubcontractPO> wrapper = new LambdaQueryWrapper<>();
        if(Func.isNotEmpty(subcontractQueryReq.getOrderNo())){
            wrapper.eq(SubcontractPO::getOrderNo,subcontractQueryReq.getOrderNo());
        }
        if(Func.isNotEmpty(subcontractQueryReq.getOrderNoList())){
            wrapper.in(SubcontractPO::getOrderNo,subcontractQueryReq.getOrderNoList());
        }
        if(Func.isNotEmpty(subcontractQueryReq.getSubcontractId())){
            wrapper.eq(SubcontractPO::getId,subcontractQueryReq.getSubcontractId());
        }
        if(Func.isNotEmpty(subcontractQueryReq.getSubcontractIdList())){
            wrapper.in(SubcontractPO::getId,subcontractQueryReq.getSubcontractIdList());
        }
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }

    @Override
    public BaseResponse<IPage<SubContractPageVO>> page(IPage<SubContractPageVO> page,SubContractPageReq subContractPageReq) {
        if(Func.isEmpty(subContractPageReq)){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        return BaseResponse.newSuccessInstance(baseMapper.querySubContractPage(page,subContractPageReq));
    }

    @Override
    public BaseResponse<Long> pageCount(SubContractPageReq subContractPageReq) {
        if(Func.isEmpty(subContractPageReq)){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        return BaseResponse.newSuccessInstance(baseMapper.querySubContractPageCount(subContractPageReq));
    }


    @Override
    public BaseResponse<List<SubcontractPO>> query2(SubcontractQueryReq subcontractQueryReq) {
        if(Func.isEmpty(subcontractQueryReq.getLab())) {
            subcontractQueryReq.setLab(SystemContextHolder.getLab());
        }
        Assert.isTrue(Func.isNotEmpty(subcontractQueryReq.getLab()),"common.param.miss",new Object[]{Constants.TERM.LAB.getCode()});
        // 判断BU为SL，orderIndex为空。不需要执行Tops逻辑
        if(Func.equalsSafe(ProductLineContextHolder.getProductLineCode(), ProductLineType.SL.getProductLineAbbr())){
            subcontractQueryReq.setBlockTops(ProductLineType.SL.getProductLineAbbr());
            if(Func.isAnyNotEmpty(subcontractQueryReq.getOrderNoList(), subcontractQueryReq.getOrderNo(),
                    subcontractQueryReq.getSubcontractIdList(), subcontractQueryReq.getSubcontractId())){
                subcontractQueryReq.setLab(null);
            }
        }
        return BaseResponse.newSuccessInstance(baseMapper.select(subcontractQueryReq));
    }

    @Override
    public SubcontractPO queryOrderIsSubOrder(String orderNo) {
        return baseMapper.queryOrderIsSubOrder(orderNo);
    }

    @Override
    public BaseResponse<List<SubcontractPO>> querySubcontractByTestLine(SubcontractQueryReq subcontractQueryReq) {
        if(Func.isEmpty(subcontractQueryReq) || Func.isEmpty(subcontractQueryReq.getTestLineInstanceIdList())){
            return BaseResponse.newFailInstance("common.miss", new Object[]{"TestLineInstanceIdList"});

        }
        List<SubcontractPO> subcontractPOList = baseMapper.selectSubcontractByTlInstanceIdList(subcontractQueryReq);
        return BaseResponse.newSuccessInstance(subcontractPOList);
    }

    @Override
    public List<SubcontractBO> convertToBO(Collection<SubcontractPO> poList) {
        return null;
    }

    @Override
    public List<SubcontractPO> convertToPO(Collection<SubcontractBO> boList) {
        return null;
    }

    @Override
    public LambdaQueryWrapper<SubcontractPO> createWrapper(SubcontractQueryReq queryReq) {
        LambdaQueryWrapper<SubcontractPO> labdaQueryWrapper=new LambdaQueryWrapper<>();
        if(Func.isNotEmpty(queryReq.getSubcontractNoList())){
            labdaQueryWrapper.in(SubcontractPO::getSubContractNo,queryReq.getSubcontractNoList());
        }
        return labdaQueryWrapper;
    }
}
