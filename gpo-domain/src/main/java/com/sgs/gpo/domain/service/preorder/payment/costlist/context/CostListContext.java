package com.sgs.gpo.domain.service.preorder.payment.costlist.context;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sgs.framework.core.base.BaseRequest;
import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.ordertrfrel.OrderTrfRelationshipPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.productinstance.ProductInstancePO;
import com.sgs.gpo.facade.model.payment.costlist.bo.CostListBO;
import com.sgs.gpo.facade.model.payment.costlist.dto.ActualFeeImportDTO;
import com.sgs.gpo.facade.model.payment.costlist.dto.BossImportDTO;
import com.sgs.gpo.facade.model.payment.costlist.rsp.ActualFeeUploadDate;
import com.sgs.gpo.facade.model.searchvalid.rsp.SearchValidRsp;
import com.sgs.priceengine.facade.model.DTO.QuotationServiceItemDTO;
import com.sgs.priceengine.facade.model.response.QuotationHeadRsp;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/15 15:02
 */
@Data
public class CostListContext<Input extends BaseRequest> extends BaseContext<Input, CostListBO> {
    private IPage<CostListBO> costListPage;
    private List<QuotationHeadRsp> quotationHeadRspList;
    private List<QuotationServiceItemDTO> quotationServiceItemDTOList;
    private List<OrderTrfRelationshipPO> orderTrfRelationshipPOList;
    private List<ProductInstancePO> productInstancePOList;
    private String productLineCode;
    private SearchValidRsp searchValidRsp;

    private List<GeneralOrderPO> generalOrderPOList;
    private List<BossImportDTO> updateOrderList;
    private List<ActualFeeImportDTO> actualFeeImportDTOList;
    private List<ActualFeeUploadDate> actualFeeUploadDateList;

}
