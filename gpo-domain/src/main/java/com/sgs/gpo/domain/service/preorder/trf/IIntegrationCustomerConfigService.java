package com.sgs.gpo.domain.service.preorder.trf;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.trf.IntegrationCustomerConfigPO;
import com.sgs.gpo.facade.model.trf.dto.TrfConfigDTO;
import com.sgs.gpo.facade.model.trf.req.QueryTrfConfigReq;

import java.util.List;

public interface IIntegrationCustomerConfigService extends IService<IntegrationCustomerConfigPO> {
    BaseResponse<List<TrfConfigDTO>> queryBindTrfConfigList(QueryTrfConfigReq queryTrfConfigReq);
}
