package com.sgs.gpo.domain.service.otsnotes.report.command;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.enums.SampleType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportMatrixRelPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.TestMatrixPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.TestSamplePO;
import com.sgs.gpo.domain.service.otsnotes.report.context.ReportContext;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportMatrixRelService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixService;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.ITestSampleService;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;
import com.sgs.gpo.facade.model.report.bo.ReportMatrixRelBO;
import com.sgs.gpo.facade.model.report.req.ReportIdReq;
import com.sgs.gpo.facade.model.report.req.ReportMatrixQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: ReportMatrixUpdateCMD
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2024/3/5 11:22
 */
public class ReportMatrixUpdateCMD extends BaseCommand<ReportContext<ReportIdReq>> {
    @Autowired
    private IReportService reportService;
    @Autowired
    private ITestMatrixService testMatrixService;
    @Autowired
    private IReportMatrixRelService reportMatrixRelService;
    @Autowired
    private ITestSampleService testSampleService;

    @Override
    public BaseResponse validateParam(ReportContext<ReportIdReq> context) {
        Assert.isTrue(Func.isNotEmpty(context.getParam()), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(context.getParam().getReportIdList()), "common.param.miss", new Object[]{"reportId"});
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(ReportContext<ReportIdReq> context) {
        List<TestSamplePO> testSamplePOList = context.getTestSamplePOList();
        List<ReportMatrixRelBO> reportMatrixRelBOList = context.getReportMatrixRelBOList();
        ReportIdReq reportIdReq = context.getParam();
        Set<String> reportIdList = reportIdReq.getReportIdList();
        Set<String> matrixSampleIdInstanceIdList = reportMatrixRelBOList.stream().map(ReportMatrixRelBO::getTestSampleInstanceId).collect(Collectors.toSet());
        Set<String> matrixSampleInstanceIdList = new HashSet<>();
        //与当前报告关联的原样sample
        List<TestSamplePO> originalSampleList = testSamplePOList.stream().filter(item -> matrixSampleIdInstanceIdList.contains(item.getId()) && SampleType.check(item.getSampleType(), SampleType.OriginalSample)).collect(Collectors.toList());
        if (Func.isNotEmpty(originalSampleList)) {
            Set<String> originalSampleInstanceIdList = originalSampleList.stream().map(TestSamplePO::getId).collect(Collectors.toSet());
            matrixSampleInstanceIdList.addAll(originalSampleInstanceIdList);
            if (Func.isNotEmpty(originalSampleInstanceIdList)) {
                List<TestSamplePO> sampleList = testSamplePOList.stream().filter(item -> originalSampleInstanceIdList.contains(item.getSampleParentId()) && !SampleType.check(item.getSampleType(), SampleType.MixSample)).collect(Collectors.toList());
                if (Func.isNotEmpty(sampleList)) {
                    Set<String> sampleInstanceIdList = sampleList.stream().map(TestSamplePO::getId).collect(Collectors.toSet());
                    if (Func.isNotEmpty(sampleInstanceIdList)) {
                        matrixSampleInstanceIdList.addAll(sampleInstanceIdList);
                        List<TestSamplePO> subSampleList = testSamplePOList.stream().filter(item -> sampleInstanceIdList.contains(item.getSampleParentId()) && !SampleType.check(item.getSampleType(), SampleType.MixSample)).collect(Collectors.toList());
                        if (Func.isNotEmpty(subSampleList)) {
                            Set<String> subSampleInstanceIdList = subSampleList.stream().map(TestSamplePO::getId).collect(Collectors.toSet());
                            if (Func.isNotEmpty(subSampleInstanceIdList)) {
                                matrixSampleInstanceIdList.addAll(subSampleInstanceIdList);
                            }
                        }
                    }
                }
            }
        }
        if (Func.isNotEmpty(matrixSampleInstanceIdList)) {
            TestMatrixQueryReq testMatrixQueryReq = new TestMatrixQueryReq();
            testMatrixQueryReq.setTestSampleInstanceIdList(matrixSampleInstanceIdList);
            List<TestMatrixPO> testMatrixPOList = testMatrixService.queryList(testMatrixQueryReq).getData();
            if (Func.isNotEmpty(testMatrixPOList) && Func.isNotEmpty(reportMatrixRelBOList)) {
                //需要新增的reportMatrix
                for (String reportId : reportIdList) {
                    //当前Report关联的原样Matrix
                    Set<String> currentReportSampleInstanceIdList = reportMatrixRelBOList.stream().filter(item -> Func.equalsSafe(reportId, item.getReportId())).map(ReportMatrixRelBO::getTestSampleInstanceId).collect(Collectors.toSet());
                    if(Func.isEmpty(currentReportSampleInstanceIdList)){
                        continue;
                    }
                    Set<String> currentReportOriginalSampleInstanceIdList = testSamplePOList.stream().filter(item->currentReportSampleInstanceIdList.contains(item.getId()) && SampleType.check(item.getSampleType(),SampleType.OriginalSample)).map(TestSamplePO::getId).collect(Collectors.toSet());


                    //对应的子样，子子样的Matrix


                }
            }

//            reportMatrixRelService.saveBatch()

        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(ReportContext<ReportIdReq> context) {
        ReportIdReq reportIdReq = context.getParam();
        //查询报告下所有的ReportMatrixRel
        ReportMatrixQueryReq reportMatrixQueryReq = new ReportMatrixQueryReq();
        reportMatrixQueryReq.setReportIdList(reportIdReq.getReportIdList());
        BaseResponse<List<ReportMatrixRelBO>> reportMatrixRelBORes = reportMatrixRelService.queryReportMatrix(reportMatrixQueryReq);
        if (reportMatrixRelBORes.isSuccess() && Func.isNotEmpty(reportMatrixRelBORes.getData())) {
            List<ReportMatrixRelBO> reportMatrixRelBOList = reportMatrixRelBORes.getData();
            context.setReportMatrixRelBOList(reportMatrixRelBOList);
            if (Func.isNotEmpty(reportMatrixRelBOList)) {
                String orderNo = reportMatrixRelBOList.get(0).getOrderNo();
                TestSampleQueryReq testSampleQueryReq = new TestSampleQueryReq();
                testSampleQueryReq.setOrderNo(orderNo);
                BaseResponse<List<TestSamplePO>> testSampleRes = testSampleService.select(testSampleQueryReq);
                if (testSampleRes.isSuccess() && Func.isNotEmpty(testSampleRes.getData())) {
                    context.setTestSamplePOList(testSampleRes.getData());
                }
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }
}
