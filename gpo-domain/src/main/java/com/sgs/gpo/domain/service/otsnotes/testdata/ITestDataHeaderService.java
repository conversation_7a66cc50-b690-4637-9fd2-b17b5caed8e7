package com.sgs.gpo.domain.service.otsnotes.testdata;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testdata.TestDataHeaderPO;
import com.sgs.gpo.facade.model.otsnotes.testdata.dto.TLDataEntryDTO;
import com.sgs.gpo.facade.model.otsnotes.testdata.req.TLDataHeaderQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testdata.req.TLDataHeaderStatusUpdateReq;
import com.sgs.gpo.facade.model.otsnotes.testdata.rsp.TLDataHeaderSaveCheckRsp;
import com.sgs.gpo.integration.dataentry.req.SaveTestHeaderReq;

import java.util.List;

public interface ITestDataHeaderService extends IService<TestDataHeaderPO> {
    BaseResponse<Long> selectTlDataEntryCount(TLDataHeaderQueryReq tLDataHeaderQueryReq);
    BaseResponse<List<TLDataEntryDTO>> selectTlDataEntryList(Page page, TLDataHeaderQueryReq tLDataHeaderQueryReq);
    BaseResponse<Boolean> updateTestDataHeaderStatus(TLDataHeaderStatusUpdateReq statusUpdateReq);
    BaseResponse<Boolean> saveTestDataHeader(List<SaveTestHeaderReq> saveTestHeaderReqList);
    BaseResponse<List<TLDataHeaderSaveCheckRsp>> saveCheckTestDataHeader(List<SaveTestHeaderReq> saveTestHeaderReqList);
}
