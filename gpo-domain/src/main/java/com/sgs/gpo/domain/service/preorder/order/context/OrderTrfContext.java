package com.sgs.gpo.domain.service.preorder.order.context;

import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.TestSampleReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.status.GpnStatusPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.productinstance.ProductInstancePO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.status.GpoStatusPO;
import com.sgs.gpo.facade.model.preorder.order.bo.OrderTrfBO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderEditableReq;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/5 15:34
 */
@Data
public class OrderTrfContext extends BaseContext<OrderEditableReq, OrderTrfBO> {
    private List<OrderTrfBO> orderTrfBOList;
    private List<ProductInstancePO> productInstancePOList;
    private List<TestSampleReportPO> testSampleReportPOList;

    private List<ReportPO> reportPOList;
    private List<GpoStatusPO> gpoStatusPOList;
    private List<GpnStatusPO> gpnStatusPOList;
}
