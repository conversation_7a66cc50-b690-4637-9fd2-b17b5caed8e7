package com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.subcontract.SubcontractRequirementMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractRequirementPO;
import com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.ISubcontractRequirementService;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractRequirementQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class SubcontractRequirementServiceImpl extends ServiceImpl<SubcontractRequirementMapper, SubcontractRequirementPO> implements ISubcontractRequirementService {
    @Override
    public BaseResponse<List<SubcontractRequirementPO>> query(SubcontractRequirementQueryReq req) {
        if(Func.isEmpty(req)||Func.isEmpty(req.getSubContractIds())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        LambdaQueryWrapper<SubcontractRequirementPO> wrapper= Wrappers.<SubcontractRequirementPO>lambdaQuery()
                .in(SubcontractRequirementPO::getSubContractId,req.getSubContractIds());
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }
}
