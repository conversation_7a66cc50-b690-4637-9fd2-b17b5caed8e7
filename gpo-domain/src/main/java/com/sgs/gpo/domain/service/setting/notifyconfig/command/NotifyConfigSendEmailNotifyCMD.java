package com.sgs.gpo.domain.service.setting.notifyconfig.command;

import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.NotifyEventTypeEnum;
import com.sgs.gpo.core.enums.NotifyObjectTypeEnum;
import com.sgs.gpo.core.util.DateUtils;
import com.sgs.gpo.dbstorages.mybatis.model.notify.NotifyConfigPO;
import com.sgs.gpo.dbstorages.mybatis.model.notify.NotifyLogPO;
import com.sgs.gpo.domain.service.setting.notifyconfig.context.NotifyConfigContext;
import com.sgs.gpo.domain.service.setting.notifyconfig.subdomain.INotifyConfigService;
import com.sgs.gpo.domain.service.setting.notifyconfig.subdomain.INotifyLogService;
import com.sgs.gpo.facade.model.notifyconfig.req.NotifyConfigQueryReq;
import com.sgs.gpo.integration.framework.NotificationClient;
import com.sgs.otsnotes.facade.model.rsp.email.SendEmailResultRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;



@Service
@Slf4j
public class NotifyConfigSendEmailNotifyCMD extends BaseCommand<NotifyConfigContext<String>>{
    @Autowired
    private INotifyConfigService  notifyConfigService;
    @Autowired
    private INotifyLogService notifyLogService;
    @Resource
    private NotificationClient notificationClient;

    @Override
    public BaseResponse validateParam(NotifyConfigContext<String> context) {
        Assert.isTrue(Func.isNotEmpty(context.getParam()), "common.miss", new Object[]{"param"});
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    @Transactional
    @DS(Constants.DB.COMMON)
    public BaseResponse execute(NotifyConfigContext<String> context) {
        List<NotifyConfigPO> notifyConfigPOList = context.getNotifyConfigPOList();
        if(Func.isNotEmpty(notifyConfigPOList)) {
            Date currDate = new Date();
            notifyConfigPOList.forEach(item -> {
                item.setRemark("Send email success");
                //发送邮件
                String emailSendResult = notificationClient.sendMailForGeneral(item.getNotifyParams());
                //如果发送成功更新状态
                if (Func.isNotEmpty(emailSendResult)){
                    SendEmailResultRsp sendEmailResultRsp = JSON.parseObject(emailSendResult, SendEmailResultRsp.class);
                    if (sendEmailResultRsp.getResult()){
                        item.setActiveIndicator(ActiveType.Disable.getStatus());
                    }else {
                        item.setRemark("Send email fail");
                    }
                }
                item.setNotifyCount(item.getNotifyCount()+1);
                item.setModifiedDate(currDate);

                NotifyLogPO notifyConfigLogPO = new NotifyLogPO();
                notifyConfigLogPO.setId(IdUtil.uuId());
                notifyConfigLogPO.setNotifyId(item.getId());
                notifyConfigLogPO.setObjectNo(item.getObjectNo());
                notifyConfigLogPO.setRequestBody(item.getNotifyParams());
                notifyConfigLogPO.setResponseBody(emailSendResult);
                notifyConfigLogPO.setNotifyTime(currDate);
                notifyConfigLogPO.setCreatedDate(currDate);
                notifyConfigLogPO.setModifiedDate(currDate);
                //插入到日志表tb_object_notify_log
                notifyLogService.save(notifyConfigLogPO);
                //更新tb_object_notify_config
                notifyConfigService.updateById(item);
            });
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(NotifyConfigContext<String> context) {
        String productLineCode = context.getProductLineCode();
        Date currDate = new Date();
        //查询类型，下次通知时间小于当前时间，待运行的tb_object_notification_config
        NotifyConfigQueryReq notifyConfigQueryReq = new NotifyConfigQueryReq();
        notifyConfigQueryReq.setObjectType(NotifyObjectTypeEnum.PRELIM_REPORT.getName());
        notifyConfigQueryReq.setEventTypeList(Lists.newArrayList(NotifyEventTypeEnum.CUSTOMER_CONFIRM.getName(), NotifyEventTypeEnum.ISSUE_REPORT.getName()));
        notifyConfigQueryReq.setNotifyDateStart(DateUtils.addDay(currDate, -Constants.BU_PARAM.Notification.BEFORE_DAYS));//防止邮件发送失败
        notifyConfigQueryReq.setNotifyDateEnd(currDate);
        notifyConfigQueryReq.setActiveIndicator(ActiveType.Enable.getStatus());
        notifyConfigQueryReq.setBuCode(productLineCode);
        BaseResponse<List<NotifyConfigPO>> notifyConfigRes = notifyConfigService.select(notifyConfigQueryReq);
        if (notifyConfigRes.isSuccess() && Func.isNotEmpty(notifyConfigRes.getData())) {
            List<NotifyConfigPO> notifyConfigPOList = notifyConfigRes.getData();
            context.setNotifyConfigPOList(notifyConfigPOList);
        }
        return BaseResponse.newSuccessInstance(true);
    }
}
