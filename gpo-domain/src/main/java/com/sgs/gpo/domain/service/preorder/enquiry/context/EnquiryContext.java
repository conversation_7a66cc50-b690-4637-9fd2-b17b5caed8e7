package com.sgs.gpo.domain.service.preorder.enquiry.context;

import com.sgs.framework.core.base.PageBO;
import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.common.productsample.ProductSampleBO;
import com.sgs.framework.model.order.enquiry.EnquiryBO;
import com.sgs.framework.model.order.v2.OrderIdBO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.attachment.OrderAttachmentPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryCustomerPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryParcelPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryPersonPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryProductPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryReportReceiverPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryTestRequestContactsPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryTestRequestPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryTrfRelationshipPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.tag.ObjectTagPO;
import com.sgs.gpo.facade.model.preorder.enquiry.req.UserLabDTO;
import com.sgs.gpo.facade.model.tag.bo.TagValueBO;
import com.sgs.priceengine.facade.model.request.CopyOrderBatchRequest;
import com.sgs.priceengine.facade.model.request.OrderToEnquiry;
import lombok.Data;

import java.util.List;

@Data
public class EnquiryContext<Input> extends BaseContext<Input, EnquiryBO> {

    /**
     * Enquiry 分页PO对象
     */
    private PageBO<EnquiryPO> enquiryPagePO;
    private List<EnquiryPO> enquiryPOList;
    private List<EnquiryBO> enquiryBOList;

    /**
     * Enquiry Create/Copy 参数
     */
    private EnquiryPO newEnquiry;
    private List<EnquiryParcelPO> newEnquiryParcels;
    private List<EnquiryPersonPO> newEnquiryPersons;

    private List<EnquiryCustomerPO> newCustomerList;
    private List<String> customerDelIds;

    private EnquiryReportReceiverPO enquiryReportReceiver;
    private EnquiryTestRequestPO enquiryTestRequest;
    private List<EnquiryTestRequestContactsPO> enquiryTestRequestContactsPOList;
    private ObjectTagPO enquiryTag;
    private List<OrderAttachmentPO> orderAttachmentList;
    // 待处理的TRF的数据
    private List<EnquiryTrfRelationshipPO> enquiryTrfList;
    private List<Long> trfDelIds;

    // enquiry copy时使用
    private CopyOrderBatchRequest copyOrderBatchRequest;
    // order copy to enquiry 时 使用
    private OrderToEnquiry orderToEnquiry;

    private List<EnquiryProductPO> productList;
    private List<ProductSampleBO> productSampleBOList;
    //
    private EnquiryBO originalEnquiry;
    // 区分新增编辑
    private Boolean isNew;

    private Integer copyFcm;

    // AFL新增的sales
    private String salesPerson;
    UserLabDTO saleUserLabDTO;

    /**
     * 需要新增的Tag参数
     */
    private List<TagValueBO> newTagValueList;
    private List<String> tagValueIds;

    private Integer copyNum;

    private String regular;

    private boolean copyQuotationFlag;

    private OrderIdBO orderIdBO;
}
