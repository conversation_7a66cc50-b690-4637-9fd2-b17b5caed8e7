package com.sgs.gpo.domain.service.otsnotes.report.context;


import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.status.GpnStatusPO;
import lombok.Data;

import java.util.List;

@Data
public class ReportUpdateContext<Input> extends BaseContext<Input, ReportBO> {
    private List<ReportBO> reportBOList;
    private ReportBO reportBO;
    private OrderBO orderBO;
    private GpnStatusPO gpnStatus;
}
