package com.sgs.gpo.domain.service.extservice.sci.command;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.model.common.attachment.AttachmentBO;
import com.sgs.framework.model.common.customer.CustomerBO;
import com.sgs.framework.model.common.customer.CustomerContactBO;
import com.sgs.framework.model.common.customer.CustomerLanguageBO;
import com.sgs.framework.model.common.dff.DFFAttrBO;
import com.sgs.framework.model.common.dff.DFFAttrLanguageBO;
import com.sgs.framework.model.common.lab.LabBO;
import com.sgs.framework.model.common.lab.LabContactBO;
import com.sgs.framework.model.common.lab.LabLanguageBO;
import com.sgs.framework.model.common.productsample.CareLabelBO;
import com.sgs.framework.model.common.productsample.ProductBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementReportBO;
import com.sgs.framework.model.enums.*;
import com.sgs.framework.model.order.order.OrderOthersBO;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.model.test.citation.CitationBO;
import com.sgs.framework.model.test.pp.pptestline.PPTestLineRelBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.test.testmatrix.TestMatrixBO;
import com.sgs.framework.model.test.testsample.TestSampleBO;
import com.sgs.framework.model.test.testsample.TestSampleGroupBO;
import com.sgs.framework.model.test.testsample.TestSampleMaterialAttrBO;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.jackson.JsonUtil;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.NumberUtil;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.TestSamplePO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.productinstance.ProductInstancePO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.status.GpoStatusPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.testrequest.TestRequestContactPO;
import com.sgs.gpo.domain.service.extservice.sci.SciTrfContext;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineDomainService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixService;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.ITestSampleService;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryService;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.domain.service.preorder.order.subdomain.ICareLabelService;
import com.sgs.gpo.domain.service.preorder.ordertrfrel.IOrderTrfRelationshipService;
import com.sgs.gpo.domain.service.preorder.productinstance.IProductInstanceService;
import com.sgs.gpo.domain.service.preorder.status.IGpoStatusService;
import com.sgs.gpo.domain.service.preorder.testrequest.ITestRequestContactService;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;
import com.sgs.gpo.facade.model.preorder.order.bo.OrderTrfBO;
import com.sgs.gpo.facade.model.preorder.order.dto.OrderExtFieldsDTO;
import com.sgs.gpo.facade.model.preorder.order.req.CareLabelReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderTrfReq;
import com.sgs.gpo.facade.model.preorder.productsample.req.ProductSampleQueryReq;
import com.sgs.gpo.facade.model.sci.bo.OrderToTrfBO;
import com.sgs.gpo.facade.model.sci.dto.*;
import com.sgs.gpo.facade.model.sci.req.GpoSciOrderToTrfReq;
import com.sgs.gpo.facade.model.sci.rsp.Order2TrfRsp;
import com.sgs.gpo.integration.customer.CustomerClient;
import com.sgs.gpo.integration.dff.DFFClient;
import com.sgs.gpo.integration.framework.FrameworkClient;
import com.sgs.gpo.integration.sci.SciClient;
import com.sgs.gpo.integration.sci.req.SciHeaderReq;
import com.sgs.gpo.integration.sci.req.SciTrfReq;
import com.sgs.otsnotes.facade.model.enums.GpoCustomerType;
import com.sgs.otsnotes.facade.model.enums.ReportRequirementEnum;
import com.sgs.preorder.facade.CustomerFacade;
import com.sgs.preorder.facade.model.dto.customer.CustomerExtDTO;
import com.sgs.preorder.facade.model.dto.dff.DffFormAttrDTO;
import com.sgs.preorder.facade.model.enums.CaseType;
import com.sgs.preorder.facade.model.enums.ContactsType;
import com.sgs.preorder.facade.model.enums.OperationModeEnum;
import com.sgs.preorder.facade.model.enums.OrderStatus;
import com.sgs.priceengine.facade.model.enums.CustomerUsage;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: SciOrder2TrfCMD
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/12/27 17:34
 */
@Service
@Slf4j
@AllArgsConstructor
public class SciOrderToTrfCMD extends BaseCommand<SciTrfContext<GpoSciOrderToTrfReq, OrderToTrfBO>> {
    private IOrderDomainService orderDomainService;
    private ITestSampleService testSampleService;
    private ITestLineDomainService testLineDomainService;
    private IOrderTrfRelationshipService orderTrfRelationshipService;
    private SciClient sciClient;
    private IEnquiryService enquiryService;
    private ITestRequestContactService testRequestContactService;
    private FrameworkClient frameworkClient;
    private DFFClient dffClient;
    private IProductInstanceService productInstanceService;
    private ITestMatrixService testMatrixService;
    private ICareLabelService careLabelService;
    private CustomerClient customerClient;
    private CustomerFacade gpoCustomerFacade;
    private IGpoStatusService gpoStatusService;

    @Override
    public BaseResponse validateParam(SciTrfContext<GpoSciOrderToTrfReq, OrderToTrfBO> context) {
        if (Func.isEmpty(context) || Func.isEmpty(context.getParam()) || Func.isEmpty(context.getParam().getOrderNo())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(SciTrfContext<GpoSciOrderToTrfReq, OrderToTrfBO> context) {
        GpoSciOrderToTrfReq gpoSciOrderToTrfReq = context.getParam();
        UserInfo user = SecurityUtil.getUser(gpoSciOrderToTrfReq.getToken());
        if (Func.isEmpty(user)) {
            return BaseResponse.newFailInstance(ResponseCode.TokenExpire);
        }
        List<GpoStatusPO> gpoStatusPOList = context.getGpoStatusPOList();
        if(gpoSciOrderToTrfReq.isToTrfCheck()){
            //校验Order Status
            //校验Trf是否已存在
            //校验applicant和buyer的order2trf标记
            //校验buyer的ToDmFlag
            List<OrderBO> orderBOList = context.getOrderList();
            List<OrderTrfBO> orderTrfBOList = context.getOrderTrfBOList();
            if(Func.isNotEmpty(orderBOList)){
                OrderBO orderBO = orderBOList.get(0);
                //校验Case Type
                String caseType = orderBO.getHeader().getCaseType();
                if(Func.isEmpty(caseType)){
                    return BaseResponse.newFailInstance("common.empty",new Object[]{"Order CaseType"});
                }
                if(Func.isEmpty(caseType)
                        || CaseType.check(caseType,CaseType.IDB)){
                    return BaseResponse.newFailInstance("sci.valid.toTrf.order.caseType",new Object[]{caseType});
                }
                //校验OperationMode
                Integer operationMode = orderBO.getHeader().getOperationMode();
                if(Func.isEmpty(operationMode)){
                    return BaseResponse.newFailInstance("common.empty",new Object[]{"Order Operation Mode"});
                }
                if(!OperationModeEnum.check(operationMode,OperationModeEnum.FullCycle)){
                    return BaseResponse.newFailInstance("sci.valid.toTrf.order.operationMode",new Object[]{OperationModeEnum.findMode(operationMode).getMessage()});
                }
                //校验Report Requirement
                String reportRequirement = orderBO.getServiceRequirement().getReportRequirement();
                if(Func.isEmpty(operationMode)){
                    return BaseResponse.newFailInstance("common.empty",new Object[]{"Report Requirement"});
                }
                if(!ReportRequirementEnum.check(reportRequirement,ReportRequirementEnum.Customer_Report_PDF)){
                    return BaseResponse.newFailInstance("sci.valid.toTrf.order.reportRequirement",new Object[]{ReportRequirementEnum.getMessage(reportRequirement)});
                }
                Integer orderStatus = orderBO.getHeader().getOrderStatus();

                if(gpoSciOrderToTrfReq.isToTrfCheck()){
                    if(OrderStatus.checkStatus(orderStatus,OrderStatus.New,OrderStatus.Cancelled)){
                        //toSGSMart失败 原因为订单状态不对
                        return BaseResponse.newFailInstance("sci.valid.toTrf.order.orderStatus",new Object[]{OrderStatus.getMessage(orderStatus)});
                    }else if(OrderStatus.checkStatus(orderStatus,OrderStatus.Pending) && Func.isNotEmpty(gpoStatusPOList)){
                        GpoStatusPO gpoStatusPO = gpoStatusPOList.stream().filter(item->Func.equalsSafe(item.getObjectNo(),orderBO.getId().getOrderNo())).sorted(Comparator.comparing(GpoStatusPO::getOperationDate).reversed()).findAny().orElse(null);
                        if(Func.isNotEmpty(gpoStatusPO)){
                            Integer beforeStatus = gpoStatusPO.getNewStatus();
                            if(OrderStatus.checkStatus(beforeStatus,OrderStatus.New,OrderStatus.Completed,OrderStatus.Closed)){
                                return BaseResponse.newFailInstance("sci.valid.toTrf.order.pending.beforeStatus",new Object[]{OrderStatus.getMessage(beforeStatus)});
                            }
                        }
                    }
                }else{
                    if(OrderStatus.checkStatus(orderStatus,OrderStatus.New,OrderStatus.Cancelled,OrderStatus.Pending)){
                        //toSGSMart失败 原因为订单状态不对
                        return BaseResponse.newFailInstance("sci.valid.toTrf.order.orderStatus",new Object[]{OrderStatus.getMessage(orderStatus)});
                    }
                }
                //是否已存在trf
                if(Func.isNotEmpty(orderTrfBOList)){
                    long count = orderTrfBOList.stream().filter(item -> RefSystemIdEnum.check(item.getRefSystemId(), RefSystemIdEnum.SGSMart) || RefIntegrationChannel.check(item.getIntegrationChannel(),RefIntegrationChannel.SCI)).count();
                    if(count>0l){
                        return BaseResponse.newFailInstance("sci.valid.toTrf.exist",new Object[]{});
                    }
                }




                List<CustomerBO> customerList = orderBO.getCustomerList();
                boolean orderToTRFFlag = false;
                if(Func.isNotEmpty(customerList)){
                    List<CustomerBO> applicantBuyerList = customerList.stream().filter(item -> Func.isNotEmpty(GpoCustomerType.enumOf(item.getCustomerUsage())) && CustomerUsage.check(GpoCustomerType.enumOf(item.getCustomerUsage()).getCode(), CustomerUsage.Applicant, CustomerUsage.Buyer) && Func.isNotEmpty(item.getCustomerId())).collect(Collectors.toList());
                    if(Func.isNotEmpty(applicantBuyerList)){
                        for (CustomerBO customerBO : applicantBuyerList) {
                            List<CustomerExtDTO> customerExtDTOList = customerClient.getCustomerExt(customerBO.getCustomerId(), orderBO.getLab().getLocationCode(), orderBO.getLab().getBuCode());
                            if (Func.isEmpty(customerExtDTOList)) {
                                continue;
                            }
                            for (CustomerExtDTO customerExtDTO : customerExtDTOList) {
                                if (Func.equalsSafe(1, customerExtDTO.getOrderToTRFFlag()) && !orderToTRFFlag) {
                                    orderToTRFFlag = true;
                                }
                            }
                        }
                    }
                }
                if(!orderToTRFFlag){
                    return BaseResponse.newFailInstance("sci.valid.toTrf.customer.tag.close",null);
                }
            }

        }
        context.setDomain(new OrderToTrfBO());
        this.buildDomain(context);
        String labCode = context.getLabCode();
        String buCode = context.getBuCode();
        if (Func.isNotEmpty(user)) {
            if (Func.isEmpty(labCode)) {
                labCode = user.getCurrentLabCode();
            }
            if (Func.isEmpty(buCode)) {
                buCode = context.getProductLineCode();
            }
        }
        OrderToTrfBO orderToTrfBO = context.getDomain();
        SciTrfReq<OrderToTrfBO> sciTrfReq = new SciTrfReq();
        SciHeaderReq sciHeaderReq = new SciHeaderReq();
        sciHeaderReq.setLabCode(labCode);
        sciHeaderReq.setSystemId(SgsSystem.GPO.getSgsSystemId());

        sciHeaderReq.setOperator(user.getRegionAccount());
        sciHeaderReq.setOperationTime(DateUtil.formatDateTime(new Date()));
        sciHeaderReq.setOperatorEmail(user.getEmail());
        sciHeaderReq.setToken(gpoSciOrderToTrfReq.getToken());
        sciHeaderReq.setRequestId(Func.randomUUID());
        sciHeaderReq.setProductLineCode(buCode);
        sciTrfReq.setHeader(sciHeaderReq);
        sciTrfReq.setBody(orderToTrfBO);
        BaseResponse<Order2TrfRsp> baseResponse = sciClient.order2Trf(sciTrfReq);
        return baseResponse;
    }

    @Override
    public BaseResponse buildDomain(SciTrfContext<GpoSciOrderToTrfReq, OrderToTrfBO> context) {
        context.setDomain(new OrderToTrfBO());
        buildTrfHeader(context);
        buildTrfOrder(context);
        buildTrfAttachment(context);
        buildTrfCustomer(context);
        buildServiceRequirement(context);
        buildProductSample(context);
        buildTrfTestSample(context);
//        buildCareLabelList(context);
        buildMatrixList(context);
        buildTrfTestLine(context);
        return BaseResponse.newSuccessInstance(true);
    }

    private void buildMatrixList(SciTrfContext<GpoSciOrderToTrfReq, OrderToTrfBO> context) {
        List<TestMatrixBO> testMatrixBOList = context.getTestMatrixBOList();
        List<TestSamplePO> testSampleList = context.getTestSampleList();
        if (Func.isEmpty(testMatrixBOList)) {
            return;
        }
        List<TrfTestMatrixDTO> trfTestMatrixDTOList = new ArrayList<>();
        for (TestMatrixBO testMatrixBO : testMatrixBOList) {
            TrfTestMatrixDTO trfTestMatrixDTO = new TrfTestMatrixDTO();
            trfTestMatrixDTO.setTestMatrixId(testMatrixBO.getTestMatrixId());
//            trfTestMatrixDTO.setTestItemId(testMatrixBO);
            trfTestMatrixDTO.setTestLineInstanceId(testMatrixBO.getTestLineInstanceId());
            trfTestMatrixDTO.setTestSampleInstanceId(testMatrixBO.getTestSampleInstanceId());

            if (Func.isNotEmpty(testSampleList)) {
                TestSamplePO testSamplePO = testSampleList.stream().filter(item -> Func.equalsSafe(item.getId(), testMatrixBO.getTestSampleInstanceId())).findAny().orElse(null);
                if (Func.isNotEmpty(testSamplePO)) {
                    trfTestMatrixDTO.setTestSampleNo(testSamplePO.getSampleNo());
                    trfTestMatrixDTO.setExternalTestSampleNo(testSamplePO.getExternalSampleId());
                }
            }
            trfTestMatrixDTOList.add(trfTestMatrixDTO);
        }
        context.getDomain().setTestMatrixList(trfTestMatrixDTOList);
    }

    private void buildCareLabelList(SciTrfContext<GpoSciOrderToTrfReq, OrderToTrfBO> context) {
        List<CareLabelBO> careLabelBOList = context.getCareLabelBOList();
        if (Func.isEmpty(careLabelBOList)) {
            return;
        }
        List<TrfCareLabelDTO> trfCareLabelList = new ArrayList<>();
        for (CareLabelBO careLabelBO : careLabelBOList) {
            TrfCareLabelDTO trfCareLabelDTO = new TrfCareLabelDTO();
            trfCareLabelDTO.setCareInstruction(careLabelBO.getCareInstruction());
            trfCareLabelDTO.setRadioType(null);
            trfCareLabelDTO.setSelectCountry(null);
            trfCareLabelDTO.setCareLabelSeq(0);
            trfCareLabelList.add(trfCareLabelDTO);
        }
        context.getDomain().setCareLabelList(trfCareLabelList);
    }

    private void buildTrfAttachment(SciTrfContext<GpoSciOrderToTrfReq, OrderToTrfBO> context) {
        List<OrderBO> orderBOList = context.getOrderList();
        if (Func.isNotEmpty(orderBOList)) {
            OrderBO orderBO = orderBOList.get(0);
            List<AttachmentBO> orderAttachmentList = orderBO.getAttachmentList();
            if (Func.isNotEmpty(orderAttachmentList)) {
                orderAttachmentList = orderAttachmentList.stream().filter(item->Func.equalsSafe(item.getToCp(),1)).collect(Collectors.toList());
                List<TrfFileDTO> trfAttachmentList = new ArrayList<>();
                for (AttachmentBO attachmentBO : orderAttachmentList) {
                    TrfFileDTO trfFileDTO = new TrfFileDTO();
                    trfFileDTO.setFileType(attachmentBO.getFileType());
                    trfFileDTO.setFileName(attachmentBO.getFileName());
                    trfFileDTO.setFilePath(attachmentBO.getFilePath());
                    trfFileDTO.setCloudId(attachmentBO.getCloudId());
                    trfFileDTO.setToCustomerFlag(1);
                    trfAttachmentList.add(trfFileDTO);
                }
                context.getDomain().setAttachmentList(trfAttachmentList);
            }
        }
    }

    private void buildProductSample(SciTrfContext<GpoSciOrderToTrfReq, OrderToTrfBO> context) {
        List<ProductBO> orderProductList = context.getOrderProductList();
        List<ProductInstancePO> allPproductInstanceList = context.getProductInstanceList();
        int primaryLanguageId = context.getPrimaryLanguageId();

        if(Func.isNotEmpty(allPproductInstanceList)){
            //Product Form
            List<ProductInstancePO> productInstancePOList  = allPproductInstanceList.stream().filter(item -> Func.isEmpty(item.getHeaderID())).collect(Collectors.toList());
            ProductInstancePO productInstancePO = productInstancePOList.stream().filter(item -> Func.equalsSafe(item.getLanguageID(), primaryLanguageId)).findAny().orElse(null);
            if(Func.isEmpty(productInstancePO)){
                productInstancePO = productInstancePOList.stream().filter(item -> !Func.equalsSafe(item.getLanguageID(), primaryLanguageId)).findAny().orElse(null);
            }
            if(Func.isNotEmpty(productInstancePO) && Func.isNotEmpty(orderProductList)){
                ProductInstancePO finalProductInstancePO = productInstancePO;
                ProductBO productBO = orderProductList.stream().filter(item -> Func.equalsSafe(finalProductInstancePO.getID(),item.getProductInstanceId())).findAny().orElse(null);
                if(Func.isNotEmpty(productBO)){
                    TrfProductSampleDTO trfProductDTO = this.convertTrfProductSample(productBO, context);
                    context.getDomain().setProduct(trfProductDTO);
                }
            }
            //Product Grid
            List<ProductInstancePO> allProductGridPOList  = allPproductInstanceList.stream().filter(item -> Func.isNotEmpty(item.getHeaderID())).collect(Collectors.toList());
            List<ProductInstancePO> productGridPOList = allProductGridPOList.stream().filter(item -> Func.equalsSafe(item.getLanguageID(), primaryLanguageId)).collect(Collectors.toList());
            if(Func.isEmpty(productGridPOList)){
                productGridPOList = allProductGridPOList.stream().filter(item -> !Func.equalsSafe(item.getLanguageID(), primaryLanguageId)).collect(Collectors.toList());
            }
            if(Func.isNotEmpty(productGridPOList) && Func.isNotEmpty(orderProductList)){
                List<TrfProductSampleDTO> trfProductSampleDTOList = new ArrayList<>();
                for (ProductInstancePO instancePO : productGridPOList) {
                    ProductBO productBO = orderProductList.stream().filter(item -> Func.equalsSafe(item.getProductInstanceId(), instancePO.getID())).findAny().orElse(null);
                    if(Func.isEmpty(productBO)){
                        continue;
                    }
                    TrfProductSampleDTO trfProductDTO = this.convertTrfProductSample(productBO, context);
                    trfProductSampleDTOList.add(trfProductDTO);
                }
                context.getDomain().setSampleList(trfProductSampleDTOList);
            }
        }
    }

    private TrfProductSampleDTO convertTrfProductSample(ProductBO productBO,SciTrfContext<GpoSciOrderToTrfReq, OrderToTrfBO> context){
        List<ProductInstancePO> productInstanceList = context.getProductInstanceList();
        String productInstanceId = productBO.getProductInstanceId();
        TrfProductSampleDTO trfProductDTO = new TrfProductSampleDTO();
        trfProductDTO.setTemplateId(productBO.getTemplateId());
        ProductInstancePO productInstancePO = productInstanceList.stream().filter(item -> Func.equalsSafe(item.getID(), productInstanceId)).findAny().orElse(null);
        if (Func.isEmpty(productInstancePO)) {
            return null;
        }
        if (Func.isEmpty(productInstancePO.getHeaderID())) {
            trfProductDTO.setProductInstanceId(productBO.getProductInstanceId());
        } else {
            trfProductDTO.setSampleInstanceId(productBO.getProductInstanceId());
            trfProductDTO.setSampleNo(productInstancePO.getSampleID());
            trfProductDTO.setProductItemNo(productInstancePO.getProductItemNo());
        }
        List<DFFAttrBO> orderProductAttrList = productBO.getProductAttrList();
        if (Func.isNotEmpty(orderProductAttrList)) {
            List<TrfProductAttrDTO> trfProductAttrDTOList = new ArrayList<>();
            List<TrfProductAttrDTO> trfSampleAttrDTOList = new ArrayList<>();
            for (DFFAttrBO dffAttrBO : orderProductAttrList) {
                TrfProductAttrDTO trfProductAttrDTO = new TrfProductAttrDTO();
                trfProductAttrDTO.setAttrSeq(dffAttrBO.getSeq());
                trfProductAttrDTO.setLabelCode(dffAttrBO.getLabelCode());
                trfProductAttrDTO.setLabelName(dffAttrBO.getLabelName());
                trfProductAttrDTO.setLabelValue(Func.toStr(dffAttrBO.getValue()));
                trfProductAttrDTO.setDataType(dffAttrBO.getDataType());
                trfProductAttrDTO.setCustomerLabel(dffAttrBO.getCustomerLabel());
                List<DFFAttrLanguageBO> dffAttrLanguageBOList = dffAttrBO.getLanguageList();
                if (Func.isNotEmpty(dffAttrLanguageBOList)) {
                    List<TrfProductAttrLangDTO> languageList = new ArrayList<>();
                    for (DFFAttrLanguageBO dffAttrLanguageBO : dffAttrLanguageBOList) {
                        TrfProductAttrLangDTO trfProductAttrLangDTO = new TrfProductAttrLangDTO();
                        trfProductAttrLangDTO.setLanguageId(dffAttrLanguageBO.getLanguageId());
                        trfProductAttrLangDTO.setLabelName(dffAttrLanguageBO.getLabelName());
                        trfProductAttrLangDTO.setLabelValue(Func.toStr(dffAttrLanguageBO.getValue()));
                        trfProductAttrLangDTO.setCustomerLabel(dffAttrLanguageBO.getCustomerLabel());
                        languageList.add(trfProductAttrLangDTO);
                    }
                    trfProductAttrDTO.setLanguageList(languageList);
                }

                if (Func.isEmpty(productInstancePO.getHeaderID())) {
                    trfProductAttrDTOList.add(trfProductAttrDTO);
                } else {
                    trfSampleAttrDTOList.add(trfProductAttrDTO);
                }
            }
            trfProductDTO.setProductAttrList(Func.isEmpty(trfProductAttrDTOList) ? null : trfProductAttrDTOList);
            trfProductDTO.setSampleAttrList(Func.isEmpty(trfSampleAttrDTOList) ? null : trfSampleAttrDTOList);
        }
        return trfProductDTO;
    }


    private List<ProductBO> convertProduct(SciTrfContext<GpoSciOrderToTrfReq, OrderToTrfBO> context) {
        int primaryLanguageId = context.getPrimaryLanguageId();
        List<ProductInstancePO> productInsList = context.getProductInstanceList();
        if (Func.isEmpty(productInsList)) {
            return null;
        }
//        productInsList = productInsList.stream().filter(i -> Func.isEmpty(i.getHeaderID())).collect(Collectors.toList());
        if (Func.isEmpty(productInsList)) {
            return Lists.newArrayList();
        }
        Set<String> formIdSets = productInsList.stream().map(ProductInstancePO::getDFFFormID).collect(Collectors.toSet());
        List<ProductBO> rdProductList = Lists.newArrayList();
        if (Func.isNotEmpty(formIdSets)) {
            List<DffFormAttrDTO> allDffFormAttrDTOList = dffClient.getDffFormAttrByDffFormIdList(formIdSets);
            List<DffFormAttrDTO> dffFormAttrDTOListEn = new ArrayList<>();
            List<DffFormAttrDTO> dffFormAttrDTOListCn = new ArrayList<>();
            List<DffFormAttrDTO> defaultLanguageDffFormAttrDTOList = new ArrayList<>();
            if (Func.isNotEmpty(allDffFormAttrDTOList)) {
                //只过滤需要getDisplayInSystem包含1(sgsMart)的dff
//                allDffFormAttrDTOList = allDffFormAttrDTOList.stream().filter(item->Func.isNotEmpty(item.getDisplayInSystem()) && Func.toStrList(item.getDisplayInSystem()).contains("1")).collect(Collectors.toList());
                dffFormAttrDTOListEn = allDffFormAttrDTOList.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.English)).collect(Collectors.toList());
                dffFormAttrDTOListCn = allDffFormAttrDTOList.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.Chinese)).collect(Collectors.toList());
                defaultLanguageDffFormAttrDTOList = allDffFormAttrDTOList.stream().filter(i -> Func.isNotEmpty(i.getLanguageID()) && i.getLanguageID() == primaryLanguageId).collect(Collectors.toList());
                if (Func.isEmpty(defaultLanguageDffFormAttrDTOList)) {
                    defaultLanguageDffFormAttrDTOList = allDffFormAttrDTOList.stream().filter(i -> Func.isNotEmpty(i.getLanguageID()) && i.getLanguageID() != primaryLanguageId).collect(Collectors.toList());
                }
            }
            List<ProductInstancePO> productInstanceDTOListEn = productInsList.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.English)).collect(Collectors.toList());
            List<ProductInstancePO> productInstanceDTOListCn = productInsList.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.Chinese)).collect(Collectors.toList());
            List<ProductInstancePO> defaultLanguageList = productInsList.stream().filter(i -> Func.isNotEmpty(i.getLanguageID()) && i.getLanguageID() == primaryLanguageId).collect(Collectors.toList());
            if (Func.isEmpty(defaultLanguageList)) {
                defaultLanguageList = productInsList.stream().filter(i -> Func.isNotEmpty(i.getLanguageID()) && i.getLanguageID() != primaryLanguageId).collect(Collectors.toList());
            }
            if (Func.isNotEmpty(productInsList)) {
                for (ProductInstancePO productInstancePO : productInsList) {
                    ProductBO rdProductDTO = new ProductBO();
                    rdProductDTO.setProductInstanceId(productInstancePO.getID());
                    rdProductDTO.setTemplateId(productInstancePO.getDFFFormID());
                    List<DFFAttrBO> rdProductAttrList = Lists.newArrayList();
                    Map<String, Object> defaultProductMap = BeanUtil.beanToMap(productInstancePO, false, true);
                    if (Func.isNotEmpty(defaultLanguageDffFormAttrDTOList)) {
                        List<DffFormAttrDTO> dffFormAttrDTOList = defaultLanguageDffFormAttrDTOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getdFFFormID(), productInstancePO.getDFFFormID())).collect(Collectors.toList());

                        dffFormAttrDTOList = dffFormAttrDTOList.stream().sorted(Comparator.comparing(item -> (Func.isEmpty(item.getSequence()) ? 0 : Integer.parseInt(item.getSequence())), Comparator.nullsLast(Integer::compareTo))).collect(Collectors.toList());

                        for (DffFormAttrDTO dffFormAttrDTO : dffFormAttrDTOList) {
                            DFFAttrBO sampleAttrDTO = new DFFAttrBO();
                            sampleAttrDTO.setLabelName(dffFormAttrDTO.getDispalyName());
                            sampleAttrDTO.setLabelCode(dffFormAttrDTO.getFieldCode());
                            sampleAttrDTO.setCustomerLabel("");
                            sampleAttrDTO.setValue(Func.toStr(defaultProductMap.getOrDefault(StrUtil.lowerFirst(dffFormAttrDTO.getFieldCode()), null)));
                            sampleAttrDTO.setSeq(Func.toInt(dffFormAttrDTO.getSequence()));
                            sampleAttrDTO.setDataType(dffFormAttrDTO.getFieldType());
                            List<DFFAttrLanguageBO> languageList = Lists.newArrayList();
                            //英文
                            List<ProductInstancePO> productInstancePOEns = productInstanceDTOListEn.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getDFFFormID(), productInstancePO.getDFFFormID())).collect(Collectors.toList());
                            ProductInstancePO productInstanceEn = productInstancePOEns.stream().filter(i -> Func.equals(i.getSampleID(), productInstancePO.getSampleID())).findAny().orElse(null);
                            if (Func.isNotEmpty(dffFormAttrDTOListEn) && Func.isNotEmpty(productInstanceEn)) {
                                DffFormAttrDTO dffFormAttrDTOEN = dffFormAttrDTOListEn.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getFieldCode(), dffFormAttrDTO.getFieldCode())).findAny().orElse(null);
                                if (Func.isNotEmpty(dffFormAttrDTOEN)) {
                                    Map<String, Object> productEnMap = BeanUtil.beanToMap(productInstanceEn, false, true);
                                    DFFAttrLanguageBO sampleAttrLanguage = new DFFAttrLanguageBO();
                                    sampleAttrLanguage.setLabelName(dffFormAttrDTOEN.getDispalyName());
                                    sampleAttrLanguage.setValue(Func.toStr(productEnMap.getOrDefault(StrUtil.lowerFirst(dffFormAttrDTOEN.getFieldCode()), null)));
                                    sampleAttrLanguage.setLanguageId(LanguageType.English.getLanguageId());
                                    languageList.add(sampleAttrLanguage);
                                }
                            }

                            //中文
                            List<ProductInstancePO> productInstancePOCns = productInstanceDTOListCn.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getDFFFormID(), productInstancePO.getDFFFormID())).collect(Collectors.toList());
                            ProductInstancePO productInstanceCn = productInstancePOCns.stream().filter(i -> Func.equals(i.getSampleID(), productInstancePO.getSampleID())).findAny().orElse(null);
                            if (Func.isNotEmpty(dffFormAttrDTOListCn) && Func.isNotEmpty(productInstanceCn)) {
                                DffFormAttrDTO dffFormAttrDTOCN = dffFormAttrDTOListCn.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getFieldCode(), dffFormAttrDTO.getFieldCode())).findAny().orElse(null);
                                if (Func.isNotEmpty(dffFormAttrDTOCN)) {
                                    Map<String, Object> productCnMap = BeanUtil.beanToMap(productInstanceCn, false, true);
                                    DFFAttrLanguageBO sampleAttrLanguage = new DFFAttrLanguageBO();
                                    sampleAttrLanguage.setLabelName(dffFormAttrDTOCN.getDispalyName());
                                    sampleAttrLanguage.setValue(Func.toStr(productCnMap.getOrDefault(StrUtil.lowerFirst(dffFormAttrDTOCN.getFieldCode()), null)));
                                    sampleAttrLanguage.setLanguageId(LanguageType.Chinese.getLanguageId());
                                    languageList.add(sampleAttrLanguage);
                                }
                            }
                            sampleAttrDTO.setLanguageList(languageList);
                            rdProductAttrList.add(sampleAttrDTO);
                        }
                    }
                    rdProductDTO.setProductAttrList(rdProductAttrList);
                    rdProductList.add(rdProductDTO);
                }
            }
        }
        return rdProductList;
    }


    private void buildServiceRequirement(SciTrfContext<GpoSciOrderToTrfReq, OrderToTrfBO> context) {
        List<OrderBO> orderList = context.getOrderList();
        List<TestRequestContactPO> testRequestContactList = context.getTestRequestContactList();
        if (Func.isNotEmpty(orderList)) {
            OrderBO orderBO = orderList.get(0);
            ServiceRequirementBO orderServiceRequirement = orderBO.getServiceRequirement();
            if (Func.isNotEmpty(orderServiceRequirement)) {
                TrfServiceRequirementDTO trfServiceRequirementDTO = new TrfServiceRequirementDTO();
                ServiceRequirementReportBO serviceRequirementReportBO = orderServiceRequirement.getReport();
                if (Func.isNotEmpty(serviceRequirementReportBO)) {
                    TrfServiceRequirementReportDTO trfServiceRequirementReportDTO = new TrfServiceRequirementReportDTO();
                    trfServiceRequirementReportDTO.setReportLanguage(serviceRequirementReportBO.getReportLanguage());
//                trfServiceRequirementReportDTO.setReportForm(serviceRequirementReportBO.getReportForm());
                    trfServiceRequirementReportDTO.setReportHeader(serviceRequirementReportBO.getReportHeader());
                    trfServiceRequirementReportDTO.setReportAddress(serviceRequirementReportBO.getReportAddress());
                    trfServiceRequirementReportDTO.setLanguageList(Lists.newArrayList());
//                trfServiceRequirementReportDTO.setAccreditation(serviceRequirementReportBO.getNeedAccreditation());
                    trfServiceRequirementReportDTO.setNeedConclusion(serviceRequirementReportBO.getNeedConclusion());
                    trfServiceRequirementReportDTO.setNeedPhoto(serviceRequirementReportBO.getNeedPhoto());
                    trfServiceRequirementReportDTO.setNeedDraft(serviceRequirementReportBO.getNeedDraft());
                    TrfDeliveryDTO softCopyDeliverDTO = new TrfDeliveryDTO();
                    softCopyDeliverDTO.setRequired(1);
                    if (Func.isNotEmpty(testRequestContactList)) {
                        TestRequestContactPO softCopyContactTo = testRequestContactList.parallelStream().filter(item -> Func.equalsSafe(item.getOrderId(), orderBO.getHeader().getOrderId()) && ContactsType.check(item.getContactsType(), ContactsType.SoftCopy)).findAny().orElse(null);
                        TestRequestContactPO softCopyContactCc = testRequestContactList.parallelStream().filter(item -> Func.equalsSafe(item.getOrderId(), orderBO.getHeader().getOrderId()) && ContactsType.check(item.getContactsType(), ContactsType.SoftCopyDeliveryCc)).findAny().orElse(null);
                        if (Func.isNotEmpty(softCopyContactTo)) {
                            softCopyDeliverDTO.setDeliveryTo(softCopyContactTo.getDeliverTo());
                            softCopyDeliverDTO.setDeliveryOthers(softCopyContactTo.getDeliverToOthers());
                        }
                        if (Func.isNotEmpty(softCopyContactCc)) {
                            softCopyDeliverDTO.setDeliveryCc(softCopyContactCc.getDeliverTo());
                        }
                        trfServiceRequirementReportDTO.setSoftcopy(softCopyDeliverDTO);
                    }

                    TrfDeliveryDTO hardCopyDeliverDTO = new TrfDeliveryDTO();
                    hardCopyDeliverDTO.setRequired(serviceRequirementReportBO.getHardcopyRequired());
                    if (Func.isNotEmpty(testRequestContactList)) {
                        TestRequestContactPO hardCopyContactTo = testRequestContactList.parallelStream().filter(item -> Func.equalsSafe(item.getOrderId(), orderBO.getHeader().getOrderId()) && ContactsType.check(item.getContactsType(), ContactsType.HardCopy)).findAny().orElse(null);
                        if (Func.isNotEmpty(hardCopyContactTo)) {
                            hardCopyDeliverDTO.setDeliveryTo(hardCopyContactTo.getDeliverTo());
                            hardCopyDeliverDTO.setDeliveryOthers(hardCopyContactTo.getDeliverToOthers());
                        }
                        hardCopyDeliverDTO.setDeliveryWay(serviceRequirementReportBO.getHardCopyReportDeliverWay());
                        trfServiceRequirementReportDTO.setHardcopy(hardCopyDeliverDTO);
                    }
                    trfServiceRequirementDTO.setReport(trfServiceRequirementReportDTO);
                }

                TrfServiceRequirementInvoiceDTO trfServiceRequirementInvoiceDTO = new TrfServiceRequirementInvoiceDTO();
                trfServiceRequirementInvoiceDTO.setInvoiceType(orderServiceRequirement.getVatType());
                trfServiceRequirementInvoiceDTO.setNeedProformaInvoice(orderServiceRequirement.getProformaInvoice());
                TrfDeliveryDTO trfInvoiceDeliveryDTO = new TrfDeliveryDTO();
                trfInvoiceDeliveryDTO.setRequired(orderServiceRequirement.getProformaInvoice());
                if (Func.isNotEmpty(testRequestContactList)) {
                    TestRequestContactPO invoiceContact = testRequestContactList.parallelStream().filter(item -> Func.equalsSafe(item.getOrderId(), orderBO.getHeader().getOrderId()) && ContactsType.check(item.getContactsType(), ContactsType.Invoice)).findAny().orElse(null);
                    if (Func.isNotEmpty(invoiceContact)) {
                        trfInvoiceDeliveryDTO.setDeliveryTo(invoiceContact.getDeliverTo());
                        trfInvoiceDeliveryDTO.setDeliveryOthers(invoiceContact.getDeliverToOthers());
                        trfInvoiceDeliveryDTO.setDeliveryWay(orderServiceRequirement.getInvoiceDeliverWay());
                    }
                }

                trfServiceRequirementInvoiceDTO.setInvoice(trfInvoiceDeliveryDTO);
                trfServiceRequirementDTO.setInvoice(trfServiceRequirementInvoiceDTO);


                TrfServiceRequirementSampleDTO trfServiceRequirementSampleDTO = new TrfServiceRequirementSampleDTO();
                trfServiceRequirementSampleDTO.setSampleSaveDuration(orderServiceRequirement.getSampleSaveDuration());

                trfServiceRequirementSampleDTO.setLiquid(orderServiceRequirement.getLiquidTestSample());
                TrfDeliveryDTO trfReturnTestSample = new TrfDeliveryDTO();
                trfReturnTestSample.setRequired(orderServiceRequirement.getReturnTestSampleFlag());
                if (Func.isNotEmpty(testRequestContactList) && Func.equalsSafe(orderServiceRequirement.getReturnTestSampleFlag(),1)) {
                    TestRequestContactPO returnTestSampleContact = testRequestContactList.parallelStream().filter(item -> Func.equalsSafe(item.getOrderId(), orderBO.getHeader().getOrderId()) && ContactsType.check(item.getContactsType(), ContactsType.ReturnSample)).findAny().orElse(null);
                    if (Func.isNotEmpty(returnTestSampleContact)) {
                        trfReturnTestSample.setDeliveryTo(returnTestSampleContact.getDeliverTo());
                        trfReturnTestSample.setDeliveryOthers(returnTestSampleContact.getDeliverToOthers());
                        trfReturnTestSample.setDeliveryWay(orderServiceRequirement.getReturnResidueSampleRemark());
                    }
                }
                trfServiceRequirementSampleDTO.setReturnTestSample(trfReturnTestSample);
                TrfDeliveryDTO trfReturnResidueSample = new TrfDeliveryDTO();
//                ReturnResidueSample与ReturnTestSample共用DeliverTO和DeliverWay, 所以ReturnResidueSample取ReturnTestSample的DeliverTO和DeliverWay即可
                trfReturnResidueSample.setRequired(orderServiceRequirement.getReturnResidueSampleFlag());
                trfReturnResidueSample.setDeliveryTo(trfReturnTestSample.getDeliveryTo());
                trfReturnResidueSample.setDeliveryOthers(trfReturnTestSample.getDeliveryOthers());
                trfReturnResidueSample.setDeliveryWay(trfReturnTestSample.getDeliveryWay());
                trfServiceRequirementSampleDTO.setReturnResidueSample(trfReturnResidueSample);
                trfServiceRequirementDTO.setSample(trfServiceRequirementSampleDTO);
                trfServiceRequirementDTO.setOtherRequestRemark(orderServiceRequirement.getOtherRequestRemark());
                context.getDomain().setServiceRequirement(trfServiceRequirementDTO);
            }
        }
    }

    private void buildTrfCustomer(SciTrfContext<GpoSciOrderToTrfReq, OrderToTrfBO> context) {
        List<OrderBO> orderList = context.getOrderList();
        List<TrfCustomerDTO> customerList = new ArrayList<>();
        if (Func.isNotEmpty(orderList)) {
            OrderBO orderBO = orderList.get(0);
            OrderOthersBO orderOthersBO = orderBO.getOthers();
            List<CustomerBO> orderCustomerList = orderBO.getCustomerList();
            if (Func.isNotEmpty(orderCustomerList)) {
                for (CustomerBO customerBO : orderCustomerList) {
                    TrfCustomerDTO trfCustomerDTO = new TrfCustomerDTO();
                    trfCustomerDTO.setCustomerId(customerBO.getCustomerId());
                    trfCustomerDTO.setCustomerUsage(customerBO.getCustomerUsage());
                    trfCustomerDTO.setBossNo(customerBO.getBossNo());
                    // GPO送0给SCI时，会触发SCI校验，送NUll不会。
                    if(Func.isNotEmpty(customerBO.getBossNo()) && customerBO.getBossNo().equals(0l)){
                        trfCustomerDTO.setBossNo(null);
                    }
                    trfCustomerDTO.setCustomerGroupCode(customerBO.getCustomerGroupCode());
                    trfCustomerDTO.setCustomerName(customerBO.getCustomerName());
                    trfCustomerDTO.setCustomerAddress(customerBO.getCustomerAddress());
                    trfCustomerDTO.setPaymentTerm(customerBO.getPaymentTerm());
                    if(Func.equalsSafe(trfCustomerDTO.getCustomerUsage(), CustomerType.Buyer.getStatus())){
                        trfCustomerDTO.setMarketSegmentCode(orderOthersBO.getDepartmentCode());
                        trfCustomerDTO.setMarketSegmentName(orderOthersBO.getDepartmentCode());
                    }
                    List<CustomerContactBO> customerContactList = customerBO.getCustomerContactList();
                    if (Func.isNotEmpty(customerContactList)) {
                        List<TrfCustomerContactDTO> trfCustomerContactDTOList = new ArrayList<>();
                        for (CustomerContactBO customerContactBO : customerContactList) {
                            TrfCustomerContactDTO trfCustomerContactDTO = new TrfCustomerContactDTO();
                            trfCustomerContactDTO.setCustomerContactId(customerContactBO.getCustomerContactId());
                            trfCustomerContactDTO.setContactAddressId(customerContactBO.getCustomerContactAddressId());
                            trfCustomerContactDTO.setBossContactId(customerContactBO.getBossContactId());
                            trfCustomerContactDTO.setBossSiteUseId(customerContactBO.getBossSiteUseId());
//                            trfCustomerContactDTO.setContactRegionAccount(customerContactBO.getContactEmail());
                            trfCustomerContactDTO.setContactName(customerContactBO.getContactName());
                            trfCustomerContactDTO.setContactEmail(customerContactBO.getContactEmail());
                            trfCustomerContactDTO.setContactMobile(customerContactBO.getContactMobile());
                            trfCustomerContactDTO.setContactTelephone(customerContactBO.getContactTelephone());
                            trfCustomerContactDTO.setContactFax(customerContactBO.getContactFAX());
                            trfCustomerContactDTO.setSgsAccountCode(customerContactBO.getContactSgsMartAccount());
                            trfCustomerContactDTO.setSgsUserId(customerContactBO.getContactSgsMartUserId());
                            trfCustomerContactDTOList.add(trfCustomerContactDTO);
                        }
                        trfCustomerDTO.setCustomerContactList(trfCustomerContactDTOList);
                    }

                    List<CustomerLanguageBO> languageList = customerBO.getLanguageList();
                    if (Func.isNotEmpty(languageList)) {
                        List<TrfCustomerLangDTO> trfCustomerLangDTOList = new ArrayList<>();
                        for (CustomerLanguageBO customerLanguageBO : languageList) {
                            TrfCustomerLangDTO trfCustomerLangDTO = new TrfCustomerLangDTO();
                            trfCustomerLangDTO.setLanguageId(customerLanguageBO.getLanguageId());
                            trfCustomerLangDTO.setCustomerName(customerLanguageBO.getCustomerName());
                            trfCustomerLangDTO.setCustomerAddress(customerLanguageBO.getCustomerAddress());
                            trfCustomerLangDTOList.add(trfCustomerLangDTO);
                        }
                        trfCustomerDTO.setLanguageList(trfCustomerLangDTOList);
                    }
                    customerList.add(trfCustomerDTO);
                }
            }
        }
        context.getDomain().setCustomerList(customerList);
    }

    private void buildTrfHeader(SciTrfContext<GpoSciOrderToTrfReq, OrderToTrfBO> context) {
        GpoSciOrderToTrfReq gpoSciOrderToTrfReq = context.getParam();
        List<OrderBO> orderList = context.getOrderList();
        TrfHeaderDTO trfHeaderDTO = new TrfHeaderDTO();
        trfHeaderDTO.setSource(2);
        trfHeaderDTO.setTrfNo(gpoSciOrderToTrfReq.getTrfNo());
        trfHeaderDTO.setRefSystemId(gpoSciOrderToTrfReq.getRefSystemId());
        if (Func.isNotEmpty(orderList)) {
            OrderBO orderBO = orderList.get(0);
            if (Func.isNotEmpty(orderBO.getHeader())) {
                trfHeaderDTO.setServiceType(Func.toInteger(orderBO.getHeader().getServiceType()));
                trfHeaderDTO.setSampleReceiveDate(orderBO.getHeader().getSampleReceiveDate());
            }
            if (Func.isEmpty(orderBO.getFlags()) || Func.isEmpty(orderBO.getFlags().getSelfTestFlag())) {
                trfHeaderDTO.setSelfTestFlag(0);
            } else {
                trfHeaderDTO.setSelfTestFlag(orderBO.getFlags().getSelfTestFlag());
            }
            if(Func.isNotEmpty(orderBO.getOthers()) && Func.isNotEmpty(orderBO.getOthers().getExtFields())){
                //解析Json
                OrderExtFieldsDTO dto = JsonUtil.parse(orderBO.getOthers().getExtFields(), new com.fasterxml.jackson.core.type.TypeReference<OrderExtFieldsDTO>(){});
                if(Func.isNotEmpty(dto) && Func.isNotEmpty(dto.getTrfTemplate()) && Func.isNotEmpty(dto.getTrfTemplate().getTemplateId()) && Func.isNotEmpty(dto.getTrfTemplate().getTemplateName())){
                    trfHeaderDTO.setTrfTemplateId(dto.getTrfTemplate().getTemplateId());
                    trfHeaderDTO.setTrfTemplateName(dto.getTrfTemplate().getTemplateName());
                }
            }
            LabBO orderLab = orderBO.getLab();
            if (Func.isNotEmpty(orderLab)) {
                context.setLabCode(orderLab.getLabCode());
                context.setBuCode(orderLab.getBuCode());
                TrfLabDTO trfLabDTO = new TrfLabDTO();
                trfLabDTO.setLabId(orderLab.getLabId());
                trfLabDTO.setLabCode(orderLab.getLabCode());
                trfLabDTO.setBuCode(orderLab.getBuCode());
                trfLabDTO.setOtherCode("");
                trfLabDTO.setLabAddress(orderLab.getLabAddress());
                trfLabDTO.setLabName(orderLab.getLabName());
                trfLabDTO.setLabType(orderLab.getLabType());
                trfLabDTO.setCountryId(orderLab.getCountryId());
                trfLabDTO.setLocationId(orderLab.getLocationId());
                trfLabDTO.setBuId(orderLab.getBuId());
                List<LabContactBO> labContactList = orderLab.getLabContactList();
                if (Func.isNotEmpty(labContactList)) {
                    List<TrfLabContactDTO> trfLabContactDTOList = new ArrayList<>();
                    for (LabContactBO labContactBO : labContactList) {
                        TrfLabContactDTO trfLabContactDTO = new TrfLabContactDTO();
                        trfLabContactDTO.setContactName(labContactBO.getContactName());
                        trfLabContactDTO.setContactTelephone(labContactBO.getContactTelephone());
                        trfLabContactDTO.setContactEmail(labContactBO.getContactEmail());
                        trfLabContactDTOList.add(trfLabContactDTO);
                    }
                    trfLabDTO.setContactList(trfLabContactDTOList);
                }
                List<LabLanguageBO> languageList = orderLab.getLanguageList();
                if (Func.isNotEmpty(languageList)) {
                    List<TrfLabLangDTO> trfLabLanguageList = new ArrayList<>();
                    for (LabLanguageBO labLanguageBO : languageList) {
                        TrfLabLangDTO labLangDTO = new TrfLabLangDTO();
                        labLangDTO.setLanguageId(labLanguageBO.getLanguageId());
                        labLangDTO.setLabName(labLanguageBO.getLabName());
                        labLangDTO.setLabAddress(labLanguageBO.getLabAddress());
                        trfLabLanguageList.add(labLangDTO);
                    }
                    trfLabDTO.setLanguageList(trfLabLanguageList);
                }

                trfHeaderDTO.setLab(trfLabDTO);
            }


        }


        context.getDomain().setHeader(trfHeaderDTO);
    }


    private void buildTrfTestLine(SciTrfContext<GpoSciOrderToTrfReq, OrderToTrfBO> context) {
        OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
        orderTestLineReq.setOrderNoList(context.getOrderNoList());
        BaseResponse<List<TestLineBO>> testLineRes = testLineDomainService.queryTestLine(orderTestLineReq);
        if (testLineRes.isSuccess() && Func.isNotEmpty(testLineRes.getData())) {
            List<TestLineBO> testLineBOList = testLineRes.getData();
            List<TrfTestItemDTO> trfTestItemDTOList = new ArrayList<>();
            for (TestLineBO testLineBO : testLineBOList) {
                TrfTestItemDTO trfTestItemDTO = new TrfTestItemDTO();
                trfTestItemDTO.setTestLineId(testLineBO.getTestLineId());
                trfTestItemDTO.setEvaluationAlias(testLineBO.getEvaluationName());
                trfTestItemDTO.setEvaluationName(testLineBO.getEvaluationName());
//                trfTestItemDTO.setTestLineSeq(testLineBO.getTestLineSeq());
                trfTestItemDTO.setLanguageList(Lists.newArrayList());

                CitationBO citation = testLineBO.getCitation();
                if (Func.isNotEmpty(citation)) {
                    TrfCitationDTO trfCitationDTO = new TrfCitationDTO();
                    trfCitationDTO.setCitationId(citation.getCitationId());
//                    trfCitationDTO.setCitationType(citation.getCitationType());
                    trfCitationDTO.setCitationFullName(citation.getCitationFullName());
                    trfTestItemDTO.setCitation(trfCitationDTO);
                }
                List<PPTestLineRelBO> ppTestLineRelList = testLineBO.getPpTestLineRelList();
                if (Func.isNotEmpty(ppTestLineRelList)) {
                    List<TrfPpTestLineDTO> trfPpTestLineDTOList = new ArrayList<>();
                    for (PPTestLineRelBO ppTestLineRelBO : ppTestLineRelList) {
                        TrfPpTestLineDTO trfPpTestLineDTO = new TrfPpTestLineDTO();
                        trfPpTestLineDTO.setPpNo(ppTestLineRelBO.getPpNo());
                        trfPpTestLineDTO.setPpName(ppTestLineRelBO.getPpName());
                        trfPpTestLineDTOList.add(trfPpTestLineDTO);
                    }
                    trfTestItemDTO.setPpTestLineRelList(trfPpTestLineDTOList);
                }
                trfTestItemDTO.setExternalInfo(new TrfTestItemExternalDTO());
                trfTestItemDTOList.add(trfTestItemDTO);
            }
            context.getDomain().setTestLineList(trfTestItemDTOList);
        }
    }

    private void buildTrfTestSample(SciTrfContext<GpoSciOrderToTrfReq, OrderToTrfBO> context) {
        GpoSciOrderToTrfReq gpoSciOrderToTrfReq = context.getParam();
        List<TrfTestSampleDTO> trfTestSampleDTOList = new ArrayList<>();
        List<OrderBO> orderList = context.getOrderList();
        if (Func.isNotEmpty(orderList)) {
            for (OrderBO orderBO : orderList) {
                TestSampleQueryReq testSampleQueryReq = new TestSampleQueryReq();
                testSampleQueryReq.setOrderNo(orderBO.getHeader().getOrderNo());
                BaseResponse<List<TestSampleBO>> testSampleRes = testSampleService.queryV1(testSampleQueryReq);
                if (testSampleRes.isSuccess() && Func.isNotEmpty(testSampleRes.getData())) {
                    List<TestSampleBO> testSamplePOList = testSampleRes.getData();
                    for (TestSampleBO testSampleBO : testSamplePOList) {
                        TrfTestSampleDTO trfTestSampleDTO = new TrfTestSampleDTO();
                        trfTestSampleDTO.setTestSampleInstanceId(testSampleBO.getId());
                        trfTestSampleDTO.setTestSampleNo(testSampleBO.getTestSampleNo());
                        List<TestSampleGroupBO> testSampleGroupList = testSampleBO.getTestSampleGroupList();
                        List<TrfTestSampleGroupDTO> trfTestSampleGroupList = new ArrayList<>();
                        if (Func.isNotEmpty(testSampleGroupList)) {
                            for (TestSampleGroupBO testSampleGroupBO : testSampleGroupList) {
                                TrfTestSampleGroupDTO trfTestSampleGroupDTO = new TrfTestSampleGroupDTO();
                                trfTestSampleGroupDTO.setTestSampleInstanceId(testSampleGroupBO.getTestSampleInstanceId());
                                trfTestSampleGroupDTO.setMainSampleFlag(testSampleGroupBO.getMainSampleFlag());
                                trfTestSampleGroupList.add(trfTestSampleGroupDTO);
                            }
                        }
                        trfTestSampleDTO.setTestSampleGroupList(trfTestSampleGroupList);
                        trfTestSampleDTO.setExternalSampleNo(testSampleBO.getExternalSampleNo());
                        trfTestSampleDTO.setTestSampleType(testSampleBO.getTestSampleType());
                        trfTestSampleDTO.setTestSampleSeq(testSampleBO.getTestSampleSeq());
                        TestSampleMaterialAttrBO materialAttr = testSampleBO.getMaterialAttr();
                        List<TrfMaterialAttrDTO> trfMaterialAttrDTOList = new ArrayList<>();
                        if(Func.isNotEmpty(materialAttr)){
                            TrfMaterialAttrDTO trfMaterialAttrDTO = new TrfMaterialAttrDTO();
                            trfMaterialAttrDTO.setMaterialColor(materialAttr.getMaterialColor());
                            trfMaterialAttrDTO.setMaterialDescription(materialAttr.getMaterialDescription());
                            trfMaterialAttrDTO.setMaterialEndUse(materialAttr.getMaterialEndUse());
                            trfMaterialAttrDTO.setMaterialTexture(materialAttr.getMaterialTexture());
                            trfMaterialAttrDTOList.add(trfMaterialAttrDTO);
                        }
                        trfTestSampleDTO.setMaterialAttrList(trfMaterialAttrDTOList);
                        trfTestSampleDTOList.add(trfTestSampleDTO);
                    }
                }
            }
        }

        context.getDomain().setTestSampleList(trfTestSampleDTOList);
    }

    private void buildTrfOrder(SciTrfContext<GpoSciOrderToTrfReq, OrderToTrfBO> context) {
        List<EnquiryPO> enquiryPOList = context.getEnquiryList();
        List<OrderBO> orderBOList = context.getOrderList();
        TrfOrderDTO trfOrderDTO = new TrfOrderDTO();
        trfOrderDTO.setOrderId(context.getSciOrderId());
        trfOrderDTO.setOrderNo(context.getSciOrderNo());
//        trfOrderDTO.setOrderExpectDueDate();
        if(Func.isNotEmpty(enquiryPOList)){
            EnquiryPO enquiryPO = enquiryPOList.get(0);
            if(Func.isNotEmpty(enquiryPO.getCreatedDate())){
                trfOrderDTO.setCreateDate(enquiryPO.getCreatedDate());
            }
            trfOrderDTO.setEnquiryNo(enquiryPO.getEnquiryNo());
        }
        if(Func.isNotEmpty(orderBOList)){
            if(Func.isNotEmpty(orderBOList.get(0).getHeader().getOrderExpectDueDate())){
                trfOrderDTO.setOrderExpectDueDate(orderBOList.get(0).getHeader().getOrderExpectDueDate());
            }
            if(Func.isNotEmpty(orderBOList.get(0).getHeader().getOrderConfirmDate())){
                trfOrderDTO.setServiceConfirmDate(orderBOList.get(0).getHeader().getOrderConfirmDate());
            }
        }
        context.getDomain().setOrder(trfOrderDTO);
    }

    @Override
    public BaseResponse before(SciTrfContext<GpoSciOrderToTrfReq, OrderToTrfBO> context) {
        GpoSciOrderToTrfReq gpoSciOrderToTrfReq = context.getParam();
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setOrderNoList(Sets.newHashSet(gpoSciOrderToTrfReq.getOrderNo()));
        BaseResponse<List<OrderBO>> orderListRsp = orderDomainService.queryBO(orderQueryReq);
        if (orderListRsp.isFail() || Func.isEmpty(orderListRsp.getData())) {
            return BaseResponse.newFailInstance("order.result.empty", null);
        }
        String primaryLanguageCode = frameworkClient.getPrimaryLanguageCode(Func.isNotEmpty(gpoSciOrderToTrfReq.getProductLineCode())?gpoSciOrderToTrfReq.getProductLineCode():ProductLineContextHolder.getProductLineCode());
        int primaryLanguageId = LanguageType.findCode(primaryLanguageCode).getLanguageId();
        context.setPrimaryLanguageId(primaryLanguageId);
        String orderId = orderListRsp.getData().get(0).getHeader().getOrderId();
        String orderNo = orderListRsp.getData().get(0).getHeader().getOrderNo();
        String enquiryNo = null;
        if(Func.isNotEmpty(orderListRsp.getData().get(0).getRelationship())
                && Func.isNotEmpty(orderListRsp.getData().get(0).getRelationship().getParent())
                && Func.isNotEmpty(orderListRsp.getData().get(0).getRelationship().getParent().getEnquiry())){
            enquiryNo = orderListRsp.getData().get(0).getRelationship().getParent().getEnquiry().getEnquiryNo();
        }
        String sciOrderNo = orderNo;
        String sciOrderId = orderId;

        OrderTrfReq orderTrfReq = new OrderTrfReq();
        orderTrfReq.setOrderNoList(Sets.newHashSet(sciOrderNo));
        BaseResponse<List<OrderTrfBO>> orderTrfRes = orderTrfRelationshipService.queryOrderTrf(orderTrfReq);
        if (orderTrfRes.isSuccess() && Func.isNotEmpty(orderTrfRes.getData())) {
            context.setOrderTrfBOList(orderTrfRes.getData());
        }

        if (Func.isNotEmpty(enquiryNo)) {
            EnquiryIdReq enquiryIdReq = new EnquiryIdReq();
            enquiryIdReq.setEnquiryNoList(Sets.newHashSet(enquiryNo));
            List<EnquiryPO> enquiryPOList = enquiryService.select(enquiryIdReq).getData();
            context.setEnquiryList(enquiryPOList);
        }
        context.setSciOrderNo(sciOrderNo);
        context.setSciOrderId(sciOrderId);

        context.setOrderNoList(Sets.newHashSet(orderNo));
        context.setOrderList(orderListRsp.getData());
        OrderIdReq orderNoReq = new OrderIdReq();
        orderNoReq.setOrderNoList(Sets.newHashSet(orderNo));
        List<GpoStatusPO> gpoStatusPOList = gpoStatusService.queryOrderStatusByOrderNo(orderNoReq).getData();
        context.setGpoStatusPOList(gpoStatusPOList);
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderIdList(Sets.newHashSet(orderId));
        BaseResponse<List<TestRequestContactPO>> testRequestContactRsp = testRequestContactService.select(orderIdReq);
        if (testRequestContactRsp.isSuccess()) {
            List<TestRequestContactPO> testRequestContactPOList = testRequestContactRsp.getData();
            context.setTestRequestContactList(testRequestContactPOList);
        }
        ProductSampleQueryReq productSampleQueryReq = new ProductSampleQueryReq();
        productSampleQueryReq.setOrderIdList(Sets.newHashSet(orderId));
        BaseResponse<List<ProductInstancePO>> orderProductSampleRsp = productInstanceService.queryOrderProductSample(productSampleQueryReq);
        if (orderProductSampleRsp.isSuccess()) {
            List<ProductInstancePO> productInstancePOList = orderProductSampleRsp.getData();
            productInstancePOList = productInstancePOList.stream().filter(item-> !Func.equalsSafe(item.getCancelFlag(),1)).collect(Collectors.toList());
            context.setProductInstanceList(productInstancePOList);
        }
        List<ProductBO> productBOS = this.convertProduct(context);
        context.setOrderProductList(productBOS);

        TestMatrixQueryReq testMatrixQueryReq = new TestMatrixQueryReq();
        testMatrixQueryReq.setOrderNoList(Sets.newHashSet(orderNo));
        BaseResponse<List<TestMatrixBO>> testMatrixRsp = testMatrixService.queryV1(testMatrixQueryReq);
        if (testMatrixRsp.isSuccess()) {
            context.setTestMatrixBOList(testMatrixRsp.getData());
        }
        CareLabelReq careLabelReq = new CareLabelReq();
        careLabelReq.setOrderIds(Sets.newHashSet(orderId));
        BaseResponse<List<CareLabelBO>> careLabelRsp = careLabelService.queryCareLabelList(careLabelReq);
        if (careLabelRsp.isSuccess()) {
            context.setCareLabelBOList(careLabelRsp.getData());
        }

        return super.before(context);
    }
}
