package com.sgs.gpo.domain.service.otsnotes.report.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportFilePO;
import com.sgs.gpo.facade.model.report.req.ReportFileQueryReq;

import java.util.List;

/**
 */
public interface IReportFileService extends IService<ReportFilePO> {


    BaseResponse<List<ReportFilePO>> query(ReportFileQueryReq req);

}
