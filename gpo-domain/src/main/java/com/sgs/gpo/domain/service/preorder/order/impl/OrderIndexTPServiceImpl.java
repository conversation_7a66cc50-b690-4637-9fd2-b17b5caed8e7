package com.sgs.gpo.domain.service.preorder.order.impl;

import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.enums.ObjectType;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderIndexPO;
import com.sgs.gpo.domain.service.common.index.Index;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Service
@Index(object = ObjectType.TP_Order)
@Slf4j
public class OrderIndexTPServiceImpl extends OrderIndexServiceImpl {

    @Override
    public List<OrderIndexPO> searchFromSource(OrderIndexPO index) {
        List<OrderIndexPO> list = baseMapper.selectTPSourceList(index);
        List<OrderIndexPO> payerList = null;
        if(Func.isNotEmpty(list)){
            Set<String> orderIdSet = list.stream().map(OrderIndexPO::getOrderId).collect(Collectors.toSet());
            if(Func.isNotEmpty(orderIdSet)) {
                payerList = baseMapper.getPayerByOrder(orderIdSet);
            }
        }
        if(Func.isNotEmpty(payerList)) {
            List<OrderIndexPO> finalPayerList = payerList;
            list.forEach(item -> {
                OrderIndexPO payerIndex = finalPayerList.stream().filter(payer->item.getOrderId().equals(payer.getOrderId())).findFirst().orElse(null);
                if(Func.isNotEmpty(payerIndex)){
                    item.setPayerNo(payerIndex.getPayerNo());
                    item.setPayerName(payerIndex.getPayerName());
                    item.setPaymentTerm(payerIndex.getPaymentTerm());
                }else{
                    log.error(item.getOrderNo()+" not found payer");
                }
            });
        }
        return list;
    }

}
