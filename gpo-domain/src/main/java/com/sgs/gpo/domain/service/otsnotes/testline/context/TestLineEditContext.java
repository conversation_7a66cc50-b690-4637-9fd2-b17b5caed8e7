package com.sgs.gpo.domain.service.otsnotes.testline.context;

import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.enums.ReportLanguage;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.test.testsample.TestSampleBO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testline.TestLineLabTeamDefaultRelationshipPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.ordercitation.OrderCitationPO;
import com.sgs.gpo.facade.model.otsnotes.testline.vo.TestLineEditVO;
import com.sgs.trimslocal.facade.model.analyte.rsp.TestLineAnalyteRsp;
import com.sgs.trimslocal.facade.model.citation.rsp.CitationListRsp;
import com.sgs.trimslocal.facade.model.condition.rsp.TestConditionRsp;
import com.sgs.trimslocal.facade.model.labsection.rsp.GetLabSectionBaseInfoRsp;
import com.sgs.trimslocal.facade.model.workinginstruction.rsp.WorkingInstructionRsp;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title: TestLineEditContext
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/26 9:54
 */
@Data
public class TestLineEditContext<OrderTestLineReq>  extends BaseContext<OrderTestLineReq, TestLineEditVO> {
    protected boolean chineseFlag = false;
    private Integer reportLanguage = ReportLanguage.EnglishReportOnly.getLanguageId();
    private List<TestLineBO> editTestLineBOList;
    private List<OrderCitationPO> orderCitationList;
    private List<TestSampleBO> testSampleList;
    private List<CitationListRsp> citationListRspList;
    private List<TestConditionRsp> testConditionRspList;
    private List<TestLineAnalyteRsp> testLineAnalyteRspList;
    private List<WorkingInstructionRsp> workingInstructionRspList;
    private List<GetLabSectionBaseInfoRsp> labSectionBaseInfoRspList;

    private List<TestLineLabTeamDefaultRelationshipPO> labTeamDefaultRelationshipPOList;
}
