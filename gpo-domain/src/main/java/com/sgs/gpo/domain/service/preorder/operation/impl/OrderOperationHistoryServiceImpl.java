package com.sgs.gpo.domain.service.preorder.operation.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.operation.OrderOperationHistoryMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.operation.OrderOperationHistoryPO;
import com.sgs.gpo.domain.service.preorder.operation.IOrderOperationHistoryService;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @title: OperationHistoryServiceImpl
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2024/1/25 21:01
 */
@Service
public class OrderOperationHistoryServiceImpl extends ServiceImpl<OrderOperationHistoryMapper, OrderOperationHistoryPO> implements IOrderOperationHistoryService {

    @Override
    public BaseResponse<List<OrderOperationHistoryPO>> select(OrderIdReq orderIdReq) {
        if (Func.isEmpty(orderIdReq) || (Func.isEmpty(orderIdReq.getOrderIdList()))) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        LambdaQueryWrapper<OrderOperationHistoryPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(OrderOperationHistoryPO::getGeneralOrderID, orderIdReq.getOrderIdList());
        List<OrderOperationHistoryPO> orderOperationHistoryPOS = baseMapper.selectList(lambdaQueryWrapper);
        return BaseResponse.newSuccessInstance(orderOperationHistoryPOS);
    }
}
