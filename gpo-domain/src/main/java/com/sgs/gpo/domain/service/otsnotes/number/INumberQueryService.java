package com.sgs.gpo.domain.service.otsnotes.number;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.facade.model.otsnotes.common.req.QueryOtsNotesNumberReq;
import com.sgs.preorder.facade.model.req.ordersearch.ExactConditionNoSearchReq;

import java.util.List;

/**
 * <AUTHOR>
 * @title: INumberQueryService
 * @projectName otsnotes-service
 * @description: TODO
 * @date 2023/7/6 14:14
 */
public interface INumberQueryService {
    BaseResponse<List<String>> queryOtsNotesNumberList(QueryOtsNotesNumberReq queryOtsNotesNumberReq);
}
