package com.sgs.gpo.domain.service.preorder.testrequest.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.TestRequestMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.testrequest.TestRequestPO;
import com.sgs.gpo.domain.service.preorder.testrequest.ITestRequestService;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
@Slf4j
public class TestRequestServiceImpl extends ServiceImpl<TestRequestMapper, TestRequestPO> implements ITestRequestService {


    @Override
    public BaseResponse<List<TestRequestPO>> select(OrderIdReq orderIdReq) {
        if(Func.isEmpty(orderIdReq)||Func.isEmpty(orderIdReq.getOrderIdList())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        LambdaQueryWrapper<TestRequestPO> wrapper= Wrappers.<TestRequestPO>lambdaQuery()
                .in(TestRequestPO::getGeneralOrderId,orderIdReq.getOrderIdList());
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }
}
