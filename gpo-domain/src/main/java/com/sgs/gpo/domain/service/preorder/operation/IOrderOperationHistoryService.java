package com.sgs.gpo.domain.service.preorder.operation;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.operation.OrderOperationHistoryPO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;

import java.util.List;

/**
 * <AUTHOR>
 * @title: IoperationHistoryService
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2024/1/25 21:00
 */
public interface IOrderOperationHistoryService extends IService<OrderOperationHistoryPO> {
    BaseResponse<List<OrderOperationHistoryPO>> select(OrderIdReq orderIdReq);
}
