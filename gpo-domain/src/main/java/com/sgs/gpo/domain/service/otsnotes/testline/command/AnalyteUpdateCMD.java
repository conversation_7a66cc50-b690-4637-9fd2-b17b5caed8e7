package com.sgs.gpo.domain.service.otsnotes.testline.command;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.domain.otsnotes.common.service.analyte.ITestAnalyteService;
import com.sgs.gpo.domain.service.otsnotes.testline.context.AnalyteUpdateContext;
import com.sgs.gpo.facade.model.otsnotes.testline.req.AnalyteUpdateItemReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.AnalyteUpdateReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @title: AnalyteUpdateCMD
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/28 15:33
 */
@Service
@Slf4j
public class AnalyteUpdateCMD extends BaseCommand<AnalyteUpdateContext<AnalyteUpdateReq>> {
    @Autowired
    private ITestAnalyteService analyteService;

    @Override
    public BaseResponse validateParam(AnalyteUpdateContext<AnalyteUpdateReq> context) {
        if (Func.isEmpty(context) || Func.isEmpty(context.getParam())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        AnalyteUpdateReq analyteUpdateReq = context.getParam();
        if (Func.isEmpty(analyteUpdateReq.getTestLineInstanceId())) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"testLineInstanceId"});
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(AnalyteUpdateContext<AnalyteUpdateReq> context) {
        AnalyteUpdateReq analyteUpdateReq = context.getParam();
        List<AnalyteUpdateItemReq> analyteUpdateItemReqList = analyteUpdateReq.getAnalyteUpdateItemReqList();
        if(Func.isNotEmpty(analyteUpdateItemReqList)){
            /*for (TestLineAnalyteRsp analyte : analyteUpdateItemReqList) {
                analyteBaseIdList.add(analyte.getAnalyteBaseId());

                Integer testAnalyteId = analyte.getTestAnalyteId();
                List<TestLineAnalyteUnitRsp> mutipleUnit = analyte.getUnits();
                if(CollectionUtils.isNotEmpty(mutipleUnit)){
                    mutipleUnit: for (TestLineAnalyteUnitRsp unit : mutipleUnit) {
                        Long unitBaseId = unit.getUnitBaseId();
                        String key = String.format("%s_%s",testAnalyteId,unitBaseId);
                        if(repeatUnit.contains(key)){
                            continue ;
                        }
                        unitBaseIdList.add(unitBaseId);
                        repeatUnit.add(key);
                        analytePO = new AnalyteInfoPO();
                        String analyteInstanceId = UUID.randomUUID().toString();
                        analytePO.setID(analyteInstanceId);
                        analytePO.setActiveIndicator(true);
                        analytePO.setCreatedBy(regionAccount);
                        analytePO.setCreatedDate(DateUtils.getNow());
                        analytePO.setModifiedBy(regionAccount);
                        analytePO.setModifiedDate(DateUtils.getNow());

                        analytePO.setUnitBaseId(unitBaseId);
                        analytePO.setGeneralOrderInstanceID(detailRsp.getOrderInstanceId());
                        analytePO.setAnalyteBaseId(analyte.getAnalyteBaseId());
                        analytePO.setTestLineInstanceID(testLineInstanceId);
                        analytePO.setAnalyteID(analyte.getTestAnalyteId());
                        analytePO.setTestAnalyteName(LOStringUtil.delHTMLTag(analyte.getTestAnalyteDesc()));
                        analytePO.setCasNo(analyte.getTestAnalyteCasNumber());
                        analytePO.setTestAnalyteSeq(analyte.getTestAnalyteSeq());
                        //设置unit相关

                        analytePO.setReportUnit(LOStringUtil.delHTMLTag(unit.getUnitShortDepiction()));
                        result.add(analytePO);
                        List<TestAnalyteLangReq> otherLanguageItems =analyte.getLanguages();
                        if(CollectionUtils.isEmpty(otherLanguageItems)){
                            continue mutipleUnit;
                        }
                        otherLanguageItems.forEach(anO->{
                            String multiTestAnalyteDesc = anO.getTestAnalyteDesc();
                            AnalyteUnitLangRsp unitReportLan = unit.getLanguage();
                            AnalyteMultipleLanguageInfoPO po = new AnalyteMultipleLanguageInfoPO();
                            po.setID(UUID.randomUUID().toString());
                            po.setAnalyteInstanceID(analyteInstanceId);
                            po.setLanguageId(anO.getLanguageId());
                            po.setTestAnalyteName(LOStringUtil.delHTMLTag(multiTestAnalyteDesc));
                            po.setCreatedBy(regionAccount);
                            po.setModifiedBy(regionAccount);
                            po.setCreatedDate(DateUtils.getNow());
                            po.setModifiedDate(DateUtils.getNow());
                            if(Func.isNotEmpty(unitReportLan)){
                                String multiUnitShortDepiction = unitReportLan.getUnitShortDepiction();
                                po.setReportUnit(multiUnitShortDepiction);
                                saveAnalyteLanguages.add(po);
                            }else{
                                saveAnalyteLanguages.add(po);
                            }
                        });

                    }
                }else{
                    analytePO = new AnalyteInfoPO();
                    String analyteInstanceId = UUID.randomUUID().toString();
                    analytePO.setID(analyteInstanceId);
                    analytePO.setActiveIndicator(true);
                    analytePO.setCreatedBy(regionAccount);
                    analytePO.setCreatedDate(DateUtils.getNow());
                    analytePO.setModifiedBy(regionAccount);
                    analytePO.setModifiedDate(DateUtils.getNow());

                    analytePO.setGeneralOrderInstanceID(detailRsp.getOrderInstanceId());
                    analytePO.setAnalyteBaseId(analyte.getAnalyteBaseId());
                    analytePO.setTestLineInstanceID(testLineInstanceId);
                    analytePO.setAnalyteID(analyte.getTestAnalyteId());
                    analytePO.setTestAnalyteName(LOStringUtil.delHTMLTag(analyte.getTestAnalyteDesc()));
                    analytePO.setCasNo(analyte.getTestAnalyteCasNumber());
                    analytePO.setTestAnalyteSeq(analyte.getTestAnalyteSeq());
                    result.add(analytePO);


                    List<TestAnalyteLangReq> otherLanguageItems = analyte.getLanguages();
                    if(CollectionUtils.isEmpty(otherLanguageItems)){
                        continue;
                    }
                    otherLanguageItems.forEach(anO->{
                        String multiTestAnalyteDesc = anO.getTestAnalyteDesc();
                        AnalyteMultipleLanguageInfoPO po = new AnalyteMultipleLanguageInfoPO();
                        po.setID(UUID.randomUUID().toString());
                        po.setAnalyteInstanceID(analyteInstanceId);
                        po.setLanguageId(anO.getLanguageId());
                        po.setTestAnalyteName(LOStringUtil.delHTMLTag(multiTestAnalyteDesc));
                        po.setCreatedBy(regionAccount);
                        po.setModifiedBy(regionAccount);
                        po.setCreatedDate(DateUtils.getNow());
                        po.setModifiedDate(DateUtils.getNow());
                        saveAnalyteLanguages.add(po);
                    });
                }
            }*/

        }
        //保存Analyte


        //保存AnalyteLanguage

        //保存OrderLanguage
        return null;
    }


}
