package com.sgs.gpo.domain.service.otsnotes.job.context;

import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.order.order.OrderBO;
import com.sgs.framework.model.test.execution.JobBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.job.JobPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.job.JobTestLinePO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineExpectDueDateReq;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/7/3 09:02
 */
@Data
public class JobEditContext<Input> extends BaseContext<Input, JobBO> {
    private UserInfo userInfo;
    private JobPO jobPO ;
    private List<JobTestLinePO> jobTestLineList;
    private Set<String> testLineInstanceIdList ;
    private String testLineGroupModel = Constants.OBJECT.JOB.TESTLINE_GROUP_MODE.LAB_SECTION;

    // Temp 相关属性
    private List<TestLineBO> testLineList;
    // DB 相关属性
    private Set<String> jobTestLineInstanceIdList ;
    // 获取需要新增的测试项目
    private Set<String> addTestLineList ;
    // 获取需要删除的测试项目
    private Set<String> deleteTestLineList;
    private List<JobTestLinePO> saveJobTestLineList;
    private List<TestLineExpectDueDateReq> updateTestLineExpectDueDateList;
    private List<OrderBO> orderBOList;
}
