package com.sgs.gpo.domain.service.preorder.enquiry;


import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.order.enquiry.EnquiryBO;
import com.sgs.framework.model.order.enquiry.EnquiryIdBO;
import com.sgs.framework.open.platform.base.service.IDomainService;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryCustomerPO;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryCreateReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryCustomerReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryProductReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryQueryReq;
import com.sgs.gpo.facade.model.preorder.enquiry.rsp.EnquiryProductInstanceRsp;

import java.util.List;

/**
 * Enquiry 领域操作方法
 */
public interface IEnquiryDomainService extends IDomainService<EnquiryBO,EnquiryIdBO, EnquiryQueryReq> {


    /**
     * Enquiry 客户查询
     */

    BaseResponse<List<EnquiryCustomerPO>> queryCustomerList(EnquiryCustomerReq req);

    /**
     * Enquiry DFF 查询
     */
    BaseResponse<List<EnquiryProductInstanceRsp>> queryProductList(EnquiryProductReq req);

    /**
     * 新增Enquiry
     */
    BaseResponse createEnquiry(EnquiryCreateReq createReq);
}
