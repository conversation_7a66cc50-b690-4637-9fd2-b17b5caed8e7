package com.sgs.gpo.domain.service.otsnotes.testline.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.trims.labsection.LabSectionBO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testline.TestLineLabSectionRelationshipPO;
import com.sgs.gpo.facade.model.otsnotes.labSection.req.QueryLabSectionReq;
import com.sgs.gpo.facade.model.otsnotes.labSection.rsp.ReportTestLineLabSectionRsp;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/30 10:56
 */
public interface ITestLineLabSectionService extends IService<TestLineLabSectionRelationshipPO> {

    BaseResponse<List<TestLineLabSectionRelationshipPO>> queryByOrderTestLine(OrderTestLineReq orderTestLineReq);

    BaseResponse<List<LabSectionBO>> queryLabSection(QueryLabSectionReq queryLabSectionReq);

    /**
     * 查询订单下Job对应的LabSection集合
     * Master List 打印使用
     * 未来可以在这个接口增加打印校验？
     * @param queryLabSectionReq
     * @return
     */
    BaseResponse<List<LabSectionBO>> queryJobLabSection(QueryLabSectionReq queryLabSectionReq);

    BaseResponse<List<ReportTestLineLabSectionRsp>> queryReportTestLineLabSection(QueryLabSectionReq queryLabSectionReq);

    BaseResponse delete(OrderTestLineReq orderTestLineReq);
}
