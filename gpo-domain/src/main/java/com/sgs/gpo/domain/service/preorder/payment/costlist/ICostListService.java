package com.sgs.gpo.domain.service.preorder.payment.costlist;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.facade.model.payment.costlist.bo.CostListBO;
import com.sgs.gpo.facade.model.payment.costlist.req.CostListQueryReq;

/**
 * <AUTHOR>
 * @title: ICostListService
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/5 14:04
 */
public interface ICostListService {
    BaseResponse<Long> selectCostListPageCount(CostListQueryReq costListQueryReq);
    BaseResponse<IPage<CostListBO>> selectCostListPage(Page page, CostListQueryReq costListQueryReq);

}
