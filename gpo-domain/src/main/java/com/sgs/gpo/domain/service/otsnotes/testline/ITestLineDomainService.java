package com.sgs.gpo.domain.service.otsnotes.testline;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.test.testline.v2.TestLineIdBO;
import com.sgs.framework.open.platform.base.service.IDomainService;
import com.sgs.gpo.facade.model.otsnotes.testline.TestLinePageBO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.*;
import com.sgs.gpo.facade.model.otsnotes.testline.rsp.LabSectionItemRsp;
import com.sgs.gpo.facade.model.otsnotes.testline.vo.TestLineEditVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/6 12:29
 */
public interface ITestLineDomainService extends IDomainService<com.sgs.framework.model.test.testline.v2.TestLineBO, TestLineIdBO, OrderTestLineReq> {
    /**
     * 查询测试项信息
     * @param orderTestLineReq
     * @return
     */
    BaseResponse<List<TestLineBO>> queryTestLine(OrderTestLineReq orderTestLineReq);
    BaseResponse<IPage<TestLinePageBO>> queryTestLinePage(OrderTestLineReq orderTestLineReq, Integer page, Integer rows);

    /**
     * 查询PP TestLine打平结构
     * @param orderTestLineReq
     * @return
     */
    BaseResponse<List<TestLineBO>> queryPPTestLine(OrderTestLineReq orderTestLineReq);

    /**
     * 查询TestLine LabSection打平结构
     * @param orderTestLineReq
     * @return
     */
    BaseResponse<List<TestLineBO>> queryTestLineLabSection(OrderTestLineReq orderTestLineReq);

    /**
     * 更新LabSection
     * @param labSectionUpdateReq
     * @return
     */
    BaseResponse<List<LabSectionItemRsp>> updateLabSection(LabSectionUpdateReq labSectionUpdateReq);

    BaseResponse<Boolean> testLineLabOut(TestLineLabOutReq testLineLabOutReq);

    /**
     * 记录开始测试时间
     * @param testLineLabInReq
     * @return
     */
    BaseResponse<List<String>> labIn(TestLineLabInReq testLineLabInReq);

    BaseResponse<Boolean> testLineSubmit(TestLineSubmitReq testLineSubmitReq);

//    BaseResponse<List<TestLineDetailVO>> testLineDetail(OrderTestLineReq orderTestLineReq);

    BaseResponse<List<TestLineEditVO>> testLineEdit(OrderTestLineReq orderTestLineReq);

    BaseResponse<Boolean> testLineUpdate(TestLineUpdateReq testLineUpdateReq);

    BaseResponse<Boolean> citationUpdate(CitationUpdateReq citationUpdateReq);

    BaseResponse<Boolean> conditionUpdate(ConditionUpdateReq conditionUpdateReq);

    BaseResponse<Boolean> analyteUpdate(AnalyteUpdateReq analyteUpdateReq);
    /**
     * 更新TestLine Document ReviewFlag
     * @param testLineUpdateDRFlagReq
     * @return
     */
    BaseResponse<Boolean> updateDRFlag(TestLineUpdateDRFlagReq testLineUpdateDRFlagReq);
}
