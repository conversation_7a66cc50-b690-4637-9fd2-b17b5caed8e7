package com.sgs.gpo.domain.service.dashboard.context;


import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.trims.labsection.LabSectionBO;
import com.sgs.gpo.facade.model.dashboard.req.DashBoardQueryRep;
import com.sgs.gpo.facade.model.dashboard.rsp.ReportTaskDetailRsp;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DashboardReportTaskDetailContext extends BaseContext<DashBoardQueryRep, ReportTaskDetailRsp> {

    private String labCode;

    private List<ReportTaskDetailRsp> reportList;

    private Map<String,String> delayTypeMap;

    private Map<Long, LabSectionBO> labSectionBOMap;
}
