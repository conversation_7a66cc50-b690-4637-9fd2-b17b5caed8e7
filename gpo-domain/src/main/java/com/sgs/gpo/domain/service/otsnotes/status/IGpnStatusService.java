package com.sgs.gpo.domain.service.otsnotes.status;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.status.GpnStatusPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.status.GpoStatusPO;
import com.sgs.gpo.facade.model.preorder.order.req.GpnStatusReq;
import com.sgs.gpo.facade.model.report.req.ReportIdReq;

import java.util.List;

/**
 * <AUTHOR>
 * @title: IGpnStatusService
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/26 16:35
 */
public interface IGpnStatusService extends IService<GpnStatusPO> {
    BaseResponse<List<GpnStatusPO>> queryReportStatus(ReportIdReq reportIdReq);
    List<GpnStatusPO> query(GpnStatusReq gpnStatusReq);
}
