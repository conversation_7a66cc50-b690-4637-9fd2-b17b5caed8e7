package com.sgs.gpo.domain.service.otsnotes.testline.context;

import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.order.order.OrderBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.test.testline.TestLineExecutionBO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.job.JobPO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineLabOutReq;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title: LabSectionUpdateContext
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/10/17 10:27
 */
@Data
public class TestLineLabOutContext extends BaseContext<TestLineLabOutReq, TestLineBO> {
    private List<TestLineBO> testLineBOList;
    private List<JobPO> jobPOList;
    private List<TestLineExecutionBO> testLineExecutionJobBOList;
    private List<OrderBO> orderBOList;
}
