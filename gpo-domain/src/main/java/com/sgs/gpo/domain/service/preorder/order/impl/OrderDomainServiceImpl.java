package com.sgs.gpo.domain.service.preorder.order.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.model.enums.RefIntegrationChannel;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.model.order.order.OrderBO;
import com.sgs.framework.model.order.v2.OrderIdBO;
import com.sgs.framework.model.quotation.QuotationHeadBO;
import com.sgs.framework.open.platform.base.service.impl.AbstractDomainService;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.BizLogConstant;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.ReportInfoLockEnum;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderReportReceiverPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.SLOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.ordertrfrel.OrderTrfRelationshipPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.quotation.QuotationHeaderPO;
import com.sgs.gpo.domain.service.otsnotes.report.command.ReportWorkFlowQueryCMD;
import com.sgs.gpo.domain.service.otsnotes.report.context.ReportContext;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.domain.service.preorder.order.command.*;
import com.sgs.gpo.domain.service.preorder.order.context.OrderContext;
import com.sgs.gpo.domain.service.preorder.order.context.OrderTrfContext;
import com.sgs.gpo.domain.service.preorder.order.context.OrderUpdatePaidStatusContext;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IGeneralOrderService;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IOrderReportReceiverService;
import com.sgs.gpo.domain.service.preorder.order.subdomain.ISLOrderService;
import com.sgs.gpo.domain.service.preorder.ordertrfrel.IOrderTrfRelationshipService;
import com.sgs.gpo.domain.service.extservice.sci.SciTrfContext;
import com.sgs.gpo.domain.service.extservice.sci.command.SciOrderToTrfCMD;
import com.sgs.gpo.domain.service.extservice.sci.command.SciUnBindTrfCMD;
import com.sgs.gpo.facade.model.payment.costlist.req.ActualFeeSaveReq;
import com.sgs.gpo.facade.model.payment.paymentlist.req.PaymentInvoiceDownloadReq;
import com.sgs.gpo.facade.model.preorder.order.req.*;
import com.sgs.gpo.facade.model.preorder.order.rsp.OrderEditableRsp;
import com.sgs.gpo.facade.model.preorder.order.rsp.OrderReferenceNoRsp;
import com.sgs.gpo.facade.model.preorder.productsample.req.QuotationQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportWorkFlowReq;
import com.sgs.gpo.facade.model.sci.bo.OrderToTrfBO;
import com.sgs.gpo.facade.model.sci.bo.SciUnBindTrfBO;
import com.sgs.gpo.facade.model.sci.req.GpoSciOrderToTrfReq;
import com.sgs.gpo.facade.model.sci.req.GpoSciTrfSyncReq;
import com.sgs.gpo.facade.model.sci.rsp.Order2TrfRsp;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.info.BizLogInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/7/5 15:31
 */
@Service
@Slf4j
public class OrderDomainServiceImpl extends AbstractDomainService<com.sgs.framework.model.order.v2.OrderBO, OrderIdBO, OrderQueryReq, IGeneralOrderService> implements IOrderDomainService {

    @Autowired
    private IOrderReportReceiverService orderReportReceiverService;
    @Autowired
    private IGeneralOrderService generalOrderService;
    @Autowired
    private ISLOrderService slOrderService;
    @Autowired
    private IOrderTrfRelationshipService orderTrfRelationshipService;
    @Autowired
    private BizLogClient bizLogClient;

    @Override
    public BaseResponse<List<OrderBO>> queryV1(OrderQueryReq orderQueryReq) {
        OrderContext<OrderQueryReq> context = new OrderContext<>();
        context.setParam(orderQueryReq);
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        context.setToken(SecurityContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(OrderQueryCMD.class, context);
    }

    @Override
    public BaseResponse<List<com.sgs.framework.model.order.v2.OrderBO>> queryBO(OrderQueryReq request) {

        // 入参不允许为空
        Assert.isTrue(Func.isNotEmpty(request), "common.param.miss", new Object[]{Constants.TERM.REQUEST});
        // 参数必须是OrderQueryReq
        Assert.isTrue(request instanceof OrderQueryReq, "common.invalid.type", null);
        com.sgs.gpo.domain.service.preorder.order.context.v2.OrderContext<OrderQueryReq> orderContext =
                new com.sgs.gpo.domain.service.preorder.order.context.v2.OrderContext<>();
        orderContext.setParam(request);
        orderContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        orderContext.setToken(SecurityContextHolder.getSgsToken());
        orderContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(com.sgs.gpo.domain.service.preorder.order.command.v2.OrderQueryCMD.class, orderContext);

    }

    @Override
    public BaseResponse<List<OrderEditableRsp>> queryOrderEditable(OrderEditableReq orderEditableReq) {
        OrderTrfContext context = new OrderTrfContext();
        context.setParam(orderEditableReq);
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        context.setToken(SecurityContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(OrderEditableCMD.class, context);
    }

    @Override
    public BaseResponse<Boolean> updatePaidStatus(OrderUpdatePaidStatusReq orderUpdatePaidStatusReq) {
        OrderUpdatePaidStatusContext context = new OrderUpdatePaidStatusContext();
        context.setParam(orderUpdatePaidStatusReq);
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        context.setToken(SecurityContextHolder.getSgsToken());
        context.setProductLineCode(orderUpdatePaidStatusReq.getProductLineCode());
        return BaseExecutor.start(OrderUpdatePaidStatusCMD.class, context);
    }

    @Override
    public BaseResponse<Boolean> lockOrderReportReceiver(OrderQueryReq orderQueryReq) {
        // 校验入参
        Assert.isTrue(Func.isNotEmpty(orderQueryReq) && Func.isNotEmpty(orderQueryReq.getOrderIdList()), "common.param.miss", new Object[]{Constants.TERM.REQUEST});
        // 查询当前数据状态
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderIdList(orderQueryReq.getOrderIdList());
        BaseResponse<List<OrderReportReceiverPO>> orderReportReceiverRes = orderReportReceiverService.select(orderIdReq);
        if (Func.isEmpty(orderReportReceiverRes) || Func.isEmpty(orderReportReceiverRes.getData())) {
            return BaseResponse.newSuccessInstance(true);
        }
        List<OrderReportReceiverPO> needLockOrderReportReceiver = Lists.newArrayList();
        orderReportReceiverRes.getData().stream().forEach(item -> {
            // 判断是否需要更新
            if (ReportInfoLockEnum.LOCK.getStatus() == item.getReportInfoLock()) {
                return;
            }
            item.setReportInfoLock(ReportInfoLockEnum.LOCK.getStatus());
            needLockOrderReportReceiver.add(item);
        });
        // 执行更新lock
        if (Func.isNotEmpty(needLockOrderReportReceiver)) {
            orderReportReceiverService.updateBatchById(needLockOrderReportReceiver);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse<Order2TrfRsp> order2Trf(GpoSciOrderToTrfReq orderToTrfReq) {
        SciTrfContext<GpoSciOrderToTrfReq, OrderToTrfBO> context = new SciTrfContext<>();
        context.setParam(orderToTrfReq);
        context.setUserInfo(SecurityContextHolder.getUserInfo());
        context.setToken(orderToTrfReq.getToken());
        context.setProductLineCode(orderToTrfReq.getProductLineCode());
        return BaseExecutor.start(SciOrderToTrfCMD.class, context);
    }

    @Override
    public BaseResponse<Boolean> unBindSciTrf(GpoSciTrfSyncReq sciTrfSyncReq) {
        SciTrfContext<GpoSciTrfSyncReq, SciUnBindTrfBO> context = new SciTrfContext<>();
        context.setParam(sciTrfSyncReq);
        context.setUserInfo(SecurityContextHolder.getUserInfo());
        context.setToken(sciTrfSyncReq.getToken());
        context.setProductLineCode(sciTrfSyncReq.getProductLineCode());
        return BaseExecutor.start(SciUnBindTrfCMD.class, context);
    }

    @Override
    public BaseResponse<List<OrderReferenceNoRsp>> queryOrderReferenceNo(List<String> orderNos) {
        if (Func.isEmpty(orderNos)) {
            return BaseResponse.newFailInstance("参数orderNo为空");
        }
        List<OrderReferenceNoRsp> orderReferenceNoList = Lists.newArrayList();

        // 查询generalOrder信息
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setOrderNoList(Sets.newHashSet(orderNos));
        BaseResponse<List<GeneralOrderPO>> generalOrderRes = generalOrderService.query2(orderQueryReq);
        if(generalOrderRes.isFail()||Func.isEmpty(generalOrderRes.getData())){
            return BaseResponse.newFailInstance("orderNo不合法");
        }
        Set<String> orderIdSets = generalOrderRes.getData().stream().map(GeneralOrderPO::getId).collect(Collectors.toSet());
        // 查询sl_order的扩展信息
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderIdList(orderIdSets);
        BaseResponse<List<SLOrderPO>> slOrderRes = slOrderService.select(orderIdReq);
        if(slOrderRes.isFail()||Func.isEmpty(slOrderRes.getData())){
            return BaseResponse.newFailInstance("sl_order信息查询失败");
        }
        // 查询 order_trf rel
        OrderTrfReq orderTrfReq = new OrderTrfReq();
        orderTrfReq.setOrderIdList(orderIdSets);
        BaseResponse<List<OrderTrfRelationshipPO>> orderTrfRes = orderTrfRelationshipService.queryByOrderId(orderTrfReq);
        if(orderTrfRes.isFail()){
            return BaseResponse.newFailInstance("OrderTrfRel信息查询失败");
        }
        List<OrderTrfRelationshipPO> orderTrfRelationshipList = orderTrfRes.getData();
        // 匹配每个订单的返回值
        for(GeneralOrderPO generalOrder:generalOrderRes.getData()){
            OrderReferenceNoRsp orderReferenceNoRsp = new OrderReferenceNoRsp();
            orderReferenceNoRsp.setOrderNo(generalOrder.getOrderNo());
            String referenceNo = null;
            // order trf关系处理
            if(Func.isNotEmpty(orderTrfRelationshipList)){
                OrderTrfRelationshipPO subContractRelInfo = orderTrfRelationshipList.parallelStream()
                        .filter(trf ->Func.equalsSafe(trf.getOrderId(),generalOrder.getId()) &&
                                RefSystemIdEnum.check(trf.getRefSystemId(),RefSystemIdEnum.SubContract))
                        .findFirst().orElse(null);
                OrderTrfRelationshipPO dmlRelInfo = orderTrfRelationshipList.parallelStream()
                        .filter(trf ->Func.equalsSafe(trf.getOrderId(),generalOrder.getId()) &&
                                RefSystemIdEnum.check(trf.getRefSystemId(),RefSystemIdEnum.DML))
                        .findFirst().orElse(null);

                OrderTrfRelationshipPO dalRelInfo = orderTrfRelationshipList.parallelStream()
                        .filter(trf ->Func.equalsSafe(trf.getOrderId(),generalOrder.getId()) &&
                                RefSystemIdEnum.check(trf.getRefSystemId(),RefSystemIdEnum.DAL))
                        .findFirst().orElse(null);

                OrderTrfRelationshipPO rstsRelInfo = orderTrfRelationshipList.parallelStream()
                        .filter(trf ->Func.equalsSafe(trf.getOrderId(),generalOrder.getId()) &&
                                RefSystemIdEnum.check(trf.getRefSystemId(),RefSystemIdEnum.RSTS))
                        .findFirst().orElse(null);

                OrderTrfRelationshipPO externalSubContractRelInfo = orderTrfRelationshipList.parallelStream()
                        .filter(trf ->Func.equalsSafe(trf.getOrderId(),generalOrder.getId()) &&
                                RefSystemIdEnum.check(trf.getRefSystemId(),RefSystemIdEnum.ExternalSubContract))
                        .findFirst().orElse(null);

                OrderTrfRelationshipPO otsRelInfo = orderTrfRelationshipList.parallelStream()
                        .filter(trf ->Func.equalsSafe(trf.getOrderId(),generalOrder.getId()) &&
                                RefSystemIdEnum.check(trf.getRefSystemId(),RefSystemIdEnum.OTS))
                        .findFirst().orElse(null);

                if(Func.isNotEmpty(subContractRelInfo)){
                    referenceNo = subContractRelInfo.getExternalOrderNo();
                    orderReferenceNoRsp.setReferenceOrderNo(subContractRelInfo.getExternalOrderNo());
                    orderReferenceNoRsp.setRefSystemId(subContractRelInfo.getRefSystemId());
                }else if(Func.isNotEmpty(dmlRelInfo)){
                    referenceNo = dmlRelInfo.getRefNo();
                    orderReferenceNoRsp.setReferenceOrderNo(dmlRelInfo.getExternalOrderNo());
                    orderReferenceNoRsp.setRefSystemId(dmlRelInfo.getRefSystemId());
                }else if(Func.isNotEmpty(dalRelInfo)){
                    referenceNo = dalRelInfo.getRefNo();
                    orderReferenceNoRsp.setReferenceOrderNo(dalRelInfo.getExternalOrderNo());
                    orderReferenceNoRsp.setRefSystemId(dalRelInfo.getRefSystemId());
                }else if(Func.isNotEmpty(rstsRelInfo)){
                    referenceNo = rstsRelInfo.getRefNo();
                    orderReferenceNoRsp.setReferenceOrderNo(rstsRelInfo.getExternalOrderNo());
                    orderReferenceNoRsp.setRefSystemId(rstsRelInfo.getRefSystemId());
                } else if(Func.isNotEmpty(externalSubContractRelInfo)){
                    referenceNo = externalSubContractRelInfo.getRefNo();
                    orderReferenceNoRsp.setReferenceOrderNo(externalSubContractRelInfo.getExternalOrderNo());
                    orderReferenceNoRsp.setRefSystemId(externalSubContractRelInfo.getRefSystemId());
                }else if(Func.isNotEmpty(otsRelInfo)){
                    referenceNo = otsRelInfo.getRefNo();
                    orderReferenceNoRsp.setReferenceOrderNo(otsRelInfo.getExternalOrderNo());
                    orderReferenceNoRsp.setRefSystemId(otsRelInfo.getRefSystemId());
                }else{
                    OrderTrfRelationshipPO otherTrfRelInfo = orderTrfRelationshipList.parallelStream()
                            .filter(trf ->Func.equalsSafe(trf.getOrderId(),generalOrder.getId()) &&
                                    RefSystemIdEnum.check(trf.getRefSystemId(),RefSystemIdEnum.Shein,RefSystemIdEnum.SheinSupplier,RefSystemIdEnum.SGSMart,RefSystemIdEnum.TIC) || RefIntegrationChannel.check(trf.getIntegrationChannel(),RefIntegrationChannel.SCI))
                            .findFirst().orElse(null);
                    if(Func.isNotEmpty(otherTrfRelInfo)){
                        referenceNo = otherTrfRelInfo.getRefNo();
                        orderReferenceNoRsp.setReferenceOrderNo(otherTrfRelInfo.getExternalOrderNo());
                    }
                }
            }
            // 匹配Sl信息
            SLOrderPO slOrder = slOrderRes.getData().stream().filter(item->Func.equals(item.getGeneralOrderID(),generalOrder.getId())).findAny().orElse(null);
            if(Func.isNotEmpty(slOrder)&&Func.isNotEmpty(slOrder.getCustomerRefNo())){
                orderReferenceNoRsp.setCustomerReferenceNo(slOrder.getCustomerRefNo());
            }
            orderReferenceNoRsp.setSgsReferenceNo(referenceNo);
            orderReferenceNoList.add(orderReferenceNoRsp);
        }
        return BaseResponse.newSuccessInstance(orderReferenceNoList);
    }


    @Override
    public BaseResponse<Integer> queryWorkFlowConfig(String orderNo) {
        ReportContext<ReportWorkFlowReq> reportContext = new ReportContext<>();
        ReportWorkFlowReq reportWorkFlowReq = new ReportWorkFlowReq();
        reportWorkFlowReq.setOrderNo(orderNo);
        reportContext.setParam(reportWorkFlowReq);
        reportContext.setUserInfo(SystemContextHolder.getUserInfo());
        reportContext.setToken(SystemContextHolder.getSgsToken());
        reportContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(ReportWorkFlowQueryCMD.class, reportContext);
    }

    @Override
    public BaseResponse<Boolean> paymentStatusSync(OrderPaymentStatusSyncReq req) {
        OrderContext<OrderPaymentStatusSyncReq> context = new OrderContext<>();
        context.setParam(req);
        context.setLab(SystemContextHolder.getLab());
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        context.setToken(SystemContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(PaymentStatusSyncCMD.class,context);
    }

    @Override
    public BaseResponse paymentStatusCalculate(CalcPaymentStatusReq calcPaymentStatusReq) {
        OrderContext<CalcPaymentStatusReq> context = new OrderContext<>();
        context.setParam(calcPaymentStatusReq);
        context.setLab(SystemContextHolder.getLab());
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        context.setToken(SystemContextHolder.getSgsToken());
        context.setProductLineCode(SystemContextHolder.getLab().getBuCode());
        return BaseExecutor.start(PaymentStatusCalculateCMD.class,context);
    }

    @Override
    public BaseResponse<Integer> buildEnableDelivery(String orderNo) {
        OrderContext<String> context = new OrderContext<>();
        context.setParam(orderNo);
        context.setLab(SystemContextHolder.getLab());
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        context.setToken(SystemContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(BuildEnableDeliveryCMD.class,context);
    }

    @Override
    public BaseResponse<List<QuotationHeadBO>> queryQuotationList(QuotationQueryReq quotationQueryReq) {
        if(Func.isEmpty(quotationQueryReq) || Func.isEmpty(quotationQueryReq.getOrderId())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        List<QuotationHeaderPO> quotationHeaderPOList = generalOrderService.selectQuotationList(quotationQueryReq);
        if(Func.isEmpty(quotationHeaderPOList)){
            return BaseResponse.newSuccessInstance(Lists.newArrayList());
        }
        List<QuotationHeadBO> quotationHeadList = Lists.newArrayList();
        quotationHeaderPOList.stream().forEach(quotationHeaderPO -> {
            QuotationHeadBO quotationHeadBO = Func.copy(quotationHeaderPO,QuotationHeadBO.class);
            quotationHeadBO.setQuotationHeadId(quotationHeaderPO.getId());
            quotationHeadList.add(quotationHeadBO);
        });
        return BaseResponse.newSuccessInstance(quotationHeadList);
    }

    @Override
    public BaseResponse updateActualFeeByOrderNo(List<ActualFeeSaveReq> request) {
        if(Func.isNotEmpty(request)){
            request = request.stream().filter(e ->
                    Func.isNotEmpty(e.getOrderNo())
                            && Func.isNotEmpty(e.getNewActualFee())
                            && Func.isNotEmpty(e.getNewCurrency())).collect(Collectors.toList());
            if(Func.isNotEmpty(request)){
                generalOrderService.updateActualFeeByOrderNo(request);

                Set<String> orderNos = request.stream().map(ActualFeeSaveReq::getOrderNo).collect(Collectors.toSet());
                // 查询generalOrder信息
                OrderQueryReq orderQueryReq = new OrderQueryReq();
                orderQueryReq.setOrderNoList(orderNos);
                BaseResponse<List<GeneralOrderPO>> generalOrderRes = generalOrderService.query2(orderQueryReq);
                List<GeneralOrderPO> generalOrderPOS = null;
                if (Func.isNotEmpty(generalOrderRes) && Func.isNotEmpty(generalOrderRes.getData())){
                    generalOrderPOS = generalOrderRes.getData();
                }
                List<GeneralOrderPO> newGeneralOrderPOS = generalOrderPOS;

                //记录BizLog
                request.stream().forEach(req ->{
                    GeneralOrderPO generalOrderPO = null;
                    if(Func.isNotEmpty(newGeneralOrderPOS)) {
                        generalOrderPO = newGeneralOrderPOS.stream().filter(e -> Func.equals(e.getOrderNo(), req.getOrderNo())).findFirst().orElse(null);
                    }
                    UserInfo userInfo = SecurityContextHolder.getUserInfoFillSystem();
                    Lab lab = SystemContextHolder.getLab();
                    BizLogInfo bizLog = new BizLogInfo();
                    bizLog.setBu((Func.isNotEmpty(generalOrderPO) && Func.isNotEmpty(generalOrderPO.getBuCode())) ? generalOrderPO.getBuCode() : ProductLineContextHolder.getProductLineCode());
                    bizLog.setLab((Func.isNotEmpty(generalOrderPO) && Func.isNotEmpty(generalOrderPO.getLocationCode())) ? generalOrderPO.getLocationCode() : lab.getLocationCode());
                    bizLog.setOpUser(userInfo.getRegionAccount());
                    bizLog.setBizId(req.getOrderNo());
                    bizLog.setOpType("Order Actual Fee Upload");
                    if(Func.isNotEmpty(req.getCurrentActualFee()) && Func.isNotEmpty(req.getCurrentCurrency())){
                        bizLog.setOriginalVal(req.getCurrentCurrency() + ":" + req.getCurrentActualFee());
                    }
                    bizLog.setNewVal(req.getNewCurrency() + ":" +req.getNewActualFee());
                    bizLog.setBizOpType(BizLogConstant.ORDER_OPERATION_HISTORY);
                    log.info("bizLog param: {}", JSON.toJSONString(bizLog));
                    bizLogClient.doSend(bizLog);
                });
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse queryToSgsMartDetail(OrderToSgsMartReq orderToSgsMartReq) {
        OrderContext<OrderToSgsMartReq> context = new OrderContext<>();
        context.setParam(orderToSgsMartReq);
        context.setLab(SystemContextHolder.getLab());
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        context.setToken(SystemContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(QueryToSgsMartDetailCMD.class,context);
    }

    @Override
    public BaseResponse updateOrderInfoToSGSMart(OrderToSgsMartReq orderToSgsMartReq) {
        OrderContext<OrderToSgsMartReq> context = new OrderContext<>();
        context.setParam(orderToSgsMartReq);
        context.setLab(SystemContextHolder.getLab());
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        context.setToken(SystemContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(UpdateOrderInfoToSGSMartCMD.class,context);
    }

    @Override
    public BaseResponse updateInvoicePDF(PaymentInvoiceDownloadReq paymentInvoiceDownloadReq) {
        OrderContext<PaymentInvoiceDownloadReq> context = new OrderContext<>();
        context.setParam(paymentInvoiceDownloadReq);
        context.setLab(SystemContextHolder.getLab());
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        context.setToken(SystemContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(PaymentInvoiceUpdateCMD.class,context);
    }

    @Override
    public BaseResponse confirm(OrderConfirmReq orderConfirmReq) {
        //
        return null;
    }


}
