package com.sgs.gpo.domain.service.preorder.order.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.OrderParcelMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderParcelPO;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IOrderParcelService;
import com.sgs.gpo.facade.model.preorder.order.req.OrderParcelReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class OrderParcelServiceImpl extends ServiceImpl<OrderParcelMapper, OrderParcelPO> implements IOrderParcelService {
    @Override
    public List<OrderParcelPO> query(OrderParcelReq orderParcelReq) {
        List<OrderParcelPO> orderExtPOList = new ArrayList<>();
        if (Func.isEmpty(orderParcelReq) || Func.isEmpty(orderParcelReq.getOrderIdList())) {
            return orderExtPOList;
        }
        LambdaQueryWrapper<OrderParcelPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderParcelPO::getGeneralOrderId, orderParcelReq.getOrderIdList());
        return baseMapper.selectList(wrapper);
    }
}
