package com.sgs.gpo.domain.service.otsnotes.report.command;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.util.StringPool;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportFileHistoryPO;
import com.sgs.gpo.domain.service.otsnotes.report.context.ReportContext;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportFileHistoryService;
import com.sgs.gpo.facade.model.report.req.ReportFileQueryReq;
import com.sgs.gpo.facade.model.report.rsp.ReportFileHistoryRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ReportFileHistoryQueryCMD extends BaseCommand<ReportContext<ReportFileQueryReq>> {

    @Autowired
    IReportFileHistoryService reportFileHistoryService;

    @Override
    public BaseResponse validateParam(ReportContext<ReportFileQueryReq> context) {
        ReportFileQueryReq reportFileQueryReq = context.getParam();
        Assert.isTrue(Func.isNotEmpty(reportFileQueryReq),"common.param.miss",new Object[]{"reportId"});
        Assert.isTrue(Func.isNotEmpty(reportFileQueryReq.getReportIdList()),"common.param.miss",new Object[]{"reportId"});
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(ReportContext<ReportFileQueryReq> context) {
        ReportFileQueryReq reportFileQueryReq = context.getParam();
        List<ReportFileHistoryPO> reportFileHistoryPOList = reportFileHistoryService.query(reportFileQueryReq);
        context.setReportFileHistoryPOList(reportFileHistoryPOList);
        return super.before(context);
    }

    @Override
    public BaseResponse execute(ReportContext<ReportFileQueryReq> context) {
        List<ReportFileHistoryRsp> result = new ArrayList<>();
        List<ReportFileHistoryPO> reportFileHistoryPOList = context.getReportFileHistoryPOList();
        if(Func.isEmpty(reportFileHistoryPOList)){
            return BaseResponse.newSuccessInstance(result);
        }
        reportFileHistoryPOList = reportFileHistoryPOList.stream().filter(e -> Func.isNotEmpty(e.getCloudId())).collect(Collectors.toList());
        reportFileHistoryPOList.stream().forEach(reportFileHistoryPO -> {
            //过滤word，截取cloudId = word的
            String cloudId = reportFileHistoryPO.getCloudId();
            int lastIndex = cloudId.lastIndexOf(StringPool.DOT);
            if (lastIndex != -1){
                String fileType = cloudId.substring(lastIndex + 1);
                if(Func.equalsSafe(fileType, Constants.FILE_TYPE.WORD)){
                    ReportFileHistoryRsp reportFileHistoryRsp = new ReportFileHistoryRsp();
                    Func.copy(reportFileHistoryPO,reportFileHistoryRsp);
                    reportFileHistoryRsp.setFileName(reportFileHistoryRsp.getReportNo() + StringPool.DOT + fileType);
                    result.add(reportFileHistoryRsp);
                }
            }
        });
        return BaseResponse.newSuccessInstance(result);
    }
}
