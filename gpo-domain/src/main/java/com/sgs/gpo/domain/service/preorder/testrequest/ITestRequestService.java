package com.sgs.gpo.domain.service.preorder.testrequest;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.testrequest.TestRequestPO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;

import java.util.List;

public interface ITestRequestService extends IService<TestRequestPO> {

    BaseResponse<List<TestRequestPO>> select(OrderIdReq orderIdReq);

}
