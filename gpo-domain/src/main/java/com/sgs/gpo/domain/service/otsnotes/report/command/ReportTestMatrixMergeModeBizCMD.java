package com.sgs.gpo.domain.service.otsnotes.report.command;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.facade.domain.req.BuParamReq;
import com.sgs.framework.facade.domain.rsp.BuParamValueRsp;
import com.sgs.framework.model.enums.ReportTestMatrixMergeMode;
import com.sgs.framework.model.enums.TestExecutionType;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.test.testline.TestLineExecutionBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.domain.service.otsnotes.report.context.ReportContext;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportMatrixRelService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.otsnotes.subcontract.ISubContractDomainService;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineDomainService;
import com.sgs.gpo.facade.model.otsnotes.report.req.ReportTestMatrixModelGetReq;
import com.sgs.gpo.facade.model.otsnotes.testline.dto.TestLineDTO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.report.bo.ReportMatrixRelBO;
import com.sgs.gpo.facade.model.report.req.ReportMatrixQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.gpo.integration.framework.FrameworkClient;
import com.sgs.preorder.facade.model.subcontract.SubContractModeInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ReportTestMatrixMergeModeBizCMD extends BaseCommand<ReportContext<ReportTestMatrixModelGetReq>> {
    @Autowired
    private IReportMatrixRelService reportMatrixRelService;
    @Autowired
    private ITestLineDomainService testLineDomainService;
    @Autowired
    private IReportService reportService;
    @Autowired
    private FrameworkClient frameworkClient;
    @Autowired
    private ISubContractDomainService subContractDomainService;

    @Override
    public BaseResponse validateParam(ReportContext<ReportTestMatrixModelGetReq> context) {
        Assert.isTrue(Func.isNotEmpty(context.getParam()), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isAnyNotEmpty(context.getParam().getReportId(), context.getParam().getReportNo()), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(ReportContext<ReportTestMatrixModelGetReq> context) {
        /**
         * 1.1、ReportMatrix 都是Job：Host
         * 1.2、ReportMatrix 含Subcontract+Job：取Light;
         *         SubcontractMode =  Host，Host
         *         SubcontractMode =  Light，Light
         *         SubcontractMode = Execute，Light
         * 1.3、ReportMatrix 都是Subcontract：取SubcontractMode ，SODA By Customer;
         * 1.4、SubReport注册Report（PDF）：设置为Execution，如果PDF时手工上传：NA;
         */
        ReportTestMatrixMergeMode testMatrixMergeMode = ReportTestMatrixMergeMode.Host;
        List<ReportPO> reportPOList = context.getReportPOList();
        List<ReportMatrixRelBO> reportMatrixRelBOList = context.getReportMatrixRelBOList();
        List<TestLineBO> testLineBOList = context.getTestLineBOList();
        if (Func.isEmpty(testLineBOList)) {
            testLineBOList = new ArrayList<>();
        }
        ReportPO reportPO = null;
        if (Func.isNotEmpty(reportPOList)) {
            reportPO = reportPOList.get(0);
        }
        List<SubContractModeInfo> subContractModeInfoList = context.getSubContractModeInfoList();
        reportMatrixRelBOList = reportMatrixRelBOList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
                () -> new TreeSet<>(Comparator.comparing(o -> o.getReportId() + o.getTestLineInstanceId()))), ArrayList::new));
        if (Func.isNotEmpty(testLineBOList)) {
            //判断ReportMatrix关联的TL有几种ExecutionType
            List<List<TestLineExecutionBO>> collect = testLineBOList.parallelStream().filter(item -> Func.isNotEmpty(item.getTestExecutionList())).map(TestLineBO::getTestExecutionList).collect(Collectors.toList());
            List<TestLineExecutionBO> testExecutionList = testLineBOList.stream().filter(job -> Func.isNotEmpty(job.getTestExecutionList())).flatMap(tl -> tl.getTestExecutionList().stream()).collect(Collectors.toList());
            if (Func.isNotEmpty(testExecutionList)) {
                List<Integer> testExecutionTypeList = testExecutionList.stream().map(TestLineExecutionBO::getTestExecutionType).filter(Func::isNotEmpty).distinct().collect(Collectors.toList());
                // 只有一种ExecutionType
                if (testExecutionTypeList.size() == 1) {
                    Integer testExecutionType = testExecutionTypeList.get(0);
                    //Job
                    if (TestExecutionType.check(testExecutionType, TestExecutionType.JOB)) {
                        testMatrixMergeMode = ReportTestMatrixMergeMode.Host;
                    } else if (TestExecutionType.check(testExecutionType, TestExecutionType.SUBCONTRACT)) {
                        //SubContract
                        // TODO 取SubContractMode
                        testMatrixMergeMode = ReportTestMatrixMergeMode.Light;
                    }
                } else if (testExecutionTypeList.size() > 1) {
                    testMatrixMergeMode = ReportTestMatrixMergeMode.Light;
                }
            }
        }

        return BaseResponse.newSuccessInstance(testMatrixMergeMode.getCode());
    }

    @Override
    public BaseResponse before(ReportContext<ReportTestMatrixModelGetReq> context) {
        ReportTestMatrixModelGetReq reportTestMatrixModelGetReq = context.getParam();
        ReportQueryReq reportQueryReq = new ReportQueryReq();
        if (Func.isNotEmpty(reportTestMatrixModelGetReq.getReportId())) {
            reportQueryReq.setReportIdList(Sets.newHashSet(reportTestMatrixModelGetReq.getReportId()));
        }
        if (Func.isNotEmpty(reportTestMatrixModelGetReq.getReportNo())) {
            reportQueryReq.setReportNoList(Sets.newHashSet(reportTestMatrixModelGetReq.getReportNo()));
        }
        List<ReportPO> reportPOList = reportService.select(reportQueryReq).getData();
        context.setReportPOList(reportPOList);
        ReportMatrixQueryReq reportMatrixQueryReq = new ReportMatrixQueryReq();
        if (Func.isNotEmpty(reportTestMatrixModelGetReq.getReportId())) {
            reportMatrixQueryReq.setReportIdList(Sets.newHashSet(reportTestMatrixModelGetReq.getReportId()));
        }
        if (Func.isNotEmpty(reportTestMatrixModelGetReq.getReportNo())) {
            reportMatrixQueryReq.setReportNoList(Sets.newHashSet(reportTestMatrixModelGetReq.getReportNo()));
        }
        List<ReportMatrixRelBO> reportMatrixRelBOList = reportMatrixRelService.queryReportMatrix(reportMatrixQueryReq).getData();
        context.setReportMatrixRelBOList(reportMatrixRelBOList);
        if (Func.isNotEmpty(reportMatrixRelBOList)) {
            Set<String> testLineInstanceIdList = reportMatrixRelBOList.parallelStream().map(ReportMatrixRelBO::getTestLineInstanceId).filter(Func::isNotEmpty).collect(Collectors.toSet());
            if (Func.isNotEmpty(testLineInstanceIdList)) {
                OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
                orderTestLineReq.setTestLineInstanceIdList(testLineInstanceIdList);
                orderTestLineReq.setProductLineCode(reportTestMatrixModelGetReq.getProductLineCode());
                List<TestLineBO> testLineBOList = testLineDomainService.queryTestLine(orderTestLineReq).getData();
                context.setTestLineBOList(testLineBOList);
            }
        }
        BuParamReq buParamReq = new BuParamReq();
        buParamReq.setGroupCode(Constants.BU_PARAM.SUBCONTRACT.GROUP);
        buParamReq.setParamCode(Constants.BU_PARAM.SUBCONTRACT.SUBCONTRACT_MODE.CODE);
        buParamReq.setLocationCode(SystemContextHolder.getLabCode());
        buParamReq.setProductLineCode(SystemContextHolder.getBuCode());
        BuParamValueRsp buParamValueRsp = frameworkClient.getBuParam(buParamReq);
        if (Func.isNotEmpty(buParamValueRsp) && Func.isNotEmpty(buParamValueRsp.getParamValue())) {
            List<SubContractModeInfo> subContractModeInfoList = JSON.parseArray(buParamValueRsp.getParamValue(), SubContractModeInfo.class);
            context.setSubContractModeInfoList(subContractModeInfoList);
        }
        return super.before(context);
    }
}
