package com.sgs.gpo.domain.service.otsnotes.report.command;



import com.alibaba.fastjson.JSONObject;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.PageBO;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.domain.service.otsnotes.report.context.ReportAutoDeliverContext;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportAutoDeliverService;
import com.sgs.gpo.facade.model.report.bo.ReportAutoDeliverBO;
import com.sgs.gpo.facade.model.report.req.ReportAutoDeliverQueryReq;
import com.sgs.otsnotes.facade.model.dto.CustomerNameDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class ReportAutoDeliverPageCMD extends BaseCommand<ReportAutoDeliverContext<ReportAutoDeliverQueryReq>> {
    @Autowired
    private IReportAutoDeliverService reportAutoDeliverService;

    @Override
    public BaseResponse validateParam(ReportAutoDeliverContext<ReportAutoDeliverQueryReq> context) {
        Assert.isTrue(Func.isNotEmpty(context.getParam()),"common.param.miss",new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(context.getLab()),"common.param.miss",new Object[]{Constants.TERM.LAB.getCode()});
        if(Func.isEmpty(context.getPage())){
            context.setPage(Constants.DEFAULT.PAGE);
        }
        if(Func.isEmpty(context.getRows())){
            context.setRows(Constants.DEFAULT.ROWS);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(ReportAutoDeliverContext<ReportAutoDeliverQueryReq> context) {
        ReportAutoDeliverQueryReq queryReq = context.getParam();
        queryReq.setLabId(context.getLab().getLabId());
        BaseResponse<PageBO<ReportAutoDeliverBO>> pageBORes = reportAutoDeliverService.page(queryReq, context.getPage(),context.getRows());
        if (pageBORes.isFail()){
            return BaseResponse.newFailInstance(pageBORes.getMessage());
        }
        context.setReportAutoDeliverBOPage(pageBORes.getData());
        context.setReportAutoDeliverBOList(pageBORes.getData().getRecords());
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(ReportAutoDeliverContext<ReportAutoDeliverQueryReq> context) {
        List<ReportAutoDeliverBO> reportAutoDeliverBOList = context.getReportAutoDeliverBOList();
        if (Func.isNotEmpty(reportAutoDeliverBOList)){
            reportAutoDeliverBOList.forEach(item->{
                // 解析CustomerName
                if(Func.isNotEmpty(item.getApplicantName())){
                    CustomerNameDTO customerNameDTO = this.resolveCustomerName(item.getApplicantName());
                    if(Func.isNotEmpty(customerNameDTO)){
                        item.setApplicantNameCN(customerNameDTO.getCn());
                        item.setApplicantNameEN(customerNameDTO.getEn());
                    }
                }
                if(Func.isNotEmpty(item.getBuyerName())){
                    CustomerNameDTO customerNameDTO = this.resolveCustomerName(item.getBuyerName());
                    if(Func.isNotEmpty(customerNameDTO)){
                        item.setBuyerNameCN(customerNameDTO.getCn());
                        item.setBuyerNameEN(customerNameDTO.getEn());
                    }
                }
            });
        }
        PageBO<ReportAutoDeliverBO> page = context.getReportAutoDeliverBOPage();
        page.setRecords(reportAutoDeliverBOList);
        return BaseResponse.newSuccessInstance(page);
    }

    private CustomerNameDTO resolveCustomerName(String customerName){
        if(Func.isEmpty(customerName)){
            return null;
        }
        List<CustomerNameDTO> customerNameDTOList = JSONObject.parseArray(customerName,CustomerNameDTO.class);
        if(Func.isEmpty(customerNameDTOList)){
            return null;
        }
        return customerNameDTOList.get(0);
    }
}
