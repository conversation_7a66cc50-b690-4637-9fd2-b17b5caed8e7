package com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testsample.TestSampleGroupMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.TestSampleGroupPO;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.ITestSampleGroupService;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class TestSampleGroupServiceImpl extends ServiceImpl<TestSampleGroupMapper, TestSampleGroupPO> implements ITestSampleGroupService {


    @Override
    public BaseResponse<List<TestSampleGroupPO>> query(TestSampleQueryReq testSampleQueryReq) {
        if(Func.isEmpty(testSampleQueryReq)|| Func.isEmpty(testSampleQueryReq.getTestSampleInstanceIdList())){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"SampleId"});
        }
        LambdaQueryWrapper<TestSampleGroupPO> wrapper= Wrappers.lambdaQuery();
        if(Func.isNotEmpty(testSampleQueryReq.getTestSampleInstanceIdList())){
            wrapper.in(TestSampleGroupPO::getSampleId,testSampleQueryReq.getTestSampleInstanceIdList());
        }
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }
}
