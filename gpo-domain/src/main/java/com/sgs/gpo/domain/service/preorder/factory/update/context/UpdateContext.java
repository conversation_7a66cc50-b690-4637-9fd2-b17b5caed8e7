package com.sgs.gpo.domain.service.preorder.factory.update.context;

import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.order.enquiry.EnquiryBO;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.enums.TrfOperationEnums;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.customer.CustomerPO;
import com.sgs.gpo.integration.framework.rsp.trf.TrfUpdateConfigRsp;
import com.sgs.preorder.facade.model.dto.dff.DffFormAttrDTO;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class UpdateContext<Input> extends BaseContext<Input,Boolean> {
    private List<String> sectionList;
    private OrderBO orderBO;
    private EnquiryBO enquiryBO;
    private List<CustomerPO> orderCustomerList;
    private List<ReportPO> reportPOList;
    private String objectType;
    private List<DffFormAttrDTO> dffFormAttrDTOList;
    private List<String> mailToList = new ArrayList<>();
    private Map<String, Object> emailTemplateVariables = new HashMap<>();
    private TrfUpdateConfigRsp trfUpdateConfigRsp;
    private String trfVersion;
    private TrfOperationEnums trfOperation;
    // 添加邮件地址到 mailToList
    public void addMailTo(String email) {
        if(Func.isEmpty(email) || mailToList.contains(email)){
            return;
        }
        mailToList.add(email);
    }
}
