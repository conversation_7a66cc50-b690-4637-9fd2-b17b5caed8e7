package com.sgs.gpo.domain.service.otsnotes.subcontract.context;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.order.order.OrderBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractRequirementPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subreport.SubReportPO;
import com.sgs.gpo.facade.model.otsnotes.subcontract.vo.SubContractPageVO;
import com.sgs.gpo.facade.model.searchvalid.rsp.SearchValidRsp;
import com.sgs.priceengine.facade.model.DTO.TLAmountDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/3 09:02
 */
@Data
public class SubContractPageContext<Input> extends BaseContext<Input, SubContractPageVO> {
    private IPage<SubContractPageVO> subContractPage;
    private List<SubcontractRequirementPO> subcontractRequirementPOList;
    private SearchValidRsp searchValidRsp;
    private List<SubReportPO> subReportPOList;
    private List<OrderBO> orderBOList;
    private List<TLAmountDTO> tlAmountDTOList;
    private List<TestLineBO> subContractTestLineList;
}
