package com.sgs.gpo.domain.service.otsnotes.subcontract.command;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.model.enums.SubReportStatusEnum;
import com.sgs.framework.model.enums.TestExecutionType;
import com.sgs.framework.model.order.order.OrderBO;
import com.sgs.framework.model.order.order.OrderPaymentBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.objectsamplequantity.ObjectSampleQuantityPO;
import com.sgs.gpo.domain.service.otsnotes.objectsamplequantity.IObjectSampleQuantityService;
import com.sgs.gpo.domain.service.otsnotes.subcontract.context.SubContractPageContext;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineDomainService;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.domain.service.common.searchvalid.ISearchValidService;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractRequirementPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subreport.SubReportPO;
import com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.ISubcontractRequirementService;
import com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.ISubcontractService;
import com.sgs.gpo.domain.service.otsnotes.subreport.ISubReportService;
import com.sgs.gpo.facade.model.job.rsp.JobListRsp;
import com.sgs.gpo.facade.model.otsnotes.objectsamplequantity.req.ObjectSampleQuantityQueryReq;
import com.sgs.gpo.facade.model.otsnotes.subcontract.req.SubContractPageReq;
import com.sgs.gpo.facade.model.otsnotes.subcontract.vo.SubContractPageVO;
import com.sgs.gpo.facade.model.otsnotes.subreport.req.SubReportQueryReq;
import com.sgs.gpo.facade.model.otsnotes.subreport.vo.SubReportVO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.searchvalid.req.SearchValidReq;
import com.sgs.gpo.facade.model.searchvalid.rsp.SearchValidRsp;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractRequirementQueryReq;
import com.sgs.gpo.integration.framework.rsp.UserLabRsp;
import com.sgs.gpo.integration.usermanagement.UserManagementClient;
import com.sgs.otsnotes.facade.model.enums.ReportFileType;
import com.sgs.otsnotes.facade.model.enums.ReportRequirementEnum;
import com.sgs.priceengine.facade.QuotationFacade;
import com.sgs.priceengine.facade.model.DTO.TLAmountDTO;
import com.sgs.priceengine.facade.model.request.QueryTLAmountRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: SubContractPageQueryCMD
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/8 13:04
 */
@Service
@Slf4j
@Scope(value = "prototype")
public class SubContractPageQueryCMD extends BaseCommand<SubContractPageContext<SubContractPageReq>> {
    @Autowired
    private ISubcontractService subcontractService;
    @Autowired
    private ISubcontractRequirementService subcontractRequirementService;
    @Autowired
    private ISubReportService subReportService;
    @Autowired
    private ISearchValidService searchValidService;
    @Autowired
    private IOrderDomainService orderDomainService;
    @Autowired
    private QuotationFacade quotationFacade;
    @Autowired
    private ITestLineDomainService testLineDomainService;
    @Autowired
    private IObjectSampleQuantityService objectSampleQuantityService;
    @Autowired
    private UserManagementClient userManagementClient;

    @Override
    public BaseResponse validateParam(SubContractPageContext<SubContractPageReq> context) {
        if (Func.isEmpty(context) || Func.isEmpty(context.getParam())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        SubContractPageReq subContractPageReq = context.getParam();
        if(Func.isEmpty(context.getPage()) || Func.isEmpty(context.getRows())){
            return BaseResponse.newFailInstance("common.miss", new Object[]{"page or rows"});
        }
        UserInfo userInfo = context.getUserInfo();
        if(Func.isEmpty(context.getParam().getLabCode()) && Func.isNotEmpty(userInfo) && Func.isNotEmpty(userInfo.getCurrentLabCode())){
            String currentLabCode = userInfo.getCurrentLabCode();
            subContractPageReq.setLabCode(currentLabCode);
        }
        if (Func.isNotEmpty(SystemContextHolder.getLab())) {
            subContractPageReq.setLabId(SystemContextHolder.getLab().getLabId());
            subContractPageReq.setProductLineCode(SystemContextHolder.getBuCode());
        }
        if(Func.isEmpty(subContractPageReq.getLabCode())){
            return BaseResponse.newFailInstance("common.miss", new Object[]{"labCode"});
        }
        SearchValidReq searchValidReq = new SearchValidReq();
        searchValidReq.setObjectCode(Constants.OBJECT_ATTRIBUTE.SUBCONTRACT_LIST.OBJECT_CODE);
        searchValidReq.setSearchCode(Constants.OBJECT_ATTRIBUTE.SUBCONTRACT_LIST.OBJECT_CODE_SEARCH);
        searchValidReq.setRequestReqObj(context.getParam());
        searchValidReq.setProductLineCode(SystemContextHolder.getLab().getProductLineCode());
        BaseResponse<SearchValidRsp> validSearchRuleResponse = searchValidService.validSearchRule(searchValidReq);
        if(Func.isNotEmpty(validSearchRuleResponse)){
            SearchValidRsp searchValidRsp = validSearchRuleResponse.getData();
            context.setSearchValidRsp(searchValidRsp);
        }
        if (Func.isNotEmpty(subContractPageReq.getBatchOrderNo())) {
            List<String> orderNoList = Func.toStrList("\n",subContractPageReq.getBatchOrderNo()).stream().filter(Func::isNotEmpty).map(e -> e.trim()).distinct().collect(Collectors.toList());
            subContractPageReq.setOrderNoList(orderNoList);
            if(Func.isNotEmpty(orderNoList) && orderNoList.size() > context.getRows()){
                context.setRows(Constants.QUERY_MAX_SIZE);
            }
        }
        if (Func.isNotEmpty(subContractPageReq.getBatchSubcontractNo())) {
            List<String> subcontractNoList = Func.toStrList("\n",subContractPageReq.getBatchSubcontractNo()).stream().filter(Func::isNotEmpty).distinct().collect(Collectors.toList());
            subContractPageReq.setSubcontractNoList(subcontractNoList);
        }
        if(Func.isNotEmpty(subContractPageReq.getSubcontractStatusList())
                || Func.isNotEmpty(subContractPageReq.getOrderNo())
                || Func.isNotEmpty(subContractPageReq.getOrderNoList())
                || Func.isNotEmpty(subContractPageReq.getBatchOrderNo())
                || Func.isNotEmpty(subContractPageReq.getSubcontractNo())
                || Func.isNotEmpty(subContractPageReq.getSubcontractNoList())
                || Func.isNotEmpty(subContractPageReq.getBatchSubcontractNo())){
            subContractPageReq.setIgnoreCancelled(false);
        }
        context.setParam(subContractPageReq);
        return validSearchRuleResponse;
    }

    @Override
    public BaseResponse buildDomain(SubContractPageContext<SubContractPageReq> context) {
        IPage<SubContractPageVO> subContractPage = context.getSubContractPage();
        List<OrderBO> orderBOList = context.getOrderBOList();
        SubContractPageReq subContractPageReq = context.getParam();
        DecimalFormat formatter = new DecimalFormat("###,##0.00");
        if(Func.isNotEmpty(subContractPage) && Func.isNotEmpty(subContractPage.getRecords())){
            List<SubContractPageVO> records = subContractPage.getRecords();
            Set<String> subcontractNoSet = records.stream().map(e -> e.getSubContractNo()).collect(Collectors.toSet());
            ObjectSampleQuantityQueryReq objectSampleQuantityQueryReq = new ObjectSampleQuantityQueryReq();
            objectSampleQuantityQueryReq.setObjectNoList(subcontractNoSet);
            List<ObjectSampleQuantityPO> objectSampleQuantityPOList = objectSampleQuantityService.select(objectSampleQuantityQueryReq);

            for (SubContractPageVO record : records) {
                record.setExternalOrderNo(record.getOrderNo());
                if(Func.isNotEmpty(record.getSubContractFee())){
                    BigDecimal subContractFee = record.getSubContractFee();
                    record.setFormatSubContractFee(Func.toStr(Func.toStr(record.getSubContractFeeCurrency()) + " " +formatter.format(subContractFee)));
                }
                if(Func.isNotEmpty(orderBOList)){
                    OrderBO orderBO = orderBOList.stream().filter(item -> Func.isNotEmpty(item.getHeader()) && Func.equalsSafe(item.getHeader().getOrderNo(), record.getOrderNo())).findAny().orElse(null);
                    OrderBO referenceOrder = orderBOList.stream().filter(item -> Func.isNotEmpty(item.getHeader()) && Func.equalsSafe(item.getHeader().getOrderNo(), record.getReferenceNo())).findAny().orElse(null);
                    if(Func.isNotEmpty(orderBO)){
                        if(Func.isNotEmpty(orderBO.getHeader())){
                            if(Func.isNotEmpty(orderBO.getHeader().getExternalOrderNo())){
                                String externalOrderNo = orderBO.getHeader().getExternalOrderNo();
                                record.setExternalOrderNo(externalOrderNo);
                            }
                            String orderId = orderBO.getHeader().getOrderId();
                            record.setOrderId(orderId);
                            record.setResponsibleCs(orderBO.getHeader().getCsName());
                        }
                        if(Func.isNotEmpty(orderBO.getServiceRequirement()) && Func.isNotEmpty(orderBO.getServiceRequirement().getReport())){
                            record.setReportLanguage(orderBO.getServiceRequirement().getReport().getReportLanguage());
                        }
                    }
                    if(Func.isNotEmpty(referenceOrder) && Func.isNotEmpty(referenceOrder.getPayment())){
                        OrderPaymentBO payment = referenceOrder.getPayment();
                        record.setReferenceAmount(payment.getTotalAmount());
                        record.setReferenceCurrency(payment.getCurrency());
                        if(Func.isNotEmpty(payment.getTotalAmount()) && Func.isNotEmpty(payment.getCurrency())){
                            record.setFormatReferenceAmount(formatter.format(payment.getTotalAmount())+ " " +Func.toStr(Func.toStr(payment.getCurrency())));
                        }
                    }
                }
                assembleSubReport(record,context);
                if(Func.isNotEmpty(subContractPageReq.getDetailFlag()) && subContractPageReq.getDetailFlag()){
                    assembleSubcontractTLAmount(record,context);
                }
                if(Func.isNotEmpty(objectSampleQuantityPOList)){
                    List<ObjectSampleQuantityPO> currentSampleQuantityList = objectSampleQuantityPOList.stream().filter(e -> Func.equalsSafe(e.getObjectNo(),record.getSubContractNo())).collect(Collectors.toList());
                    if(Func.isNotEmpty(currentSampleQuantityList)) {
                        record.setStylesQty(summaryStylesQty(currentSampleQuantityList));
                        record.setPiecesQty(summaryPiecesQty(currentSampleQuantityList));
                        record.setPartsQty(summaryPartsQty(currentSampleQuantityList));
                    }
                }
            }
            subContractPage.setRecords(records);
        }
        return super.buildDomain(context);
    }


    private void assembleSubcontractTLAmount(SubContractPageVO subContractPageVO, SubContractPageContext<SubContractPageReq> context) {
        List<TLAmountDTO> tlAmountDTOList = context.getTlAmountDTOList();
        List<TestLineBO> allSubContractTestLineList = context.getSubContractTestLineList();
        if (Func.isEmpty(tlAmountDTOList) || Func.isEmpty(allSubContractTestLineList)) {
            return;
        }
        BigDecimal tlAndStandardAmount = BigDecimal.ZERO;
        //sub下的所有tl必须都有金额
        boolean isAmount = true;
        List<TestLineBO>  subContractTestLineList = allSubContractTestLineList.stream().filter(item->{
            return Func.isNotEmpty(item.getTestExecutionList()) && item.getTestExecutionList().stream().filter(subcontract-> TestExecutionType.check(subcontract.getTestExecutionType(),TestExecutionType.SUBCONTRACT) && StringUtils.equalsIgnoreCase(subcontract.getTestExecutionNo(),subContractPageVO.getSubContractNo())).count()>0;
        }).collect(Collectors.toList());
        if (Func.isEmpty(subContractTestLineList)) {
            return;
        }
        for (TestLineBO testLineBO : subContractTestLineList) {
            TLAmountDTO objTLAmountDTO = tlAmountDTOList.stream().filter(e -> {
                        return Func.isNotEmpty(testLineBO.getPpTestLineRel()) &&  Func.equals(e.getPpTlRelId(),testLineBO.getPpTestLineRel().getPpTlRelId());
                    }
            ).findFirst().orElse(null);

            if (objTLAmountDTO == null || objTLAmountDTO.getAmount() == null) {
                isAmount = false;
            } else {
                tlAndStandardAmount = tlAndStandardAmount.add(objTLAmountDTO.getAmount());
            }
        }

        DecimalFormat df = new DecimalFormat(",###,###.00");
        subContractPageVO.setTlTotalAmount(tlAndStandardAmount);
        String amountText=tlAndStandardAmount==null||tlAndStandardAmount.compareTo(BigDecimal.ZERO)==0?"0.00":df.format(tlAndStandardAmount);
        subContractPageVO.setTlTotalAmountDisplay(isAmount ? tlAmountDTOList.get(0).getCurrencyCodeDisplay() + amountText : "Unable to calculate");
    }

    private void assembleSubReport(SubContractPageVO subContractPageVO, SubContractPageContext<SubContractPageReq> context) {
        subContractPageVO.setSubReportList(new ArrayList<>());
        List<SubReportPO> subReportPOList = context.getSubReportPOList();
        List<SubcontractRequirementPO> subcontractRequirementPOList = context.getSubcontractRequirementPOList();

        if(Func.isEmpty(subReportPOList) || Func.isEmpty(subcontractRequirementPOList)){
            return;
        }
        subReportPOList = subReportPOList.stream().filter(item -> Func.equalsSafe(item.getSubcontractId(), subContractPageVO.getId())).collect(Collectors.toList());
        SubcontractRequirementPO subcontractRequirementPO = subcontractRequirementPOList.stream().filter(item -> Func.equalsSafe(item.getSubContractId(), subContractPageVO.getId())).findAny().orElse(null);
        if(Func.isEmpty(subReportPOList) || Func.isEmpty(subcontractRequirementPO)){
            return;
        }

        List<SubReportVO> subReportVOList  = new ArrayList<>();

        String reportRequirement = subcontractRequirementPO.getReportRequirement();
        Integer draftReportRequired = subcontractRequirementPO.getDraftReportRequired();
        List<SubReportPO> resultList = new ArrayList<>();
        Map<String, List<SubReportPO>> subReportMap = subReportPOList.stream().collect(Collectors.groupingBy(b -> b.getSubReportNo()));
        for (Map.Entry<String, List<SubReportPO>> valueMap : subReportMap.entrySet()) {
            String key = valueMap.getKey();
            List<SubReportPO> matchSubReportList = subReportPOList.stream().filter(item->Func.equalsSafe(item.getSubReportNo(),key)).collect(Collectors.toList());
            List<SubReportPO> subDraftReportListRsps;
            List<SubReportPO> subFinalReportListRsps;
            subDraftReportListRsps = matchSubReportList.stream().filter(reportFilePO -> isDraftReportFile(reportRequirement,Func.toStr(reportFilePO.getReportFileType())) && StringUtils.isNotEmpty(reportFilePO.getCloudId()))
                    .collect(Collectors.toList());
            if(draftReportRequired != 1){
                subDraftReportListRsps = subDraftReportListRsps.stream().filter(i->!SubReportStatusEnum.check(i.getStatus(),SubReportStatusEnum.Approved)).collect(Collectors.toList());
            }
            subFinalReportListRsps = matchSubReportList.stream().filter(reportFilePO -> isFinalReportFile(reportRequirement,Func.toStr(reportFilePO.getReportFileType())) && StringUtils.isNotEmpty(reportFilePO.getCloudId()))
                    .collect(Collectors.toList());
            if(draftReportRequired != 1){
                subFinalReportListRsps = subFinalReportListRsps.stream().filter(i->!SubReportStatusEnum.check(i.getStatus(),SubReportStatusEnum.Approved)).collect(Collectors.toList());
            }
            if(Func.isNotEmpty(subFinalReportListRsps)){
                resultList.addAll(subFinalReportListRsps);
            }else if(Func.isNotEmpty(subDraftReportListRsps)){
                resultList.addAll(subDraftReportListRsps);
            }
        }
        // 2022-02-31 修改SubReport 过滤掉Reworked和Cancelled状态数据
        List<SubReportPO> subReportListRsps =  resultList.stream().filter(subReportListRsp ->
                !SubReportStatusEnum.check(subReportListRsp.getStatus(), SubReportStatusEnum.Reworked,SubReportStatusEnum.Cancelled)
        ).collect(Collectors.toList());

        for (SubReportPO subReportPO : subReportListRsps) {
            SubReportVO subReportVO = new SubReportVO();
            Func.copy(subReportPO,subReportVO);
            subReportVOList.add(subReportVO);
        }
        subContractPageVO.setSubReportList(subReportVOList);
    }

    @Override
    public BaseResponse execute(SubContractPageContext<SubContractPageReq> context) {
        SubContractPageReq subContractPageReq = context.getParam();
        List<String> batchOrderNoList =subContractPageReq.getOrderNoList();
        BaseResponse buildResponse = this.buildDomain(context);
        if (buildResponse.isFail()) {
            return buildResponse;
        }
        BaseResponse<IPage<SubContractPageVO>> baseResponse = new BaseResponse<>();
        baseResponse.setData(context.getSubContractPage());
        if(Func.isNotEmpty(batchOrderNoList)){
            if(Func.isNotEmpty(context.getSubContractPage()) && Func.isNotEmpty(context.getSubContractPage().getRecords())){
                Set<String> resultOrderNoList = context.getSubContractPage().getRecords().stream().map(SubContractPageVO::getOrderNo).collect(Collectors.toSet());
                Set<String> noResultOrderNoList = batchOrderNoList.stream().filter(orderNo -> !resultOrderNoList.contains(orderNo)).collect(Collectors.toSet());
                if(Func.isNotEmpty(noResultOrderNoList)){
                    baseResponse.setMessage(noResultOrderNoList.toString() + "(orderNo) Subcontract not found!");
                }
            } else {
                baseResponse.setMessage(batchOrderNoList.toString() + "(orderNo) Subcontract not found!");
            }
        }
        return baseResponse;
    }

    @Override
    public BaseResponse before(SubContractPageContext<SubContractPageReq> context) {
        Page<SubContractPageVO> page = new Page<>(context.getPage(), context.getRows());
        page.setSearchCount(false);
        SubContractPageReq param = context.getParam();
        UserInfo userInfo = context.getUserInfo();
        if(Func.isNotEmpty(userInfo)){
            Map<String, List<String>> dimensionMap = userInfo.getDimensionMap();
            //有没有tops权限，没有值是有权限
            if(dimensionMap!=null){
                List<String> block_tops = dimensionMap.get("block_tops");
                if(block_tops!=null && block_tops.size()>0){
                    param.setBlockTops(block_tops.get(0));
                }
            }
        }
//        AF 接包方可查看分包单信息
        if(Func.isNotEmpty(param.getPageType())){
            if(Func.equals(param.getPageType(),"ToMyLabSubContractList")  //接包方查看分包单
            || Func.equals(param.getPageType(),"orderDetailHasToMyLab") //接包方查看订单详情的分包单
            ) {
                UserLabRsp userLabRsp = userManagementClient.getUserTeamLab(context.getToken());//GPOAF-112 按所属Lab查看分包单
                if (Func.isNotEmpty(userLabRsp) && Func.isNotEmpty(userLabRsp.getLabId())) {
                    param.setLabCode("");//接包方查询分包单时不限制分包方lab
                    param.setLabId(null);
                    List<String> subcontractLabCodeList = param.getSubcontractLabCodeList();
                    if (Func.isEmpty(subcontractLabCodeList) || subcontractLabCodeList.size() == 0) {
                        subcontractLabCodeList = new ArrayList<>();
                        subcontractLabCodeList.add(userLabRsp.getLabCode());
                        param.setSubcontractLabCodeList(subcontractLabCodeList);
                    }
                }else{
                    return BaseResponse.newFailInstance("search.result.empty.userTeamLab",null);
                }
            }
            if(Func.equals(param.getPageType(), "getStarLimsFolders")){
                //接包方查看starlims信息
                param.setLabCode("");
                param.setLabId(null);
            }
        }

        Long total = subcontractService.pageCount(param).getData();
        page.setTotal(total);
        // B1、查询分包单信息
        IPage<SubContractPageVO> subContractPageInfo = subcontractService.page(page, param).getData();
        context.setSubContractPage(subContractPageInfo);
        if(Func.isNotEmpty(subContractPageInfo) && Func.isNotEmpty(subContractPageInfo.getRecords())){
            List<SubContractPageVO> subContractPageVOList = subContractPageInfo.getRecords();
            Set<String> subContractIdList = subContractPageVOList.stream().map(SubContractPageVO::getId).distinct().filter(Func::isNotEmpty).collect(Collectors.toSet());
            SubcontractRequirementQueryReq subcontractRequirementQueryReq = new SubcontractRequirementQueryReq();
            subcontractRequirementQueryReq.setSubContractIds(subContractIdList);
            BaseResponse<List<SubcontractRequirementPO>> subcontractRequirementResponse = subcontractRequirementService.query(subcontractRequirementQueryReq);
            if(subcontractRequirementResponse.isSuccess()){
                context.setSubcontractRequirementPOList(subcontractRequirementResponse.getData());
            }
            SubReportQueryReq subReportQueryReq = new SubReportQueryReq();
            subReportQueryReq.setSubContractIdList(subContractIdList);
            // B2、查询分包单信息
            BaseResponse<List<SubReportPO>> subReportResponse = subReportService.query(subReportQueryReq);
            if(subReportResponse.isSuccess()){
                List<SubReportPO> subReportPOList = subReportResponse.getData();
                context.setSubReportPOList(subReportPOList);
            }
            Set<String> orderNoList = subContractPageVOList.stream().map(SubContractPageVO::getOrderNo).distinct().filter(Func::isNotEmpty).collect(Collectors.toSet());
            Set<String> referenceOrderNoList = subContractPageVOList.stream().map(SubContractPageVO::getReferenceNo).distinct().filter(Func::isNotEmpty).collect(Collectors.toSet());
            // B3、查询订单信息
            Set<String> allOrderNoList = new HashSet<>();
            allOrderNoList.addAll(orderNoList);
            allOrderNoList.addAll(referenceOrderNoList);
            OrderQueryReq orderQueryReq = new OrderQueryReq();
            orderQueryReq.setOrderNoList(allOrderNoList);
            BaseResponse<List<OrderBO>> orderListResponse = orderDomainService.queryV1(orderQueryReq);
            if(orderListResponse.isSuccess()){
                context.setOrderBOList(orderListResponse.getData());
                if(Func.isNotEmpty(context.getParam()) && Func.isNotEmpty(context.getParam().getDetailFlag()) && context.getParam().getDetailFlag() && Func.isNotEmpty(orderListResponse.getData())){
                    OrderBO orderBO = orderListResponse.getData().stream().filter(item -> Func.isNotEmpty(item.getHeader()) && Func.equalsSafe(item.getHeader().getOrderNo(), subContractPageVOList.get(0).getOrderNo())).findAny().orElse(null);
                    if(Func.isNotEmpty(orderBO)){
                        QueryTLAmountRequest objQueryTLAmountRequest = new QueryTLAmountRequest();
                        objQueryTLAmountRequest.setSystemId(15);
                        objQueryTLAmountRequest.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                        objQueryTLAmountRequest.setOrderIdList(Lists.newArrayList(orderBO.getHeader().getOrderId()));
                        BaseResponse<List<TLAmountDTO>> tlAmountResponse = quotationFacade.queryTLAmount(objQueryTLAmountRequest);
                        if(tlAmountResponse.isSuccess()) {
                            List<TLAmountDTO> tlAmountDTOList = tlAmountResponse.getData();
                            context.setTlAmountDTOList(tlAmountDTOList);
                        }
                        Set<String> subContractNoList = subContractPageVOList.stream().map(SubContractPageVO::getSubContractNo).collect(Collectors.toSet());
                        OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
                        orderTestLineReq.setOrderNoList(Sets.newHashSet(orderBO.getHeader().getOrderNo()));
                        orderTestLineReq.setSubcontractNoList(subContractNoList);
                        BaseResponse<List<TestLineBO>> testLineBaseResponse = testLineDomainService.queryPPTestLine(orderTestLineReq);
                        if(testLineBaseResponse.isSuccess()){
                            List<TestLineBO> testLineBOList = testLineBaseResponse.getData();
                            context.setSubContractTestLineList(testLineBOList);
                        }
                    }

                }
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse after(SubContractPageContext<SubContractPageReq> context) {
        SearchValidRsp searchValidRsp = context.getSearchValidRsp();
        IPage<SubContractPageVO> subContractPage = context.getSubContractPage();
        if(Func.isNotEmpty(searchValidRsp) && Func.isNotEmpty(searchValidRsp.getOnlyUseDateRange()) && searchValidRsp.getOnlyUseDateRange() && (Func.isEmpty(subContractPage) || Func.isEmpty(subContractPage.getRecords()))){
            return BaseResponse.newFailInstance("search.result.empty.useOther",null);
        }else{
            return BaseResponse.newSuccessInstance(true);
        }
    }

    private boolean isDraftReportFile(String reportRequirement, String reportFileType) {
        if (Func.equals(ReportRequirementEnum.Customer_Report_PDF.getCode(),reportRequirement) && Func.equalsSafe(reportFileType,Func.toStr(ReportFileType.DraftPDF.getCode()))){
            return true;
        }else if((Func.equals(ReportRequirementEnum.Customer_Report_Word.getCode(),reportRequirement) || Func.equals(ReportRequirementEnum.Sub_Report_Word.getCode(),reportRequirement)) && Func.equalsSafe(reportFileType,Func.toStr(ReportFileType.Word.getCode()))){
            return true;
        }else{
            return false;
        }
    }

    private boolean isFinalReportFile(String reportRequirement, String reportFileType) {
        if (Func.equals(ReportRequirementEnum.Customer_Report_PDF.getCode(),reportRequirement) && Func.equalsSafe(reportFileType,Func.toStr(ReportFileType.PDF.getCode()))){
            return true;
        }else if((Func.equals(ReportRequirementEnum.Customer_Report_Word.getCode(),reportRequirement) || Func.equals(ReportRequirementEnum.Sub_Report_Word.getCode(),reportRequirement)) && Func.equalsSafe(reportFileType,Func.toStr(ReportFileType.Word.getCode()))){
            return true;
        }else{
            return false;
        }
    }

    private Integer summaryStylesQty(List<ObjectSampleQuantityPO> objectSampleQuantityPOList) {
        if(Func.isEmpty(objectSampleQuantityPOList)){
            return 0;
        }
        return objectSampleQuantityPOList.stream().filter(e -> Func.isNotEmpty(e.getStylesQty())).mapToInt(e -> e.getStylesQty()).sum();
    }
    private Integer summaryPiecesQty(List<ObjectSampleQuantityPO> objectSampleQuantityPOList) {
        if(Func.isEmpty(objectSampleQuantityPOList)){
            return 0;
        }
        return objectSampleQuantityPOList.stream().filter(e -> Func.isNotEmpty(e.getPiecesQty())).mapToInt(e -> e.getPiecesQty()).sum();
    }
    private Integer summaryPartsQty(List<ObjectSampleQuantityPO> objectSampleQuantityPOList) {
        if (Func.isEmpty(objectSampleQuantityPOList)) {
            return 0;
        }
        return objectSampleQuantityPOList.stream().filter(e -> Func.isNotEmpty(e.getPartsQty())).mapToInt(e -> e.getPartsQty()).sum();
    }
}
