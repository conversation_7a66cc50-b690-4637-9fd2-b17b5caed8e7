package com.sgs.gpo.domain.service.otsnotes.report.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportCertificatePO;
import com.sgs.gpo.facade.model.otsnotes.report.req.ReportCertificateQueryReq;
import com.sgs.gpo.facade.model.otsnotes.report.req.ReportCertificateSaveReq;
import com.sgs.gpo.facade.model.otsnotes.report.req.ReportCertificateUpdateReq;
import com.sgs.gpo.facade.model.otsnotes.report.rsp.ReportCertificateRsp;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.otsnotes.facade.model.req.report.ReportIdReq;

import java.util.List;

public interface IReportCertificateService extends IService<ReportCertificatePO> {
    BaseResponse<List<ReportCertificatePO>> select(ReportQueryReq reportQueryReq);
    BaseResponse<List<ReportCertificateRsp>> reportCertificateList(ReportQueryReq reportQueryReq);
    BaseResponse<Boolean> reportCertificateSave(ReportCertificateSaveReq reportCertificateSaveReq);
    BaseResponse<Boolean> reportCertificateCancel(ReportCertificateUpdateReq reportCertificateUpdateReq);
    BaseResponse<ReportCertificateRsp> reportCertificateDetail(ReportCertificateQueryReq reportCertificateQueryReq);
}
