package com.sgs.gpo.domain.service.preorder.order.subdomain.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.BossOrderTaxInvoiceMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.BossOrderTaxInvoicePO;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IBossOrderTaxInvoiceService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BossOrderTaxInvoiceServiceImpl extends ServiceImpl<BossOrderTaxInvoiceMapper, BossOrderTaxInvoicePO> implements IBossOrderTaxInvoiceService {

    public int updateByInvoiceNoAndTaxBatch(List<BossOrderTaxInvoicePO> bossOrderTaxInvoiceInfoPOS){
        return baseMapper.updateByInvoiceNoAndTaxBatch(bossOrderTaxInvoiceInfoPOS);
    }

}
