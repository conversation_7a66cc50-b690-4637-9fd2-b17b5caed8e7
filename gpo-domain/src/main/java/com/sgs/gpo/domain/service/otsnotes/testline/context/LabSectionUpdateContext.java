package com.sgs.gpo.domain.service.otsnotes.testline.context;

import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.trims.labsection.LabSectionBO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testline.TestLineLabSectionRelationshipPO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.LabSectionUpdateReq;
import com.sgs.gpo.facade.model.otsnotes.testline.rsp.LabSectionItemRsp;
import com.sgs.gpo.facade.model.report.bo.ReportMatrixRelBO;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @title: LabSectionUpdateContext
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/10/17 10:27
 */
@Data
public class LabSectionUpdateContext extends BaseContext<LabSectionUpdateReq, LabSectionBO> {
    private List<TestLineBO> testLineList;
    // 请求参数设计的测试项实例ID
    private Set<String> testLineInstanceIdList;
    private List<ReportMatrixRelBO> reportMatrixRelBOList;
    // 用户信息
    private UserInfo userInfo;
    // DB 里面查询的所有数据
    private List<TestLineLabSectionRelationshipPO> dbTestLineLabSectionList;
    // 需要添加或修改的数据
    private List<TestLineLabSectionRelationshipPO> saveTestLineLabSectionList;
    // 需要删除的数据
    private Set<String> deleteIdList;
    // 返回结果
    private List<LabSectionItemRsp> labSectionItemRsps;
}
