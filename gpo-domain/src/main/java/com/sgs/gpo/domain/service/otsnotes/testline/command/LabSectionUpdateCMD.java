package com.sgs.gpo.domain.service.otsnotes.testline.command;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.model.enums.TestLineStatus;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.trims.labsection.LabSectionBO;
import com.sgs.framework.security.utils.SecurityUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testline.TestLineLabSectionRelationshipPO;
import com.sgs.gpo.domain.service.otsnotes.report.IReportDomainService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportMatrixRelService;
import com.sgs.gpo.domain.service.otsnotes.testline.context.LabSectionUpdateContext;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.ITestLineLabSectionService;
import com.sgs.gpo.domain.service.setting.buparam.IBUParam;
import com.sgs.gpo.facade.model.otsnotes.testline.req.LabSectionReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.LabSectionUpdateItemReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.LabSectionUpdateReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testline.rsp.LabSectionItemRsp;
import com.sgs.gpo.facade.model.report.bo.ReportMatrixRelBO;
import com.sgs.gpo.facade.model.report.req.ReportExtForTLUpdateReq;
import com.sgs.gpo.facade.model.report.req.ReportMatrixQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 更新测试项的LabSection
 * @date 2023/6/28 23:00
 */
@Service
@Slf4j
public class LabSectionUpdateCMD extends BaseCommand<LabSectionUpdateContext> {

    @Autowired
    private ITestLineLabSectionService testLineLabSectionService;
    @Autowired
    private IReportMatrixRelService reportMatrixRelService;
    @Autowired
    private IBUParam buParam;
    @Autowired
    private IReportDomainService reportDomainService;


    @Override
    public BaseResponse validateParam(LabSectionUpdateContext context) {
        // 校验入参不能为空
        if(Func.isEmpty(context)||Func.isEmpty(context.getParam())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        LabSectionUpdateReq labSectionUpdateReq = context.getParam();
        // 校验入参不能为空
        if(Func.isEmpty(labSectionUpdateReq.getTestLineLabSectionList())){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"Request Param"});
        }
        // 校验订单Id不能为空
        String orderId = labSectionUpdateReq.getOrderId();
        if(Func.isEmpty(orderId)){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"order id"});
        }
        // 校验选择测试项不能为空
        Set<String> testLineInstanceIdList = labSectionUpdateReq.getTestLineLabSectionList().stream().map(LabSectionUpdateItemReq::getTestLineInstanceId).collect(Collectors.toSet());
        if(Func.isEmpty(testLineInstanceIdList)){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"test line instance id"});
        }
        context.setTestLineInstanceIdList(testLineInstanceIdList);
        // 校验LabSection不能为空
        Boolean required = buParam.labSectionRequired(context.getProductLineCode(), null).getData();
        if(required){
            for(LabSectionUpdateItemReq labSectionUpdateItemReq : labSectionUpdateReq.getTestLineLabSectionList()) {
                if (Func.isEmpty(labSectionUpdateItemReq.getLabSectionList())) {
                    return BaseResponse.newFailInstance("common.select.required", new Object[]{"lab section"});
                }
            }
        }
        // 校验Testline都是有效数据
        if(Func.isEmpty(context.getTestLineList())){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"test line"});
        }
        BaseResponse validateResponse = this.validateDomain(context);
        if(validateResponse.isFail()){
            return validateResponse;
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse validateDomain(LabSectionUpdateContext context) {
        LabSectionUpdateReq labSectionUpdateReq = context.getParam();
        // 校验Testline未关联Job、Subcontract
        List<TestLineBO> hasExecutionNoTestLineList = context.getTestLineList().stream().filter(item->{
           return Func.isNotEmpty(item.getTestExecutionList());
        }).collect(Collectors.toList());
        //校验TL LabSection是否有变化，有变化才提示已经创建Job/Subcontract
        List<LabSectionUpdateItemReq> testLineLabSectionList = labSectionUpdateReq.getTestLineLabSectionList();
        BaseResponse<String> jobGroupModelRsp = buParam.createJobGroupModel(context.getProductLineCode());
        String jobGroupModel = Constants.BU_PARAM.JOB.CREATE_JOB_GROUP_BY.VALUE.LAB_SECTION;
        if(jobGroupModelRsp.isSuccess() && Func.isNotEmpty(jobGroupModelRsp.getData())){
            jobGroupModel = jobGroupModelRsp.getData();
        }
        List<LabSectionItemRsp> labSectionItemRsps = new ArrayList<>();
        if(Func.isNotEmpty(hasExecutionNoTestLineList)){
            String finalJobGroupModel = jobGroupModel;
            hasExecutionNoTestLineList.forEach(testLineBO -> {
                LabSectionUpdateItemReq labSectionUpdateItemReq = testLineLabSectionList.stream().filter(item -> Func.equalsSafe(item.getTestLineInstanceId(), testLineBO.getTestLineInstanceId())).findFirst().orElse(null);
                List<LabSectionReq> labSectionReqList = new ArrayList<>();
                if(Func.isNotEmpty(labSectionUpdateItemReq)){
                    labSectionReqList = labSectionUpdateItemReq.getLabSectionList();
                }
                List<LabSectionBO> labSectionList = testLineBO.getLabSectionList();
                if(Func.isEmpty(labSectionList)){
                    labSectionList = new ArrayList<>();
                }
                String reqLabSectionIds = labSectionReqList.stream().map(item -> Func.toStr(item.getLabSectionId())).sorted().collect(Collectors.joining(","));
                String tlLabSectionIds = labSectionList.stream().map(item -> Func.toStr(item.getLabSectionId())).sorted().collect(Collectors.joining(","));
                if(StringUtils.equalsIgnoreCase(finalJobGroupModel,Constants.BU_PARAM.JOB.CREATE_JOB_GROUP_BY.VALUE.LAB_SECTION) && !Func.equalsSafe(reqLabSectionIds,tlLabSectionIds)){
                    LabSectionItemRsp labSectionItemRsp = new LabSectionItemRsp();
                    labSectionItemRsp.setMessage(String.format("%s: 已经创建Job/Subcontract %s!",testLineBO.getTestLineId(),testLineBO.getTestExecutionNo()));
                    labSectionItemRsp.setTestLineInstanceId(testLineBO.getTestLineInstanceId());
                    labSectionItemRsps.add(labSectionItemRsp);
                }
            });
        }
        // 校验TestLine状态
        List<TestLineBO> invalidTestLineList = context.getTestLineList().stream().filter(item->{
            return !TestLineStatus.checkCategory(item.getTestLineStatus(),Constants.STATUS_CATEGORY.EDIT);
        }).collect(Collectors.toList());
        if(Func.isNotEmpty(invalidTestLineList)){
            invalidTestLineList.forEach(testLineBO -> {
                LabSectionItemRsp labSectionItemRsp = new LabSectionItemRsp();
                labSectionItemRsp.setMessage(String.format("test line status %s not allow!",TestLineStatus.findStatus(testLineBO.getTestLineStatus())));
                labSectionItemRsp.setTestLineInstanceId(testLineBO.getTestLineInstanceId());
                labSectionItemRsps.add(labSectionItemRsp);
            });
        }
        context.setLabSectionItemRsps(labSectionItemRsps);
        if(Func.isNotEmpty(labSectionItemRsps)){
            BaseResponse baseResponse = new BaseResponse();
            baseResponse.setStatus(ResponseCode.UNKNOWN.getCode());
            baseResponse.setMessage(ResponseCode.UNKNOWN.getMessage());
            baseResponse.setData(labSectionItemRsps);
            return baseResponse;
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(LabSectionUpdateContext context) {
        // 请求的ID列表
        Set<String> reqKeys = Sets.newHashSet();
        // DB ID列表
        Set<String> dbKeys = Sets.newHashSet();
        // Key 定义
        String keyTemplate = "%s-%s-%s";
        LabSectionUpdateReq labSectionUpdateReq = context.getParam();
        // 组装前台请求的Key列表
        labSectionUpdateReq.getTestLineLabSectionList().forEach(testLine->{
            testLine.getLabSectionList().forEach(labSection->{
                String key = String.format(keyTemplate,testLine.getTestLineInstanceId(),labSection.getLabSectionId(),labSection.getLabSectionBaseId());
                reqKeys.add(key);
            });
        });

        // 查询现在db的数据 & 组装DB的Key列表
        OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
        orderTestLineReq.setOrderIdList(Sets.newHashSet(labSectionUpdateReq.getOrderId()));
        orderTestLineReq.setTestLineInstanceIdList(context.getTestLineInstanceIdList());
        BaseResponse<List<TestLineLabSectionRelationshipPO>> baseResponse = testLineLabSectionService.queryByOrderTestLine(orderTestLineReq);
        if(baseResponse.isFail()){
            return baseResponse;
        }
        List<TestLineLabSectionRelationshipPO> dbTestLineLabSectionList = context.getDbTestLineLabSectionList();
        dbTestLineLabSectionList = baseResponse.getData();
        Set<String> deleteIdList = Sets.newHashSet();
        if(Func.isNotEmpty(dbTestLineLabSectionList)){
            // 组装需要删除的数据
            dbTestLineLabSectionList.forEach(item->{
                String key = String.format(keyTemplate,item.getTestLineInstanceId(),item.getLabSectionId(),item.getLabSectionBaseId());
                dbKeys.add(key);
                if(!reqKeys.contains(key)){
                    deleteIdList.add(item.getId());
                }
            });
        }

        // 组装需要保存的数据
        UserInfo userInfoFillSystem = SystemContextHolder.getUserInfoFillSystem();
        String userName = userInfoFillSystem.getRegionAccount();
        List<TestLineLabSectionRelationshipPO> saveTestLineLabSectionList = Lists.newArrayList();
        String finalUserName = userName;
        labSectionUpdateReq.getTestLineLabSectionList().forEach(testLine->{
            testLine.getLabSectionList().forEach(labSection->{
                String key = String.format(keyTemplate,testLine.getTestLineInstanceId(),labSection.getLabSectionId(),labSection.getLabSectionBaseId());
                if(!dbKeys.contains(key)){
                    TestLineLabSectionRelationshipPO testLineLabSection = new TestLineLabSectionRelationshipPO();
                    testLineLabSection.setId(IdUtil.uuId());
                    testLineLabSection.setOrderId(labSectionUpdateReq.getOrderId());
                    testLineLabSection.setTestLineInstanceId(testLine.getTestLineInstanceId());
                    testLineLabSection.setLabSectionId(labSection.getLabSectionId());
                    testLineLabSection.setLabSectionBaseId(labSection.getLabSectionBaseId());
                    testLineLabSection.setCreatedBy(finalUserName);
                    testLineLabSection.setCreatedDate(new Date());
                    testLineLabSection.setModifiedBy(finalUserName);
                    testLineLabSection.setModifiedDate(new Date());
                    testLineLabSection.setActiveIndicator(ActiveType.Enable.getStatus());
                    saveTestLineLabSectionList.add(testLineLabSection);
                }
            });
        });
        //查询TestLine关联的Report
        ReportMatrixQueryReq reportMatrixQueryReq = new ReportMatrixQueryReq();
        reportMatrixQueryReq.setTestLineInstanceIdList(context.getTestLineInstanceIdList());
        List<ReportMatrixRelBO> reportMatrixRelBOList = reportMatrixRelService.queryReportMatrix(reportMatrixQueryReq).getData();
        if(Func.isNotEmpty(reportMatrixRelBOList)){
            Set<String> reportNoList = reportMatrixRelBOList.stream().map(ReportMatrixRelBO::getReportNo).distinct().collect(Collectors.toSet());
            if(Func.isNotEmpty(reportNoList)){
                reportMatrixQueryReq = new ReportMatrixQueryReq();
                reportMatrixQueryReq.setReportNoList(reportNoList);
                reportMatrixRelBOList = reportMatrixRelService.queryReportMatrix(reportMatrixQueryReq).getData();
            }
        }
        context.setReportMatrixRelBOList(reportMatrixRelBOList);
        context.setSaveTestLineLabSectionList(saveTestLineLabSectionList);
        context.setDeleteIdList(deleteIdList);
        return super.before(context);
    }

    @Override
    @Transactional
    public BaseResponse<List<LabSectionItemRsp>> execute(LabSectionUpdateContext context) {
        // 不存在的插入,删除的删除；
        List<TestLineLabSectionRelationshipPO> saveTestLineLabSectionList = context.getSaveTestLineLabSectionList();

        if(Func.isNotEmpty(saveTestLineLabSectionList)) {
            testLineLabSectionService.saveBatch(saveTestLineLabSectionList);
        }
        if(Func.isNotEmpty(context.getDeleteIdList())) {
            testLineLabSectionService.removeByIds(context.getDeleteIdList());
        }
        //更新TestLine关联的所有的Report中的labSectionId
        this.updateReportLabSection(context);
        return BaseResponse.newSuccessInstance(context.getLabSectionItemRsps());
    }

    private void updateReportLabSection(LabSectionUpdateContext context){
        List<ReportMatrixRelBO> reportMatrixRelBOList = context.getReportMatrixRelBOList();
        if(Func.isNotEmpty(reportMatrixRelBOList)){
            Set<String> reportIdList = reportMatrixRelBOList.stream().map(ReportMatrixRelBO::getReportId).distinct().collect(Collectors.toSet());
            if(Func.isNotEmpty(reportIdList)){
                ReportExtForTLUpdateReq reportExtForTLUpdateReq = new ReportExtForTLUpdateReq();
                reportExtForTLUpdateReq.setReportIdList(reportIdList);
                reportExtForTLUpdateReq.setToken(SecurityUtil.getSgsToken());
                BaseResponse<Boolean> baseResponse = reportDomainService.updateReportExtForTL(reportExtForTLUpdateReq);
                log.info("update report labSection res:{}", JSON.toJSONString(baseResponse));
            }
        }
    }
}
