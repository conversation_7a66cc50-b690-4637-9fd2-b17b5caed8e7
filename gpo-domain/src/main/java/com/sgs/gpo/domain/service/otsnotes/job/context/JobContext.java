package com.sgs.gpo.domain.service.otsnotes.job.context;

import com.sgs.framework.core.base.PageBO;
import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.util.SpringUtil;
import com.sgs.framework.model.common.object.ObjectBO;
import com.sgs.framework.model.common.object.ObjectIdBO;
import com.sgs.framework.model.common.object.StatusControlBO;
import com.sgs.framework.model.order.order.OrderBO;
import com.sgs.framework.model.test.execution.v2.job.JobBO;
import com.sgs.framework.model.test.testline.v2.TestLineBO;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.job.JobPO;
import com.sgs.gpo.domain.service.setting.object.IObjectDomainService;
import com.sgs.gpo.facade.model.searchvalid.rsp.SearchValidRsp;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/7/3 09:02
 */
@Data
public class JobContext<Input> extends BaseContext<Input, JobBO> {

    /**对象定义*/
    private ObjectBO objectBO;

    /**Job 实例化数据*/
    private List<JobPO> jobPOList;

    /**Job 分页信息*/
    private PageBO<JobPO> jobPOPage;

    /**本次需要操作的Job对象*/
    private List<JobBO> jobBOList;

    /**本次操作关联的订单信息*/
    private List<OrderBO> orderBOList;

    /**本次操作关联的测试项信息*/
    private List<TestLineBO> testLineBOList;

    /**状态更新校验逻辑处理*/
    private List<StatusControlBO<JobBO>> jobStatusControlList;
    /**状态校验通过的列表*/
    private List<StatusControlBO<JobBO>> jobStatusControlSuccessList;
    /**状态校验失败的列表*/
    private List<StatusControlBO<JobBO>> jobStatusControlFailList;
    private SearchValidRsp searchValidRsp;
    private Set<String> batchOrderNoList;

    private String autoClosed;

    public JobContext(Input input){
        this.setLab(SystemContextHolder.getLab());
        this.setToken(SecurityContextHolder.getSgsToken());
        this.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        this.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        this.setParam(input);
        //查询对象配置信息
        ObjectIdBO objectIdBO = new ObjectIdBO();
        objectIdBO.setObjectCode(Constants.OBJECT.JOB.OBJECT_CODE);
        ObjectBO objectBO = SpringUtil.getBean(IObjectDomainService.class).getDetail(objectIdBO).getData();
        Assert.notEmpty(objectBO);
        this.setObjectBO(objectBO);
    }
}
