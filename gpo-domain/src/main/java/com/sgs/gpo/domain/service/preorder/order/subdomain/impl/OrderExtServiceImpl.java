package com.sgs.gpo.domain.service.preorder.order.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.OrderExtMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderExtPO;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IOrderExtService;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class OrderExtServiceImpl extends ServiceImpl<OrderExtMapper, OrderExtPO> implements IOrderExtService {

    @Override
    public List<OrderExtPO> select(OrderIdReq orderIdReq) {
        List<OrderExtPO> orderExtPOList = new ArrayList<>();
        if(Func.isEmpty(orderIdReq) || Func.isEmpty(orderIdReq.getOrderIdList())){
            return orderExtPOList;
        }
        LambdaQueryWrapper<OrderExtPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderExtPO::getOrderId,orderIdReq.getOrderIdList());
        return baseMapper.selectList(wrapper);
    }
}
