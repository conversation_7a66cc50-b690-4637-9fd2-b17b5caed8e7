package com.sgs.gpo.domain.service.otsnotes.report.command;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.model.common.attachment.AttachmentBO;
import com.sgs.framework.model.common.process.ProcessBO;
import com.sgs.framework.model.common.signatures.SignaturesBO;
import com.sgs.framework.model.common.signatures.SignaturesLanguageBO;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.order.v2.OrderIdRelBO;
import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.framework.model.report.report.v2.ReportCertificateBO;
import com.sgs.framework.model.report.report.v2.ReportChildrenBO;
import com.sgs.framework.model.report.report.v2.ReportHeaderBO;
import com.sgs.framework.model.report.report.v2.ReportHeaderLanguageBO;
import com.sgs.framework.model.report.report.v2.ReportIdBO;
import com.sgs.framework.model.report.report.v2.ReportMatrixRelBO;
import com.sgs.framework.model.report.report.v2.ReportOthersBO;
import com.sgs.framework.model.report.report.v2.ReportParentBO;
import com.sgs.framework.model.report.report.v2.ReportRelationBO;
import com.sgs.framework.model.report.template.AccreditationBO;
import com.sgs.framework.model.report.template.ReportTemplateBO;
import com.sgs.framework.model.report.template.ReportTemplateLanguageBO;
import com.sgs.framework.model.test.conclusion.ConclusionBO;
import com.sgs.framework.model.test.conclusion.v2.ObjectConclusionBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.AttachmentBusinessType;
import com.sgs.gpo.core.enums.ReportFileType;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.conclusion.ConclusionListPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportCertificatePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportExtPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportFilePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportMatrixRelPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportTemplatePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportTypePO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.attachment.OrderAttachmentPO;
import com.sgs.gpo.domain.service.otsnotes.report.context.ReportContext;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportCertificateService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportExtService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportFileService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportMatrixRelService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportTemplateService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportTypeService;
import com.sgs.gpo.domain.service.preorder.attachment.IOrderAttachmentService;
import com.sgs.gpo.domain.service.setting.buparam.IBUParam;
import com.sgs.gpo.facade.model.enums.ProductLines;
import com.sgs.gpo.facade.model.enums.ReportCertificateStatus;
import com.sgs.gpo.facade.model.otsnotes.report.dto.ReportNotesDTO;
import com.sgs.gpo.facade.model.otsnotes.report.dto.ReportNotesLanguage;
import com.sgs.gpo.facade.model.preorder.order.req.OrderAttachmentQueryReq;
import com.sgs.gpo.facade.model.report.bo.ReportCountryOfDestinationBO;
import com.sgs.gpo.facade.model.report.req.ReportFileQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportTemplateReq;
import com.sgs.gpo.integration.dataentry.DataEntryClient;
import com.sgs.gpo.integration.dataentry.req.ConclusionReq;
import com.sgs.gpo.integration.framework.FrameworkClient;
import com.sgs.gpo.integration.framework.rsp.UserSignatureRsp;
import com.sgs.otsnotes.facade.model.rsp.AccreditationRsp;
import com.sgs.otsnotes.facade.model.rsp.AccreditationVO;
import com.sgs.otsnotes.facade.model.rsp.LabClaimer;
import com.sgs.preorder.facade.model.info.DataDictInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/25 13:55
 * @Description report查询输出标准结构ReportBO
 */
@Primary
@Service
@Slf4j
public class ReportQueryCMD extends BaseCommand<ReportContext<ReportQueryReq>> {

    @Autowired
    private IReportService reportService;
    @Autowired
    private IReportMatrixRelService reportMatrixRelService;
    @Autowired
    private IReportTemplateService reportTemplateService;
    @Autowired
    private IReportFileService reportFileService;
    @Autowired
    private IReportTypeService reportTypeService;
    @Autowired
    private FrameworkClient frameWorkClient;
    @Autowired
    private DataEntryClient dataEntryClient;
    @Autowired
    private IReportCertificateService reportCertificateService;
    @Autowired
    private IOrderAttachmentService orderAttachmentService;
    @Autowired
    private IBUParam buParam;
    @Autowired
    private IReportExtService reportExtService;
    @Override
    public BaseResponse validateParam(ReportContext<ReportQueryReq> context) {
        Assert.isTrue(Func.isNotEmpty(context.getParam()), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
//        Assert.isTrue(Func.isNotEmpty(context.getParam().getLab()),"common.param.miss",new Object[]{Constants.TERM.LAB.getCode()});
        //Assert.isTrue(Func.isNotEmpty(context.getParam().getReportIdList()) || Func.isNotEmpty(context.getParam().getReportNoList()) || Func.isNotEmpty(context.getParam().getOrderNoList()),"common.param.miss",new Object[]{Constants.TERM.REQUEST.getCode()});
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(ReportContext<ReportQueryReq> context) {
        ReportQueryReq reportQueryReq = context.getParam();
        Lab lab = context.getLab();
        if(Func.isNotEmpty(lab) && Func.isNotEmpty(lab.getLabCode())){
            reportQueryReq.setLab(lab);
        }
        BaseResponse<LanguageType> languageTypeBaseResponse = buParam.getPrimaryLanguage(ProductLineContextHolder.getProductLineCode());
        if(Func.isNotEmpty(languageTypeBaseResponse) && Func.isNotEmpty(languageTypeBaseResponse.getData())){
            context.setPrimaryLanguage(languageTypeBaseResponse.getData());
        }
        //查询Report List
        BaseResponse<List<ReportPO>> reportRsp = reportService.queryV1(reportQueryReq);
        Assert.isTrue(reportRsp.isSuccess(), reportRsp.getMessage(), new Object[]{});
        List<ReportPO> reportPOList = reportRsp.getData();
        context.setReportPOList(reportPOList);
        if(Func.isNotEmpty(reportPOList)){
            Set<String> reportIdList = reportPOList.parallelStream().map(ReportPO::getId).collect(Collectors.toSet());
            Set<String> reportNoList = reportPOList.parallelStream().map(ReportPO::getReportNo).collect(Collectors.toSet());
            reportQueryReq.setReportIdList(reportIdList);
            BaseResponse<List<ReportCertificatePO>> reportCertificateRsp = reportCertificateService.select(reportQueryReq);
            if(reportCertificateRsp.isSuccess()){
                context.setReportCertificatePOList(reportCertificateRsp.getData());
            }
            BaseResponse<List<ReportExtPO>> reportExtRsp = reportExtService.query(reportQueryReq);
            if(reportExtRsp.isSuccess()){
                context.setReportExtPOList(reportExtRsp.getData());
            }
            if(!reportQueryReq.getBaseQuery()){
                OrderAttachmentQueryReq orderAttachmentQueryReq = new OrderAttachmentQueryReq();
                orderAttachmentQueryReq.setReportNoList(reportNoList);
                orderAttachmentQueryReq.setBusinessTypeList(Sets.newHashSet(AttachmentBusinessType.Sample_Photo.getValue()));
                BaseResponse<List<OrderAttachmentPO>> reportAttachmentRsp = orderAttachmentService.select(orderAttachmentQueryReq);
                if(reportAttachmentRsp.isSuccess() && Func.isNotEmpty(reportAttachmentRsp.getData())){
                    context.setReportAttachmentList(reportAttachmentRsp.getData());
                }
            }

        }
        return super.before(context);
    }

    @Override
    public BaseResponse<List<ReportBO>> buildDomain(ReportContext<ReportQueryReq> context) {
        ReportQueryReq reportQueryReq = context.getParam();
        // 初始化返回结果
        context.setReportBOList(new ArrayList<>());
        List<ReportBO> reportList = new ArrayList<>();
        List<ReportPO> reportPOList = context.getReportPOList();
        List<ReportExtPO> reportExtPOList = context.getReportExtPOList();
        List<OrderAttachmentPO> reportAttachmentList = context.getReportAttachmentList();
        if (Func.isNotEmpty(reportPOList)) {
            //查询Report Matrix
            List<ReportMatrixRelPO> reportMatrixList = Lists.newArrayList();
            reportQueryReq.setReportIdList(reportPOList.stream().map(ReportPO::getId).collect(Collectors.toSet()));
            BaseResponse<List<ReportMatrixRelPO>> reportMatrixResponse = reportMatrixRelService.query(reportQueryReq);
            if (reportMatrixResponse.isSuccess() && Func.isNotEmpty(reportMatrixResponse.getData())) {
                reportMatrixList = reportMatrixResponse.getData();
            }
            context.setReportMatrixRelPOList(reportMatrixList);
            //查询Attachment
            List<ReportFilePO> reportFileList = Lists.newArrayList();
            ReportFileQueryReq reportFileQueryReq = new ReportFileQueryReq();
            reportFileQueryReq.setReportIdList(reportPOList.stream().map(ReportPO::getId).collect(Collectors.toSet()));
            BaseResponse<List<ReportFilePO>> reportFileResponse = reportFileService.query(reportFileQueryReq);
            if (reportFileResponse.isSuccess() && Func.isNotEmpty(reportFileResponse.getData())) {
                reportFileList = reportFileResponse.getData();
            }
            context.setReportFileList(reportFileList);
            List<ReportMatrixRelPO> reportMatrixRelPOList = reportMatrixList;
            List<ReportFilePO> reportFilePOList = reportFileList;
            reportPOList.stream().forEach(localReport -> {
                ReportBO reportBO = new ReportBO();
                // 1.id
                ReportIdBO id = new ReportIdBO();
                id.setReportId(localReport.getId());
                id.setReportNo(localReport.getReportNo());
                id.setActualReportNo(localReport.getActualReportNo());
                id.setParentReportNo(localReport.getParentReportNo());
                id.setRootReportNo(localReport.getRootReportNo());
                id.setExternalReportNo(localReport.getExternalReportNo());
                reportBO.setId(id);
                // 2.relationship
                String orderNo = localReport.getOrderNo();
                assembleRelationship(reportBO, reportMatrixRelPOList, orderNo);
                // 3.header
                ReportHeaderBO header = new ReportHeaderBO();
                header.setCertificateId(localReport.getCertificateId());
                header.setCertificateName(localReport.getCertificateName());
                header.setCertificateFileCloudKey(localReport.getCertificateFileCloudKey());
                header.setReportDueDate(localReport.getReportDueDate());
                header.setReportStatus(localReport.getReportStatus());
                header.setRemark(localReport.getRemark());
                header.setReportTypeId(localReport.getReportTypeID());
                header.setApproveDate(localReport.getApproverDate());
                header.setAwbNo(localReport.getAwbNo());
                header.setApproverBy(localReport.getApproverBy());
                header.setEditorBy(localReport.getEditorBy());
                header.setReviewerBy(localReport.getReviewerBy());
                header.setSignatureLanguage(localReport.getSignatureLanguage());
                header.setWorkFlow(localReport.getWorkFlow());
                header.setTestMatrixMergeMode(localReport.getTestMatrixMergeMode());
                header.setReportVersion(localReport.getReportVersion());
                header.setCreateBy(localReport.getCreatedBy());
                header.setCreateDate(localReport.getCreatedDate());
                header.setReportFlag(localReport.getReportFlag());
                header.setSoftDeliveryDate(localReport.getSoftcopyDeliveryDate());
                if(Func.isNotEmpty(reportExtPOList)){
                    ReportExtPO reportExtPO = reportExtPOList.stream().filter(e -> Func.equalsSafe(e.getReportId(),localReport.getId())).findAny().orElse(null);
                    if(Func.isNotEmpty(reportExtPO) && Func.isNotEmpty(reportExtPO.getNotes())){
                        ReportNotesDTO reportNotesDTO = JSON.parseObject(reportExtPO.getNotes(),ReportNotesDTO.class);
                        if(Func.isNotEmpty(reportNotesDTO)){
                            header.setReportNotes(reportNotesDTO.getReportNotes());
                            List<ReportNotesLanguage> languageList = reportNotesDTO.getLanguageList();
                            if(Func.isNotEmpty(languageList)){
                                List<ReportHeaderLanguageBO> headerLanguageBOList = new ArrayList<>();
                                for(ReportNotesLanguage reportNotesLanguage : languageList){
                                    if(Func.isNotEmpty(reportNotesLanguage.getLanguageId()) &&Func.isNotEmpty(reportNotesLanguage.getReportNotes())){
                                        ReportHeaderLanguageBO languageBO = new ReportHeaderLanguageBO();
                                        languageBO.setLanguageId(reportNotesLanguage.getLanguageId());
                                        languageBO.setReportNotes(reportNotesLanguage.getReportNotes());
                                        headerLanguageBOList.add(languageBO);
                                    }
                                }
                                header.setLanguageList(headerLanguageBOList);
                            }
                        }
                    }
                }
                assembleCountryOfDestination(header, localReport.getCountryOfDestination(),context);
                reportBO.setHeader(header);
                // 4.conclusion
                assembleConclusion(reportBO);
                if(!reportQueryReq.getBaseQuery()){
                    // 5.attachmentList
                    assembleAttachmentList(reportBO, reportFilePOList, reportAttachmentList);
                    // 7.reportTemplate
                    assembleReportTemplate(reportBO, context, localReport);
                }
                //ReportCertificate
                assembleReportCertificate(reportBO, context, localReport);
                // 6.processList
                assembleProcessList(reportBO, localReport);
                // 8. ReportConclusionList
                assembleReportConclusionList(reportBO, context);
                // 9.translationReportStatement设置
                if (Func.isNotEmpty(localReport.getReportTypeID())) {
                    ReportTypePO reportType = reportTypeService.getById(localReport.getReportTypeID());
                    if (Func.isNotEmpty(reportType) && Func.isNotEmpty(reportType.getAmendRemarkRule())) {
                        String translationReportStatement = reportType.getAmendRemarkRule();
                        if (translationReportStatement.toLowerCase().contains("{reportno}")) {
                            translationReportStatement = translationReportStatement.replaceAll("\\{ReportNo\\}", localReport.getActualReportNo());
                        }
                        reportBO.getHeader().setTranslationReportStatement(translationReportStatement);
                    }
                }
                // 10。other Info
                ReportOthersBO reportOthersBO = new ReportOthersBO();
                reportOthersBO.setReportSource(localReport.getFromDB());
                reportOthersBO.setPhotoRemark(localReport.getPhotoRemark());
                reportOthersBO.setTestingType(localReport.getTestingType());
                reportBO.setOthers(reportOthersBO);
                reportList.add(reportBO);
            });
        }

        context.setReportBOList(reportList);
        return BaseResponse.newSuccessInstance(reportList);
    }

    /**
     * 调用soda接口获取ConclusionList
     *
     * @param reportBO
     */
    private void assembleReportConclusionList(ReportBO reportBO, ReportContext<ReportQueryReq> context) {
        // 基于报告查询Conclusion集合
        ConclusionReq conclusionReq = new ConclusionReq();
        conclusionReq.setReportId(reportBO.getId().getReportId());
        conclusionReq.setProductLineCode(context.getProductLineCode());
        BaseResponse<List<ObjectConclusionBO>> conclusionRes = dataEntryClient.getConclusionList(conclusionReq);
        if (conclusionRes.isSuccess() && Func.isNotEmpty(conclusionRes.getData())) {
            reportBO.setConclusionList(conclusionRes.getData());
        }
    }
    private void assembleReportCertificate(ReportBO reportBO, ReportContext<ReportQueryReq> context, ReportPO reportPO) {
        List<ReportCertificatePO> reportCertificatePOList = context.getReportCertificatePOList();
        List<ReportCertificateBO> reportCertificateBOList = new ArrayList<>();
        if(Func.isNotEmpty(reportCertificatePOList)){
            Integer buId = context.getLab().getBuId();
            List<DataDictInfo> dataDictList = frameWorkClient.getDataDictList(Constants.SYS_KEY_GROUP.CERTIFICATE_TYPE, buId);
            List<ReportCertificatePO> reportCertificatePOS = reportCertificatePOList.parallelStream().filter(item -> Func.equalsSafe(item.getReportId(), reportPO.getId())).collect(Collectors.toList());
            if(Func.isNotEmpty(reportCertificatePOS)){
                reportCertificatePOS = reportCertificatePOS.stream().filter(item->ReportCertificateStatus.check(item.getStatus(),ReportCertificateStatus.VALID)).collect(Collectors.toList());
                for (ReportCertificatePO reportCertificatePO : reportCertificatePOS) {
                    ReportCertificateBO reportCertificateBO = Func.copy(reportCertificatePO, ReportCertificateBO.class);
                    if(Func.isNotEmpty(dataDictList)){
                        DataDictInfo dataDictInfo = dataDictList.stream().filter(item -> Func.equalsSafe(item.getSysKey(), reportCertificatePO.getCertificateType())).findAny().orElse(null);
                        if(Func.isNotEmpty(dataDictInfo)){
                            reportCertificateBO.setCertificateTypeDisplay( dataDictInfo.getSysValue());
                        }
                    }
                    reportCertificateBO.setStatusDisplay(ReportCertificateStatus.getName(reportCertificatePO.getStatus()));
                    reportCertificateBO.setCertificateId(reportCertificatePO.getId());
                    reportCertificateBOList.add(reportCertificateBO);
                }
            }
        }
        reportBO.setReportCertificateList(reportCertificateBOList);
    }


    private void assembleReportTemplate(ReportBO reportBO, ReportContext<ReportQueryReq> context, ReportPO reportPO) {
        ReportTemplateBO reportTemplate = new ReportTemplateBO();
        if (Func.isEmpty(context.getLab())) {
            log.info("assembleReportTemplate 未获取到 lab");
            return;
        }
        //CoverPageTemplate
        reportTemplate.setCoverPageTemplatePath(reportPO.getCoverPageTemplatePath());
        reportTemplate.setCoverPageTemplateName(reportPO.getCoverPageTemplateName());
        // 获取当前用户对应的Lab信息
        String labCode = context.getLab().getLabCode();
        Integer buId = context.getLab().getBuId();
        Integer locationID = context.getLab().getLocationId();
        String productLineCode = context.getLab().getBuCode();
        // accreditation
        assembleAccreditation(reportTemplate, reportPO, labCode);
        // labClaimer
        List<ReportTemplateLanguageBO> reportTemplateLanguageList = Lists.newArrayList();
        // qrcode处理
        ReportTemplateReq reportTemplateReq = new ReportTemplateReq();
        reportTemplateReq.setReportId(reportPO.getId());
        BaseResponse<List<ReportTemplatePO>> reportTemplateListRes = reportTemplateService.queryReportTemplateList(reportTemplateReq);
        ReportTemplatePO reportTemplatePOEN = reportTemplateListRes.getData().stream().filter(template ->
                LanguageType.check(template.getLanguageId(), LanguageType.English) || LanguageType.check(template.getLanguageId(), LanguageType.EnglishAndChinese)).findAny().orElse(null);
        //  多语言处理
        String labClaimerEn = frameWorkClient.getLabClaimer(labCode, LanguageType.English.getLanguageId());
        // EN
        ReportTemplateLanguageBO reportTemplateEn = new ReportTemplateLanguageBO();
        reportTemplateEn.setLanguageId(LanguageType.English.getLanguageId());
        if (Func.isNotEmpty(reportTemplatePOEN) && Func.isNotEmpty(reportTemplatePOEN.getQrcodePath())) {
            reportTemplate.setQrcodePath(Constants.CLOUD_PREFIX + reportTemplatePOEN.getQrcodePath());
            reportTemplateEn.setQrcodePath(Constants.CLOUD_PREFIX + reportTemplatePOEN.getQrcodePath());
        }
        if (Func.isNotEmpty(labClaimerEn)) {
            reportTemplate.setLabClaimer(Constants.CLOUD_PREFIX + labClaimerEn);
            reportTemplateEn.setLabClaimer(Constants.CLOUD_PREFIX + labClaimerEn);
        }
        // labGBClaimer gbCompanyChop
        LabClaimer labGBClaimerEn = frameWorkClient.getLabGBClaimer(LanguageType.English.getLanguageId(), buId, locationID);
        if (Func.isNotEmpty(labGBClaimerEn)) {
            if(Func.isNotEmpty(labGBClaimerEn.getLabGBClaimer())){
                reportTemplateEn.setLabGBClaimer(Constants.CLOUD_PREFIX + labGBClaimerEn.getLabGBClaimer());
                reportTemplate.setLabGBClaimer(Constants.CLOUD_PREFIX + labGBClaimerEn.getLabGBClaimer());
            }
            if(Func.isNotEmpty(labGBClaimerEn.getGbCompanyChop())){
                reportTemplate.setGbCompanyChop(Constants.CLOUD_PREFIX + labGBClaimerEn.getGbCompanyChop());
                reportTemplateEn.setGbCompanyChop(Constants.CLOUD_PREFIX + labGBClaimerEn.getGbCompanyChop());
            }
        }
        ReportTemplatePO reportTemplatePOCN = reportTemplateListRes.getData().stream().filter(template ->
                LanguageType.check(template.getLanguageId(), LanguageType.Chinese)).findAny().orElse(null);
        // CN
        ReportTemplateLanguageBO reportTemplateCn = new ReportTemplateLanguageBO();
        reportTemplateCn.setLanguageId(LanguageType.Chinese.getLanguageId());
        if (Func.isNotEmpty(reportTemplatePOCN) && Func.isNotEmpty(reportTemplatePOCN.getQrcodePath())) {
            reportTemplateCn.setQrcodePath(Constants.CLOUD_PREFIX + reportTemplatePOCN.getQrcodePath());
        }
        String labClaimerCn = frameWorkClient.getLabClaimer(labCode, LanguageType.Chinese.getLanguageId());
        if (Func.isNotEmpty(labClaimerCn)) {
            reportTemplateCn.setLabClaimer(Constants.CLOUD_PREFIX + labClaimerCn);
        }

        LabClaimer labGBClaimerCn = frameWorkClient.getLabGBClaimer(LanguageType.Chinese.getLanguageId(), buId, locationID);
        if (Func.isNotEmpty(labGBClaimerCn)) {
            if(Func.isNotEmpty(labGBClaimerCn.getLabGBClaimer())){
                reportTemplateCn.setLabGBClaimer(Constants.CLOUD_PREFIX + labGBClaimerCn.getLabGBClaimer());
            }
            if(Func.isNotEmpty(labGBClaimerCn.getGbCompanyChop())){
                reportTemplateCn.setGbCompanyChop(Constants.CLOUD_PREFIX + labGBClaimerCn.getGbCompanyChop());
            }
        }
        reportTemplateLanguageList.add(reportTemplateCn);
        reportTemplateLanguageList.add(reportTemplateEn);
        reportTemplate.setLanguageList(reportTemplateLanguageList);
        // 取本地存储的数据 SL 跟GPO设计不一样
        if (!Func.equalsSafe(ProductLines.SOFTLINE.getCode(), productLineCode)) {
            // signatureList
            List<SignaturesBO> signatureList = Lists.newArrayList();
            String approves = reportPO.getApprover();
            String editor = reportPO.getEditor();
            String reviewer = reportPO.getReviewer();
            this.assembleSignatureList(signatureList, approves, "approves");
            if (Func.isNotEmpty(editor)) {
                this.assembleSignatureList(signatureList, editor, "editor");
            }
            if (Func.isNotEmpty(reviewer)) {
                this.assembleSignatureList(signatureList, reviewer, "reviewer");
            }
            reportTemplate.setSignatureList(signatureList);
        }
        reportBO.setReportTemplate(reportTemplate);
    }

    private void assembleAccreditation(ReportTemplateBO reportTemplate, ReportPO reportPO, String labCode) {
        if (Func.isEmpty(reportTemplate.getAccreditation())) {
            reportTemplate.setAccreditation(new AccreditationBO());
        }
        // 基于CertificateFileCloudKey 查询最新的资质信息，防止过期数据
        //调用CS接口获取accreditationTypeList
        AccreditationRsp accreditationInfo = frameWorkClient.getAccreditationInfo(labCode, reportPO.getCertificateFileCloudKey());
        if (Func.isNotEmpty(accreditationInfo) && Func.isNotEmpty(accreditationInfo.getRows())) {
            List<AccreditationVO> accreditationInfoRows = accreditationInfo.getRows();
            AccreditationVO accreditationVO = accreditationInfoRows.stream().filter(item ->
                    StringUtils.equalsIgnoreCase(reportPO.getCertificateFileCloudKey(), item.getFileCloudKey())).findFirst().orElse(null);

            if (Func.isNotEmpty(accreditationVO)) {
                reportTemplate.getAccreditation().setType(accreditationVO.getAccreditationTypeList());
                if(Func.isNotEmpty(accreditationVO.getFileCloudKey())){
                    reportTemplate.getAccreditation().setAccreditationLogo(Constants.CLOUD_PREFIX + accreditationVO.getFileCloudKey());
                }
                reportTemplate.getAccreditation().setAccreditationRemark(accreditationVO.getAcceditationRemark());
                reportTemplate.getAccreditation().setStatement(accreditationVO.getStatement());
                reportTemplate.getAccreditation().setDescription(accreditationVO.getDescription());
            }
        }
    }

    /**
     * 组装签名信息中英文
     *
     * @param signatureList
     * @param signatureJson
     * @param signType
     */
    private void assembleSignatureList(List<SignaturesBO> signatureList, String signatureJson, String signType) {
        Map<String, Object> approvesMap = JSON.parseObject(signatureJson);
        // DB保存的签名信息
        List<UserSignatureRsp> signatureRspList = Lists.newArrayList();
        // 英文签名
        List<UserSignatureRsp> signatureEnRspList = Lists.newArrayList();
        // 中文签名
        List<UserSignatureRsp> signatureCnRspList = Lists.newArrayList();
        // 获取签名数据，优先获取英文
        if(Func.isNotEmpty(approvesMap)){
            if (approvesMap.containsKey(LanguageType.English.getCode())) {
                signatureEnRspList = JSON.parseArray(approvesMap.get(LanguageType.English.getCode()).toString(), UserSignatureRsp.class);
                if (Func.isNotEmpty(signatureEnRspList)) {
                    signatureRspList = signatureEnRspList;
                }
            }
            if (approvesMap.containsKey(LanguageType.Chinese.getCode())) {
                signatureCnRspList = JSON.parseArray(approvesMap.get(LanguageType.Chinese.getCode()).toString(), UserSignatureRsp.class);
                if (Func.isNotEmpty(signatureCnRspList)) {
                    if (Func.isEmpty(signatureRspList)) {
                        signatureRspList = signatureCnRspList;
                    }
                }
            }
        }
        // 转换BO结构
        List<UserSignatureRsp> finalSignatureEnRspList = signatureEnRspList;
        List<UserSignatureRsp> finalSignatureCnRspList = signatureCnRspList;
        if(Func.isNotEmpty(signatureRspList)){
            signatureRspList.stream().forEach(userSignatureRsp ->{
                SignaturesBO signatureBO = new SignaturesBO();
                if(Func.isNotEmpty(userSignatureRsp.getAutographId())){
                    signatureBO.setCloudId(Constants.CLOUD_PREFIX + userSignatureRsp.getAutographId());
                }
                signatureBO.setUserName(userSignatureRsp.getSignatureName());
                signatureBO.setSignatureName(userSignatureRsp.getSignatureName());
                signatureBO.setTitle(userSignatureRsp.getTitle());
                signatureBO.setBranch(userSignatureRsp.getBranch());
                signatureBO.setSignType(signType);
                signatureBO.setEmail(userSignatureRsp.getEmail());
                signatureBO.setRegionAccount(userSignatureRsp.getRegionAccount());
                // 组装多语言数组
                List<SignaturesLanguageBO> languageList = Lists.newArrayList();
                // 英文
                if(Func.isNotEmpty(finalSignatureEnRspList)){
                    UserSignatureRsp userSignatureEn = finalSignatureEnRspList.stream().filter(item->Func.equalsSafe(item.getId(),userSignatureRsp.getId())).findAny().orElse(null);
                    if(Func.isNotEmpty(userSignatureEn)){
                        languageList.add(assembleSignLanguageList(userSignatureEn,LanguageType.English.getLanguageId()));
                    }
                }
                if(Func.isNotEmpty(finalSignatureCnRspList)){
                    UserSignatureRsp userSignatureCn = finalSignatureCnRspList.stream().filter(item->Func.equalsSafe(item.getId(),userSignatureRsp.getId())).findAny().orElse(null);
                    if(Func.isNotEmpty(userSignatureCn)){
                        languageList.add(assembleSignLanguageList(userSignatureCn,LanguageType.Chinese.getLanguageId()));
                    }
                }
                signatureBO.setLanguageList(languageList);
                signatureList.add(signatureBO);
            });
        }else if(StringUtils.equalsIgnoreCase(signType,"approves")){
            SignaturesBO signatureBO = new SignaturesBO();
            signatureBO.setSignType(signType);
            List<SignaturesLanguageBO> languageList = new ArrayList<>();
            SignaturesLanguageBO signaturesLanguageBOEn = new SignaturesLanguageBO();
            signaturesLanguageBOEn.setLanguageId(LanguageType.English.getLanguageId());
            SignaturesLanguageBO signaturesLanguageBOCn = new SignaturesLanguageBO();
            signaturesLanguageBOCn.setLanguageId(LanguageType.Chinese.getLanguageId());
            languageList.add(signaturesLanguageBOEn);
            languageList.add(signaturesLanguageBOCn);
            signatureBO.setLanguageList(languageList);
            signatureList.add(signatureBO);
        }

    }

    private SignaturesLanguageBO assembleSignLanguageList(UserSignatureRsp signatureRsp,Integer languageId){
        SignaturesLanguageBO signaturesLanguage = new SignaturesLanguageBO();
        if(Func.isNotEmpty(signatureRsp.getAutographId())){
            signaturesLanguage.setCloudId(Constants.CLOUD_PREFIX + signatureRsp.getAutographId());
        }
        signaturesLanguage.setUserName(signatureRsp.getSignatureName());
        signaturesLanguage.setSignatureName(signatureRsp.getSignatureName());
        signaturesLanguage.setTitle(signatureRsp.getTitle());
        signaturesLanguage.setBranch(signatureRsp.getBranch());
        signaturesLanguage.setLanguageId(languageId);
        return signaturesLanguage;
    }

    private void assembleCountryOfDestination(ReportHeaderBO header, String countryOfDestination,ReportContext<ReportQueryReq> context) {
        if (Func.isEmpty(countryOfDestination)) {
            return;
        }
        List<ReportCountryOfDestinationBO> countryOfDestinationList = JSON.parseArray(countryOfDestination, ReportCountryOfDestinationBO.class);
        if (Func.isEmpty(countryOfDestinationList)) {
            return;
        }
        LanguageType primaryLanguage = context.getPrimaryLanguage();
        List<ReportHeaderLanguageBO> languageList = header.getLanguageList();
        if(Func.isEmpty(languageList)){
            languageList = new ArrayList<>();
        }
        Map<String, List<ReportCountryOfDestinationBO>> reportCountryOfDestinationBOMap = countryOfDestinationList.stream().collect(Collectors.groupingBy(ReportCountryOfDestinationBO::getKey));

        for (String key : reportCountryOfDestinationBOMap.keySet()) {
            List<ReportCountryOfDestinationBO> reportCountryOfDestinationBOS = reportCountryOfDestinationBOMap.get(key);
            for (ReportCountryOfDestinationBO reportCountryOfDestinationBO : reportCountryOfDestinationBOS) {
                Integer languageId = reportCountryOfDestinationBO.getLanguageId();
                String countryOfDestinationValue = reportCountryOfDestinationBO.getValue();
                String finalCountryOfDestinationValue = countryOfDestinationValue;
                ReportHeaderLanguageBO reportHeaderLanguageBO = languageList.stream().filter(item -> Func.equalsSafe(item.getLanguageId(), languageId)).findAny().orElse(null);
                if(Func.isEmpty(reportHeaderLanguageBO)){
                    reportHeaderLanguageBO = new ReportHeaderLanguageBO();
                    reportHeaderLanguageBO.setLanguageId(languageId);
                    reportHeaderLanguageBO.setCountryOfDestination(countryOfDestinationValue);
                    languageList.add(reportHeaderLanguageBO);
                }else{
                    if(Func.isNotEmpty(countryOfDestinationValue)){
                        countryOfDestinationValue += ",";
                        countryOfDestinationValue += reportHeaderLanguageBO.getCountryOfDestination();
                    }
                    reportHeaderLanguageBO.setCountryOfDestination(countryOfDestinationValue);
                }
            }
        }
        if(Func.isNotEmpty(languageList)){
            ReportHeaderLanguageBO primaryLanguageBO = languageList.stream().filter(item -> LanguageType.check(item.getLanguageId(), primaryLanguage)).findAny().orElse(null);
            if(Func.isNotEmpty(primaryLanguageBO) ){
                header.setCountryOfDestination(primaryLanguageBO.getCountryOfDestination());
            }else{
                ReportHeaderLanguageBO otherLanguageBO = languageList.stream().filter(item -> !LanguageType.check(item.getLanguageId(), primaryLanguage)).findAny().orElse(null);
                if(Func.isNotEmpty(otherLanguageBO)){
                    header.setCountryOfDestination(otherLanguageBO.getCountryOfDestination());
                }
            }
        }
        header.setLanguageList(languageList);
    }

    private void assembleConclusion(ReportBO reportBO) {
        // 查询Conclusion
        // TODO 单独处理Conclusion
        BaseResponse<ConclusionListPO> reportConclusionRes = reportService.queryReportConclusion(reportBO.getId().getReportId());
        if (reportConclusionRes.isSuccess() && Func.isNotEmpty(reportConclusionRes.getData())) {
            ConclusionBO conclusion = new ConclusionBO();
            ConclusionListPO reportConclusion = reportConclusionRes.getData();
            if (Func.isNotEmpty(reportConclusion.getConclusionCode())) {
                conclusion.setConclusionCode(reportConclusion.getConclusionCode().toString());
            }
            conclusion.setCustomerConclusion(reportConclusion.getDescription());
            conclusion.setConclusionSettingId(reportConclusion.getConclusionSettingID());
            reportBO.setConclusion(conclusion);
        }
    }

    private void assembleProcessList(ReportBO reportBO, ReportPO gpnReport) {
        //TODO 查询BizLog
        List<ProcessBO> processList = Lists.newArrayList();
        if (Func.isNotEmpty(gpnReport.getCreatedDate())) {
            ProcessBO create = new ProcessBO();
            create.setNodePoint(Constants.OBJECT.REPORT.ACTION.CREATE.name());
            create.setOperator(gpnReport.getCreatedBy());
            create.setCompletedDateTime(gpnReport.getCreatedDate());
            processList.add(create);
        }
        if (Func.isNotEmpty(gpnReport.getApproverDate())) {
            ProcessBO approve = new ProcessBO();
            approve.setNodePoint(Constants.OBJECT.REPORT.ACTION.APPROVE.name());
            approve.setOperator(gpnReport.getApprover());
            approve.setCompletedDateTime(gpnReport.getApproverDate());
            processList.add(approve);
        }
        if (Func.isNotEmpty(gpnReport.getSoftcopyDeliveryDate())) {
            ProcessBO delivery = new ProcessBO();
            delivery.setNodePoint(Constants.OBJECT.REPORT.ACTION.SOFTCOPY_DELIVERY.name());
            delivery.setCompletedDateTime(gpnReport.getSoftcopyDeliveryDate());
            processList.add(delivery);
        }
        reportBO.setProcessList(processList);
    }

    private void assembleRelationship(ReportBO reportBO, List<ReportMatrixRelPO> reportMatrixList, String orderNo) {
        ReportRelationBO relationship = new ReportRelationBO();
        ReportParentBO parent = new ReportParentBO();
        // order
        OrderIdRelBO orderIdRel = new OrderIdRelBO();
        orderIdRel.setOrderNo(orderNo);
        parent.setOrder(orderIdRel);
        relationship.setParent(parent);
        ReportChildrenBO children = new ReportChildrenBO();
        // report matrix
        if (Func.isNotEmpty(reportMatrixList)) {
            List<ReportMatrixRelPO> currentReportMatrixList = reportMatrixList.stream().filter(r -> Func.equalsSafe(r.getReportId(), reportBO.getId().getReportId()))
                    .collect(Collectors.toList());
            if (Func.isNotEmpty(currentReportMatrixList)) {
                List<ReportMatrixRelBO> reportMatrixRelList = Func.copy(currentReportMatrixList, ReportMatrixRelPO.class, ReportMatrixRelBO.class);
                children.setReportMatrixList(reportMatrixRelList);
            }
        }
        relationship.setChildren(children);
        reportBO.setRelationship(relationship);
    }

    private void assembleAttachmentList(ReportBO reportBO, List<ReportFilePO> reportFileList, List<OrderAttachmentPO> reportAttachmentList) {
        List<AttachmentBO> attachmentList = Lists.newArrayList();
        reportFileList = reportFileList.stream().filter(e -> e.getReportID().equals(reportBO.getId().getReportId())).collect(Collectors.toList());
        if (Func.isEmpty(reportFileList) && Func.isEmpty(reportAttachmentList)) {
            return;
        }
        // TestResult多语言处理
//        List<ReportFilePO> testResultList = reportFileList.stream().filter(item -> Func.isNotEmpty(item.getReportFileType()) && ReportFileType
//                .check(Integer.valueOf(item.getReportFileType()), ReportFileType.TestResult)).collect(Collectors.toList());
//        // 存在多语言的场景
//        AttachmentBO testResult = null;
//        if (Func.isNotEmpty(testResultList) && testResultList.size() > 1) {
//            ReportFilePO testResultEn = testResultList.stream().filter(item -> LanguageType.check(item.getLanguageID(), LanguageType.English)).findAny().orElse(null);
//            ReportFilePO testResultCn = testResultList.stream().filter(item -> LanguageType.check(item.getLanguageID(), LanguageType.Chinese)).findAny().orElse(null);
//            if (Func.isNotEmpty(testResultEn)) {
//                testResult = new AttachmentBO();
//                testResult.setId(testResultEn.getID());
//                testResult.setCloudId(testResultEn.getCloudID());
//                testResult.setFileName(testResultEn.getFilename());
//                testResult.setFileType(testResultEn.getReportFileType().toString());
//                if (Func.isNotEmpty(testResultEn.getReportFileType()) &&
//                        Func.isNotEmpty(ReportFileType.getCode(testResultEn.getReportFileType()))) {
//                    testResult.setBusinessType(ReportFileType.getCode(testResultEn.getReportFileType()).message());
//                }
//                List<AttachmentLanguageBO> languageList = Lists.newArrayList();
//                AttachmentLanguageBO attachmentLanguageEn = new AttachmentLanguageBO();
//                attachmentLanguageEn.setLanguageId(LanguageType.English.getLanguageId());
//                attachmentLanguageEn.setFileName(testResultEn.getFilename());
//                attachmentLanguageEn.setCloudId(testResultEn.getCloudID());
//                languageList.add(attachmentLanguageEn);
//                AttachmentLanguageBO attachmentLanguageCn = new AttachmentLanguageBO();
//                attachmentLanguageCn.setLanguageId(LanguageType.Chinese.getLanguageId());
//                attachmentLanguageCn.setFileName(testResultCn.getFilename());
//                attachmentLanguageCn.setCloudId(testResultCn.getCloudID());
//                languageList.add(attachmentLanguageCn);
//                testResult.setLanguageList(languageList);
//            }
//        }
//        if (Func.isNotEmpty(testResult)) {
//            attachmentList.add(testResult);
//        }
//        AttachmentBO finalTestResult = testResult;
        if(Func.isNotEmpty(reportFileList)){
            reportFileList.stream().forEach(reportFilePO -> {
//            if (Func.isNotEmpty(finalTestResult) && Func.isNotEmpty(reportFilePO.getReportFileType()) &&
//                    ReportFileType.check(reportFilePO.getReportFileType(), ReportFileType.TestResult)) {
//                return;
//            }
                AttachmentBO attachmentBO = new AttachmentBO();
                attachmentBO.setId(reportFilePO.getID());
                attachmentBO.setCloudId(reportFilePO.getCloudID());
                attachmentBO.setFileName(reportFilePO.getFilename());
                attachmentBO.setId(reportFilePO.getID());
                attachmentBO.setFileType(reportFilePO.getReportFileType().toString());
                attachmentBO.setFileLanguageId(reportFilePO.getLanguageID());
                attachmentBO.setStatus(Func.toStr(reportFilePO.getStatus()));
                if (Func.isNotEmpty(reportFilePO.getReportFileType()) &&
                        Func.isNotEmpty(ReportFileType.getCode(reportFilePO.getReportFileType()))) {
                    attachmentBO.setBusinessType(ReportFileType.getCode(reportFilePO.getReportFileType()).message());
                }
                attachmentList.add(attachmentBO);
            });
        }
        if(Func.isNotEmpty(reportAttachmentList)){
            reportAttachmentList.stream().forEach(attachment ->{
                AttachmentBO attachmentBO = new AttachmentBO();
                attachmentBO.setId(attachment.getId());
                attachmentBO.setFileName(attachment.getAttachmentName());
                attachmentBO.setFileType(attachment.getFileType());
                attachmentBO.setCloudId(attachment.getCloudID());
                attachmentBO.setDisplayInReport(true);
                attachmentBO.setBusinessType(AttachmentBusinessType.Report_Photo.getValue());
                attachmentList.add(attachmentBO);
            });
        }
        reportBO.setAttachmentList(attachmentList);
    }


    @Override
    public BaseResponse execute(ReportContext<ReportQueryReq> context) {
        return this.buildDomain(context);
    }
}
