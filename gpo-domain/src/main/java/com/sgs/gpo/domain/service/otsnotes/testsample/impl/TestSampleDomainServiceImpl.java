package com.sgs.gpo.domain.service.otsnotes.testsample.impl;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.test.testsample.v2.TestSampleBO;
import com.sgs.framework.model.test.testsample.v2.TestSampleIdBO;
import com.sgs.framework.open.platform.base.service.impl.AbstractDomainService;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.domain.service.otsnotes.testsample.ITestSampleDomainServe;
import com.sgs.gpo.domain.service.otsnotes.testsample.command.TestSampleQueryCMD;
import com.sgs.gpo.domain.service.otsnotes.testsample.context.TestSampleContext;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.ITestSampleService;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
public class TestSampleDomainServiceImpl extends AbstractDomainService<TestSampleBO, TestSampleIdBO,TestSampleQueryReq, ITestSampleService> implements ITestSampleDomainServe {

    @Override
    public BaseResponse<List<TestSampleBO>> queryBO(TestSampleQueryReq request) {
        // 入参不允许为空
        Assert.isTrue(Func.isNotEmpty(request),"common.param.miss",new Object[]{Constants.TERM.REQUEST});
        // 参数必须是TestSampleQueryReq
        TestSampleContext<TestSampleQueryReq> testSampleContext = new TestSampleContext<>(request);
        testSampleContext.setLab(SystemContextHolder.getLab());
        if(Func.isEmpty(request.getLab())){
            request.setLab(SystemContextHolder.getLab());
        }
        return BaseExecutor.start(TestSampleQueryCMD.class, testSampleContext);
    }

}
