package com.sgs.gpo.domain.service.dashboard.command;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.core.util.StringPool;
import com.sgs.framework.model.common.lab.LabBO;
import com.sgs.framework.model.enums.OrderStatusEnum;
import com.sgs.framework.model.enums.TestLineStatus;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.domain.service.dashboard.context.DashboardContext;
import com.sgs.gpo.core.enums.ReportStatus;
import com.sgs.gpo.dbstorages.mybatis.mapper.dashboard.DashboardMapper;
import com.sgs.gpo.domain.service.common.lab.ILabService;
import com.sgs.gpo.facade.model.dashboard.rsp.StatusTodaySummaryRsp;
import com.sgs.gpo.facade.model.dashboard.req.DashBoardQueryRep;
import com.sgs.gpo.facade.model.dashboard.rsp.TodaySummaryRsp;
import com.sgs.gpo.facade.model.echarts.Pie;
import com.sgs.gpo.facade.model.lab.req.LabReq;
import com.sgs.gpo.facade.model.trims.labsection.rsp.LabSectionRsp;
import com.sgs.gpo.integration.trims.TrimsClient;
import com.sgs.trimslocal.facade.model.labsection.rsp.GetLabSectionBaseInfoRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.*;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Service
@Slf4j
@Scope(value = "prototype")
public class TodaySummaryQueryCMD extends BaseCommand<DashboardContext> {

    @Autowired
    private DashboardMapper dashboardMapper;
    @Autowired
    private TrimsClient trimsClient;
    @Autowired
    private ILabService labService;

    @Override
    public BaseResponse validateParam(DashboardContext context) {
        // VP1、校验入参不能为空
        if(Func.isEmpty(context)){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        // VP2、校验用户不能为空
        UserInfo userInfo = context.getUserInfo();
        if(Func.isEmpty(userInfo)){
            return BaseResponse.newFailInstance(ResponseCode.TokenExpire);
        }
        String labCode= userInfo.getCurrentLabCode();
        context.setLabCode(labCode);
        // 根据labCode获取labId
        LabReq labReq = new LabReq();
        labReq.setLabCode(labCode);
        BaseResponse<List<LabBO>> labListRes = labService.search(labReq);
        if(Func.isNotEmpty(labListRes) && Func.isNotEmpty(labListRes.getData())){
            context.setLabId(labListRes.getData().get(0).getLabId());
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse execute(DashboardContext context) {

        Date currentDate = new Date();
        DashBoardQueryRep dashBoardQueryRep = new DashBoardQueryRep();
        dashBoardQueryRep.setLabCode(context.getLabCode());
        dashBoardQueryRep.setLabId(context.getLabId());
        dashBoardQueryRep.setLabSectionBaseIdList(context.getParam().getLabSectionBaseIdList());
        dashBoardQueryRep.setStartDate(getDate(Func.isEmpty(context.getParam().getCurrentDate()) ? currentDate : context.getParam().getCurrentDate(),LocalTime.MIN));
        dashBoardQueryRep.setEndDate(getDate(Func.isEmpty(context.getParam().getCurrentDate()) ? currentDate : context.getParam().getCurrentDate(),LocalTime.MAX));


        TodaySummaryRsp todaySummaryRsp =new TodaySummaryRsp();

        // Order 统计
        List<StatusTodaySummaryRsp> orderStatusCountList = dashboardMapper.selectOrderTodaySummary(dashBoardQueryRep);
        List<Pie> orderStatusList = Lists.newArrayList();
        if(Func.isNotEmpty(orderStatusCountList)){
            orderStatusCountList.forEach(statusTodaySummaryRsp -> {
                Pie pie = Pie.builder()
                        .name(Func.isEmpty(statusTodaySummaryRsp.getStatus())?StringPool.DASH:OrderStatusEnum.getOrderStatus(statusTodaySummaryRsp.getStatus()).name())
                        .value(statusTodaySummaryRsp.getQty())
                        .build();
                orderStatusList.add(pie);
            });
        }
        todaySummaryRsp.setOrderStatusList(orderStatusList);

        // TestMatrix 统计
        List<Pie> matrixStatusList = Lists.newArrayList();
        List<StatusTodaySummaryRsp> matrixStatusCountList = dashboardMapper.selectMatrixTodaySummary(dashBoardQueryRep);
        if(Func.isNotEmpty(matrixStatusCountList)){
            matrixStatusCountList.forEach(statusTodaySummaryRsp -> {
                Pie pie = Pie.builder()
                        .name(Func.isEmpty(statusTodaySummaryRsp.getStatus())? StringPool.DASH :TestLineStatus.findStatus(statusTodaySummaryRsp.getStatus()).name())
                        .value(statusTodaySummaryRsp.getQty())
                        .build();
                matrixStatusList.add(pie);
            });
        }
        todaySummaryRsp.setTestMatrixStatusList(matrixStatusList);

        // Report 统计
        List<StatusTodaySummaryRsp> reportStatusCountList = dashboardMapper.selectReportTodaySummary(dashBoardQueryRep);
        List<Pie> reportStatusList = Lists.newArrayList();
        if(Func.isNotEmpty(reportStatusCountList)){
            reportStatusCountList.forEach(statusTodaySummaryRsp -> {
                Pie pie = Pie.builder()
                        .name(Func.isEmpty(statusTodaySummaryRsp.getStatus())? StringPool.DASH : ReportStatus.getMessage(statusTodaySummaryRsp.getStatus()))
                        .value(statusTodaySummaryRsp.getQty())
                        .code(statusTodaySummaryRsp.getStatus())
                        .build();
                reportStatusList.add(pie);
            });
        }
        todaySummaryRsp.setReportStatusList(reportStatusList);
        // 查询当前Lab下的labSection列表
        Set<Integer> labIds = Sets.newHashSet();
        labIds.add(context.getLabId());
        BaseResponse<List<GetLabSectionBaseInfoRsp>> trimsLabSectionRes = trimsClient.getLabSectionByLabId(labIds);
        if(Func.isNotEmpty(trimsLabSectionRes)&&Func.isNotEmpty(trimsLabSectionRes.getData())){
            List<LabSectionRsp> labSectionList = Func.copy(trimsLabSectionRes.getData(),GetLabSectionBaseInfoRsp.class,LabSectionRsp.class);
            todaySummaryRsp.setLabSectionList(labSectionList);
        }
        return BaseResponse.newSuccessInstance(todaySummaryRsp);
    }

    private Date getDate(Date date,LocalTime localTime){
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(localTime);
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }
}
