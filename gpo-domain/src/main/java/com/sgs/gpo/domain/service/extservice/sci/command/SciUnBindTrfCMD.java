package com.sgs.gpo.domain.service.extservice.sci.command;

import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.model.enums.RefIntegrationChannel;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.model.order.order.OrderBO;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryTrfRelationshipPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.domain.service.extservice.sci.SciTrfContext;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryService;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryTrfRelationshipService;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IGeneralOrderService;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.sci.bo.SciUnBindTrfBO;
import com.sgs.gpo.facade.model.sci.dto.TrfHeaderDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfOrderDTO;
import com.sgs.gpo.facade.model.sci.req.GpoSciTrfSyncReq;
import com.sgs.gpo.integration.sci.SciClient;
import com.sgs.gpo.integration.sci.req.SciHeaderReq;
import com.sgs.gpo.integration.sci.req.SciTrfReq;
import com.sgs.preorder.core.common.StandardObjectType;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: SciUnBindTrfCMD
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2024/1/22 21:27
 */
@Service
@Slf4j
@AllArgsConstructor
public class SciUnBindTrfCMD extends BaseCommand<SciTrfContext<GpoSciTrfSyncReq, SciUnBindTrfBO>> {
    private IOrderDomainService orderDomainService;
    private SciClient sciClient;
    private IEnquiryService enquiryService;
    private IGeneralOrderService generalOrderService;
    private IEnquiryTrfRelationshipService enquiryTrfRelationshipService;

    @Override
    public BaseResponse validateParam(SciTrfContext<GpoSciTrfSyncReq, SciUnBindTrfBO> context) {
        if (Func.isEmpty(context) || Func.isEmpty(context.getParam())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(SciTrfContext<GpoSciTrfSyncReq, SciUnBindTrfBO> context) {
        this.buildDomain(context);
        GpoSciTrfSyncReq gpoSciTrfSyncReq = context.getParam();
        SciTrfReq<SciUnBindTrfBO> sciTrfReq = new SciTrfReq();

        UserInfo user = SystemContextHolder.getUserInfoFillSystem();
        String operatorEmail = user.getEmail();
        String regionAccount = user.getRegionAccount();
        String labCode = user.getCurrentLabCode();
        SciHeaderReq sciHeaderReq = new SciHeaderReq();
        sciHeaderReq.setLabCode(labCode);
        sciHeaderReq.setSystemId(SgsSystem.GPO.getSgsSystemId());
        sciHeaderReq.setOperator(regionAccount);
        sciHeaderReq.setOperationTime(DateUtil.formatDateTime(new Date()));
        sciHeaderReq.setOperatorEmail(operatorEmail);
        sciHeaderReq.setToken(gpoSciTrfSyncReq.getToken());
        sciHeaderReq.setRequestId(Func.randomUUID());
        sciHeaderReq.setProductLineCode(gpoSciTrfSyncReq.getProductLineCode());
        sciTrfReq.setHeader(sciHeaderReq);
        sciTrfReq.setBody(context.getDomain());
        BaseResponse baseResponse = sciClient.unBindTrf(sciTrfReq, gpoSciTrfSyncReq.getSciUrl());
        return baseResponse;
    }

    @Override
    public BaseResponse buildDomain(SciTrfContext<GpoSciTrfSyncReq, SciUnBindTrfBO> context) {
        String sciOrderNo = context.getSciOrderNo();
        GpoSciTrfSyncReq gpoSciTrfSyncReq = context.getParam();
        SciUnBindTrfBO sciUnBindTrfBO = new SciUnBindTrfBO();
        List<TrfHeaderDTO> trfList = new ArrayList<>();
        TrfHeaderDTO trfHeaderDTO = new TrfHeaderDTO();
        trfHeaderDTO.setTrfNo(gpoSciTrfSyncReq.getTrfNo());
        trfHeaderDTO.setRefSystemId(gpoSciTrfSyncReq.getRefSystemId());
        trfList.add(trfHeaderDTO);
        sciUnBindTrfBO.setTrfList(trfList);
        TrfOrderDTO trfOrderDTO = new TrfOrderDTO();
        trfOrderDTO.setOrderNo(context.getSyncTrfRootOrderNo());
        trfOrderDTO.setRealOrderNo(gpoSciTrfSyncReq.getOrderNo());
        sciUnBindTrfBO.setOrder(trfOrderDTO);
        context.setDomain(sciUnBindTrfBO);
        return super.buildDomain(context);
    }

    @Override
    public BaseResponse before(SciTrfContext<GpoSciTrfSyncReq, SciUnBindTrfBO> context) {
        GpoSciTrfSyncReq gpoSciTrfSyncReq = context.getParam();
        String orderNo = gpoSciTrfSyncReq.getOrderNo();
        String orderId = gpoSciTrfSyncReq.getOrderId();
        String trfNo = gpoSciTrfSyncReq.getTrfNo();
        String objectType = gpoSciTrfSyncReq.getObjectType();
        Integer refSystemId = gpoSciTrfSyncReq.getRefSystemId();
        String sciOrderNo = orderNo;
        String sciOrderId = orderId;
        String enquiryNo = "";
        if (StandardObjectType.Enquiry.check(objectType)) {
            context.setSyncTrfRootOrderNo(orderNo);
            enquiryNo = orderNo;
            EnquiryIdReq enquiryIdReq = new EnquiryIdReq();
            enquiryIdReq.setEnquiryNoList(Sets.newHashSet(orderNo));
            List<EnquiryPO> enquiryPOList = enquiryService.select(enquiryIdReq).getData();
            if (Func.isNotEmpty(enquiryPOList)) {
                context.setLabCode(enquiryPOList.get(0).getLabCode());
            }
            context.setEnquiryList(enquiryPOList);
        } else {
            OrderQueryReq orderQueryReq = new OrderQueryReq();
            orderQueryReq.setOrderNoList(Sets.newHashSet(orderNo));
            context.setOrderNoList(Sets.newHashSet(orderNo));
            BaseResponse<List<OrderBO>> orderBoRsp = orderDomainService.queryV1(orderQueryReq);
            List<OrderBO> orderBOList = orderBoRsp.getData();
            if (Func.isNotEmpty(orderBOList)) {
                String labCode = orderBOList.get(0).getLab().getLabCode();
                context.setLabCode(labCode);
            }
            List<GeneralOrderPO> generalOrderPOList = generalOrderService.query2(orderQueryReq).getData();
            if (Func.isNotEmpty(generalOrderPOList)) {
                GeneralOrderPO generalOrderPO = generalOrderPOList.get(0);
                String rootOrderNo = generalOrderPO.getRootOrderNo();
                if (Func.isEmpty(rootOrderNo)) {
                    enquiryNo = generalOrderPO.getEnquiryNo();
                } else {
                    OrderQueryReq orderQueryReq1 = new OrderQueryReq();
                    orderQueryReq1.setOrderNoList(Sets.newHashSet(rootOrderNo));
                    List<GeneralOrderPO> rootOrderInfos = generalOrderService.query2(orderQueryReq1).getData();
                    if (Func.isNotEmpty(rootOrderInfos)) {
                        GeneralOrderPO rootOrder = rootOrderInfos.get(0);
                        enquiryNo = rootOrder.getEnquiryNo();
                    }
                }
                //设置同步sci的rootOrderNo
                String syncTrfRootOrderNo = generalOrderPO.getOrderNo();
                if(Func.isNotEmpty(generalOrderPO.getEnquiryNo())){
                    //查询enquiryNo
                    List<EnquiryTrfRelationshipPO> enquiryTrfRelationshipPOList = null;
                    if (Func.isNotEmpty(enquiryNo)) {
                        EnquiryIdReq enquiryIdReq = new EnquiryIdReq();
                        enquiryIdReq.setEnquiryNoList(Sets.newHashSet(generalOrderPO.getEnquiryNo()));
                        List<EnquiryPO> enquiryPOList = enquiryService.select(enquiryIdReq).getData();
                        enquiryIdReq.setEnquiryIdList(Sets.newHashSet(enquiryPOList.stream().map(EnquiryPO::getId).filter(Func::isNotEmpty).collect(Collectors.toSet())));
                        enquiryTrfRelationshipPOList = enquiryTrfRelationshipService.select(enquiryIdReq).getData();
                    }
                    if (Func.isNotEmpty(enquiryTrfRelationshipPOList)) {
                        boolean existEnquiryTrfRel = enquiryTrfRelationshipPOList.stream().anyMatch(item -> RefIntegrationChannel.check(item.getIntegrationChannel(), RefIntegrationChannel.SCI));
                        if (existEnquiryTrfRel) {
                            syncTrfRootOrderNo = enquiryNo;
                        }
                    }
                }
                context.setSyncTrfRootOrderNo(syncTrfRootOrderNo);
            }
        }
        if (Func.isNotEmpty(enquiryNo)) {
            EnquiryIdReq enquiryIdReq = new EnquiryIdReq();
            enquiryIdReq.setEnquiryNoList(Sets.newHashSet(enquiryNo));
            List<EnquiryPO> enquiryPOList = enquiryService.select(enquiryIdReq).getData();
            context.setEnquiryList(enquiryPOList);
            enquiryIdReq.setEnquiryIdList(Sets.newHashSet(enquiryPOList.stream().map(EnquiryPO::getId).filter(Func::isNotEmpty).collect(Collectors.toSet())));
            BaseResponse<List<EnquiryTrfRelationshipPO>> enquiryTrfRsp = enquiryTrfRelationshipService.select(enquiryIdReq);
            if(enquiryTrfRsp.isSuccess()){
                context.setEnquiryTrfRelationshipPOList(enquiryTrfRsp.getData());
            }
        }
        /**
         * Enquiry
         * Order:查询Order是否有对应的Trf关系，有则取OrderNo,OrderId，没有则取关联的Enquiry的No和Id
         */
        if (!StandardObjectType.Enquiry.check(objectType)) {
            if(Func.isNotEmpty(context.getEnquiryList()) && Func.isNotEmpty(context.getEnquiryTrfRelationshipPOList())){
                EnquiryTrfRelationshipPO enquiryTrfRelationshipPO = context.getEnquiryTrfRelationshipPOList().stream().filter(item -> Func.isNotEmpty(item.getRefSystemId()) && Func.equalsSafe(item.getRefSystemId(), refSystemId)).findAny().orElse(null);
                if(Func.isNotEmpty(enquiryTrfRelationshipPO) && Func.equalsSafe(trfNo,enquiryTrfRelationshipPO.getRefNo())){
                    sciOrderNo = context.getEnquiryList().get(0).getEnquiryNo();
                    sciOrderId = context.getEnquiryList().get(0).getId();
                }
            }
        }
        context.setSciOrderNo(sciOrderNo);
        context.setSciOrderId(sciOrderId);
        return super.before(context);
    }
}
