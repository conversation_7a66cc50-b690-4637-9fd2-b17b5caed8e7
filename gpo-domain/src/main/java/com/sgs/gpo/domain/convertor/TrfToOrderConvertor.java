package com.sgs.gpo.domain.convertor;
import java.util.*;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.enums.CustomerType;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.ReportLanguage;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.StringPool;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.util.FieldUtils;
import com.sgs.gpo.core.util.StandardCharPool;
import com.sgs.gpo.facade.model.extsystem.sci.req.GetSCICustomerInfoReq;
import com.sgs.gpo.facade.model.sci.dto.*;
import com.sgs.preorder.facade.model.dto.dff.DFFFormRspDTO;
import com.sgs.preorder.facade.model.dto.order.OrderDetailDto;
import com.sgs.preorder.facade.model.info.*;
import com.sgs.preorder.facade.model.info.user.UserLabBuInfo;
import com.sgs.preorder.facade.model.rsp.ProductSampleRsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public abstract class TrfToOrderConvertor {

    /**
     * @param trfDTO
     * @return
     */
    public static OrderDetailDto convertToOrderDetail(TrfDTO trfDTO, GetSCICustomerInfoReq getSCICustomerInfoReq) {
        OrderDetailDto orderDetailDto = new OrderDetailDto();

        Assert.notNull(trfDTO);
        Assert.notNull(trfDTO.getHeader(), "请检查Trf Header");
        Assert.notNull(trfDTO.getHeader().getLab(), "请检查Trf Lab");


        //Lab
        orderDetailDto.setLab(convertOrderLab(trfDTO.getHeader().getLab()));

        // Header
        orderDetailDto.setHeaders(convertHeader(trfDTO.getHeader()));

        // Customer
        convertCustomer(orderDetailDto, trfDTO.getCustomerList());
        // Dff Sample
        orderDetailDto.setProductSampleRspList(convertDffInfo(trfDTO,getSCICustomerInfoReq));
        // DFF From 外层数据优先使用英文
        orderDetailDto.setProduct(convertProductInfo(orderDetailDto.getProductSampleRspList()));

        if (trfDTO.getHeader() != null) {
            orderDetailDto.setTestRequest(convertServiceRequirement(trfDTO.getServiceRequirement()));
        }

        return orderDetailDto;
    }


    private static UserLabBuInfo convertOrderLab(TrfLabDTO trfLabDTO){
        UserLabBuInfo userLabBuInfo = new UserLabBuInfo();
        if(Func.isNotEmpty(trfLabDTO)){
            CopyOptions copyOptions = CopyOptions.create().setIgnoreCase(true).ignoreNullValue();
            BeanUtil.copyProperties(trfLabDTO, userLabBuInfo, copyOptions);
        }
        return userLabBuInfo;
    }
    /**
     * @param header
     * @return
     */
    private static OrderHeaderInfo convertHeader(TrfHeaderDTO header) {
        OrderHeaderInfo headers = new OrderHeaderInfo();
        if (header == null) {
            return headers;
        }
        // POSL-5784 保存SCI接口返回的TrfNo
        headers.setTrfNo(header.getTrfNo());
        headers.setRefSystemId(header.getRefSystemId());
//        headers.setReferenceNo(header.getTrfNo());
//        headers.setReferenceId(header.getRefSystemId());
//        headers.setTrfReportLevel(header.getTrfReportLevel());

        ReferenceInfo referenceInfo = new ReferenceInfo();
        referenceInfo.setReferenceNo(header.getTrfNo());
        referenceInfo.setRefSystemId(header.getRefSystemId());
        headers.setReferences(Lists.newArrayList(referenceInfo));
        if (Func.toInt(header.getServiceType()) > 0) {
            headers.setServiceType(Func.toInt(header.getServiceType()));
            headers.setServiceLevel(Func.toInt(header.getServiceType()));
        }

        if (Func.toInt(header.getSelfTestFlag()) > 0) {
            headers.setSelfTestFlag(Func.toInt(header.getSelfTestFlag()));
        }

        if (header.getTrfSubmissionDate() != null) {
            headers.settRFSubmissionDate(header.getTrfSubmissionDate());
        }
        if (header.getLab() != null) {
            headers.setLabCode(header.getLab().getLabCode());
        }
//        headers.setCustomerRemark(header.getCustomerRemark());
        return headers;
    }

    /**
     * @param orderDetailDto
     * @param customerList
     */
    private static void convertCustomer(OrderDetailDto orderDetailDto, List<TrfCustomerDTO> customerList) {
        if (CollectionUtil.isEmpty(customerList)) {
            return;
        }
        Map<Integer, TrfCustomerDTO> customerDTOMap = customerList.stream()
                .filter(custom -> Func.toInt(custom.getCustomerUsage()) > 0)
                .collect(Collectors.toMap(TrfCustomerDTO::getCustomerUsage, Function.identity(), (k1, k2) -> k1));

        for (Integer usage : customerDTOMap.keySet()) {
            CustomerType customerUsage = CustomerType.findStatus(usage);
            if (customerUsage == null) {
                log.error("Customer匹配错误，usage：{}", usage);
                continue;
            }
            TrfCustomerDTO trfCustomerDTO = customerDTOMap.get(usage);
            switch (customerUsage) {
                case Applicant:
                    orderDetailDto.setApplicant(buildOrderCustomerDto(trfCustomerDTO));
                    break;
                case Payer:
                    orderDetailDto.setPayer(buildOrderCustomerDto(trfCustomerDTO));
                    break;
                case Buyer:
                    orderDetailDto.setBuyer(buildOrderCustomerDto(trfCustomerDTO));
                    break;
                case Agent:
                    orderDetailDto.setAgent(buildOrderCustomerDto(trfCustomerDTO));
                    break;
                case Manufacture:
                    orderDetailDto.setManufacture(buildOrderCustomerDto(trfCustomerDTO));
                    break;
                case Supplier:
                    orderDetailDto.setSupplier(buildOrderCustomerDto(trfCustomerDTO));
                    break;
                case OEM:
                    orderDetailDto.setOem(buildOrderCustomerDto(trfCustomerDTO));
                    break;
                case SUBCONTRACT_FROM:
                    orderDetailDto.setSubcontractFrom(buildOrderCustomerDto(trfCustomerDTO));
                    break;
                default:
                    log.error("Customer匹配错误，usage：{}", customerUsage.getCode());
                    break;
            }
        }
    }

    /**
     * TODO Customer 暂时只用于校验  赋值
     *
     * @param trfCustomerDTO
     * @return
     */
    private static CustomerInfo buildOrderCustomerDto(TrfCustomerDTO trfCustomerDTO) {
        CustomerInfo customerInfo = new CustomerInfo();
        customerInfo.setCustomerId(trfCustomerDTO.getCustomerId());
//        customerInfo.setCustomerGroupId(trfCustomerDTO.getCus);
//        customerInfo.setCustomerAddressCN(trfCustomerDTO.get);
        customerInfo.setCustomerAddressEN(trfCustomerDTO.getCustomerAddress());
//        customerInfo.setCustomerNameCN();
//        customerInfo.setCustomerNameEN(trfCustomerDTO.getCustomerName());
//        customerInfo.setContactPersonEmail(trfCustomerDTO.get);
//        customerInfo.setContactPersonFax();
//        customerInfo.setContactPersonPhone1();
//        customerInfo.setContactPersonName();
//        customerInfo.setContactPersonRemark();
//        customerInfo.setContactPersonPhone2();
//        customerInfo.setCustomerCredit(trfCustomerDTO);
        CustomerType customerUsage = CustomerType.findStatus(trfCustomerDTO.getCustomerUsage());
        customerInfo.setCustomerUsage(customerUsage.getCode());
//        customerInfo.setAccountID(trfCustomerDTO.get);
        customerInfo.setBossNumber(trfCustomerDTO.getBossNo());
//        customerInfo.setContactAddressID();
        customerInfo.setBuyerGroup(trfCustomerDTO.getCustomerGroupCode());
        //General的客户按照空处理
        if(!Func.equalsSafe(trfCustomerDTO.getCustomerGroupCode(),"General")){
            customerInfo.setBuyerGroup(trfCustomerDTO.getCustomerGroupCode());
        }
//        customerInfo.setBuyerGroupName(trfCustomerDTO.getCustomerName());
////        customerInfo.setSupplierNo();
////        customerInfo.setLogoCloudID();
////        customerInfo.setOrganizationName();
////        customerInfo.setIsAsApplicant();
//        customerInfo.setReportDeliveredTo();
//        customerInfo.setFailedReportDeliveredTo();
//        customerInfo.setBossSiteUseID();
//        customerInfo.setBossContactID();
//        customerInfo.setBossLocationCode();
//        customerInfo.setMonthlyPayment();
//        customerInfo.setPrimaryFlag();
//        customerInfo.setModifiedBy();
//        customerInfo.setModifiedDate();
//        customerInfo.setOldVersionId();
//        customerInfo.setPaymentTermName();
//        customerInfo.setCustomerLock();
//        customerInfo.setEnquiryId();

        return customerInfo;
    }


    /**
     * @param trfDTO
     * @return
     */
    private static List<ProductSampleRsp> convertDffInfo(TrfDTO trfDTO, GetSCICustomerInfoReq getSCICustomerInfoReq) {
        List<ProductSampleRsp> productSampleRsps = Lists.newArrayList();

        if (trfDTO.getProduct() == null && CollectionUtil.isEmpty(trfDTO.getSampleList())) {
            return getSCICustomerInfoReq.getProductSampleRsps();
        }

        Assert.notNull(trfDTO.getServiceRequirement(), "请检查ServiceRequirement 数据");
        Assert.notNull(trfDTO.getServiceRequirement().getReport(), "请检查ServiceRequirement.Report 数据");
        Assert.notNull(trfDTO.getServiceRequirement().getReport().getReportLanguage(), "请检查报告语音");

        Integer languageId = trfDTO.getServiceRequirement().getReport().getReportLanguage();
        TrfProductDTO productDTO = trfDTO.getProduct();
        List<TrfSampleDffDTO> sampleList = trfDTO.getSampleList();

        ReportLanguage name = ReportLanguage.findName(String.valueOf(languageId));
        Assert.notNull(name, "请检查报告语言");

        List<LanguageType> languageType = LanguageType.getLanguageType(name.getLanguageId());
        for (LanguageType language : languageType) {
            if (language == LanguageType.EnglishAndChinese) {
                // 不需要单独处理 EnglishAndChinese
                continue;
            }

            ProductSampleRsp productSampleRsp = new ProductSampleRsp();
            productSampleRsp.setLanguageCode(language.getCode());
            productSampleRsp.setLanguageID(language.getLanguageId());

            ProductSampleRsp dbProductSampleRsp = getSCICustomerInfoReq.getProductSampleRsps().stream().filter(e -> Func.equalsSafe(e.getLanguageID(),language.getLanguageId())).findAny().orElse(null);
            if(Func.isNotEmpty(dbProductSampleRsp)){
                // 设置 Product
                productSampleRsp.setProduct(setProductInfo(productDTO, language, dbProductSampleRsp.getProduct(), getSCICustomerInfoReq.getSgsMartDffMap()));

                // 设置 ProductSample
                productSampleRsp.setProductSamples(setProductSampleInfo(sampleList, language, dbProductSampleRsp.getProductSamples()));

                productSampleRsps.add(productSampleRsp);
            }
        }

        return productSampleRsps;
    }

    /**
     * @param trfProduct
     * @param language
     * @return
     */
    private static ProductInfo setProductInfo(TrfProductDTO trfProduct, LanguageType language, ProductInfo productInfo,Map<String, DFFFormRspDTO> sgsmartDffMap) {

        // Product
        if (Func.isEmpty(trfProduct) || Func.isEmpty(trfProduct.getProductAttrList())) {
            return productInfo;
        }
        if (Func.isEmpty(trfProduct.getTemplateId())) {
            return productInfo;
        }
        SortedMap<String, Field> orderDeclaredFields = FieldUtils.getDeclaredFields(productInfo);
        List<TrfProductAttrDTO> productAttrList = trfProduct.getProductAttrList();
        // 将 FieldCode 变为小写
        Map<String, TrfProductAttrDTO> productAttrMap = Maps.newHashMap();
        for (TrfProductAttrDTO attrDTO : productAttrList) {
            if (Func.isEmpty(attrDTO.getLabelCode()) || Func.isEmpty(attrDTO.getLabelValue())) {
                continue;
            }
            String key = attrDTO.getLabelCode().toLowerCase();
            if (productAttrMap.containsKey(key)) {
                continue;
            }
            productAttrMap.put(key, attrDTO);
        }

        //对比进来的数据 与 模板上的数据 补充缺失的数据
        for(DFFFormRspDTO dffFormRsp : sgsmartDffMap.values()){
            String key = dffFormRsp.getFieldCode().toLowerCase();
            if(productAttrMap.containsKey(key)){
                continue;
            }
            TrfProductAttrDTO trfProductAttrDTO = new TrfProductAttrDTO();
            trfProductAttrDTO.setLabelCode(dffFormRsp.getFieldCode());
            trfProductAttrDTO.setLabelValue(StringPool.EMPTY);
            List<TrfProductAttrLangDTO> languageList = new ArrayList<>();
            //构造中英文
            TrfProductAttrLangDTO en = new TrfProductAttrLangDTO();
            en.setLanguageId(LanguageType.English.getLanguageId());
            en.setLabelValue(StringPool.EMPTY);
            languageList.add(en);
            TrfProductAttrLangDTO cn = new TrfProductAttrLangDTO();
            cn.setLanguageId(LanguageType.Chinese.getLanguageId());
            cn.setLabelValue(StringPool.EMPTY);
            languageList.add(cn);
            trfProductAttrDTO.setLanguageList(languageList);
            productAttrMap.put(key, trfProductAttrDTO);
        }

        if (Func.isEmpty(productAttrMap)) {
            return productInfo;
        }

        for (String labelCode : orderDeclaredFields.keySet()) {
            if (!productAttrMap.containsKey(labelCode.toLowerCase())) {
                continue;
            }
            String labValue = StringUtils.EMPTY;
            TrfProductAttrDTO productAttrDTO = productAttrMap.get(labelCode.toLowerCase());

            // SCI返回的标准对象中，language 中包含所有的中英文数据，所以直接使用LanguageList 中的数据
            if (CollectionUtil.isEmpty(productAttrDTO.getLanguageList())) {
                continue;
            }
            Map<Integer, TrfProductAttrLangDTO> langAttrMaps = productAttrDTO.getLanguageList().stream().collect(Collectors.toMap(TrfProductAttrLangDTO::getLanguageId, Function.identity(), (k1, k2) -> k1));
            if (!langAttrMaps.containsKey(Func.toInt(language.getLanguageId()))) {
                continue;
            }
            TrfProductAttrLangDTO langDTO = langAttrMaps.get(Func.toInt(language.getLanguageId()));
            labValue = langDTO.getLabelValue();
            try {
                Field targetField = orderDeclaredFields.get(labelCode.toLowerCase());
                if (targetField == null) {
                    continue;
                }
                targetField.setAccessible(true);

                targetField.set(productInfo, labValue);
            } catch (Exception e) {
                continue;
            }
        }
        productInfo.setdFFFormID(trfProduct.getTemplateId());
        productInfo.setLanguageID(language.getLanguageId());

        return productInfo;
    }

    /**
     * @param sampleList
     * @param language
     */
    private static List<ProductSampleInfo> setProductSampleInfo(List<TrfSampleDffDTO> sampleList, LanguageType language, List<ProductSampleInfo> productSampleList) {

        // Product
        if (CollectionUtil.isEmpty(sampleList)) {
            return productSampleList;
        }
        Map<String, TrfSampleDffDTO> sampleMap = new HashMap<>();
        for(TrfSampleDffDTO trfSampleDffDTO : sampleList){
            sampleMap.put(trfSampleDffDTO.getProductItemNo(),trfSampleDffDTO);
        }
        Map<String, ProductSampleInfo> dbSampleMap = new HashMap<>();
        for(ProductSampleInfo productSampleInfo : productSampleList){
            dbSampleMap.put(productSampleInfo.getProductItemNo(),productSampleInfo);
        }
        List<ProductSampleInfo> productSamples = Lists.newArrayList();

        //以GPO Sample信息为准 对每一个Sample进行更新
        for (String productItemNo : dbSampleMap.keySet()) {
            TrfSampleDffDTO trfSampleDffDTO = sampleMap.get(productItemNo);
            ProductSampleInfo productSampleInfo = dbSampleMap.get(productItemNo);
            if(Func.isNotEmpty(trfSampleDffDTO)){
                SortedMap<String, Field> orderDeclaredFields = FieldUtils.getDeclaredFields(productSampleInfo);
                String templateId = trfSampleDffDTO.getTemplateId();
                List<TrfSampleAttrDTO> trfSampleAttrDTOS = trfSampleDffDTO.getSampleAttrList();
                // 将 FieldCode 变为小写
                Map<String, TrfSampleAttrDTO> productAttrMap = Maps.newHashMap();
                for (TrfSampleAttrDTO attrDTO : trfSampleAttrDTOS) {
                    if (Func.isEmpty(attrDTO.getLabelCode()) || Func.isEmpty(attrDTO.getLabelValue())) {
                        continue;
                    }
                    String key = attrDTO.getLabelCode().toLowerCase();
                    if (productAttrMap.containsKey(key)) {
                        continue;
                    }
                    productAttrMap.put(key, attrDTO);
                }
                if (Func.isNotEmpty(productAttrMap)) {
                    for (String labelCode : orderDeclaredFields.keySet()) {
                        if (!productAttrMap.containsKey(labelCode.toLowerCase())) {
                            continue;
                        }
                        String labValue = StringUtils.EMPTY;
                        TrfSampleAttrDTO productAttrDTO = productAttrMap.get(labelCode.toLowerCase());
                        switch (language) {
                            // 英文场景下 直接取 TrfProductDTO productAttrList 下的数据
                            case English:
                                if (StringUtils.isBlank(productAttrDTO.getLabelValue())) {
                                    continue;
                                }
                                labValue = productAttrDTO.getLabelValue();
                                break;

                            case Chinese:
                                if (CollectionUtil.isEmpty(productAttrDTO.getLanguageList())) {
                                    continue;
                                }
                                TrfProductAttrLangDTO trfProductAttrLangDTO = productAttrDTO.getLanguageList().stream().filter(item -> NumberUtil.equals(language.getLanguageId(), item.getLanguageId())).findFirst().orElse(null);
                                if (trfProductAttrLangDTO == null || StringUtils.isBlank(trfProductAttrLangDTO.getLabelValue())) {
                                    continue;
                                }
                                labValue = trfProductAttrLangDTO.getLabelValue();
                                break;
                        }

                        try {
                            Field targetField = orderDeclaredFields.get(labelCode.toLowerCase());
                            if (targetField == null) {
                                continue;
                            }
                            if(Func.isNotEmpty(targetField.get(productSampleInfo))){
                                continue;
                            }
                            targetField.setAccessible(true);

                            if(Func.equalsSafe(labelCode.toLowerCase(), Constants.DFF.FILED.NO_OF_SAMPLE.toLowerCase())){
                                Integer sampleQty = Integer.valueOf(labValue);
                                targetField.set(productSampleInfo, sampleQty);
                            } else {
                                targetField.set(productSampleInfo, labValue);
                            }
                        } catch (Exception e) {
                            continue;
                        }
                    }
                }
                productSampleInfo.setdFFFormID(templateId);
            }
            productSampleInfo.setLanguageID(language.getLanguageId());
            productSamples.add(productSampleInfo);
        }
        return productSamples;
    }


    /**
     * @param sampleDffDTOS
     * @return
     */
    private static List<ProductSampleInfo> convertDffGrid(List<TrfSampleDffDTO> sampleDffDTOS) {
        if (CollectionUtil.isEmpty(sampleDffDTOS)) {
            return null;
        }
        List<ProductSampleInfo> productSampleInfos = Lists.newArrayList();

        return productSampleInfos;
    }

    /**
     * @param productSampleRspList
     * @return
     */
    private static ProductInfo convertProductInfo(List<ProductSampleRsp> productSampleRspList) {
        if (CollectionUtil.isEmpty(productSampleRspList)) {
            return null;
        }
        Map<Integer, ProductSampleRsp> langProductMaps = productSampleRspList.stream().collect(Collectors.toMap(ProductSampleRsp::getLanguageID, Function.identity(), (k1, k2) -> k1));

        if (langProductMaps.containsKey(LanguageType.English.getLanguageId())) {
            ProductSampleRsp productSampleRsp = langProductMaps.get(LanguageType.English.getLanguageId());
            return productSampleRsp.getProduct();
        }

        ProductSampleRsp productSampleRsp = langProductMaps.get(LanguageType.Chinese.getLanguageId());
        return productSampleRsp.getProduct();
    }

    /**
     * https://sgs-digitalization.feishu.cn/wiki/COJQwXDs3ifr5pktHmpc0LL8nYg
     *
     * @param serviceRequirementDTO
     * @return
     */
    private static TestRequestInfo convertServiceRequirement(TrfServiceRequirementDTO serviceRequirementDTO) {
        if (serviceRequirementDTO == null) {
            return null;
        }
        TestRequestInfo testRequestInfo = new TestRequestInfo();


        if (serviceRequirementDTO.getReport() != null) {
            TrfServiceRequirementReportDTO report = serviceRequirementDTO.getReport();
            if(Func.isNotEmpty(report.getReportLanguage())){
                testRequestInfo.setReportLanguage(Func.toStr(report.getReportLanguage()));
            }
            List<String> reportHeaderList = new ArrayList<>();
            List<String> reportAddressList = new ArrayList<>();
            List<TrfServiceRequirementLangDTO> languageList = report.getLanguageList();
            if(Func.isNotEmpty(languageList)){
                TrfServiceRequirementLangDTO trfServiceRequirementLangDTO = languageList.stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese)).findAny().orElse(null);
                if(Func.isNotEmpty(trfServiceRequirementLangDTO)){
                    String reportHeaderCn = trfServiceRequirementLangDTO.getReportHeader();
                    String reportAddressCn = trfServiceRequirementLangDTO.getReportAddress();
                    if(Func.isNotEmpty(reportHeaderCn)){
                        reportHeaderList.add(reportHeaderCn);
                    }
                    if(Func.isNotEmpty(reportAddressCn)){
                        reportAddressList.add(reportAddressCn);
                    }
                }
            }
            String reportHeaderEn = report.getReportHeader();
            String reportAddressEn = report.getReportAddress();


            if (Func.isNotEmpty(reportHeaderEn)) {
                reportHeaderList.add(reportHeaderEn);
            }
            if (Func.isNotEmpty(reportAddressEn)) {
                reportAddressList.add(reportAddressEn);
            }

            testRequestInfo.setReportHeader(Func.join(reportHeaderList, StandardCharPool.GPO_SEPARATOR));
            testRequestInfo.setReportDeliveredTo(Func.join(reportAddressList, StandardCharPool.GPO_SEPARATOR));
            testRequestInfo.setNeedConclusion(report.getNeedConclusion());
            testRequestInfo.setTakePhotoFlag(Func.toInteger(report.getNeedPhoto(),0));

            if (report.getSoftcopy() != null) {
                TrfDeliveryDTO softcopy = report.getSoftcopy();
                testRequestInfo.setSoftCopyToOther(softcopy.getDeliveryTo());
            }
            if (report.getHardcopy() != null) {
                TrfDeliveryDTO hardcopy = report.getHardcopy();
                testRequestInfo.setHardCopyToOther(hardcopy.getDeliveryTo());
            }
        }

        if (serviceRequirementDTO.getSample() != null) {
            TrfServiceRequirementSampleDTO sample = serviceRequirementDTO.getSample();
            if (sample.getReturnTestSample() != null) {
                TrfDeliveryDTO returnTestSample = sample.getReturnTestSample();
                testRequestInfo.setReturnTestedSampleFlag(Func.toInteger(returnTestSample.getRequired(),0));
                testRequestInfo.setReturnTestedSampleRemark(returnTestSample.getDeliveryTo());
            }
            if (sample.getReturnResidueSample() != null) {
                TrfDeliveryDTO returnResidueSample = sample.getReturnResidueSample();
                testRequestInfo.setReturnTestedSampleFlag(Func.toInteger(returnResidueSample.getRequired(),0));
                testRequestInfo.setReturnResidueSampleRemark(returnResidueSample.getDeliveryTo());
            }
        }
        if (serviceRequirementDTO.getInvoice() != null) {

        }
        testRequestInfo.setOtherRequirements(serviceRequirementDTO.getOtherRequestRemark());

        return testRequestInfo;
    }


}
