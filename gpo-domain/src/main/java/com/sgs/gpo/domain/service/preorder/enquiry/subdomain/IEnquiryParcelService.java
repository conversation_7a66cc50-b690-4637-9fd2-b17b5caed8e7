package com.sgs.gpo.domain.service.preorder.enquiry.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryParcelPO;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;

import java.util.List;

public interface IEnquiryParcelService extends IService<EnquiryParcelPO> {
    List<EnquiryParcelPO> select(EnquiryIdReq enquiryIdReq);
}
