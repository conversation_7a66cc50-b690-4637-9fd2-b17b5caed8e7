package com.sgs.gpo.domain.service.otsnotes.testdata.impl;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testdata.TestDataHeaderMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testdata.TestDataHeaderPO;
import com.sgs.gpo.domain.service.otsnotes.testdata.ITestDataHeaderService;
import com.sgs.gpo.facade.model.otsnotes.testdata.dto.TLDataEntryDTO;
import com.sgs.gpo.facade.model.otsnotes.testdata.req.TLDataHeaderQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testdata.req.TLDataHeaderStatusUpdateReq;
import com.sgs.gpo.facade.model.otsnotes.testdata.rsp.TLDataHeaderSaveCheckRsp;
import com.sgs.gpo.integration.dataentry.DataEntryClient;
import com.sgs.gpo.integration.dataentry.req.SaveTestHeaderReq;
import lombok.extern.log4j.Log4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Service
@Log4j
public class TestDataHeaderServiceImpl extends ServiceImpl<TestDataHeaderMapper, TestDataHeaderPO> implements ITestDataHeaderService {
    @Autowired
    private DataEntryClient dataEntryClient;
    @Override
    public BaseResponse<Long> selectTlDataEntryCount(TLDataHeaderQueryReq tLDataHeaderQueryReq) {
        return BaseResponse.newSuccessInstance(baseMapper.selectTLDataEntryCount(tLDataHeaderQueryReq));
    }

    @Override
    public BaseResponse<List<TLDataEntryDTO>> selectTlDataEntryList(Page page, TLDataHeaderQueryReq tlDataHeaderQueryReq) {
        if(Func.isEmpty(tlDataHeaderQueryReq)){
            return BaseResponse.newFailInstance("common.param.miss",new Object[]{Constants.TERM.REQUEST.getCode()});
        }
        return BaseResponse.newSuccessInstance(baseMapper.selectTLDataEntryList(page, tlDataHeaderQueryReq));
    }

    @Override
    public BaseResponse<Boolean> updateTestDataHeaderStatus(TLDataHeaderStatusUpdateReq statusUpdateReq) {
        Assert.isTrue(Func.isNotEmpty(statusUpdateReq),"common.param.miss",new Object[]{Constants.TERM.REQUEST});
        Assert.isTrue(Func.isNotEmpty(statusUpdateReq.getIdList()),"common.param.miss",new Object[]{"ID"});
        Assert.isTrue(Func.isNotEmpty(statusUpdateReq.getNewStatus()),"common.param.miss",new Object[]{"NewStatus"});
        Set<String> idList = statusUpdateReq.getIdList();
        LambdaUpdateChainWrapper<TestDataHeaderPO> updateChainWrapper = new LambdaUpdateChainWrapper<>(getBaseMapper());
        updateChainWrapper.set(TestDataHeaderPO::getStatus, statusUpdateReq.getNewStatus());
        updateChainWrapper.in(TestDataHeaderPO::getId, idList);
        updateChainWrapper.update();
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse<Boolean> saveTestDataHeader(List<SaveTestHeaderReq> saveTestHeaderReqList) {
        if(Func.isEmpty(saveTestHeaderReqList)){
            return BaseResponse.newFailInstance("common.param.miss",new Object[]{"ReportId"});
        }
        dataEntryClient.saveTestHeader(saveTestHeaderReqList, SystemContextHolder.getLabCode(),SystemContextHolder.getSgsToken());
        return BaseResponse.newSuccessInstance(true);
    }
    @Override
    public BaseResponse<List<TLDataHeaderSaveCheckRsp>> saveCheckTestDataHeader(List<SaveTestHeaderReq> saveTestHeaderReqList) {
        if(Func.isEmpty(saveTestHeaderReqList)){
            return BaseResponse.newFailInstance("common.param.miss",new Object[]{"ReportId"});
        }
        return dataEntryClient.testHeaderSaveCheck(saveTestHeaderReqList, SystemContextHolder.getLabCode(),SystemContextHolder.getSgsToken());
    }
}
