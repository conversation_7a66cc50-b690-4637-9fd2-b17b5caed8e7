package com.sgs.gpo.domain.service.otsnotes.testmatrix.command;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.model.order.v2.TestLineIdRelBO;
import com.sgs.framework.model.test.condition.ConditionLanguageBO;
import com.sgs.framework.model.test.condition.v2.ConditionBO;
import com.sgs.framework.model.test.condition.v2.ConditionHeaderBO;
import com.sgs.framework.model.test.condition.v2.ConditionIdBO;
import com.sgs.framework.model.test.testline.v2.TestLineConditionGroupIdBO;
import com.sgs.framework.model.test.testmatrix.v2.*;
import com.sgs.framework.model.test.testsample.v2.TestSampleIdBO;
import com.sgs.framework.model.test.testscheme.TestSchemeIdBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.condition.TestConditionInstanceLanguagePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.condition.TestConditionInstancePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.ProductAttributeInstancePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.TestMatrixExtPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.TestMatrixPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.TestSamplePO;
import com.sgs.gpo.domain.otsnotes.common.service.condition.ITestConditionService;
import com.sgs.gpo.domain.otsnotes.common.subdomain.ITestConditionLanguageService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.context.TestMatrixContext;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.IProductAttributeService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixExtService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixService;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.ITestSampleService;
import com.sgs.gpo.facade.model.otsnotes.testline.req.QueryTestConditionReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.ProductAttributeReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixExtQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 查询返回TestMatrix标准结构
 */
@Service
@Slf4j
public class TestMatrixQueryCMD extends BaseCommand<TestMatrixContext<TestMatrixQueryReq, TestMatrixBO>> {

    @Autowired
    private ITestMatrixService testMatrixService;
    @Autowired
    private IProductAttributeService productAttributeService;
    @Autowired
    private ITestConditionService testConditionService;
    @Autowired
    private ITestConditionLanguageService testConditionLanguageService;
    @Autowired
    private ITestSampleService testSampleService;
    @Autowired
    private ITestMatrixExtService testMatrixExtService;


    @Override
    public BaseResponse validateParam(TestMatrixContext<TestMatrixQueryReq, TestMatrixBO> context) {
        Assert.isTrue(Func.isNotEmpty(context.getParam()), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(TestMatrixContext<TestMatrixQueryReq, TestMatrixBO> context) {
        // 查询Matrix信息
        BaseResponse<List<TestMatrixPO>> testMatrixResponse = testMatrixService.queryList(context.getParam());
        if (testMatrixResponse.isSuccess() && Func.isNotEmpty(testMatrixResponse.getData())) {
            context.setTestMatrixPOList(testMatrixResponse.getData());
            Set<String> testMatrixSets = testMatrixResponse.getData().stream().map(TestMatrixPO::getId).collect(Collectors.toSet());
            // By Matrix 查询PA数据
            ProductAttributeReq productAttributeReq = new ProductAttributeReq();
            productAttributeReq.setTestMatrixIdList(testMatrixSets);
            BaseResponse<List<ProductAttributeInstancePO>> productAttributeResponse = productAttributeService.query(productAttributeReq);
            if (productAttributeResponse.isSuccess() && Func.isNotEmpty(productAttributeResponse.getData())) {
                context.setProductAttributePOList(productAttributeResponse.getData());
            }
            // By Order 查询Condition数据
            //TODO 调用Cidtion.QueryBO
            QueryTestConditionReq queryTestConditionReq = new QueryTestConditionReq();
            queryTestConditionReq.setTestMatrixIdList(testMatrixSets);
            List<TestConditionInstancePO> conditionBOList = testConditionService.query(queryTestConditionReq);
            if (Func.isNotEmpty(conditionBOList)) {
                context.setTestConditionInstancePOList(conditionBOList);
                // 多语言查询
                queryTestConditionReq.setTestConditionInstanceIdList(conditionBOList.stream().map(TestConditionInstancePO::getId).collect(Collectors.toSet()));
                BaseResponse<List<TestConditionInstanceLanguagePO>> testConditionInstanceLanguageRes = testConditionLanguageService.query(queryTestConditionReq);
                if (testConditionInstanceLanguageRes.isSuccess() && Func.isNotEmpty(testConditionInstanceLanguageRes.getData())) {
                    context.setTestConditionInstanceLanguagePOList(testConditionInstanceLanguageRes.getData());
                }
            }
            // 查询Sample信息
            TestSampleQueryReq testSampleQueryReq = new TestSampleQueryReq();
            Set<String> testSampleInstanceIdList = testMatrixResponse.getData().stream().map(TestMatrixPO::getTestSampleID).collect(Collectors.toSet());
            testSampleQueryReq.setTestSampleInstanceIdList(testSampleInstanceIdList);
            BaseResponse<List<TestSamplePO>> testSampleResponse = testSampleService.select(testSampleQueryReq);
            if (Func.isNotEmpty(testSampleResponse.getData())) {
                context.setTestSampleList(testSampleResponse.getData());
            }
            // 查询MatrixExt信息
            Set<String> testMatrixIdList = testMatrixResponse.getData().stream().map(TestMatrixPO::getId).collect(Collectors.toSet());
            TestMatrixExtQueryReq testMatrixExtQueryReq = new TestMatrixExtQueryReq();
            testMatrixExtQueryReq.setTestMatrixIdList(testMatrixIdList);
            List<TestMatrixExtPO> testMatrixExtPOList = testMatrixExtService.query(testMatrixExtQueryReq);
            if(Func.isNotEmpty(testMatrixExtPOList)){
                context.setTestMatrixExtList(testMatrixExtPOList);
            }
            // TODO 补充Analyte
        }
        return super.before(context);
    }

    @Override
    public BaseResponse<List<TestMatrixBO>> buildDomain(TestMatrixContext<TestMatrixQueryReq, TestMatrixBO> context) {
        List<TestMatrixBO> testMatrixList = Lists.newArrayList();
        if (Func.isNotEmpty(context.getTestMatrixPOList())) {
            // 组装BO的结构
            context.getTestMatrixPOList().stream().forEach(testMatrix -> {
                TestMatrixBO testMatrixBO = new TestMatrixBO();
                //1.id
                TestMatrixIdBO id = new TestMatrixIdBO();
                id.setTestMatrixId(testMatrix.getId());
                id.setTestMatrixNo(testMatrix.getMatrixNo());
                testMatrixBO.setId(id);
                //2.relationship
                assembleRelationship(testMatrixBO, testMatrix,context.getTestSampleList());
                //3.header
                TestMatrixHeaderBO header = new TestMatrixHeaderBO();
                header.setTestMatrixGroupId(testMatrix.getMatrixGroupId());
                header.setMatrixConfirmDate(testMatrix.getMatrixConfirmDate());
                header.setExtData(testMatrix.getExtData());
                testMatrixBO.setHeader(header);
                //4.productAttributeList
                assembleProductAttribute(testMatrixBO, context.getProductAttributePOList());
                //5.conditionList
                assembleCondition(testMatrixBO, context.getTestConditionInstancePOList(), context.getTestConditionInstanceLanguagePOList());
                // testMatrixExt信息
                assembleMatrixExt(testMatrixBO,context.getTestMatrixExtList());
                if (Func.isNotEmpty(testMatrix.getActiveIndicator()) && Func.equalsSafe(testMatrix.getActiveIndicator(), ActiveType.Enable.getStatus())) {
                    testMatrixBO.setActiveIndicator(ActiveType.Enable.getStatus());
                } else {
                    testMatrixBO.setActiveIndicator(ActiveType.Disable.getStatus());
                }
                testMatrixList.add(testMatrixBO);
            });
        }
        return BaseResponse.newSuccessInstance(testMatrixList);
    }

    private void assembleRelationship(TestMatrixBO testMatrixBO, TestMatrixPO testMatrixPO,List<TestSamplePO> testSampleList) {
        if (Func.isEmpty(testMatrixPO)) {
            return;
        }
        TestMatrixRelationshipBO relationship = new TestMatrixRelationshipBO();
        TestMatrixParentBO parent = new TestMatrixParentBO();

        TestSchemeIdBO testSchemeIdBO=new TestSchemeIdBO();
//        testSchemeIdBO.setTestSchemeId(testMatrixPO.getTestSchemeId());

        // testConditionGroupList
        if (Func.isNotEmpty(testMatrixPO.getTestConditionGroupID())) {
            List<TestLineConditionGroupIdBO> testConditionGroupList = Lists.newArrayList();
            TestLineConditionGroupIdBO testLineConditionGroupId = new TestLineConditionGroupIdBO();
            testLineConditionGroupId.setTestConditionGroupId(testMatrixPO.getTestConditionGroupID());
            testConditionGroupList.add(testLineConditionGroupId);
            parent.setTestConditionGroupList(testConditionGroupList);
        }
        // testLine
        TestLineIdRelBO testLineIdRel = new TestLineIdRelBO();
        testLineIdRel.setTestLineInstanceId(testMatrixPO.getTestLineInstanceID());
        parent.setTestLine(testLineIdRel);
        parent.setTestScheme(testSchemeIdBO);
        // testSampleList
        TestSampleIdBO testSampleID = new TestSampleIdBO();
        testSampleID.setTestSampleInstanceId(testMatrixPO.getTestSampleID());
        // 过滤当前的Sample信息
        if(Func.isNotEmpty(testSampleList)){
            TestSamplePO testSample = testSampleList.stream().filter(item->Func.equalsSafe(item.getId(),testMatrixPO.getTestSampleID())).findAny().orElse(null);
            if(Func.isNotEmpty(testSample)){
                testSampleID.setTestSampleNo(testSample.getSampleNo());
                testSampleID.setSampleSeq(testSample.getSampleSeq());
            }
        }
        parent.setTestSample(testSampleID);
        relationship.setParent(parent);
        //
        testMatrixBO.setRelationship(relationship);
    }

    private void assembleCondition(TestMatrixBO testMatrixBO, List<TestConditionInstancePO> testConditionInstanceList, List<TestConditionInstanceLanguagePO> testConditionInstanceLanguageList) {
        if (Func.isEmpty(testConditionInstanceList)) {
            return;
        }
        List<ConditionBO> conditionList = Lists.newArrayList();
        testConditionInstanceList.stream().forEach(testConditionInstancePO -> {
            if (!Func.equalsSafe(testConditionInstancePO.getTestMatrixId(), testMatrixBO.getId().getTestMatrixId())) {
                return;
            }
            ConditionBO conditionBO = new ConditionBO();
            // id
            ConditionIdBO id = new ConditionIdBO();
            id.setConditionId(testConditionInstancePO.getTestConditionId());
            id.setConditionInstanceId(testConditionInstancePO.getId());
            id.setConditionTypeId(testConditionInstancePO.getTestConditionTypeId());
            conditionBO.setId(id);
            // header
            ConditionHeaderBO header = new ConditionHeaderBO();
            header.setConditionDesc(testConditionInstancePO.getTestConditionDesc());
            header.setConditionName(testConditionInstancePO.getTestConditionName());
            header.setConditionSeq(testConditionInstancePO.getTestConditionSeq());
            header.setConditionTypeName(testConditionInstancePO.getTestConditionTypeName());
            header.setConditionTypeBlockLevel(testConditionInstancePO.getConditionTypeBlockLevel());
            if(testConditionInstancePO.getIsProcedureCondition()){
                header.setConditionType(3);
            }else {
                header.setConditionType(testConditionInstancePO.getIsConditionTypeBlock()?1:2);
            }
            conditionBO.setHeader(header);
            // languageList
            if (Func.isNotEmpty(testConditionInstanceLanguageList)) {
                List<TestConditionInstanceLanguagePO> thisTestConditionLanguageList = testConditionInstanceLanguageList
                        .stream()
                        .filter(testConditionInstanceLanguagePO -> Func.equalsSafe(testConditionInstanceLanguagePO.getTestConditionInstanceId(), testConditionInstancePO.getId()))
                        .collect(Collectors.toList());
                if (Func.isNotEmpty(thisTestConditionLanguageList)) {
                    List<ConditionLanguageBO> languageList = Lists.newArrayList();
                    thisTestConditionLanguageList.stream().forEach(testConditionInstanceLanguagePO -> {
                        ConditionLanguageBO conditionLanguageBO = new ConditionLanguageBO();
                        conditionLanguageBO.setConditionDesc(testConditionInstanceLanguagePO.getTestConditionDesc());
                        conditionLanguageBO.setConditionName(testConditionInstanceLanguagePO.getTestConditionName());
                        conditionLanguageBO.setConditionTypeName(testConditionInstanceLanguagePO.getTestConditionTypeName());
                        conditionLanguageBO.setLanguageId(testConditionInstanceLanguagePO.getLanguageId());
                        languageList.add(conditionLanguageBO);
                    });
                    conditionBO.getHeader().setLanguageList(languageList);
                }
            }
            conditionList.add(conditionBO);
        });
        testMatrixBO.setConditionList(conditionList);
    }

    private void assembleProductAttribute(TestMatrixBO testMatrixBO, List<ProductAttributeInstancePO> productAttributeList) {
        if (Func.isEmpty(productAttributeList)) {
            return;
        }
        List<ProductAttributeInstancePO> currentProductAttribute = productAttributeList.stream().filter
                (pa -> Func.isNotEmpty(pa.getActiveIndicator()) && Func.equalsSafe(pa.getActiveIndicator(), Constants.ActiveIndicator)
                        && Func.equalsSafe(pa.getTestMatrixId(), testMatrixBO.getId().getTestMatrixId()))
                .collect(Collectors.toList());
        if (Func.isEmpty(currentProductAttribute)) {
            return;
        }
        List<ProductAttributeBO> productAttributeBOList = Lists.newArrayList();
        currentProductAttribute.stream().forEach(pa -> {
            ProductAttributeBO productAttribute = new ProductAttributeBO();
            productAttribute.setProductAttributeId(Func.isNotEmpty(pa.getProductAttributeId()) ? pa.getProductAttributeId().toString() : "");
            productAttribute.setProductAttributeName(pa.getProductAttributeValue());
            productAttribute.setLimitGroupId(pa.getLimitGroupId());
            productAttributeBOList.add(productAttribute);
        });
        testMatrixBO.setProductAttributeList(productAttributeBOList);
    }

    private void assembleMatrixExt(TestMatrixBO testMatrixBO,List<TestMatrixExtPO> testMatrixExtPOList){
        if(Func.isEmpty(testMatrixExtPOList)){
            return;
        }
        TestMatrixExtPO testMatrixExt = testMatrixExtPOList.stream()
                .filter(testMatrixExtPO -> Func.equalsSafe(testMatrixExtPO.getTestMatrixId(), testMatrixBO.getId().getTestMatrixId()))
                .findAny().orElse(null);
        if(Func.isEmpty(testMatrixExt)){
            return;
        }
        try {
            String applicationFactorId = testMatrixExt.getApplicationFactorId();
            if(Func.isNotEmpty(applicationFactorId) && !applicationFactorId.equals("null")){
                testMatrixBO.getHeader().setApplicationFactorIds(JSONObject.parseArray(applicationFactorId,Long.class));
            }
        }catch (Exception e){
            throw new BizException("applicationFactor 转换异常");
        }
    }

    @Override
    public BaseResponse<List<TestMatrixBO>> execute(TestMatrixContext<TestMatrixQueryReq, TestMatrixBO> context) {
        return buildDomain(context);
    }
}
