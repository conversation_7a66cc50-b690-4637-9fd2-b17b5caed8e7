package com.sgs.gpo.domain.service.otsnotes.report.context;


import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.gpo.facade.model.otsnotes.report.dto.ReportAutoDeliverImportDTO;
import lombok.Data;

import java.util.List;

@Data
public class ReportAutoDeliverImportContext<Input> extends BaseContext<Input, ReportBO> {
    private List<ReportAutoDeliverImportDTO> importList;

    private String batchNo;

}
