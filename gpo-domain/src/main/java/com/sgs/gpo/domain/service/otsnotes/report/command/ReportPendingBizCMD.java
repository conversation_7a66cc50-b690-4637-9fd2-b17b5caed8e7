package com.sgs.gpo.domain.service.otsnotes.report.command;


import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.common.customer.CustomerBO;
import com.sgs.framework.model.enums.CustomerType;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.StringPool;
import com.sgs.gpo.core.constants.BizLogConstant;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.ReportStatus;
import com.sgs.gpo.core.util.DateUtils;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.status.GpnStatusLogPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.status.GpnStatusPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.report.GpoReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.setting.operation.OperationHistoryPO;
import com.sgs.gpo.domain.service.otsnotes.report.IReportDomainService;
import com.sgs.gpo.domain.service.otsnotes.report.context.ReportUpdateContext;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.otsnotes.status.IGpnStatusLogService;
import com.sgs.gpo.domain.service.otsnotes.status.IGpnStatusService;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.domain.service.preorder.report.IGpoReportService;
import com.sgs.gpo.domain.service.setting.notifyconfig.INotifyConfigDomainService;
import com.sgs.gpo.domain.service.setting.operation.IOperationHistoryService;
import com.sgs.gpo.facade.model.notifyconfig.req.NotifyConfigAddReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportUpdateReq;
import com.sgs.gpo.integration.framework.NotificationClient;
import com.sgs.gpo.integration.framework.req.SendEmailReq;
import com.sgs.grus.bizlog.BizLogClient;
import com.sgs.grus.bizlog.info.BizLogInfo;
import com.sgs.otsnotes.facade.model.enums.ObjectType;
import com.sgs.otsnotes.facade.model.enums.TbStatusObjectType;
import com.sgs.otsnotes.facade.model.enums.TbStatusType;
import com.sgs.otsnotes.facade.model.req.gpn.email.SendJobPendingEmailReq;
import com.sgs.preorder.facade.OrderFacade;
import com.sgs.preorder.facade.model.enums.ActiveType;
import com.sgs.preorder.facade.model.enums.FullCyclePendingFlag;
import com.sgs.preorder.facade.model.enums.OperationTypeEnums;
import com.sgs.preorder.facade.model.req.UpdateFullCyclePendingFlagReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
@Slf4j
public class ReportPendingBizCMD extends BaseCommand<ReportUpdateContext<ReportUpdateReq>> {

    @Autowired
    IReportDomainService reportDomainService;
    @Autowired
    IOrderDomainService orderDomainService;
    @Autowired
    IReportService reportService;
    @Autowired
    IGpoReportService gpoReportService;
    @Autowired
    private OrderFacade gpoOrderFacade;
    @Autowired
    private IOperationHistoryService operationHistoryService;
    @Autowired
    private IGpnStatusService gpnStatusService;
    @Autowired
    private NotificationClient notificationClient;
    @Autowired
    private BizLogClient bizLogClient;
    @Autowired
    private IGpnStatusLogService gpnStatusLogService;
    @Autowired
    private INotifyConfigDomainService notifyConfigDomainService;


    @Override
    public BaseResponse validateParam(ReportUpdateContext<ReportUpdateReq> context) {
        ReportUpdateReq reportUpdateReq = context.getParam();
        Assert.isTrue(Func.isNotEmpty(reportUpdateReq),"common.param.miss",new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(reportUpdateReq.getReportNo()),"common.param.miss",new Object[]{"reportNo"});
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(ReportUpdateContext<ReportUpdateReq> context) {
        ReportUpdateReq reportUpdateReq = context.getParam();
        //查询Report 信息
        ReportQueryReq reportQueryReq = new ReportQueryReq();
        reportQueryReq.setBaseQuery(true);
        reportQueryReq.setReportNoList(Sets.newHashSet(reportUpdateReq.getReportNo()));
        BaseResponse<List<ReportBO>> reportBOListRsp = reportDomainService.queryBO(reportQueryReq);
        if(reportBOListRsp.isFail() || Func.isEmpty(reportBOListRsp.getData())){
            return BaseResponse.newFailInstance("report.not.exist",new Object[]{});
        }
        ReportBO reportBO = reportBOListRsp.getData().stream().filter(e -> Func.equalsSafe(e.getId().getReportNo(),reportUpdateReq.getReportNo())).findAny().orElse(null);
        if(Func.isEmpty(reportBO)){
            return BaseResponse.newFailInstance("report.not.exist",new Object[]{});
        }
        context.setReportBO(reportBO);
        //查询Report对应的Order
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setBaseQuery(true);
        orderQueryReq.setOrderNoList(Sets.newHashSet(reportBO.getRelationship().getParent().getOrder().getOrderNo()));
        BaseResponse<List<OrderBO>> orderBOListRsp = orderDomainService.queryBO(orderQueryReq);
        if(orderBOListRsp.isFail() || Func.isEmpty(orderBOListRsp.getData())){
            return BaseResponse.newFailInstance("Not Find Order!",new Object[]{});
        }
        OrderBO orderBO = orderBOListRsp.getData().stream().filter(e -> Func.equalsSafe(e.getHeader().getOrderNo(),reportBO.getRelationship().getParent().getOrder().getOrderNo())).findAny().orElse(null);
        if(Func.isEmpty(orderBO)){
            return BaseResponse.newFailInstance("Not Find Order!",new Object[]{});
        }
        context.setOrderBO(orderBO);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(ReportUpdateContext<ReportUpdateReq> context) {
        ReportBO reportBO = context.getReportBO();
        OrderBO orderBO = context.getOrderBO();
        ReportUpdateReq reportUpdateReq = context.getParam();
        UserInfo user = Func.isEmpty(reportUpdateReq.getUserInfo()) ? context.getUserInfo() : reportUpdateReq.getUserInfo();
        //report 状态校验
        if(!ReportStatus.check(reportBO.getHeader().getReportStatus(),
                ReportStatus.New,
                ReportStatus.Approved,
                ReportStatus.Draft,
                ReportStatus.Confirmed,
                ReportStatus.TypingFinished,
                ReportStatus.HostReviewed,
                ReportStatus.Reviewed)){
            return BaseResponse.newFailInstance("report.status.not.match", new Object[]{"New/Approved/Draft/Confirmed/TypingFinished/HostReviewed/Reviewed"});
        }
        //更新 report Status = pending
        ReportPO reportPO = new ReportPO();
        reportPO.setId(reportBO.getId().getReportId());
        reportPO.setReportStatus(ReportStatus.Pending.getCode());
        reportPO.setModifiedDate(DateUtils.now());
        reportPO.setModifiedBy(user.getRegionAccount());
        reportService.updateById(reportPO);
        // 更新 GPO report Status
        GpoReportPO gpoReportPO = new GpoReportPO();
        gpoReportPO.setId(reportBO.getId().getReportId());
        gpoReportPO.setReportStatus(ReportStatus.Pending.getCode());
        gpoReportPO.setModifiedDate(DateUtils.now());
        gpoReportPO.setModifiedBy(user.getRegionAccount());
        gpoReportService.updateById(gpoReportPO);
        // 更新 Order FullCyclePendingFlag
        UpdateFullCyclePendingFlagReq updateFullCyclePendingFlagReq = new UpdateFullCyclePendingFlagReq();
        updateFullCyclePendingFlagReq.setOperationType(1);
        updateFullCyclePendingFlagReq.setFullCyclePendingFlag(FullCyclePendingFlag.Report.getCode());
        updateFullCyclePendingFlagReq.setProductLineCode(orderBO.getLab().getProductLineCode());
        updateFullCyclePendingFlagReq.setOrderNos(Lists.newArrayList(reportBO.getRelationship().getParent().getOrder().getOrderNo()));
        BaseResponse updatePendingFlagRes = gpoOrderFacade.updateFullCyclePendingFlag(updateFullCyclePendingFlagReq);
        //增加 OperationHistory 表数据
        OperationHistoryPO operationHistoryPO = new OperationHistoryPO();
        operationHistoryPO.setId(UUID.randomUUID().toString());
        operationHistoryPO.setObjectId(reportBO.getId().getReportId());
        operationHistoryPO.setObjectNo(reportBO.getId().getReportNo());
        operationHistoryPO.setOperationType(OperationTypeEnums.PendReport.getStatus());
        operationHistoryPO.setReasonType(reportUpdateReq.getReasonType());
        operationHistoryPO.setRemark(reportUpdateReq.getRemark());
        operationHistoryPO.setActiveIndicator(ActiveType.Enable.getStatus());
        operationHistoryPO.setCreatedDate(DateUtil.now());
        operationHistoryPO.setCreatedBy(user.getRegionAccount());
        operationHistoryService.insert(Lists.newArrayList(operationHistoryPO));
        // 保存tb_status
        GpnStatusPO gpnStatusPO = new GpnStatusPO();
        gpnStatusPO.setId(IdUtil.uuId());
        gpnStatusPO.setBUID(orderBO.getLab().getProductLineId());
        gpnStatusPO.setBUCode(orderBO.getLab().getBuCode());
        gpnStatusPO.setLocationCode(orderBO.getLab().getLocationCode());
        gpnStatusPO.setLocationID(orderBO.getLab().getLocationId());
        gpnStatusPO.setOrderNo(orderBO.getId().getOrderNo());
        gpnStatusPO.setObjectNo(reportBO.getId().getReportNo());
        gpnStatusPO.setObjectType(TbStatusObjectType.REPORT.getCode());
        gpnStatusPO.setOldStatus(reportBO.getHeader().getReportStatus());
        gpnStatusPO.setOperationType(TbStatusType.PENDING.getCode());
        gpnStatusPO.setOperationInfo(reportUpdateReq.getReasonType());
        gpnStatusPO.setRemark(reportUpdateReq.getRemark());
        gpnStatusPO.setCreatedBy(user.getRegionAccount());
        gpnStatusPO.setCreatedDate(new Date());
        gpnStatusService.save(gpnStatusPO);
        //保存status log
        GpnStatusLogPO gpnStatusLogPO = new GpnStatusLogPO();
        gpnStatusLogPO.setId(IdUtil.uuId());
        gpnStatusLogPO.setOrderNo(orderBO.getId().getOrderNo());
        gpnStatusLogPO.setReportId(reportBO.getId().getReportId());
        gpnStatusLogPO.setObjectType(ObjectType.Report.getCode());
        gpnStatusLogPO.setStatus("pending");
        gpnStatusLogPO.setActiveIndicator(ActiveType.Enable.getStatus());
        gpnStatusLogPO.setCreatedDate(new Date());
        gpnStatusLogPO.setCreatedBy(user.getRegionAccount());
        gpnStatusLogService.save(gpnStatusLogPO);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse after(ReportUpdateContext<ReportUpdateReq> context) {
        ReportBO reportBO = context.getReportBO();
        OrderBO orderBO = context.getOrderBO();
        ReportUpdateReq reportUpdateReq = context.getParam();
        UserInfo user = Func.isEmpty(reportUpdateReq.getUserInfo()) ? context.getUserInfo() : reportUpdateReq.getUserInfo();
        CustomerBO customer = orderBO.getCustomerList().stream().filter(e -> Func.equalsSafe(e.getCustomerUsage(), CustomerType.Applicant.getCode())).findAny().orElse(new CustomerBO());

        //发送Email
        SendEmailReq sendEmailReq = new SendEmailReq();
        sendEmailReq.setMailTo(Lists.newArrayList(orderBO.getHeader().getCsEmail()));
        sendEmailReq.setLabCode(orderBO.getLab().getLabCode());
        sendEmailReq.setBuCode(orderBO.getLab().getBuCode());
        sendEmailReq.setSystemId(Func.toStr(SgsSystem.GPO.getSgsSystemId()));
        //TODO 邮件内容
        //Func.toStr(dffMap.get(Constants.DFF.FILED.ITEM_NO))
        sendEmailReq.setMailSubject(orderBO.getId().getOrderNo()+" 报告暂停通知:" + reportBO.getId().getReportNo() + " " + Func.toStr(customer.getCustomerName()) + " ");
        sendEmailReq.setMailText("Dear Receiver,<Br><br>" + orderBO.getId().getOrderNo() + " The report is pended by " + user.getRegionAccount() + " due to "
                + reportUpdateReq.getReasonType() + (Func.isNotEmpty(reportUpdateReq.getRemark())? StringPool.SEMICOLON + reportUpdateReq.getRemark():StringPool.EMPTY) + ".<br>"
                + "Please contact "+ user.getRegionAccount()+" and revise the Order due date when the information is confirmed or the samples are resubmitted.<br><br>"
                + "Best Regard!<br><br>General PreOrder");
        BaseResponse<String> response = notificationClient.sendEmail(sendEmailReq);

        //保存 NotifyConfigAddReq
        NotifyConfigAddReq notifyConfigAddReq = new NotifyConfigAddReq();
        notifyConfigAddReq.setObjectNo(reportBO.getId().getReportNo());
        notifyConfigAddReq.setBuId(orderBO.getLab().getProductLineId());
        notifyConfigAddReq.setBuCode(orderBO.getLab().getBuCode());
        notifyConfigAddReq.setLabId(orderBO.getLab().getLabId());
        notifyConfigAddReq.setLabCode(orderBO.getLab().getLabCode());
        notifyConfigAddReq.setLocationId(orderBO.getLab().getLocationId());
        notifyConfigAddReq.setLocationCode(orderBO.getLab().getLocationCode());
        notifyConfigAddReq.setObjectType(TbStatusObjectType.REPORT.getName());
        notifyConfigAddReq.setEventType(TbStatusType.PENDING.getName());
        notifyConfigAddReq.setNotifyParams(response.getData());
        notifyConfigAddReq.setIsCalculateNotifyDate(true);
        notifyConfigDomainService.add(notifyConfigAddReq);

        BizLogInfo bizLog = new BizLogInfo();
        bizLog.setBu(orderBO.getLab().getBuCode());
        bizLog.setLab(orderBO.getLab().getLocationCode());
        bizLog.setOpUser(user.getRegionAccount());
        bizLog.setBizId(orderBO.getId().getOrderNo());
        bizLog.setOpType("Pending");
        bizLog.setBizOpType(BizLogConstant.REPORT_OPERATION_HISTORY);
        bizLog.setNewVal("ReportNo:"+ reportBO.getId().getReportNo() + " Reason：" + reportUpdateReq.getReasonType() + "Remark:[" + reportUpdateReq.getRemark() + "]");

        BizLogInfo bizLogInfo = new BizLogInfo();
        bizLogInfo.setBu(orderBO.getLab().getBuCode());
        bizLogInfo.setLab(orderBO.getLab().getLocationCode());
        bizLogInfo.setOpUser(user.getRegionAccount());
        bizLogInfo.setBizId(orderBO.getId().getOrderNo());
        bizLogInfo.setOpType("Pending");
        bizLogInfo.setBizOpType(BizLogConstant.REPORT_STATUS_CHANGE_HISTORY);
        bizLogInfo.setOriginalVal(com.sgs.otsnotes.facade.model.enums.ReportStatus.Pending.getMessage());
        bizLogInfo.setNewVal(reportBO.getHeader().getReportStatus());
        bizLogClient.doSend(bizLog);
        bizLogClient.doSend(bizLogInfo);
        return super.after(context);
    }
}
