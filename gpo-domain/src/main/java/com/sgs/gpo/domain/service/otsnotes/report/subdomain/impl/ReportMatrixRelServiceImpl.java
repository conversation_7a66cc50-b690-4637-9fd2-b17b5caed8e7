package com.sgs.gpo.domain.service.otsnotes.report.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.util.DateUtils;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.report.ReportMatrixRelMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportMatrixRelPO;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportMatrixRelService;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.report.bo.ReportMatrixRelBO;
import com.sgs.gpo.facade.model.report.req.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ReportMatrixRelServiceImpl extends ServiceImpl<ReportMatrixRelMapper, ReportMatrixRelPO> implements IReportMatrixRelService {
    @Override
    public BaseResponse<List<ReportMatrixRelBO>> queryReportMatrix(ReportMatrixQueryReq reportMatrixQueryReq) {
        if(Func.isEmpty(reportMatrixQueryReq)){
            return BaseResponse.newFailInstance("param missing.");
        }
        if(Func.isAllEmpty(reportMatrixQueryReq.getSampleIdList(), reportMatrixQueryReq.getReportNoList(),reportMatrixQueryReq.getReportIdList(),reportMatrixQueryReq.getTestLineInstanceIdList(),reportMatrixQueryReq.getMatrixIdList())){
            return BaseResponse.newFailInstance("param:ReportNo/ReportId/TestLineInstanceId/MatrixIdList/SampleIdList  missing.");
        }
        BaseResponse<List<ReportMatrixRelBO>> response = new BaseResponse<>();
        response.setData(this.baseMapper.queryReportMatrix(reportMatrixQueryReq));
        return response;
    }

    @Override
    public BaseResponse<List<ReportMatrixRelPO>> select(ReportQueryReq reportIdReq) {
        if(Func.isEmpty(reportIdReq) || (Func.isEmpty(reportIdReq.getReportId())&&Func.isEmpty(reportIdReq.getReportIdList()) && Func.isEmpty(reportIdReq.getTestMatrixIds()))){
            return BaseResponse.newFailInstance("reportNo  missing.");
        }
        LambdaQueryWrapper<ReportMatrixRelPO> wrapper= Wrappers.lambdaQuery();
        if(Func.isNotEmpty(reportIdReq.getReportIdList())){
            wrapper.in(ReportMatrixRelPO::getReportId,reportIdReq.getReportIdList());
        }
        if(Func.isNotEmpty(reportIdReq.getReportId())){
            wrapper.eq(ReportMatrixRelPO::getReportId,reportIdReq.getReportId());
        }
        if(Func.isNotEmpty(reportIdReq.getTestMatrixIds())){
            wrapper.in(ReportMatrixRelPO::getTestMatrixId,reportIdReq.getTestMatrixIds());
        }
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }

    @Override
    public BaseResponse<List<ReportMatrixRelPO>> query(ReportQueryReq reportQueryReq) {
        if(Func.isEmpty(reportQueryReq) || Func.isEmpty(reportQueryReq.getReportIdList())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        LambdaQueryWrapper<ReportMatrixRelPO> wrapper = Wrappers.lambdaQuery();
        if(Func.isNotEmpty(reportQueryReq.getReportIdList())){
            wrapper.in(ReportMatrixRelPO::getReportId,reportQueryReq.getReportIdList());
        }
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }

    @Override
    public BaseResponse<Long> updateReportMatrixReq(ReportMatrixSeqUpdateReq req) {
        Assert.isTrue(Func.isNotEmpty(req), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNoneEmpty(req.getReportId(), req.getReportMatrixSeqItemUpdateList()), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        List<ReportMatrixSeqItemUpdateReq> reportMatrixSeqItemUpdateList = req.getReportMatrixSeqItemUpdateList();
        String reportId = req.getReportId();
        Set<String> testLineInstanceIdList = reportMatrixSeqItemUpdateList.stream().map(ReportMatrixSeqItemUpdateReq::getTestLineInstanceId).collect(Collectors.toSet());
        ReportMatrixQueryReq reportMatrixQueryReq = new ReportMatrixQueryReq();
        reportMatrixQueryReq.setTestLineInstanceIdList(testLineInstanceIdList);
        reportMatrixQueryReq.setReportIdList(Sets.newHashSet(reportId));
        List<ReportMatrixRelBO> allReportMatrixRelBOList = this.queryReportMatrix(reportMatrixQueryReq).getData();
        Date now = DateUtils.getNow();
        Long updateCount = 0L;
        if(Func.isNotEmpty(allReportMatrixRelBOList)){
            List<ReportMatrixSeqItemUpdateReq> reportMatrixSeqItemUpdateReqList = new ArrayList<>();
            for (ReportMatrixSeqItemUpdateReq reportMatrixSeqItemUpdateReq : reportMatrixSeqItemUpdateList) {
                if(Func.isNull(reportMatrixSeqItemUpdateReq.getSubTestLineInstanceIdList())){
                    reportMatrixSeqItemUpdateReq.setSubTestLineInstanceIdList(new HashSet<>());
                }
                List<ReportMatrixRelBO> reportMatrixRelBOList = allReportMatrixRelBOList.stream().filter(item -> reportMatrixSeqItemUpdateReq.getSubTestLineInstanceIdList().contains(item.getTestLineInstanceId()) || Func.equalsSafe(item.getTestLineInstanceId(), reportMatrixSeqItemUpdateReq.getTestLineInstanceId())).collect(Collectors.toList());
                for (ReportMatrixRelBO reportMatrixRelBO : reportMatrixRelBOList) {
                    ReportMatrixSeqItemUpdateReq newReportMatrixSeqItemUpdateReq = Func.deepCopy(reportMatrixSeqItemUpdateReq, ReportMatrixSeqItemUpdateReq.class);
                    newReportMatrixSeqItemUpdateReq.setReportMatrixRelId(reportMatrixRelBO.getReportMatrixRelId());
                    newReportMatrixSeqItemUpdateReq.setModifiedBy(SecurityContextHolder.getUserInfoFillSystem().getRegionAccount());
                    newReportMatrixSeqItemUpdateReq.setModifiedDate(now);
                    reportMatrixSeqItemUpdateReqList.add(newReportMatrixSeqItemUpdateReq);
                }
            }
            updateCount = baseMapper.batchUpdateReportMatrixSeq(reportMatrixSeqItemUpdateReqList);
        }
        return BaseResponse.newSuccessInstance(updateCount);
    }

    @Override
    public BaseResponse<List<ReportMatrixRelPO>> delete(ReportQueryReq reportIdReq) {
        if(Func.isEmpty(reportIdReq) || (Func.isEmpty(reportIdReq.getReportId())&&Func.isEmpty(reportIdReq.getReportIdList()) && Func.isEmpty(reportIdReq.getTestMatrixIds()))){
            return BaseResponse.newFailInstance("reportNo  missing.");
        }
        LambdaQueryWrapper<ReportMatrixRelPO> wrapper= Wrappers.lambdaQuery();
        if(Func.isNotEmpty(reportIdReq.getReportIdList())){
            wrapper.in(ReportMatrixRelPO::getReportId,reportIdReq.getReportIdList());
        }
        if(Func.isNotEmpty(reportIdReq.getReportId())){
            wrapper.eq(ReportMatrixRelPO::getReportId,reportIdReq.getReportId());
        }
        if(Func.isNotEmpty(reportIdReq.getTestMatrixIds())){
            wrapper.in(ReportMatrixRelPO::getTestMatrixId,reportIdReq.getTestMatrixIds());
        }
        return BaseResponse.newSuccessInstance(baseMapper.delete(wrapper));
    }
}
