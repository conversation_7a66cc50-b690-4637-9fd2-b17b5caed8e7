package com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testsample.TestSampleLanguageMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.TestSampleLanguagePO;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.ITestSampleLanguageService;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleLanguageQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class TestSampleLanguageServiceImpl extends ServiceImpl<TestSampleLanguageMapper, TestSampleLanguagePO>
        implements ITestSampleLanguageService {


    @Override
    public BaseResponse<List<TestSampleLanguagePO>> query(TestSampleLanguageQueryReq req) {
        if(Func.isEmpty(req)||Func.isEmpty(req.getTestSampleInstanceIdList())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        LambdaQueryWrapper<TestSampleLanguagePO> wrapper= Wrappers.<TestSampleLanguagePO>lambdaQuery()
                .in(TestSampleLanguagePO::getSampleId,req.getTestSampleInstanceIdList())
                .eq(TestSampleLanguagePO::getStatus, Constants.ActiveIndicator);
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }
}
