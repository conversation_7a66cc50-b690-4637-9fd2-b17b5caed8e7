package com.sgs.gpo.domain.service.otsnotes.testmatrix.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.framework.core.base.BaseBO;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.common.print.OutPutDataBO;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.ReportEntryModeEnum;
import com.sgs.framework.model.enums.TestLineType;
import com.sgs.framework.model.test.condition.ConditionLanguageBO;
import com.sgs.framework.model.test.condition.v2.ConditionBO;
import com.sgs.framework.model.test.condition.v2.ConditionHeaderBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.test.testline.v2.TestLineIdBO;
import com.sgs.framework.model.test.testmatrix.TestLineWithSampleBO;
import com.sgs.framework.model.test.testmatrix.TestLineWithSampleMatrixBO;
import com.sgs.framework.model.test.testmatrix.TestMatrixBO;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixHeaderLanguageBO;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixIdBO;
import com.sgs.framework.open.platform.base.service.impl.AbstractDomainService;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.MatrixActionEnums;
import com.sgs.gpo.core.util.Utils;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportMatrixRelService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.otsnotes.testline.command.TestLinePageQueryCMD;
import com.sgs.gpo.domain.service.otsnotes.testline.context.TestLineQueryContext;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.ITestMatrixDomainService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.ITestMatrixProcessService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.command.TestLineWithSampleMatrixQueryCMD;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.command.TestLineWithSampleQueryCMD;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.command.TestMatrixConfirmCMD;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.command.TestMatrixQueryCMD;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.context.TestMatrixContext;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.impl.TestMatrixServiceImpl;
import com.sgs.gpo.domain.service.setting.buparam.IBUParam;
import com.sgs.gpo.facade.model.otsnotes.testline.TestLinePageBO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixProcessReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.rsp.TestMatrixSampleRsp;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import com.sgs.gpo.integration.trims.TrimsClient;
import com.sgs.trimslocal.facade.model.artifact.req.PpArtifactInfoReq;
import com.sgs.trimslocal.facade.model.artifact.rsp.PpArtifactInfoRsp;
import com.sgs.trimslocal.facade.model.artifact.rsp.PpArtifactRelLangInfoRsp;
import com.sgs.trimslocal.facade.model.testline.req.GetTestLineBaseInfoReq;
import com.sgs.trimslocal.facade.model.testline.rsp.GetTestLineBaseInfoRsp;
import com.sgs.trimslocal.facade.model.testline.rsp.TestLineBaseInfoLanguagesRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: ITestMatrixDomainServiceImpl
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/8/30 15:18
 */
@Service
@Slf4j
public class TestMatrixDomainServiceImpl extends AbstractDomainService<com.sgs.framework.model.test.testmatrix.v2.TestMatrixBO, TestMatrixIdBO,TestMatrixQueryReq, ITestMatrixService> implements ITestMatrixDomainService {
    @Autowired
    private ITestMatrixProcessService testMatrixProcessService;
    @Autowired
    private TestMatrixServiceImpl testMatrixService;
    @Autowired
    private TrimsClient trimsClient;
    @Autowired
    private IBUParam ibuParam;
    @Autowired
    private IReportService reportService;
    @Autowired
    private IReportMatrixRelService reportMatrixRelService;

    @Override
    public BaseResponse confirmMatrix(OrderIdReq orderIdReq) {
        TestMatrixContext<OrderIdReq, BaseBO> testMatrixContext = new TestMatrixContext<>();
        testMatrixContext.setParam(orderIdReq);
        testMatrixContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        testMatrixContext.setToken(SecurityContextHolder.getSgsToken());
        testMatrixContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(TestMatrixConfirmCMD.class, testMatrixContext);
    }

    @Override
    public BaseResponse<List<TestLineWithSampleMatrixBO>> queryTestLineWithSampleMatrix(TestMatrixQueryReq testMatrixQueryReq) {
        TestMatrixContext<TestMatrixQueryReq, TestLineWithSampleMatrixBO> testMatrixContext = new TestMatrixContext<>();
        testMatrixContext.setParam(testMatrixQueryReq);
        testMatrixContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        testMatrixContext.setToken(SecurityContextHolder.getSgsToken());
        testMatrixContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(TestLineWithSampleMatrixQueryCMD.class, testMatrixContext);
    }

    @Override
    public BaseResponse<List<TestLineWithSampleBO>> queryTestLineWithSample(TestMatrixQueryReq testMatrixQueryReq) {
        TestMatrixContext<TestMatrixQueryReq, TestLineWithSampleBO> testMatrixContext = new TestMatrixContext<>();
        testMatrixContext.setParam(testMatrixQueryReq);
        testMatrixContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        testMatrixContext.setToken(SecurityContextHolder.getSgsToken());
        testMatrixContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(TestLineWithSampleQueryCMD.class, testMatrixContext);
    }

    @Override
    public BaseResponse<List<TestLinePageBO>> queryTestLineWithSampleForTestData(TestMatrixQueryReq testMatrixQueryReq) {
        if (Func.isEmpty(testMatrixQueryReq) || Func.isEmpty(testMatrixQueryReq.getTestMatrixIdList())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        List<TestMatrixBO> testMatrixBOList = testMatrixService.queryV1(testMatrixQueryReq).getData();
        if (Func.isEmpty(testMatrixBOList)) {
            return BaseResponse.newSuccessInstance(new ArrayList<>());
        }
        Set<String> testLineInstanceIdList = testMatrixBOList.parallelStream().map(TestMatrixBO::getTestLineInstanceId).collect(Collectors.toSet());
        OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
        orderTestLineReq.setTestLineInstanceIdList(testLineInstanceIdList);
        TestLineQueryContext<OrderTestLineReq, TestLineBO> testLinePageContext = new TestLineQueryContext<>();
        testLinePageContext.setParam(orderTestLineReq);
        testLinePageContext.setPage(1);
        testLinePageContext.setRows(-1);
        testLinePageContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        testLinePageContext.setUserInfo(SecurityContextHolder.getUserInfo());
        BaseResponse response = BaseExecutor.start(TestLinePageQueryCMD.class, testLinePageContext);
        Page<TestLinePageBO> page = (Page<TestLinePageBO>) response.getData();
        List<TestLinePageBO> testLinePageBOList = page.getRecords();
        testLinePageBOList = testLinePageBOList.stream().filter(item -> TestLineType.check(item.getTestLineType(), TestLineType.VIRTAUL_TL)).collect(Collectors.toList());

        for (TestLinePageBO testLinePageBO : testLinePageBOList) {
            List<TestMatrixSampleRsp> testMatrixSampleList = testLinePageBO.getTestMatrixSampleList();
            testMatrixSampleList = testMatrixSampleList.stream().filter(item -> testMatrixQueryReq.getTestMatrixIdList().contains(item.getTestMatrixId())).collect(Collectors.toList());
            testLinePageBO.setTestMatrixSampleList(testMatrixSampleList);
        }
        return BaseResponse.newSuccessInstance(testLinePageBOList);
    }


    @Override
    public BaseResponse updateMatrixStatus(TestMatrixProcessReq testMatrixProcessReq) {
        log.info("test-matrix update status: {}",Func.toJson(testMatrixProcessReq));
        BaseResponse<Boolean> validateResponse = this.validateAttr(testMatrixProcessReq);
        if (validateResponse.isFail()) {
            return validateResponse;
        }
        String action = testMatrixProcessReq.getAction();
        if(Func.isEmpty(testMatrixProcessReq.getEntryMode())){
            testMatrixProcessReq.setEntryMode(ReportEntryModeEnum.PROTOCOL_DATA_ENTRY.getCode());
        }
        MatrixActionEnums matrixActionEnum = MatrixActionEnums.getAction(action);
        Set<String> testMatrixIdList = testMatrixProcessReq.getTestMatrixIdList();
        TestMatrixQueryReq testMatrixQueryReq = new TestMatrixQueryReq();
        testMatrixQueryReq.setTestMatrixIdList(testMatrixIdList);
        List<TestMatrixBO> testMatrixBOList = testMatrixService.queryV1(testMatrixQueryReq).getData();
        if(Func.isEmpty(matrixActionEnum)){
            return BaseResponse.newFailInstance("common.param.invalid",new Object[]{"Matrix Update Action:["+action+"]"});
        }
        log.info("test-matrix before update Info: {}",Func.toJson(testMatrixBOList));
        BaseResponse resultResponse = BaseResponse.newSuccessInstance(true);
        switch (matrixActionEnum) {
            case Assign_Sample:
                resultResponse = testMatrixProcessService.typing(testMatrixProcessReq);
                break;
            case Save:
            case Return:
            case Reset:
            case Change:
                resultResponse = testMatrixProcessService.enter(testMatrixProcessReq);
                break;
            case Submit:
                resultResponse = testMatrixProcessService.submit(testMatrixProcessReq);
                break;
            case Validate:
                resultResponse = testMatrixProcessService.complete(testMatrixProcessReq);
                break;
            case Cancel:
                resultResponse = testMatrixProcessService.cancel(testMatrixProcessReq);
            case NA:
                resultResponse = testMatrixProcessService.na(testMatrixProcessReq);
                break;
        }
        return resultResponse;
    }

    @Override
    public BaseResponse<Boolean> buildMatrixCombinedConditionDesc(OutPutDataBO outPutData) {
        if(Func.isEmpty(outPutData)){
            return BaseResponse.newFailInstance("common.empty",new Object[]{"outPutData"});
        }
        try {
            if(Func.isNotEmpty(outPutData.getTestLineList()) && Func.isNotEmpty(outPutData.getTestMatrixList())){
                List<com.sgs.framework.model.test.testline.v2.TestLineBO> testLineList = outPutData.getTestLineList();
                List<com.sgs.framework.model.test.testmatrix.v2.TestMatrixBO> testMatrixList = outPutData.getTestMatrixList();
                // 查询PP下维护的PPNotes
                Set<Long> ppArtifactRelIds = testLineList.stream().filter(testLine -> Func.isNotEmpty(testLine.getPpTestLineRelList())
                                && Func.isNotEmpty(testLine.getPpTestLineRelList().get(0).getId()) && Func.isNotEmpty(testLine.getPpTestLineRelList().get(0).getId().getPpArtifactRelId()))
                        .map(testLine -> testLine.getPpTestLineRelList().get(0).getId().getPpArtifactRelId()).collect(Collectors.toSet());
                Map<Long, List<PpArtifactInfoRsp>> ppArtifactInfoMaps = new HashMap<>();
                if (Func.isNotEmpty(ppArtifactRelIds)) {
                    PpArtifactInfoReq ppArtifactInfoReq = new PpArtifactInfoReq();
                    ppArtifactInfoReq.setPpArtifactRelIds(ppArtifactRelIds);
                    List<PpArtifactInfoRsp> ppArtifactList = trimsClient.getPpArtifactBaseInfoList(ppArtifactInfoReq);
                    if (Func.isNotEmpty(ppArtifactList)) {
                        ppArtifactInfoMaps = ppArtifactList.stream().collect(Collectors.groupingBy(PpArtifactInfoRsp::getPpArtifactRelId));
                    }
                }
                Set<Integer> testLineIdList = testLineList.stream().map(com.sgs.framework.model.test.testline.v2.TestLineBO::getId).map(TestLineIdBO::getTestLineId).filter(Func::isNotEmpty).collect(Collectors.toSet());
                Map<Integer, List<GetTestLineBaseInfoRsp>> testLineBaseInfoMap = this.getTestLineBaseInfo(testLineIdList);
                LanguageType primaryLanguage = LanguageType.English;
                BaseResponse<LanguageType> primaryLanguageRsp = ibuParam.getPrimaryLanguage(ProductLineContextHolder.getProductLineCode());
                if (Func.isNotEmpty(primaryLanguageRsp)) {
                    primaryLanguage = primaryLanguageRsp.getData();
                }
                boolean isChinese = LanguageType.check(primaryLanguage.getLanguageId(), LanguageType.Chinese);
                for (com.sgs.framework.model.test.testmatrix.v2.TestMatrixBO testMatrixBO : testMatrixList) {
                    if(Func.isNotEmpty(testMatrixBO.getRelationship()) && Func.isNotEmpty(testMatrixBO.getRelationship().getParent()) && Func.isNotEmpty(testMatrixBO.getRelationship().getParent().getTestLine())){
                        String testLineInstanceId = testMatrixBO.getRelationship().getParent().getTestLine().getTestLineInstanceId();
                        com.sgs.framework.model.test.testline.v2.TestLineBO testLineBO = testLineList.stream().filter(item -> Func.equalsSafe(testLineInstanceId, item.getId().getTestLineInstanceId())).findAny().orElse(null);
                        if(Func.isNotEmpty(testLineBO)){
                            Integer testLineId = testLineBO.getId().getTestLineId();
                            Integer testLineVersionId = testLineBO.getId().getTestLineVersionId();
                            Boolean isPP = Func.isNotEmpty(testLineBO.getPpTestLineRelList()) && Func.isNotEmpty(testLineBO.getPpTestLineRelList().get(0))
                                    && Func.isNotEmpty(testLineBO.getPpTestLineRelList().get(0).getId().getPpArtifactRelId()) && !Func.equalsSafe(testLineBO.getPpTestLineRelList().get(0).getId().getPpArtifactRelId(),0L);
                            String ppNotesEn = "";
                            String ppNotesCn = "";
                            if(isPP){
                                Long ppArtifactRelId = testLineBO.getPpTestLineRelList().get(0).getId().getPpArtifactRelId();
                                if (Func.isNotEmpty(ppArtifactInfoMaps) && ppArtifactInfoMaps.containsKey(ppArtifactRelId)) {
                                    List<PpArtifactInfoRsp> ppArtifactInfoRspList = ppArtifactInfoMaps.get(ppArtifactRelId);
                                    if(Func.isNotEmpty(ppArtifactInfoRspList)){
                                        PpArtifactInfoRsp ppArtifactInfoRsp = ppArtifactInfoRspList.get(0);
                                        if(Func.isNotEmpty(ppArtifactInfoRsp.getPpNotesAlias())){
                                            ppNotesEn = ppArtifactInfoRsp.getPpNotesAlias();
                                        }
                                        if(Func.isNotEmpty(ppArtifactInfoRsp.getLanguages())){
                                            PpArtifactRelLangInfoRsp ppArtifactRelCn = ppArtifactInfoRsp.getLanguages().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese))
                                                    .findAny().orElse(null);
                                            if(Func.isNotEmpty(ppArtifactRelCn)){
                                                ppNotesCn = ppArtifactRelCn.getPpNotesAlias();
                                            }
                                        }
                                    }
                                }
                            }else{
                                List<GetTestLineBaseInfoRsp> matchTestLineBaseInfoList = testLineBaseInfoMap.getOrDefault(testLineId, null);
                                if(Func.isNotEmpty(matchTestLineBaseInfoList)){
                                    GetTestLineBaseInfoRsp testLineBaseInfo = matchTestLineBaseInfoList.stream().filter(item->Func.equalsSafe(item.getTestLineVersionId(), testLineVersionId)).findAny().orElse(null);
                                    if(Func.isNotEmpty(testLineBaseInfo)){
                                        if (Func.isNotEmpty(testLineBaseInfo.getPpNotes())) {
                                            ppNotesEn = testLineBaseInfo.getPpNotes();
                                        }
                                        if (Func.isNotEmpty(testLineBaseInfo.getLanguages())) {
                                            TestLineBaseInfoLanguagesRsp testLineBaseInfoCn = testLineBaseInfo.getLanguages().stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese))
                                                    .findAny().orElse(null);
                                            if (Func.isNotEmpty(testLineBaseInfoCn)) {
                                                ppNotesCn = testLineBaseInfoCn.getPpNotes();
                                            }
                                        }
                                    }
                                }
                            }
                            List<ConditionBO> conditionList = testMatrixBO.getConditionList();

                            if(Func.isNotEmpty(conditionList)){
                                Map<Integer, List<ConditionBO>> conditionTypeListMap = conditionList.stream().collect(Collectors.groupingBy(item -> item.getId().getConditionTypeId()));
                                for (Integer conditionTypeId : conditionTypeListMap.keySet()) {
                                    List<ConditionBO> conditionBOList = conditionTypeListMap.get(conditionTypeId);
                                    if(Func.isNotEmpty(conditionBOList)){
                                        List<String> conditionDescEnList = new ArrayList<>();
                                        List<String> conditionDescCnList = new ArrayList<>();
                                        String conditionTypeNameEn = conditionBOList.get(0).getHeader().getConditionTypeName();
                                        String conditionTypeNameCn = conditionBOList.get(0).getHeader().getConditionTypeName();
                                        if(Func.isNotEmpty(conditionBOList.get(0).getHeader()) && Func.isNotEmpty(conditionBOList.get(0).getHeader().getLanguageList())){
                                            List<ConditionLanguageBO> languageList = conditionBOList.get(0).getHeader().getLanguageList();
                                            ConditionLanguageBO conditionLanguageBOEn = languageList.stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.English)).findAny().orElse(null);
                                            ConditionLanguageBO conditionLanguageBOCn = languageList.stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese)).findAny().orElse(null);
                                            if(Func.isNotEmpty(conditionLanguageBOEn) && Func.isNotEmpty(conditionLanguageBOEn.getConditionTypeName())){
                                                conditionTypeNameEn = conditionLanguageBOEn.getConditionTypeName();
                                            }
                                            if(Func.isNotEmpty(conditionLanguageBOCn) && Func.isNotEmpty(conditionLanguageBOCn.getConditionTypeName())){
                                                conditionTypeNameCn = conditionLanguageBOCn.getConditionTypeName();
                                            }
                                        }
                                        for (ConditionBO conditionBO : conditionBOList) {
                                            ConditionHeaderBO conditionHeaderBO = conditionBO.getHeader();
                                            if(Func.isEmpty(conditionHeaderBO)){
                                                continue;
                                            }
                                            String conditionDesc = conditionHeaderBO.getConditionDesc();
                                            List<ConditionLanguageBO> languageList = conditionHeaderBO.getLanguageList();
                                            if(Func.isNotEmpty(languageList)){
                                                ConditionLanguageBO conditionLanguageBOEn = languageList.stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.English)).findAny().orElse(null);
                                                ConditionLanguageBO conditionLanguageBOCn = languageList.stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese)).findAny().orElse(null);
                                                if(Func.isNotEmpty(conditionLanguageBOEn) && Func.isNotEmpty(conditionLanguageBOEn.getConditionDesc())){
                                                    conditionDescEnList.add(conditionLanguageBOEn.getConditionDesc());
                                                }else if(Func.isNotEmpty(conditionDesc)){
                                                    conditionDescEnList.add(conditionDesc);
                                                }else if(Func.isNotEmpty(conditionLanguageBOCn) && Func.isNotEmpty(conditionLanguageBOCn.getConditionDesc())){
                                                    conditionDescEnList.add(conditionLanguageBOCn.getConditionDesc());
                                                }
                                                if(Func.isNotEmpty(conditionLanguageBOCn) && Func.isNotEmpty(conditionLanguageBOCn.getConditionDesc())){
                                                    conditionDescCnList.add(conditionLanguageBOCn.getConditionDesc());
                                                }else if(Func.isNotEmpty(conditionLanguageBOEn) && Func.isNotEmpty(conditionLanguageBOEn.getConditionDesc())){
                                                    conditionDescCnList.add(conditionLanguageBOEn.getConditionDesc());
                                                }else if(Func.isNotEmpty(conditionDesc)){
                                                    conditionDescCnList.add(conditionDesc);
                                                }
                                            }else if(Func.isNotEmpty(conditionDesc)){
                                                conditionDescEnList.add(conditionDesc);
                                                conditionDescCnList.add(conditionDesc);
                                            }
                                        }
                                        if(Func.isNotEmpty(ppNotesEn)){
                                            if(ppNotesEn.contains("{" + conditionTypeNameEn + "}")){
                                                ppNotesEn = Utils.replaceAll(ppNotesEn, "\\{" +conditionTypeNameEn + "\\}", Func.join(conditionDescEnList,"&")).toString();
                                            }
                                        }
                                        if(Func.isNotEmpty(ppNotesCn)){
                                            if(ppNotesCn.contains("{" + conditionTypeNameCn + "}")){
                                                ppNotesCn = Utils.replaceAll(ppNotesCn, "\\{" +conditionTypeNameCn + "\\}", Func.join(conditionDescCnList,"&")).toString();
                                            }
                                        }
                                    }
                                }
                            }
                            if(isChinese){
                                testMatrixBO.getHeader().setCombinedConditionDescription(Func.isEmpty(ppNotesCn)?ppNotesEn:ppNotesCn);
                            }else{
                                testMatrixBO.getHeader().setCombinedConditionDescription(ppNotesEn);
                            }
                            List<TestMatrixHeaderLanguageBO> languageList = testMatrixBO.getHeader().getLanguageList();
                            if(Func.isEmpty(languageList)){
                                languageList = new ArrayList<>();
                            }
                            TestMatrixHeaderLanguageBO testMatrixHeaderLanguageBO = languageList.stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.English)).findAny().orElse(null);
                            if(Func.isEmpty(testMatrixHeaderLanguageBO)){
                                testMatrixHeaderLanguageBO = new TestMatrixHeaderLanguageBO();
                                testMatrixHeaderLanguageBO.setLanguageId(LanguageType.English.getLanguageId());
                                testMatrixHeaderLanguageBO.setCombinedConditionDescription(Func.toStr(ppNotesEn));
                                languageList.add(testMatrixHeaderLanguageBO);
                            }else{
                                String finalCombinedConditionDescriptionEn = ppNotesEn;
                                languageList.stream().forEach(item -> {
                                    if(LanguageType.check(item.getLanguageId(), LanguageType.English)){
                                        item.setCombinedConditionDescription(Func.toStr(finalCombinedConditionDescriptionEn));
                                    }
                                });
                            }
                            TestMatrixHeaderLanguageBO testMatrixHeaderLanguageBOCN = languageList.stream().filter(item -> LanguageType.check(item.getLanguageId(), LanguageType.Chinese)).findAny().orElse(null);
                            if(Func.isEmpty(testMatrixHeaderLanguageBOCN)){
                                testMatrixHeaderLanguageBOCN = new TestMatrixHeaderLanguageBO();
                                testMatrixHeaderLanguageBOCN.setLanguageId(LanguageType.Chinese.getLanguageId());
                                testMatrixHeaderLanguageBOCN.setCombinedConditionDescription(Func.toStr(ppNotesCn));
                                languageList.add(testMatrixHeaderLanguageBOCN);
                            }else{
                                String finalCombinedConditionDescriptionCn = ppNotesCn;
                                languageList.stream().forEach(item -> {
                                    if(LanguageType.check(item.getLanguageId(), LanguageType.Chinese)){
                                        item.setCombinedConditionDescription(Func.toStr(finalCombinedConditionDescriptionCn));
                                    }
                                });
                            }
                            testMatrixBO.getHeader().setLanguageList(languageList);
                        }
                    }
                }
                outPutData.setTestMatrixList(testMatrixList);
            }
        } catch (Exception e) {
            log.error("buildMatrixCombinedConditionDesc error:{}",e);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    private Map<Integer, List<GetTestLineBaseInfoRsp>> getTestLineBaseInfo(Set<Integer> testLineIds) {
        Map<Integer, List<GetTestLineBaseInfoRsp>> testLineBaseInfoMaps = Maps.newHashMap();
        if (Func.isEmpty(testLineIds)) {
            return testLineBaseInfoMaps;
        }
        GetTestLineBaseInfoReq reqObject = new GetTestLineBaseInfoReq();
        reqObject.setTestLineIds(testLineIds);
        List<Integer> languageIds = Lists.newArrayList();
        languageIds.add(LanguageType.English.getLanguageId());
        languageIds.add(LanguageType.Chinese.getLanguageId());
        reqObject.setLanguageIds(Lists.newArrayList(languageIds));
        List<GetTestLineBaseInfoRsp> testLineBaseInfoList = trimsClient.getTestLineBaseInfo(reqObject);
        if (Func.isNotEmpty(testLineBaseInfoList)) {
            testLineBaseInfoMaps = testLineBaseInfoList.stream().collect(Collectors.groupingBy(GetTestLineBaseInfoRsp::getTestLineId));
        }
        return testLineBaseInfoMaps;
    }

    private BaseResponse<Boolean> validateAttr(TestMatrixProcessReq testMatrixProcessReq) {
        if (Func.isEmpty(testMatrixProcessReq)) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        if (Func.isEmpty(testMatrixProcessReq.getTestMatrixIdList())) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"TestMatrixIdList"});
        }
        if (Func.isEmpty(testMatrixProcessReq.getAction())) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"Action"});
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse<List<com.sgs.framework.model.test.testmatrix.v2.TestMatrixBO>> queryBO(TestMatrixQueryReq request) {
        // 入参不允许为空
        Assert.isTrue(Func.isNotEmpty(request),"common.param.miss",new Object[]{Constants.TERM.REQUEST});
        // 参数必须是TestMatrixQueryReq
        TestMatrixContext<TestMatrixQueryReq,com.sgs.framework.model.test.testmatrix.v2.TestMatrixBO> testMatrixContext =
                new TestMatrixContext();
        testMatrixContext.setParam(request);
        if(Func.isEmpty(request.getLab())){
            request.setLab(SystemContextHolder.getLab());
        }
        testMatrixContext.setLab(SystemContextHolder.getLab());
        testMatrixContext.setToken(SecurityContextHolder.getSgsToken());
        testMatrixContext.setUserInfo(SecurityContextHolder.getUserInfo());
        return BaseExecutor.start(TestMatrixQueryCMD.class, testMatrixContext);
    }

}
