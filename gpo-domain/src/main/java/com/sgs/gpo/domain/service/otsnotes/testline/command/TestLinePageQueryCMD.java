package com.sgs.gpo.domain.service.otsnotes.testline.command;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.TestLineStatus;
import com.sgs.framework.model.order.order.OrderBO;
import com.sgs.framework.model.order.order.OrderHeaderBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.test.testmatrix.TestMatrixBO;
import com.sgs.framework.model.test.testsample.TestSampleBO;
import com.sgs.framework.model.trims.labsection.LabSectionBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.domain.service.otsnotes.testline.context.TestLineQueryContext;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.attachment.OrderAttachmentPO;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixService;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.ITestSampleService;
import com.sgs.gpo.domain.service.preorder.attachment.IOrderAttachmentService;
import com.sgs.gpo.facade.model.lab.req.LabTeamReq;
import com.sgs.gpo.facade.model.otsnotes.testline.TestLinePageBO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.rsp.TestMatrixSampleRsp;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.priceengine.facade.QuotationFacade;
import com.sgs.priceengine.facade.model.DTO.TLAmountDTO;
import com.sgs.priceengine.facade.model.request.QueryTLAmountRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: TestLinePageQueryCMD
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/5 10:37
 */
@Service
@Slf4j
@Scope(value = "prototype")
public class TestLinePageQueryCMD extends PPTestLineQueryCMD {
    @Autowired
    private IOrderDomainService orderDomainService;
    @Autowired
    private QuotationFacade quotationFacade;
    @Autowired
    private IOrderAttachmentService orderAttachmentService;
    @Autowired
    private ITestMatrixService testMatrixService;
    @Autowired
    private ITestSampleService testSampleService;


    @Override
    public BaseResponse validateParam(TestLineQueryContext<OrderTestLineReq, TestLineBO> context) {
        if (Func.isEmpty(context) || Func.isEmpty(context.getParam())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
//        校验分页参数
        Integer rows = context.getRows();
        Integer page = context.getPage();
        if (Func.isEmpty(page) || Func.isEmpty(rows)) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"rows/page"});
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse buildDomain(TestLineQueryContext<OrderTestLineReq, TestLineBO> context) {
        super.buildDomain(context);
        List<TestLinePageBO> testLinePageBOList = new ArrayList<>();
        List<TestLineBO> testLineList = context.getTestLineList();
        List<OrderBO> orderBOList = context.getOrderBOList();
        List<TestMatrixBO> testMatrixBOList = context.getTestMatrixBOList();
        List<TestSampleBO> testSampleBOList = context.getTestSampleBOList();
        if (Func.isNotEmpty(testLineList)) {
            for (TestLineBO testLineBO : testLineList) {
                TestLinePageBO testLinePageBO = Func.deepCopy(testLineBO, TestLinePageBO.class);
                if (Func.isNotEmpty(orderBOList)) {
                    OrderBO orderBO = orderBOList.stream().filter(item -> Func.equals(item.getHeader().getOrderNo(), testLineBO.getOrderNo())).findAny().orElse(null);
                    if (Func.isNotEmpty(orderBO) && Func.isNotEmpty(orderBO.getHeader())) {
                        testLinePageBO.setCsName(orderBO.getHeader().getCsName());
                        testLinePageBO.setOrderStatus(orderBO.getHeader().getOrderStatus());
                    }
                }
                if (testLinePageBO.getPendingFlag()) {
                    testLinePageBO.setTestLineStatus(TestLineStatus.Pending.getStatus());
                }
                this.setTlAmount(context, testLinePageBO);
                this.buildTestLineAttachment(context, testLinePageBO);
                this.buildDisplayName(testLinePageBO);

                if (Func.isNotEmpty(testMatrixBOList) && Func.isNotEmpty(testSampleBOList)) {
                    List<TestMatrixBO> testMatrixBOS = testMatrixBOList.parallelStream().filter(item -> Func.equalsSafe(item.getTestLineInstanceId(), testLineBO.getTestLineInstanceId())).collect(Collectors.toList());
                    List<TestMatrixSampleRsp> testMatrixSampleRspList = new ArrayList<>();
                    for (TestMatrixBO testMatrixBO : testMatrixBOS) {
                        TestMatrixSampleRsp testMatrixSampleRsp = new TestMatrixSampleRsp();
                        String testMatrixId = testMatrixBO.getTestMatrixId();
                        TestSampleBO testSampleBO = testSampleBOList.stream().filter(item -> Func.equalsSafe(item.getTestSampleInstanceId(), testMatrixBO.getTestSampleInstanceId())).sorted(Comparator.comparing(TestSampleBO::getTestSampleNo, Comparator.nullsLast(String::compareTo))).findFirst().orElse(null);
                        if (Func.isNotEmpty(testSampleBO)) {
                            testMatrixSampleRsp.setSampleNo(testSampleBO.getTestSampleNo());
                        }
                        testMatrixSampleRsp.setTestMatrixId(testMatrixId);
                        testMatrixSampleRspList.add(testMatrixSampleRsp);
                    }
                    testLinePageBO.setTestMatrixSampleList(testMatrixSampleRspList);
                    Set<String> matchTestSampleIdList = testMatrixBOList.stream().filter(item -> Func.equalsSafe(item.getTestLineInstanceId(), testLineBO.getTestLineInstanceId())).map(TestMatrixBO::getTestSampleInstanceId).filter(Func::isNotEmpty).collect(Collectors.toSet());
                    if (Func.isNotEmpty(matchTestSampleIdList)) {
                        String sampleNo = testSampleBOList.stream().filter(item -> matchTestSampleIdList.contains(item.getTestSampleInstanceId())).sorted(Comparator.comparing(TestSampleBO::getTestSampleSeq, Comparator.nullsLast(Integer::compareTo))).map(TestSampleBO::getTestSampleNo).filter(Func::isNotEmpty).collect(Collectors.joining(","));
                        testLinePageBO.setSample(sampleNo);
                    }
                }

                testLinePageBOList.add(testLinePageBO);
            }
        }
        Page<TestLinePageBO> testLinePageBOPage = new Page<>();
        testLinePageBOPage.setRecords(testLinePageBOList);
        testLinePageBOPage.setPages(context.getTestLineBOPage().getPages());
        testLinePageBOPage.setTotal(context.getTestLineBOPage().getTotal());
        context.setTestLinePageBOPage(testLinePageBOPage);
        return BaseResponse.newSuccessInstance(true);
    }

    private void buildDisplayName(TestLinePageBO testLinePageBO) {
        if (Func.isNotEmpty(testLinePageBO)) {
            if (Func.isNotEmpty(testLinePageBO.getCitation())) {
                testLinePageBO.setDisplayCitationName(testLinePageBO.getCitation().getCitationFullName());
            }
        }
        if (Func.isNotEmpty(testLinePageBO.getLabSectionList())) {
            String labSectionNames = testLinePageBO.getLabSectionList().stream().map(LabSectionBO::getLabSectionName).collect(Collectors.joining(","));
            testLinePageBO.setDisplayLabSectionName(labSectionNames);
        }
        if (Func.isNotEmpty(testLinePageBO.getSubcontractBO())) {
            String subcontractLabCode = testLinePageBO.getSubcontractBO().getSubcontractLabCode();
            testLinePageBO.setDisplaySubcontractLabCode(subcontractLabCode);
        }
        if (Func.isNotEmpty(testLinePageBO.getPpTestLineRel())) {
            testLinePageBO.setDisplayPpNo(Func.toStr(testLinePageBO.getPpTestLineRel().getPpNo()));
        }

    }

    private void setTlAmount(TestLineQueryContext<OrderTestLineReq, TestLineBO> context, TestLinePageBO testLinePageBO) {
        if (Func.isEmpty(testLinePageBO)) {
            return;
        }
        List<TLAmountDTO> tlAmountList = context.getTlAmountList();
        testLinePageBO.setAmount(BigDecimal.ZERO);
        testLinePageBO.setAmountText("Unable to calculate");
        String ppTlRelId = "";
        if (Func.isNotEmpty(testLinePageBO.getPpTestLineRel())) {
            ppTlRelId = testLinePageBO.getPpTestLineRel().getPpTlRelId();
        }
        if (Func.isNotEmpty(tlAmountList)) {
            String finalPpTlRelId = ppTlRelId;
            List<TLAmountDTO> tlOrderAmountDTOList = tlAmountList.stream().filter(e ->
                    {
                        return Func.equalsSafe(e.getPpTlRelId(), finalPpTlRelId);
                    }

            ).collect(Collectors.toList());
            BigDecimal tlBigAmount = null;
            if (Func.isNotEmpty(tlOrderAmountDTOList)) {
                Double d = tlOrderAmountDTOList.stream().mapToDouble(e -> e.getAmount() == null ? 0 : e.getAmount().doubleValue()).sum();
                tlBigAmount = BigDecimal.valueOf(d).setScale(2, BigDecimal.ROUND_HALF_UP);
            }
            DecimalFormat df = new DecimalFormat(",###,###.00");
            String amountText = tlBigAmount == null || tlBigAmount.compareTo(BigDecimal.ZERO) == 0 ? "0.00" : df.format(tlBigAmount);
            testLinePageBO.setAmount(tlBigAmount);
            testLinePageBO.setAmountText(tlBigAmount != null ? tlOrderAmountDTOList.get(0).getCurrencyCodeDisplay() + amountText : "Unable to calculate");
        }
    }

    private void buildTestLineAttachment(TestLineQueryContext<OrderTestLineReq, TestLineBO> context, TestLinePageBO testLinePageBO) {
        List<TestMatrixBO> testMatrixBOList = context.getTestMatrixBOList();
        List<OrderAttachmentPO> orderAttachmentPOList = context.getOrderAttachmentPOList();
        //是否有attachment
        List<TestMatrixBO> testLineMatrixBoList = null;
        Set<String> matrixSampleIdList = null;
        if (Func.isNotEmpty(testMatrixBOList)) {
            testLineMatrixBoList = testMatrixBOList.stream().filter(item -> Func.equalsSafe(item.getTestLineInstanceId(), testLinePageBO.getTestLineInstanceId())).collect(Collectors.toList());
            matrixSampleIdList = testLineMatrixBoList.stream().map(TestMatrixBO::getTestSampleInstanceId).collect(Collectors.toSet());
        }
        List<OrderAttachmentPO> testLineAttachmentList = new ArrayList<>();
        if (Func.isNotEmpty(orderAttachmentPOList)) {
            Set<String> finalMatrixSampleIdList = matrixSampleIdList;
            testLineAttachmentList = orderAttachmentPOList.stream().filter(item -> {
                return Func.equalsSafe(item.getTestLineInstanceId(), testLinePageBO.getTestLineInstanceId()) || (Func.isNotEmpty(finalMatrixSampleIdList) && finalMatrixSampleIdList.contains(item.getObjectID()));
            }).collect(Collectors.toList());
        }
        if (Func.isNotEmpty(testLineAttachmentList)) {
            testLinePageBO.setHavingAttachment(true);
        }
    }

    private List<TLAmountDTO> getTLAmount(List<String> orderIdList) {
        if (Func.isEmpty(orderIdList)) {
            return null;
        }
        //查询tl amount
        QueryTLAmountRequest objQueryTLAmountRequest = new QueryTLAmountRequest();
        objQueryTLAmountRequest.setSystemId(15);
        objQueryTLAmountRequest.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        objQueryTLAmountRequest.setOrderIdList(orderIdList);
        log.info("get tl amount req:" + JSON.toJSONString(objQueryTLAmountRequest));
        BaseResponse<List<TLAmountDTO>> tlAmountReponse = quotationFacade.queryTLAmount(objQueryTLAmountRequest);
        log.info("get tl amount rsp:" + JSON.toJSONString(tlAmountReponse));
        List<TLAmountDTO> tlAmountDTOList = com.google.common.collect.Lists.newArrayList();
        if (tlAmountReponse.getStatus() == 200 && Func.isNotEmpty(tlAmountReponse.getData())) {
            tlAmountDTOList = tlAmountReponse.getData();
        }
        return tlAmountDTOList;
    }

    @Override
    public BaseResponse<List<TestLineBO>> execute(TestLineQueryContext<OrderTestLineReq, TestLineBO> context) {
        BaseResponse buildResponse = this.buildDomain(context);
        if (buildResponse.isFail()) {
            return buildResponse;
        }
        return BaseResponse.newSuccessInstance(context.getTestLinePageBOPage());
    }

    @Override
    public BaseResponse before(TestLineQueryContext<OrderTestLineReq, TestLineBO> context) {
        OrderTestLineReq orderTestLineReq = context.getParam();
        if (Func.isNotEmpty(SystemContextHolder.getLab())) {
            orderTestLineReq.setLabId(SystemContextHolder.getLab().getLabId());
            orderTestLineReq.setLabCode(SystemContextHolder.getLab().getLabCode());
        }
        orderTestLineReq.setIgnoreChargeOrder(true);
        Page<TestLineBO> testLineBOPage = new Page<>(context.getPage(), context.getRows());
        testLineBOPage.setSearchCount(false);
        Long total = testLineMapper.selectPPTestLinePageCount(orderTestLineReq);
        testLineBOPage.setTotal(total);
        testLineBOPage.setRecords(new ArrayList<>());
        context.setTestLineBOPage(testLineBOPage);
        context.setTestLineList(new ArrayList<>());
        if(Func.toLong(total)== 0L){
            return BaseResponse.newSuccessInstance(true);
        }

        super.before(context);
        List<TestLineBO> testLineList = context.getTestLineList();
        testLineBOPage.setRecords(testLineList);
        context.setTestLineBOPage(testLineBOPage);
        context.setTestLineList(testLineList);

        List<OrderBO> orderBOList = null;
        List<TestMatrixBO> testMatrixBOList = null;
        List<TestSampleBO> testSampleBOList = null;
        List<OrderAttachmentPO> orderAttachmentPOList = null;
        List<TLAmountDTO> tlAmountList = null;
        if (Func.isNotEmpty(testLineList)) {
            Set<String> orderIdList = testLineList.stream().map(TestLineBO::getOrderId).collect(Collectors.toSet());
            context.setOrderIdList(orderIdList);
            Set<String> orderNoList = testLineList.stream().map(TestLineBO::getOrderNo).collect(Collectors.toSet());
            Set<String> testLineInstanceIdList = testLineList.stream().map(TestLineBO::getTestLineInstanceId).collect(Collectors.toSet());
            OrderQueryReq orderQueryReq = new OrderQueryReq();
            orderQueryReq.setOrderNoList(orderNoList);
            BaseResponse<List<OrderBO>> queryOrderResponse = orderDomainService.queryV1(orderQueryReq);
            if (queryOrderResponse.isSuccess()) {
                orderBOList = queryOrderResponse.getData();
            }
            if (Func.isNotEmpty(orderBOList)) {
                List<String> preorderIdList = orderBOList.stream().map(OrderBO::getHeader).map(OrderHeaderBO::getOrderId).collect(Collectors.toList());
                tlAmountList = this.getTLAmount(preorderIdList);
            }
            //查询testLine对应的Attachment
            //先查询testLine对应的matrix
            TestMatrixQueryReq testMatrixQueryReq = new TestMatrixQueryReq();
            testMatrixQueryReq.setTestLineInstanceIdList(testLineInstanceIdList);
            BaseResponse<List<TestMatrixBO>> testMatrixResponse = testMatrixService.queryV1(testMatrixQueryReq);
            Set<String> testSampleIdList = new HashSet<>();
            if (Func.isNotEmpty(testMatrixResponse) && Func.isNotEmpty(testMatrixResponse.getData())) {
                testMatrixBOList = testMatrixResponse.getData();
                testSampleIdList = testMatrixBOList.stream().map(TestMatrixBO::getTestSampleInstanceId).collect(Collectors.toSet());
            }
            if (Func.isNotEmpty(testSampleIdList)) {
                TestSampleQueryReq testSampleQueryReq = new TestSampleQueryReq();
                testSampleQueryReq.setTestSampleInstanceIdList(testSampleIdList);
                BaseResponse<List<TestSampleBO>> testSampleRes = testSampleService.queryV1(testSampleQueryReq);
                if (Func.isNotEmpty(testSampleRes) && Func.isNotEmpty(testSampleRes.getData())) {
                    testSampleBOList = testSampleRes.getData();
                    testSampleBOList = testSampleBOList.stream().sorted(Comparator.comparing(TestSampleBO::getTestSampleNo, Comparator.nullsLast(String::compareTo))).collect(Collectors.toList());
                }
            }
            if (Func.isNotEmpty(context.getParam().getIsToDoList()) && !context.getParam().getIsToDoList()) {
                BaseResponse<List<OrderAttachmentPO>> orderAttachmentPORes = orderAttachmentService.queryTestLineAttachment(testLineInstanceIdList, testSampleIdList);
                if (orderAttachmentPORes.isSuccess()) {
                    orderAttachmentPOList = orderAttachmentPORes.getData();
                }
            }
        }
        context.setTlAmountList(tlAmountList);
        context.setOrderAttachmentPOList(orderAttachmentPOList);
        context.setTestSampleBOList(testSampleBOList);
        context.setTestMatrixBOList(testMatrixBOList);
        context.setOrderBOList(orderBOList);
        return BaseResponse.newSuccessInstance(true);
    }
}
