package com.sgs.gpo.domain.service.preorder.order.subdomain;


import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.BossOrderInvoicePO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.payment.BossOrderInvoiceDTO;

import java.util.List;
import java.util.Set;

public interface IBossOrderInvoiceService extends IService<BossOrderInvoicePO> {
    BaseResponse<List<BossOrderInvoiceDTO>> getBossInvoiceByOrderNo(String orderNo);
    BaseResponse<List<BossOrderInvoicePO>> getInvoiceByOrderNo(String orderNo);
    Long getNoInvoiceQuotationCount(String orderNo);
    int batchUpdatePaidAmountByInvoiceNo(List<BossOrderInvoicePO> bossOrderInvoicePOList);
    List<String> getOrderNoByInvoice(List<String> invoiceNos);
    int insertBatch(List<BossOrderInvoicePO> bossOrderInvoiceInfoPOS);
    Boolean getDataByBossOrderAndInvoice(String bossOrderNo, String invoiceNo);
    int updatePaidAmountByInvoiceNoBatch(List<BossOrderInvoicePO> bossOrderInvoicePOList);
    List<BossOrderInvoicePO> getBossOrderNoByInvoice(String invoiceNo);
    List<BossOrderInvoiceDTO> getBossInvoiceByOrderNos(Set<String> orderNoList);
}
