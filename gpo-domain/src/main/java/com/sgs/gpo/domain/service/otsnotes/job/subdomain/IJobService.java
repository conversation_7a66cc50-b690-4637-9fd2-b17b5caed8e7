package com.sgs.gpo.domain.service.otsnotes.job.subdomain;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.test.execution.v2.job.JobBO;
import com.sgs.framework.model.test.execution.v2.job.JobIdBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.open.platform.base.service.IBaseService;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.job.JobPO;
import com.sgs.gpo.facade.model.job.req.JobDeleteReq;
import com.sgs.gpo.facade.model.job.req.JobQueryReq;
import com.sgs.gpo.facade.model.otsnotes.job.req.JobLabInReq;
import com.sgs.gpo.facade.model.otsnotes.job.req.JobLabOutReq;
import com.sgs.gpo.facade.model.otsnotes.job.req.JobAssignOwnerReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/7 12:00
 */
public interface IJobService extends IBaseService<JobBO,JobPO, JobIdBO,JobQueryReq> {

    /**
     * 校验TestLine 是否满足创建的条件
     * @param testLineList
     * @return
     */
    BaseResponse<Boolean> validateRel(List<TestLineBO> testLineList,boolean protocolFlag);

    BaseResponse<Boolean> updateJobOwner(JobAssignOwnerReq assignJobOwnerReq);

    BaseResponse<Boolean> updateJobForLabOut(JobLabOutReq jobLabOutReq);
    /**
     * jobList labIn
     * @param jobLabInReq
     * @return
     */
    @Deprecated
    BaseResponse jobLabIn(JobLabInReq jobLabInReq);

    /**
     * jobList labIn
     * @param jobLabInReq
     * @return
     */
    BaseResponse<List<JobPO>>  labIn(JobLabInReq jobLabInReq);

    /**
     * jobList labOut
     * @param jobLabOutReq
     * @return
     */
    BaseResponse<List<JobPO>> labOutCompleted(JobLabOutReq jobLabOutReq);

    BaseResponse<List<JobPO>> labOutClosed(JobLabOutReq jobLabOutReq);

    BaseResponse<List<JobPO>> queryJobByTestLine(JobQueryReq jobQueryReq);


    /**
     * 删除Job和TL的关系，清空TL的TestStartDate和Engineer
     * @return
     */
    BaseResponse<Boolean> removeByJobAndTL(JobDeleteReq jobDeleteReq);

    BaseResponse<List<JobPO>> select(JobQueryReq jobQueryReq);
}
