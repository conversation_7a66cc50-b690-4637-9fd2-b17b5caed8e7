package com.sgs.gpo.domain.service.otsnotes.testsample.command;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.enums.SampleType;
import com.sgs.framework.model.order.v2.OrderIdRelBO;
import com.sgs.framework.model.test.testsample.TestSampleGroupBO;
import com.sgs.framework.model.test.testsample.TestSampleMaterialAttrBO;
import com.sgs.framework.model.test.testsample.v2.*;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.*;
import com.sgs.gpo.domain.service.otsnotes.testsample.context.TestSampleContext;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.*;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.LimitGroupQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.ReferDataRelationShipReq;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleLanguageQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/28 16:55
 * @Description TestSample查询输出标准结构TestSampleBO
 */
@Primary
@Service
@Slf4j
public class TestSampleQueryCMD extends BaseCommand<TestSampleContext<TestSampleQueryReq>> {

    @Autowired
    private ITestSampleService testSampleService;
    @Autowired
    private ILimitGroupService limitGroupService;
    @Autowired
    private ITestSampleLanguageService testSampleLanguageService;
    @Autowired
    private IReferDataRelationShipService referDataRelationShipService;
    @Autowired
    private ITestSampleGroupService testSampleGroupService;

    @Override
    public BaseResponse validateParam(TestSampleContext<TestSampleQueryReq> context) {
        Assert.isTrue(Func.isNotEmpty(context.getParam()),"common.param.miss",new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(context.getParam().getLab()),"common.param.miss",new Object[]{Constants.TERM.LAB.getCode()});
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(TestSampleContext<TestSampleQueryReq> context) {
        //查询TestSample
        TestSampleQueryReq testSampleQueryReq = context.getParam();
        List<TestSamplePO> samplePOList = Lists.newArrayList();
        //TODO Query方法占用 先用select
        BaseResponse<List<TestSamplePO>> testSampleResponse = testSampleService.select(testSampleQueryReq);
        if(testSampleResponse.isSuccess() && Func.isNotEmpty(testSampleResponse.getData())){
            samplePOList = testSampleResponse.getData();
        }
        context.setSamplePOList(samplePOList);
        if(Func.isNotEmpty(samplePOList)){
            //查询limitGroup
            Set<String> testSampleInstanceIds = samplePOList.stream().map(TestSamplePO::getId).collect(Collectors.toSet());
            List<LimitGroupPO> limitGroupList =Lists.newArrayList();
            LimitGroupQueryReq limitGroupQueryReq = new LimitGroupQueryReq();
            limitGroupQueryReq.setTestSampleInstanceIds(testSampleInstanceIds);
            BaseResponse<List<LimitGroupPO>> limitGroupResponse = limitGroupService.select(limitGroupQueryReq);
            if(limitGroupResponse.isSuccess() && Func.isNotEmpty(limitGroupResponse.getData())){
                limitGroupList = limitGroupResponse.getData();
            }
            context.setLimitGroupList(limitGroupList);
            //查询testSampleLanguage
            List<TestSampleLanguagePO> testSampleLanguagePOList = Lists.newArrayList();
            TestSampleLanguageQueryReq testSampleLanguageQueryReq = new TestSampleLanguageQueryReq();
            testSampleLanguageQueryReq.setTestSampleInstanceIdList(testSampleInstanceIds);
            BaseResponse<List<TestSampleLanguagePO>> testSampleLanguageResponse = testSampleLanguageService.query(testSampleLanguageQueryReq);
            if(testSampleLanguageResponse.isSuccess() && Func.isNotEmpty(testSampleLanguageResponse.getData())){
                testSampleLanguagePOList = testSampleLanguageResponse.getData();
            }
            context.setTestSampleLanguagePOList(testSampleLanguagePOList);
            // 查询当前订单下的样品引用关系
            ReferDataRelationShipReq relationShipReq = new ReferDataRelationShipReq();
            relationShipReq.setCurrentOrderNoList(samplePOList.stream().map(e -> e.getOrderNo()).collect(Collectors.toSet()));
            BaseResponse<List<ReferDataRelationshipPO>> referDataRes = referDataRelationShipService.query(relationShipReq);
            if(referDataRes.isSuccess() && Func.isNotEmpty(referDataRes.getData())){
                context.setReferSampleList(referDataRes.getData());
            }
            //查询SampleGroup
            testSampleQueryReq.setTestSampleInstanceIdList(samplePOList.stream().map(e -> e.getId()).collect(Collectors.toSet()));
            List<TestSampleGroupPO> testSampleGroupPOList = Lists.newArrayList();
            BaseResponse<List<TestSampleGroupPO>> testSampleGroupRsp = testSampleGroupService.query(testSampleQueryReq);
            if(testSampleGroupRsp.isSuccess() && Func.isNotEmpty(testSampleGroupRsp.getData())){
                testSampleGroupPOList = testSampleGroupRsp.getData();
            }
            context.setTestSampleGroupPOList(testSampleGroupPOList);
        }
        return super.before(context);
    }

    @Override
    public BaseResponse buildDomain(TestSampleContext<TestSampleQueryReq> context) {
        List<TestSampleBO> testSampleList = Lists.newArrayList();
        List<TestSamplePO> samplePOList = context.getSamplePOList();
        List<LimitGroupPO> limitGroupList = context.getLimitGroupList();
        List<TestSampleLanguagePO> testSampleLanguagePOList = context.getTestSampleLanguagePOList();
        List<ReferDataRelationshipPO> referSampleList = context.getReferSampleList();
        List<TestSampleGroupPO> testSampleGroupPOList = context.getTestSampleGroupPOList();
        Map<String,TestSamplePO> testSampleMap = Maps.newHashMap();
        samplePOList.stream().forEach(item-> testSampleMap.put(item.getId(),item));
        samplePOList.stream().forEach(ts->{
            TestSampleBO testSampleBO = new TestSampleBO();
            testSampleBO.setActiveIndicator(ts.getActiveIndicator());
            List<TestSampleGroupPO> testSampleGroupList = testSampleGroupPOList.stream().filter(
                    e -> Func.equalsSafe(e.getSampleId(), ts.getId())).collect(Collectors.toList());
            // 1.id
            TestSampleIdBO id = new TestSampleIdBO();
            id.setTestSampleInstanceId(ts.getId());
            id.setTestSampleNo(ts.getSampleNo());
            TestSampleExternalBO external = new TestSampleExternalBO();
            external.setExternalSampleId(ts.getExternalSampleId());
            id.setExternal(external);
            testSampleBO.setId(id);
            // 2.relationship
            String orderNo = ts.getOrderNo();
            assembleRelationship(testSampleBO,orderNo);
            // 3.header
            TestSampleHeaderBO header = new TestSampleHeaderBO();
            Func.copy(ts,header);
            header.setParentTestSampleId(ts.getSampleParentId());
            header.setTestSampleType(ts.getSampleType());
            header.setTestSampleSeq(ts.getSampleSeq());
            header.setGroupType(ts.getGroupType());
            if(Func.isNotEmpty(testSampleGroupList)){
                List<TestSampleGroupBO> testSampleGroupBoList = Lists.newArrayList();
                testSampleGroupList.forEach(item->{
                    TestSampleGroupBO testSampleGroupBO = new TestSampleGroupBO();
                    testSampleGroupBO.setTestSampleInstanceId(item.getSampleId());
                    testSampleGroupBO.setSampleGroupId(item.getSampleGroupId());
                    testSampleGroupBO.setMainSampleFlag(item.getMainMaterialFlag());
                    testSampleGroupBO.setSequence(item.getSequence());
                    testSampleGroupBoList.add(testSampleGroupBO);
                });
                header.setTestSampleGroupList(testSampleGroupBoList);
            }
            testSampleBO.setHeader(header);
            // 4.materialAttr
            TestSampleMaterialAttrBO materialAttr = new TestSampleMaterialAttrBO();
            materialAttr.setMaterialDescription(ts.getDescription());
            materialAttr.setMaterialOtherSampleInfo(ts.getOtherSampleInfo());
            materialAttr.setMaterialName(ts.getMaterial());
            materialAttr.setMaterialEndUse(ts.getEndUse());
            materialAttr.setMaterialColor(ts.getColor());
            materialAttr.setMaterialComposition(ts.getComposition());
            materialAttr.setMaterialRemark(ts.getSampleRemark());
            materialAttr.setMaterialCategory(ts.getCategory());
            testSampleBO.setMaterialAttr(materialAttr);
            assembleMaterial(testSampleBO,testSampleLanguagePOList,testSampleGroupList,testSampleMap);
            //TODO conclusion & attachmentList 后续补充
            // 5.conclusion
            // 6.attachmentList
            // 7 limitGroupList
            assembleLimitGroup(testSampleBO, limitGroupList);
            //设置SourceSampleId
            if(Func.isNotEmpty(referSampleList)){
                ReferDataRelationshipPO currentSampleRel = referSampleList.stream().filter(item->Func.equalsSafe(item.getCurrentSampleId(),testSampleBO.getId().getTestSampleInstanceId()))
                        .findAny().orElse(null);
                if(Func.isNotEmpty(currentSampleRel)){
                    testSampleBO.getRelationship().getParent().setSourceSampleId(currentSampleRel.getSourceSampleId());
                    // 查询SourceSampleNo
                    TestSamplePO sourceSample = testSampleService.getById(currentSampleRel.getSourceSampleId());
                    if(Func.isNotEmpty(sourceSample)){
                        ReferSampleBO referSample = new ReferSampleBO();
                        referSample.setSourceSampleId(sourceSample.getId());
                        referSample.setSourceSampleNo(sourceSample.getSampleNo());
                        referSample.setSourceOrderNo(currentSampleRel.getSourceOrderNo());
                        testSampleBO.getRelationship().getParent().setReferSample(referSample);
                    }
                }
            }
            testSampleList.add(testSampleBO);
        });
        return BaseResponse.newSuccessInstance(testSampleList);
    }

    @Override
    public BaseResponse execute(TestSampleContext<TestSampleQueryReq> context) {
        return buildDomain(context);
    }
    private void assembleMaterial(TestSampleBO testSampleBO, List<TestSampleLanguagePO> testSampleLanguageList,List<TestSampleGroupPO> testSampleGroupList
            ,Map<String,TestSamplePO> testSampleMap){
        if(Func.isEmpty(testSampleLanguageList)){
            return;
        }
        List<TestSamplePO> testSampleList = Lists.newArrayList();
        if(SampleType.check(testSampleBO.getHeader().getTestSampleType(),SampleType.MixSample)){
            if(Func.isNotEmpty(testSampleGroupList)){
                testSampleGroupList.stream().forEach(item->{
                    if(testSampleMap.containsKey(item.getSampleGroupId())){
                        testSampleList.add(testSampleMap.get(item.getSampleGroupId()));
                    }
                });
            }
        }else {
            testSampleList.add(testSampleMap.get(testSampleBO.getId().getTestSampleInstanceId()));
        }
        List<String> sampleMaterialList = Lists.newArrayList();
        testSampleList.stream().sorted(Comparator.comparing(TestSamplePO::getSampleSeq)).forEach(testSamplePO -> {
            List<TestSampleLanguagePO> currentTestSampleLanguage = testSampleLanguageList.stream().filter(item->Func.equalsSafe(item.getSampleId(),testSamplePO.getId()))
                    .collect(Collectors.toList());
            if(Func.isEmpty(currentTestSampleLanguage)){
                return;
            }
            // 按照Group分组 按language排序
            Map<String,List<TestSampleLanguagePO>> testSampleLanguageMap = currentTestSampleLanguage.stream().sorted(Comparator.comparing(TestSampleLanguagePO::getLanguageId).thenComparing(TestSampleLanguagePO::getCreatedDate))
                    .collect(Collectors.groupingBy(TestSampleLanguagePO::getGroupId));
            // 对Map 按照key排序
            TreeMap<String,List<TestSampleLanguagePO>> sortedMap = testSampleLanguageMap.entrySet().stream()
                    .sorted(Comparator.comparing(Map.Entry::getKey))
                    .collect(Collectors.toMap(Map.Entry::getKey,Map.Entry::getValue,(k1,k2)->k1,TreeMap::new));
            List<String> materialList = Lists.newArrayList();
            sortedMap.forEach((key,val)->{
                if(Func.isNotEmpty(val)){
                    materialList.add(val.stream().map(TestSampleLanguagePO::getMaterial).collect(Collectors.joining("\\")));
                }
            });
            if(Func.isNotEmpty(materialList)){
                sampleMaterialList.add(materialList.stream().collect(Collectors.joining(",")));
            }
        });
        testSampleBO.getMaterialAttr().setMaterialName(sampleMaterialList.stream().collect(Collectors.joining("+")));
    }

    private void assembleRelationship(TestSampleBO testSampleBO, String orderNo){
        TestSampleRelationshipBO relationship = new TestSampleRelationshipBO();
        TestSampleParentBO parent = new TestSampleParentBO();
        OrderIdRelBO orderIdRel = new OrderIdRelBO();
        orderIdRel.setOrderNo(orderNo);
        //TODO orderId暂不处理
        //orderIdRel.setOrderId(context.getOrderId());
        parent.setOrder(orderIdRel);
        relationship.setParent(parent);
        //TODO 单个查询是否要提取？
        TestSampleQueryReq testSampleQueryReq = new TestSampleQueryReq();
        testSampleQueryReq.setTestSampleInstanceIdList(Sets.newHashSet(testSampleBO.getId().getTestSampleInstanceId()));
        BaseResponse<List<TestSampleTestLineBO>> testSampleResponse = testSampleService.querySampleTestLineList(testSampleQueryReq);
        if(testSampleResponse.isSuccess() && Func.isNotEmpty(testSampleResponse.getData())){
            TestSampleParallelBO parallel = new TestSampleParallelBO();
            parallel.setTestLineList(testSampleResponse.getData());
            relationship.setParallel(parallel);
        }else{
            relationship.setParallel(new TestSampleParallelBO());
        }
        testSampleBO.setRelationship(relationship);
    }

    private void assembleLimitGroup(TestSampleBO testSampleBO,List<LimitGroupPO> limitGroupList){
        if(Func.isEmpty(limitGroupList)){
            return;
        }
        List<LimitGroupBO> limitGroupBoList = Lists.newArrayList();
        limitGroupList.stream().forEach(limitGroupPO -> {
            if(!Func.equalsSafe(limitGroupPO.getTestSampleId(),testSampleBO.getId().getTestSampleInstanceId())){
                return;
            }
            if(Func.isNotEmpty(limitGroupPO.getActiveIndicator()) && limitGroupPO.getActiveIndicator()!=
                    Constants.ActiveIndicator){
                return;
            }
            LimitGroupBO limitGroupBO = new LimitGroupBO();
            Func.copy(limitGroupPO,limitGroupBO);
            if(Func.isNotEmpty(limitGroupPO.getLimitGroupId())){
                limitGroupBO.setLimitGroupId(limitGroupPO.getLimitGroupId().toString());
            }
            if(Func.isNotEmpty(limitGroupPO.getLimitGroupTypeId())){
                limitGroupBO.setLimitGroupTypeId(limitGroupPO.getLimitGroupTypeId().toString());
            }
            if(Func.isNotEmpty(limitGroupPO.getPpBaseId())){
                limitGroupBO.setPpBaseId(limitGroupPO.getPpBaseId().toString());
            }
            limitGroupBoList.add(limitGroupBO);
        });
        testSampleBO.setLimitGroupList(limitGroupBoList);
    }
}
