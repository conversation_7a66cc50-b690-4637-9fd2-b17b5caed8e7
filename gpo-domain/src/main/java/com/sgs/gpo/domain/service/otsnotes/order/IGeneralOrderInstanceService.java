package com.sgs.gpo.domain.service.otsnotes.order;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.order.GeneralOrderInstancePO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.facade.model.otsnotes.order.OrderInstanceQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/7 12:00
 */
public interface IGeneralOrderInstanceService extends IService<GeneralOrderInstancePO> {

    BaseResponse<List<GeneralOrderInstancePO>> select(OrderInstanceQueryReq orderInstanceQueryReq);

}
