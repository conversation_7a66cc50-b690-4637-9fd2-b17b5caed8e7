package com.sgs.gpo.domain.service.preorder.externalno.impl;


import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.open.platform.base.service.impl.AbstractDomainService;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.preorder.externalno.IExternalNoDomainService;
import com.sgs.gpo.domain.service.preorder.externalno.command.ExternalNoQueryCMD;
import com.sgs.gpo.domain.service.preorder.externalno.context.ExternalNoContext;
import com.sgs.gpo.domain.service.preorder.externalno.subdomain.IExternalNoRelService;
import com.sgs.gpo.facade.model.preorder.externalno.bo.ExternalNoBO;
import com.sgs.gpo.facade.model.preorder.externalno.bo.ExternalNoIdBO;
import com.sgs.gpo.facade.model.preorder.externalno.req.ExternalNoQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class ExternalNoDomainServiceImpl
        extends AbstractDomainService<ExternalNoBO, ExternalNoIdBO, ExternalNoQueryReq, IExternalNoRelService>
        implements IExternalNoDomainService {

    @Autowired
    IReportService reportService;
    @Autowired
    IExternalNoRelService externalNoRelService;

    @Override
    public BaseResponse<List<ExternalNoBO>> queryBO(ExternalNoQueryReq queryReq) {
        ExternalNoContext<ExternalNoQueryReq> context = new ExternalNoContext<>();
        context.setParam(queryReq);
        context.setLab(SystemContextHolder.getLab());
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        context.setToken(SystemContextHolder.getSgsToken());
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(ExternalNoQueryCMD.class,context);
    }
}
