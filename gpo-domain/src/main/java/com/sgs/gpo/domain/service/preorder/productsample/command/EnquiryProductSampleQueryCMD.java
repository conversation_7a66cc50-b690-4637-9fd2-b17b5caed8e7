package com.sgs.gpo.domain.service.preorder.productsample.command;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.model.common.dff.DFFAttrBO;
import com.sgs.framework.model.common.dff.DFFAttrLanguageBO;
import com.sgs.framework.model.common.dff.DFFTemplateBO;
import com.sgs.framework.model.common.productsample.ProductBO;
import com.sgs.framework.model.common.productsample.ProductSampleBO;
import com.sgs.framework.model.common.productsample.SampleBO;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryProductPO;
import com.sgs.gpo.domain.service.common.dff.IDFFService;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryProductService;
import com.sgs.gpo.domain.service.preorder.productsample.context.ProductSampleContext;
import com.sgs.gpo.domain.service.setting.buparam.IBUParam;
import com.sgs.gpo.facade.model.preorder.productsample.req.ProductSampleQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
@Scope(value = "prototype")
public class EnquiryProductSampleQueryCMD extends BaseCommand<ProductSampleContext> {
    @Autowired
    private IDFFService dffService;
    @Autowired
    private IBUParam buParam;
    @Autowired
    private IEnquiryProductService enquiryProductService;

    @Override
    public BaseResponse validateParam(ProductSampleContext context) {
        if (Func.isEmpty(context) || Func.isEmpty(context.getParam()) ||
                Func.isEmpty(context.getParam().getOrderIdList()) &&
                        Func.isEmpty(context.getParam().getRefSampleIdList())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(ProductSampleContext context) {
        setPrimaryLanguage(context);
        return loadProductInstances(context);
    }

    private void setPrimaryLanguage(ProductSampleContext context) {
        BaseResponse<LanguageType> languageTypeBaseResponse = buParam.getPrimaryLanguage(ProductLineContextHolder.getProductLineCode());
        if (Func.isNotEmpty(languageTypeBaseResponse) && Func.isNotEmpty(languageTypeBaseResponse.getData())) {
            context.setPrimaryLanguage(languageTypeBaseResponse.getData());
        }
    }

    private BaseResponse loadProductInstances(ProductSampleContext context) {
        ProductSampleQueryReq productSampleQueryReq = context.getParam();
        BaseResponse<List<EnquiryProductPO>> queryOrderProductSampleResponse = enquiryProductService.queryEnquiryProductSampleByEnquiryId(productSampleQueryReq);
        if (queryOrderProductSampleResponse.isFail()) {
            return queryOrderProductSampleResponse;
        }

        List<EnquiryProductPO> productInstanceList = queryOrderProductSampleResponse.getData();
        context.setEnquiryProductList(productInstanceList);
        loadDFFTemplates(context, productInstanceList);
        return super.before(context);
    }

    private void loadDFFTemplates(ProductSampleContext context, List<EnquiryProductPO> productInstanceList) {
        if (Func.isNotEmpty(productInstanceList)) {
            Set<String> dffTemplateIdList = productInstanceList.stream()
                    .map(EnquiryProductPO::getDffFormId)
                    .collect(Collectors.toSet());
            BaseResponse<List<DFFTemplateBO>> dffResponse = dffService.queryByIds(dffTemplateIdList);
            if (dffResponse.isFail() || Func.isEmpty(dffResponse.getData())) {
                throw new RuntimeException("NotFound DFF Template");
            }
            context.setDffTemplateList(dffResponse.getData());
        }
    }

    @Override
    public BaseResponse<List<ProductSampleBO>> buildDomain(ProductSampleContext context) {
        List<EnquiryProductPO> productInstanceList = context.getEnquiryProductList();
        List<ProductSampleBO> allProductSampleBOList = new ArrayList<>();

        if (Func.isNotEmpty(productInstanceList)) {
            Set<String> orderIdList = productInstanceList.stream()
                    .map(EnquiryProductPO::getEnquiryId)
                    .collect(Collectors.toSet());

            for (String orderId : orderIdList) {
                ProductSampleBO productSampleBO = initOrGetProductSampleBO(allProductSampleBOList, orderId);
                buildProduct(context, orderId, productSampleBO);
                buildSample(context, orderId, productSampleBO);
                allProductSampleBOList.add(productSampleBO);
            }
        }
        return BaseResponse.newSuccessInstance(allProductSampleBOList);
    }

    private ProductSampleBO initOrGetProductSampleBO(List<ProductSampleBO> allProductSampleBOList, String orderId) {
        return allProductSampleBOList.stream()
                .filter(item -> Func.equalsSafe(item.getOrderId(), orderId))
                .findAny()
                .orElseGet(() -> initOrderProductSampleBO(orderId));
    }

    private void buildProduct(ProductSampleContext context, String orderId, ProductSampleBO productSampleBO) {
        List<EnquiryProductPO> productInstanceList = context.getEnquiryProductList();
        List<EnquiryProductPO> productInstancePOList = productInstanceList.stream()
                .filter(item -> Func.isEmpty(item.getHeaderId()) && Func.equalsSafe(item.getEnquiryId(), orderId))
                .collect(Collectors.toList());

        ProductBO productBO = productSampleBO.getProduct();
        if (Func.isEmpty(productBO)) {
            productBO = new ProductBO();
            productSampleBO.setProduct(productBO);
        }

        for (EnquiryProductPO productInstancePO : productInstancePOList) {
            buildProductAttributes(productInstancePO, context, productBO);
        }
    }

    private void buildProductAttributes(EnquiryProductPO productInstancePO, ProductSampleContext context, ProductBO productBO) {
        List<DFFTemplateBO> dffTemplateList = context.getDffTemplateList();
        LanguageType primaryLanguage = context.getPrimaryLanguage();
        Map<String, Object> productMap = Func.toMap(productInstancePO);
        DFFTemplateBO dffTemplateBO = dffTemplateList.stream()
                .filter(item -> item.getTemplateId().equals(productInstancePO.getDffFormId()))
                .findFirst()
                .orElse(null);

        if (Func.isNotEmpty(dffTemplateBO)) {
            productBO.setTemplateId(dffTemplateBO.getTemplateId());
            List<DFFAttrBO> productAttrList = new ArrayList<>();
            for (DFFAttrBO item : dffTemplateBO.getAttrList()) {
                DFFAttrBO dffAttrBO = createDFFAttrBO(item, productMap, productInstancePO);
                productAttrList.add(dffAttrBO);
            }
            setPrimaryLanguageValue(productAttrList, primaryLanguage);
            productBO.setProductAttrList(productAttrList);
        }
    }

    private DFFAttrBO createDFFAttrBO(DFFAttrBO item, Map<String, Object> productMap, EnquiryProductPO productInstancePO) {
        DFFAttrBO dffAttrBO = Func.deepCopy(item, DFFAttrBO.class);
        dffAttrBO.setLanguageList(new ArrayList<>());

        Object value = productMap.getOrDefault(Func.firstCharToLower(item.getLabelCode()), productMap.get(item.getLabelCode()));
        DFFAttrLanguageBO dffAttrLanguageBO = new DFFAttrLanguageBO();
        dffAttrLanguageBO.setLanguageId(productInstancePO.getLanguageId());
        dffAttrLanguageBO.setLangId(productInstancePO.getId());
        dffAttrLanguageBO.setLabelName(item.getLabelName());
        dffAttrLanguageBO.setCustomerLabel(item.getCustomerLabel());
        dffAttrLanguageBO.setValue(value);

        dffAttrBO.getLanguageList().add(dffAttrLanguageBO);
        return dffAttrBO;
    }

    private void setPrimaryLanguageValue(List<DFFAttrBO> productAttrList, LanguageType primaryLanguage) {
        for (DFFAttrBO dffAttrBO : productAttrList) {
            List<DFFAttrLanguageBO> languageList = dffAttrBO.getLanguageList();
            if (Func.isNotEmpty(languageList)) {
                DFFAttrLanguageBO primaryLanguageDFFBO = languageList.stream()
                        .filter(item -> LanguageType.check(item.getLanguageId(), primaryLanguage))
                        .findAny()
                        .orElse(null);
                dffAttrBO.setValue(Func.isNotEmpty(primaryLanguageDFFBO) ? primaryLanguageDFFBO.getValue() : null);
            }
        }
    }

    private void buildSample(ProductSampleContext context, String orderId, ProductSampleBO productSampleBO) {
        List<EnquiryProductPO> productInstanceList = context.getEnquiryProductList();
        List<EnquiryProductPO> sampleInstanceList = productInstanceList.stream()
                .filter(item -> Func.isNotEmpty(item.getHeaderId()) && Func.equalsSafe(item.getEnquiryId(), orderId))
                .collect(Collectors.toList());


        List<SampleBO> sampleBOList = productSampleBO.getSampleList();

        for (EnquiryProductPO productInstancePO : sampleInstanceList) {
            SampleBO sampleBO = initOrGetSampleBO(sampleBOList, productInstancePO);
            buildSampleAttributes(sampleBO, productInstancePO, context);
            if (!sampleBOList.contains(sampleBO)) {
                sampleBOList.add(sampleBO);
            }
        }
        productSampleBO.setSampleList(sampleBOList);
    }

    private SampleBO initOrGetSampleBO(List<SampleBO> sampleBOList, EnquiryProductPO productInstancePO) {
        return sampleBOList.stream()
                .filter(item -> item.getTestSampleInstanceId().equals(productInstancePO.getRefSampleId()))
                .findFirst()
                .orElseGet(() -> initSampleBO(productInstancePO));
    }

    private SampleBO initSampleBO(EnquiryProductPO productInstancePO) {
        SampleBO sampleBO = new SampleBO();
        sampleBO.setTestSampleInstanceId(productInstancePO.getRefSampleId());
        sampleBO.setTemplateId(productInstancePO.getDffFormId());
        sampleBO.setSampleNo(productInstancePO.getSampleId());
        sampleBO.setExternalSampleNo(productInstancePO.getExternalSampleId());
        sampleBO.setSampleAttrList(new ArrayList<>());
        return sampleBO;
    }

    private void buildSampleAttributes(SampleBO sampleBO, EnquiryProductPO productInstancePO, ProductSampleContext context) {
        List<DFFTemplateBO> dffTemplateList = context.getDffTemplateList();
        LanguageType primaryLanguage = context.getPrimaryLanguage();
        Map<String, Object> productMap = Func.toMap(productInstancePO);
        DFFTemplateBO dffTemplateBO = dffTemplateList.stream()
                .filter(item -> item.getTemplateId().equals(productInstancePO.getDffFormId()))
                .findFirst()
                .orElse(null);

        if (Func.isNotEmpty(dffTemplateBO)) {
            for (DFFAttrBO item : dffTemplateBO.getAttrList()) {
                DFFAttrBO dffAttrBO = createDFFAttrBO(item, productMap, productInstancePO);
                sampleBO.getSampleAttrList().add(dffAttrBO);
            }
        }
        setPrimaryLanguageValue(sampleBO.getSampleAttrList(), primaryLanguage);
    }

    private ProductSampleBO initOrderProductSampleBO(String orderId) {
        ProductSampleBO productSampleBO = new ProductSampleBO();
        productSampleBO.setOrderId(orderId);
        productSampleBO.setSampleList(new ArrayList<>());
        return productSampleBO;
    }

    @Override
    public BaseResponse<List<ProductSampleBO>> execute(ProductSampleContext context) {
        return this.buildDomain(context);
    }
}