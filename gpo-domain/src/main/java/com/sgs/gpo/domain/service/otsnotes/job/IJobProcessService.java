package com.sgs.gpo.domain.service.otsnotes.job;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.test.execution.v2.job.JobBO;
import com.sgs.framework.model.test.execution.v2.job.JobIdBO;
import com.sgs.framework.open.platform.base.service.IProcessService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/6 20:37
 */
public interface IJobProcessService extends IProcessService<JobBO,JobIdBO> {

    /**
     * 置为初始状态
     * @param jobIdList
     * @return
     */
    BaseResponse toNew(List<JobIdBO> jobIdList);

    /**
     * 置为测试中
     * @param jobIdList
     * @return
     */
    BaseResponse toTesting(List<JobIdBO> jobIdList);

    /**
     * 置为测试完成
     * @param jobIdList
     * @return
     */
    BaseResponse toComplete(List<JobIdBO> jobIdList);

    /**
     * 置为关闭
     * @param jobIdList
     * @return
     */
    BaseResponse toClose(List<JobIdBO> jobIdList);

}
