package com.sgs.gpo.domain.service.preorder.ordertrfrel.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseIdBO;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.model.order.trf.TrfBO;
import com.sgs.framework.open.platform.base.service.impl.AbstractBaseService;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.ordertrfrel.OrderTrfRelationshipMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.ordertrfrel.OrderTrfRelationshipPO;
import com.sgs.gpo.domain.service.preorder.ordertrfrel.IOrderTrfRelationshipService;
import com.sgs.gpo.facade.model.preorder.order.bo.OrderTrfBO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderTrfReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @title: OrderTrfRelationshipServiceImpl
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/4 11:11
 */
@Service
@Slf4j
public class OrderTrfRelationshipServiceImpl
        extends AbstractBaseService<TrfBO, OrderTrfRelationshipPO, BaseIdBO, OrderTrfRelationshipMapper, OrderTrfReq>
        implements IOrderTrfRelationshipService {

    @Override
    public List<TrfBO> queryBO(OrderTrfReq orderTrfReq){
        if (Func.isEmpty(orderTrfReq) || Func.isEmpty(orderTrfReq.getOrderIdList())) {
            return Lists.newArrayList();
        }
        QueryWrapper<OrderTrfRelationshipPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(OrderTrfRelationshipPO.COLUMN.ORDER_ID, orderTrfReq.getOrderIdList());
        List<OrderTrfRelationshipPO> orderTrfRelationshipList = getBaseMapper().selectList(queryWrapper);
        if(Func.isEmpty(orderTrfRelationshipList)){
            return Lists.newArrayList();
        }
        return this.convertToBO(orderTrfRelationshipList);
    }


    @Override
    public BaseResponse<List<OrderTrfRelationshipPO>> queryByOrderId(OrderTrfReq orderTrfReq) {
        if (Func.isEmpty(orderTrfReq) || Func.isEmpty(orderTrfReq.getOrderIdList())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        QueryWrapper<OrderTrfRelationshipPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(OrderTrfRelationshipPO.COLUMN.ORDER_ID, orderTrfReq.getOrderIdList());
        List<OrderTrfRelationshipPO> orderTrfRelationshipPOList = getBaseMapper().selectList(queryWrapper);
        return BaseResponse.newSuccessInstance(orderTrfRelationshipPOList);
    }

    @Override
    public BaseResponse<List<OrderTrfBO>> queryOrderTrf(OrderTrfReq orderTrfReq) {
        if (Func.isEmpty(orderTrfReq) || (Func.isEmpty(orderTrfReq.getOrderIdList()) && Func.isEmpty(orderTrfReq.getOrderNoList()) && Func.isEmpty(orderTrfReq.getRefNo()) && Func.isEmpty(orderTrfReq.getExternalOrderNo()))) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        List<OrderTrfBO> orderTrfBOS = baseMapper.queryOrderTrf(orderTrfReq);
        if(Func.isNull(orderTrfBOS)){
            orderTrfBOS = Lists.newArrayList();
        }
        return BaseResponse.newSuccessInstance(orderTrfBOS);
    }

    /**
     * 根据OrderId获取对应商机信息
     * @param orderTrfReq
     * @return
     */
    @Override
    public BaseResponse<List<OrderTrfRelationshipPO>> queryOppRelationshipByOrderId(OrderTrfReq orderTrfReq) {
        if (Func.isEmpty(orderTrfReq) || Func.isEmpty(orderTrfReq.getOrderIdList())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        List<OrderTrfRelationshipPO> orderTrfRelationshipPOList = this.lambdaQuery().in(OrderTrfRelationshipPO::getOrderId, orderTrfReq.getOrderIdList())
                .eq(OrderTrfRelationshipPO::getRefSystemId, RefSystemIdEnum.CRM.getRefSystemId()).list();
        return BaseResponse.newSuccessInstance(orderTrfRelationshipPOList);
    }


    @Override
    public List<TrfBO> convertToBO(Collection<OrderTrfRelationshipPO> poList) {
        List<TrfBO> trfList = Lists.newArrayList();
        if(Func.isNotEmpty(poList)){
            poList.stream().forEach(item->{
                TrfBO trfbo = new TrfBO();
                trfbo.setRefSystemId(item.getRefSystemId());
                trfbo.setTrfNo(item.getRefNo());
                trfbo.setRefObjectId(item.getOrderId());
                trfbo.setExternalOrderNo(item.getExternalOrderNo());
                trfbo.setIntegrationChannel(item.getIntegrationChannel());
                trfbo.setTrfSourceType(item.getTrfSourceType());
                trfList.add(trfbo);
            });
        }
        return trfList;
    }

    @Override
    public List<OrderTrfRelationshipPO> convertToPO(Collection<TrfBO> boList) {
        return null;
    }

    @Override
    public LambdaQueryWrapper<OrderTrfRelationshipPO> createWrapper(OrderTrfReq queryReq) {
        return null;
    }
}
