package com.sgs.gpo.domain.service.otsnotes.report.context;


import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.framework.model.test.execution.v2.subcontract.SubcontractBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.test.testsample.v2.TestSampleBO;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.*;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractTestLinePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.TestSampleGroupPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.TestSamplePO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.attachment.OrderAttachmentPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.facade.model.digital.req.GenerateReportRequest;
import com.sgs.gpo.facade.model.otsnotes.labSection.rsp.ReportTestLineLabSectionRsp;
import com.sgs.gpo.facade.model.report.bo.ReportMatrixRelBO;
import com.sgs.gpo.integration.digital.req.DigitalReportReq;
import com.sgs.otsnotes.facade.model.rsp.AccreditationVO;
import com.sgs.preorder.facade.model.dto.customer.CustomerInstanceDTO;
import com.sgs.preorder.facade.model.dto.order.OrderAllDTO;
import com.sgs.preorder.facade.model.dto.order.OrderAttachmentDTO;
import com.sgs.preorder.facade.model.dto.order.OrderTrfDTO;
import com.sgs.preorder.facade.model.dto.order.ProductInstanceDTO;
import com.sgs.preorder.facade.model.info.TestRequestInfo;
import com.sgs.preorder.facade.model.rsp.ReportReceiverRsp;
import com.sgs.preorder.facade.model.subcontract.SubContractModeInfo;
import lombok.Data;

import java.util.List;

@Data
public class ReportContext<Input> extends BaseContext<Input, ReportBO> {
    /**
     * 生成报告使用的上下文参数
     */
    private ReportPO gpnReport;
    private OrderAllDTO gpoOrder;
    private List<OrderAttachmentDTO> orderAttachmentList;
    private List<CustomerInstanceDTO> customerList;
    private OrderTrfDTO orderTrfDTOList;
    private TestRequestInfo orderTestRequest;
    private ReportReceiverRsp reportReceiver;
    private List<ReportTemplatePO> reportTemplateList;
    private  List<ProductInstanceDTO> productInsList;

    private GenerateReportRequest reportRequest;
    private DigitalReportReq digitalReportReqEn;
    private DigitalReportReq digitalReportReqCn;

    private List<ReportBO> reportBOList;

    private List<ReportPO> reportPOList;
    private List<ReportMatrixRelPO> reportMatrixRelPOList;
    private List<GeneralOrderPO> generalOrderPOList;
    private List<ReportFilePO> reportFileList;
    private List<ReportCertificatePO> reportCertificatePOList;

    private List<ReportTestLineLabSectionRsp> reportTestLineLabSectionRspList;
    private List<ReportExtPO> reportExtPOList;

    private List<ReportMatrixRelBO> reportMatrixRelBOList;
    private List<TestSamplePO> testSamplePOList;
    private List<TestSampleGroupPO> testSampleGroupPOList;
    private List<TestLineBO> testLineBOList;
    private List<OrderBO> orderBOList;
    private List<AccreditationVO> accreditationList;
    private List<OrderAttachmentPO> reportAttachmentList;
    private SubcontractBO subcontractBO;
    private List<SubcontractTestLinePO> subcontractTestLinePOList;
    private List<SubContractModeInfo> subContractModeInfoList;
    private List<ReportFileHistoryPO> reportFileHistoryPOList;
    private LanguageType primaryLanguage = LanguageType.English;
    public ReportContext(Input input){
        this.setLab(SystemContextHolder.getLab());
        this.setToken(SecurityContextHolder.getSgsToken());
        this.setUserInfo(SecurityContextHolder.getUserInfo());
        this.setParam(input);
    }
    public ReportContext(){
    }
}
