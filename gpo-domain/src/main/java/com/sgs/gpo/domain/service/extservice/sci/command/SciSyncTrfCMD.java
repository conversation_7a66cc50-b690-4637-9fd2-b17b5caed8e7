package com.sgs.gpo.domain.service.extservice.sci.command;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.model.common.attachment.AttachmentBO;
import com.sgs.framework.model.common.contact.ContactPersonBO;
import com.sgs.framework.model.common.customer.CustomerBO;
import com.sgs.framework.model.common.customer.CustomerContactBO;
import com.sgs.framework.model.common.customer.CustomerLanguageBO;
import com.sgs.framework.model.common.dff.DFFAttrBO;
import com.sgs.framework.model.common.dff.DFFAttrLanguageBO;
import com.sgs.framework.model.common.lab.LabBO;
import com.sgs.framework.model.common.productsample.ProductBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementBO;
import com.sgs.framework.model.common.servicerequirement.ServiceRequirementReportBO;
import com.sgs.framework.model.enums.CustomerType;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.enums.RefIntegrationChannel;
import com.sgs.framework.model.enums.ReportStatus;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.model.order.order.OrderFlagBO;
import com.sgs.framework.model.order.order.OrderOthersBO;
import com.sgs.framework.model.order.order.OrderPaymentBO;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.model.order.v2.OrderHeaderBO;
import com.sgs.framework.model.test.citation.CitationBO;
import com.sgs.framework.model.test.pp.pptestline.PPTestLineRelBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.test.testmatrix.TestMatrixBO;
import com.sgs.framework.model.test.testsample.TestSampleBO;
import com.sgs.framework.model.test.testsample.TestSampleGroupBO;
import com.sgs.framework.model.test.testsample.TestSampleMaterialAttrBO;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.ReportFileType;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportFilePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.TestSamplePO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryTrfRelationshipPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.operation.OrderOperationHistoryPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.payment.BossOrderInvoiceDTO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.productinstance.ProductInstancePO;
import com.sgs.gpo.domain.service.extservice.sci.SciTrfContext;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportFileService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportMatrixRelService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineDomainService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixService;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.ITestSampleService;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryService;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryTrfRelationshipService;
import com.sgs.gpo.domain.service.preorder.operation.IOrderOperationHistoryService;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IBossOrderInvoiceService;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IGeneralOrderService;
import com.sgs.gpo.domain.service.preorder.ordertrfrel.IOrderTrfRelationshipService;
import com.sgs.gpo.domain.service.preorder.payment.payment.IPaymentDomainService;
import com.sgs.gpo.domain.service.preorder.productinstance.IProductInstanceService;
import com.sgs.gpo.facade.model.otsnotes.conclusion.ConclusionDTO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;
import com.sgs.gpo.facade.model.preorder.order.bo.OrderTrfBO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderTrfReq;
import com.sgs.gpo.facade.model.preorder.productsample.req.ProductSampleQueryReq;
import com.sgs.gpo.facade.model.report.bo.ReportMatrixRelBO;
import com.sgs.gpo.facade.model.report.req.ReportFileQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportIdReq;
import com.sgs.gpo.facade.model.report.req.ReportMatrixQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.gpo.facade.model.sci.bo.SciTrfSyncBO;
import com.sgs.gpo.facade.model.sci.dto.TrfCitationDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfConclusionDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfCustomerContactDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfCustomerDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfCustomerLangDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfFileDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfHeaderDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfInvoiceDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfMaterialAttrDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfOrderContactPersonDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfOrderDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfOrderFlagsDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfOrderOthersDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfOrderPendingDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfOtherDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfPaymentDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfPendingDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfPpTestLineDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfProductAttrDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfProductAttrLangDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfProductSampleDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfQuotationDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfQuotationServiceItemDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfReportDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfReportMatrixDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfServiceRequirementDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfServiceRequirementReportDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfSyncHeaderDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfTestItemExternalDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfTestLineDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfTestSampleDTO;
import com.sgs.gpo.facade.model.sci.dto.TrfTestSampleGroupDTO;
import com.sgs.gpo.facade.model.sci.req.GpoSciTrfSyncReq;
import com.sgs.gpo.facade.model.sci.rsp.SciSyncTrfRsp;
import com.sgs.gpo.integration.dff.DFFClient;
import com.sgs.gpo.integration.framework.FrameworkClient;
import com.sgs.gpo.integration.sci.SciClient;
import com.sgs.gpo.integration.sci.req.SciHeaderReq;
import com.sgs.gpo.integration.sci.req.SciTrfReq;
import com.sgs.preorder.core.common.EventType;
import com.sgs.preorder.core.common.StandardObjectType;
import com.sgs.preorder.facade.model.dto.dff.DffFormAttrDTO;
import com.sgs.preorder.facade.model.enums.OrderPersonType;
import com.sgs.preorder.facade.model.enums.TrfSourceType;
import com.sgs.priceengine.facade.QuotationFacade;
import com.sgs.priceengine.facade.model.DTO.QuotationServiceItemDTO;
import com.sgs.priceengine.facade.model.request.OrderIdsRequest;
import com.sgs.priceengine.facade.model.request.QuotationServiceItemRequest;
import com.sgs.priceengine.facade.model.response.QuotationHeadRsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: SciTrfSyncCMD
 * @projectName gpo-micro-service
 * @description:
 * @date 2023/12/26 14:54
 */
@Service
@Slf4j
@AllArgsConstructor
public class SciSyncTrfCMD extends BaseCommand<SciTrfContext<GpoSciTrfSyncReq, SciTrfSyncBO>> {
    private IOrderDomainService orderDomainService;
    private ITestSampleService testSampleService;
    private ITestLineDomainService testLineDomainService;
    private IBossOrderInvoiceService bossOrderInvoiceService;
    private QuotationFacade quotationFacade;
    private IOrderTrfRelationshipService orderTrfRelationshipService;
    private IReportService reportService;
    private SciClient sciClient;
    private IEnquiryService enquiryService;
    private IGeneralOrderService generalOrderService;
    private IReportMatrixRelService reportMatrixRelService;
    private IReportFileService reportFileService;
    private ITestMatrixService testMatrixService;
    private FrameworkClient frameworkClient;
    private DFFClient dffClient;
    private IProductInstanceService productInstanceService;
    private IOrderOperationHistoryService orderOperationHistoryService;
    private IEnquiryTrfRelationshipService enquiryTrfRelationshipService;
    private IPaymentDomainService paymentDomainService;
    @Override
    public BaseResponse validateParam(SciTrfContext<GpoSciTrfSyncReq, SciTrfSyncBO> context) {
        if (Func.isEmpty(context) || Func.isEmpty(context.getParam())) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse<SciSyncTrfRsp> execute(SciTrfContext<GpoSciTrfSyncReq, SciTrfSyncBO> context) {
        context.setDomain(new SciTrfSyncBO());
        GpoSciTrfSyncReq gpoSciTrfSyncReq = context.getParam();
        List<OrderTrfBO> orderTrfBOList = context.getOrderTrfBOList();

        this.buildDomain(context);
        SciTrfSyncBO sciTrfSyncBO = context.getDomain();
        sciTrfSyncBO.setAction(gpoSciTrfSyncReq.getSciAction());
        sciTrfSyncBO.setOperator("");
        sciTrfSyncBO.setOperationTime("");
        SciTrfReq<SciTrfSyncBO> sciTrfReq = new SciTrfReq();
        UserInfo user = SystemContextHolder.getUserInfoFillSystem();
        String eventTypeStr = gpoSciTrfSyncReq.getEventType();
        EventType eventType = EventType.findEventType(eventTypeStr);

//        boolean order2TRF = TrfSourceType.Order2TRF.getSourceType().equalsIgnoreCase(referenceInfo.getTrfSourceType());
        if (Func.isNotEmpty(orderTrfBOList)) {
            boolean order2TRF = orderTrfBOList.stream().filter(item -> StringUtils.equalsIgnoreCase(TrfSourceType.Order2TRF.getSourceType(), item.getTrfSourceType())).count() > 0;
            if (order2TRF && EventType.check(eventType.getType(), EventType.Confirmed)) {
                log.info("order2TRF单，不需要同步Confirmed");
                return BaseResponse.newFailInstance("order2TRF单，不需要同步Confirmed");
            }
        }

        String operatorEmail = user.getEmail();
        String regionAccount = user.getRegionAccount();
        String labCode = context.getLabCode();
        String buCode = context.getBuCode();
        if (Func.isNotEmpty(user)) {
            if (Func.isEmpty(labCode)) {
                labCode = user.getCurrentLabCode();
            }
            if (Func.isEmpty(buCode)) {
                buCode = context.getProductLineCode();
            }
        }
        context.getDomain().setOperator(regionAccount);
        SciHeaderReq sciHeaderReq = new SciHeaderReq();
        sciHeaderReq.setLabCode(labCode);
        sciHeaderReq.setSystemId(SgsSystem.GPO.getSgsSystemId());
        sciHeaderReq.setOperator(regionAccount);
        sciHeaderReq.setOperationTime(DateUtil.formatDateTime(new Date()));
        sciHeaderReq.setOperatorEmail(operatorEmail);
        sciHeaderReq.setToken(gpoSciTrfSyncReq.getToken());
        sciHeaderReq.setProductLineCode(buCode);
        sciTrfReq.setHeader(sciHeaderReq);
        sciTrfReq.setBody(sciTrfSyncBO);
        BaseResponse<SciSyncTrfRsp> baseResponse = sciClient.syncTrf(sciTrfReq, gpoSciTrfSyncReq.getSciUrl());
        return baseResponse;
    }

    @Override
    public BaseResponse buildDomain(SciTrfContext<GpoSciTrfSyncReq, SciTrfSyncBO> context) {
        buildTrfHeader(context);
        buildTrfOrder(context);
        buildProductSample(context);
        buildTrfReport(context);
        buildTrfTestSample(context);
        buildTrfTestLine(context);
        buildTrfQuotation(context);
        buildTrfInvoice(context);
        return BaseResponse.newSuccessInstance(true);
    }


    private void buildProductSample(SciTrfContext<GpoSciTrfSyncReq, SciTrfSyncBO> context) {
        List<ProductBO> orderProductList = context.getOrderProductList();
        List<ProductInstancePO> allPproductInstanceList = context.getProductInstanceList();
        int primaryLanguageId = context.getPrimaryLanguageId();

        if(Func.isNotEmpty(allPproductInstanceList)){
            //Product Form
            List<ProductInstancePO> allProductFormPOList  = allPproductInstanceList.stream().filter(item -> Func.isEmpty(item.getHeaderID())).collect(Collectors.toList());
            List<ProductInstancePO> productFormPOList = allProductFormPOList.stream().filter(item -> Func.equalsSafe(item.getLanguageID(), primaryLanguageId)).collect(Collectors.toList());
            if(Func.isEmpty(productFormPOList)){
                productFormPOList = allProductFormPOList.stream().filter(item -> !Func.equalsSafe(item.getLanguageID(), primaryLanguageId)).collect(Collectors.toList());
            }
            if(Func.isNotEmpty(productFormPOList) && Func.isNotEmpty(orderProductList)){
                List<TrfProductSampleDTO> trfProductDTOList = new ArrayList<>();
                for (ProductInstancePO productInstancePO : productFormPOList) {
                    ProductBO productBO = orderProductList.stream().filter(item -> Func.equalsSafe(item.getProductInstanceId(), productInstancePO.getID())).findAny().orElse(null);
                    if(Func.isEmpty(productBO)){
                        continue;
                    }
                    TrfProductSampleDTO trfProductDTO = this.convertTrfProductSample(productBO, context);
                    trfProductDTOList.add(trfProductDTO);
                }
                context.getDomain().getOrder().setProductList(trfProductDTOList);
            }
            //Product Grid
            List<ProductInstancePO> allProductGridPOList  = allPproductInstanceList.stream().filter(item -> Func.isNotEmpty(item.getHeaderID())).collect(Collectors.toList());
            List<ProductInstancePO> productGridPOList = allProductGridPOList.stream().filter(item -> Func.equalsSafe(item.getLanguageID(), primaryLanguageId)).collect(Collectors.toList());
            if(Func.isEmpty(productGridPOList)){
                productGridPOList = allProductGridPOList.stream().filter(item -> !Func.equalsSafe(item.getLanguageID(), primaryLanguageId)).collect(Collectors.toList());
            }
            if(Func.isNotEmpty(productGridPOList) && Func.isNotEmpty(orderProductList)){
                List<TrfProductSampleDTO> trfProductSampleDTOList = new ArrayList<>();
                for (ProductInstancePO instancePO : productGridPOList) {
                    ProductBO productBO = orderProductList.stream().filter(item -> Func.equalsSafe(item.getProductInstanceId(), instancePO.getID())).findAny().orElse(null);
                    if(Func.isEmpty(productBO)){
                        continue;
                    }
                    TrfProductSampleDTO trfProductDTO = this.convertTrfProductSample(productBO, context);
                    trfProductSampleDTOList.add(trfProductDTO);
                }
                context.getDomain().getOrder().setSampleList(trfProductSampleDTOList);
            }
        }
    }



    private TrfProductSampleDTO convertTrfProductSample(ProductBO productBO,SciTrfContext<GpoSciTrfSyncReq, SciTrfSyncBO> context){
        List<ProductInstancePO> productInstanceList = context.getProductInstanceList();
        String productInstanceId = productBO.getProductInstanceId();
        TrfProductSampleDTO trfProductDTO = new TrfProductSampleDTO();
        trfProductDTO.setTemplateId(productBO.getTemplateId());
        ProductInstancePO productInstancePO = productInstanceList.stream().filter(item -> Func.equalsSafe(item.getID(), productInstanceId)).findAny().orElse(null);
        if (Func.isEmpty(productInstancePO)) {
            return null;
        }
        if (Func.isEmpty(productInstancePO.getHeaderID())) {
            trfProductDTO.setProductInstanceId(productBO.getProductInstanceId());
        } else {
            trfProductDTO.setSampleInstanceId(productBO.getProductInstanceId());
            trfProductDTO.setSampleNo(productInstancePO.getSampleID());
            trfProductDTO.setProductItemNo(productInstancePO.getProductItemNo());
        }
        List<DFFAttrBO> orderProductAttrList = productBO.getProductAttrList();
        if (Func.isNotEmpty(orderProductAttrList)) {
            List<TrfProductAttrDTO> trfProductAttrDTOList = new ArrayList<>();
            List<TrfProductAttrDTO> trfSampleAttrDTOList = new ArrayList<>();
            for (DFFAttrBO dffAttrBO : orderProductAttrList) {
                TrfProductAttrDTO trfProductAttrDTO = new TrfProductAttrDTO();
                trfProductAttrDTO.setAttrSeq(dffAttrBO.getSeq());
                trfProductAttrDTO.setLabelCode(dffAttrBO.getLabelCode());
                trfProductAttrDTO.setLabelName(dffAttrBO.getLabelName());
                trfProductAttrDTO.setLabelValue(Func.toStr(dffAttrBO.getValue()));
                trfProductAttrDTO.setDataType(dffAttrBO.getDataType());
                trfProductAttrDTO.setCustomerLabel(dffAttrBO.getCustomerLabel());
                List<DFFAttrLanguageBO> dffAttrLanguageBOList = dffAttrBO.getLanguageList();
                if (Func.isNotEmpty(dffAttrLanguageBOList)) {
                    List<TrfProductAttrLangDTO> languageList = new ArrayList<>();
                    for (DFFAttrLanguageBO dffAttrLanguageBO : dffAttrLanguageBOList) {
                        TrfProductAttrLangDTO trfProductAttrLangDTO = new TrfProductAttrLangDTO();
                        trfProductAttrLangDTO.setLanguageId(dffAttrLanguageBO.getLanguageId());
                        trfProductAttrLangDTO.setLabelName(dffAttrLanguageBO.getLabelName());
                        trfProductAttrLangDTO.setLabelValue(Func.toStr(dffAttrLanguageBO.getValue()));
                        trfProductAttrLangDTO.setCustomerLabel(dffAttrLanguageBO.getCustomerLabel());
                        languageList.add(trfProductAttrLangDTO);
                    }
                    trfProductAttrDTO.setLanguageList(languageList);
                }

                if (Func.isEmpty(productInstancePO.getHeaderID())) {
                    trfProductAttrDTOList.add(trfProductAttrDTO);
                } else {
                    trfSampleAttrDTOList.add(trfProductAttrDTO);
                }
            }
            trfProductDTO.setProductAttrList(Func.isEmpty(trfProductAttrDTOList) ? null : trfProductAttrDTOList);
            trfProductDTO.setSampleAttrList(Func.isEmpty(trfSampleAttrDTOList) ? null : trfSampleAttrDTOList);
        }
        return trfProductDTO;
    }


    private void buildTrfHeader(SciTrfContext<GpoSciTrfSyncReq, SciTrfSyncBO> context) {
        GpoSciTrfSyncReq gpoSciTrfSyncReq = context.getParam();
        String eventTypeStr = gpoSciTrfSyncReq.getEventType();
        List<OrderOperationHistoryPO> orderOperationHistoryPOList = context.getOrderOperationHistoryPOList();
        List<OrderBO> orderList = context.getOrderList();

        //Header
        TrfSyncHeaderDTO trfSyncHeaderDTO = new TrfSyncHeaderDTO();
        trfSyncHeaderDTO.setRefSystemId(gpoSciTrfSyncReq.getRefSystemId());
        TrfHeaderDTO trfHeaderDTO = new TrfHeaderDTO();
        trfHeaderDTO.setTrfNo(gpoSciTrfSyncReq.getTrfNo());
        trfHeaderDTO.setRefSystemId(gpoSciTrfSyncReq.getRefSystemId());
        if (Func.isNotEmpty(orderList)) {
            OrderBO orderBO = orderList.get(0);
            if (Func.isNotEmpty(orderBO.getHeader())) {
                trfHeaderDTO.setSampleReceiveDate(orderBO.getHeader().getSampleReceiveDate());
            }
        }
        TrfOtherDTO trfOtherDTO = new TrfOtherDTO();
        EventType eventType = EventType.findEventType(eventTypeStr);
        TrfPendingDTO trfPendingDTO = new TrfPendingDTO();

        switch (eventType) {
            case Pending:
                trfPendingDTO.setPendingFlag(1);
                /*if (Func.isNotEmpty(orderOperationHistoryPOList)) {
                    OrderOperationHistoryPO pendingHistory = orderOperationHistoryPOList.stream().filter(item -> StringUtils.equalsIgnoreCase("Pending", item.getOperationType())).sorted(Comparator.comparing(OrderOperationHistoryPO::getCreatedDate).reversed()).findAny().orElse(null);
                    if (Func.isNotEmpty(pendingHistory)) {
                        trfPendingDTO.setPendingType(pendingHistory.getReasonType());
                        trfPendingDTO.setPendingRemark(pendingHistory.getRemark());
                    }
                }*/
                break;
            case UnPending:
                /*trfPendingDTO.setPendingFlag(0);
                if(Func.isNotEmpty(orderOperationHistoryPOList)){
                    OrderOperationHistoryPO unPendingHistory = orderOperationHistoryPOList.stream().filter(item -> StringUtils.equalsIgnoreCase("UnPending", item.getOperationType())).sorted(Comparator.comparing(OrderOperationHistoryPO::getCreatedDate).reversed()).findAny().orElse(null);
                    if(Func.isNotEmpty(unPendingHistory)){
                        trfPendingDTO.setPendingType(unPendingHistory.getReasonType());
                        trfPendingDTO.setPendingRemark(unPendingHistory.getRemark());
                    }
                }*/
                break;
            default:
                break;
        }
        trfOtherDTO.setPending(trfPendingDTO);
        trfHeaderDTO.setOthers(trfOtherDTO);
        trfSyncHeaderDTO.setTrfList(Lists.newArrayList(trfHeaderDTO));
        context.getDomain().setHeader(trfSyncHeaderDTO);
    }

    private void buildTrfInvoice(SciTrfContext<GpoSciTrfSyncReq, SciTrfSyncBO> context) {
        GpoSciTrfSyncReq gpoSciTrfSyncReq = context.getParam();
        String orderNo = gpoSciTrfSyncReq.getOrderNo();
        String objectType = gpoSciTrfSyncReq.getObjectType();
        List<TrfInvoiceDTO> trfInvoiceDTOList = new ArrayList<>();
        if (!StandardObjectType.Enquiry.check(objectType)) {
            List<BossOrderInvoiceDTO> allBossOrderInvoicePOList = context.getInvoiceList();
//            List<BossOrderInvoiceDTO> allBossOrderInvoicePOList = bossOrderInvoiceService.getBossInvoiceByOrderNo(orderNo).getData();
            if (Func.isNotEmpty(allBossOrderInvoicePOList)) {
                Map<String, List<BossOrderInvoiceDTO>> invoiceMap = allBossOrderInvoicePOList.stream().collect(Collectors.groupingBy(BossOrderInvoiceDTO::getInvoiceNo));
                for (Map.Entry<String, List<BossOrderInvoiceDTO>> entry : invoiceMap.entrySet()){
                    String key = entry.getKey();
                    List<BossOrderInvoiceDTO> bossOrderInvoiceDTOList = entry.getValue();
                    if(Func.isNotEmpty(key) && Func.isNotEmpty(bossOrderInvoiceDTOList)){
                        TrfInvoiceDTO trfInvoiceDTO = new TrfInvoiceDTO();
                        trfInvoiceDTO.setInvoiceNo(key);
                        List<String> quotationNoList = bossOrderInvoiceDTOList.stream().map(BossOrderInvoiceDTO::getQuotationNo).distinct().sorted().collect(Collectors.toList());
                        if(Func.isEmpty(quotationNoList)){
                            continue;
                        }
                        trfInvoiceDTO.setQuotationNoList(quotationNoList);
                        trfInvoiceDTO.setCurrency(bossOrderInvoiceDTOList.get(0).getCurrencyCode());
                        trfInvoiceDTO.setTotalAmount(Func.toStr(bossOrderInvoiceDTOList.get(0).getInvoiceAmount()));
                        trfInvoiceDTO.setPrePaidAmount("");
                        trfInvoiceDTO.setInvoiceFileList(Lists.newArrayList());
                        trfInvoiceDTOList.add(trfInvoiceDTO);
                    }
                }
            }
        }
        context.getDomain().setInvoiceList(trfInvoiceDTOList);
    }

    private void buildTrfQuotation(SciTrfContext<GpoSciTrfSyncReq, SciTrfSyncBO> context) {
        GpoSciTrfSyncReq gpoSciTrfSyncReq = context.getParam();
        String objectType = gpoSciTrfSyncReq.getObjectType();
        List<TrfQuotationDTO> trfQuotationDTOList = new ArrayList<>();
        if (!StandardObjectType.Enquiry.check(objectType)) {
            List<String> orderNoList = context.getOrderList().stream().map(OrderBO::getHeader).map(OrderHeaderBO::getOrderNo).collect(Collectors.toList());
            if (Func.isNotEmpty(orderNoList)) {
                OrderIdsRequest orderIdsRequest = new OrderIdsRequest();
                orderIdsRequest.setSgsToken(context.getToken());
                orderIdsRequest.setSystemId(SgsSystem.GPO.getSgsSystemId());
                orderIdsRequest.setProductLineCode(context.getProductLineCode());
                orderIdsRequest.setOrderNos(orderNoList);
                BaseResponse<List<QuotationHeadRsp>> quotationHeadRes = quotationFacade.getActivedQuotationByOrderIds(orderIdsRequest);
                QuotationServiceItemRequest quotationServiceItemRequest = new QuotationServiceItemRequest();
                quotationServiceItemRequest.setOrderNoList(orderNoList);
                quotationServiceItemRequest.setSystemId(SgsSystem.GPO.getSgsSystemId());
                quotationServiceItemRequest.setProductLineCode(context.getProductLineCode());
                BaseResponse<List<QuotationServiceItemDTO>> quotationServiceItemRes = quotationFacade.queryServiceItemListForGeneralOrder(quotationServiceItemRequest);
                List<QuotationServiceItemDTO> quotationServiceItemDTOList = new ArrayList<>();
                if (quotationServiceItemRes.isSuccess() && Func.isNotEmpty(quotationServiceItemRes.getData())) {
                    quotationServiceItemDTOList = quotationServiceItemRes.getData();
                }
                if (quotationHeadRes.isSuccess() && Func.isNotEmpty(quotationHeadRes)) {
                    List<QuotationHeadRsp> quotationHeadRspList = quotationHeadRes.getData();
                    for (QuotationHeadRsp quotationHeadRsp : quotationHeadRspList) {
                        TrfQuotationDTO trfQuotationDTO = new TrfQuotationDTO();
                        trfQuotationDTO.setQuotationNo(quotationHeadRsp.getQuotationNo());
                        List<TrfQuotationServiceItemDTO> trfQuotationServiceItemDTOList = new ArrayList<>();
                        if (Func.isNotEmpty(quotationServiceItemDTOList)) {
                            List<QuotationServiceItemDTO> quotationServiceItemDTOS = quotationServiceItemDTOList.stream().filter(item -> Func.equalsSafe(item.getHeadId(), quotationHeadRsp.getId())).collect(Collectors.toList());
                            for (QuotationServiceItemDTO quotationServiceItemDTO : quotationServiceItemDTOS) {
                                TrfQuotationServiceItemDTO trfQuotationServiceItemDTO = new TrfQuotationServiceItemDTO();
                                trfQuotationServiceItemDTO.setServiceItemName(quotationServiceItemDTO.getServiceItemName());
//                            trfQuotationServiceItemDTO.setPpNo(0);
//                            trfQuotationServiceItemDTO.setTestLineId(0);
//                            trfQuotationServiceItemDTO.setCitationId(0);
//                            trfQuotationServiceItemDTO.setCitationType(0);
                                trfQuotationServiceItemDTO.setCitationName(quotationServiceItemDTO.getCitationName());
                                trfQuotationServiceItemDTO.setServiceItemSalesUnitPrice(Func.toStr(quotationServiceItemDTO.getSalesUnitPrice()));
                                trfQuotationServiceItemDTO.setQuantity(quotationServiceItemDTO.getQuantity());
                                trfQuotationServiceItemDTOList.add(trfQuotationServiceItemDTO);
                            }
                        }
                        trfQuotationDTO.setServiceItemList(trfQuotationServiceItemDTOList);
                        trfQuotationDTOList.add(trfQuotationDTO);
                    }
                }

            }
        }
        context.getDomain().setQuotationList(trfQuotationDTOList);
    }

    private void buildTrfTestLine(SciTrfContext<GpoSciTrfSyncReq, SciTrfSyncBO> context) {

        GpoSciTrfSyncReq gpoSciTrfSyncReq = context.getParam();
        String objectType = gpoSciTrfSyncReq.getObjectType();
        if (!StandardObjectType.Enquiry.check(objectType)) {
            OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
            orderTestLineReq.setOrderNoList(context.getOrderNoList());
            BaseResponse<List<TestLineBO>> testLineRes = testLineDomainService.queryTestLine(orderTestLineReq);
            if (testLineRes.isSuccess() && Func.isNotEmpty(testLineRes.getData())) {
                List<TestLineBO> testLineBOList = testLineRes.getData();
                List<TrfTestLineDTO> trfTestLineDTOList = new ArrayList<>();
                for (TestLineBO testLineBO : testLineBOList) {

                    TrfTestLineDTO trfTestLineDTO = new TrfTestLineDTO();
                    trfTestLineDTO.setTestLineInstanceId(testLineBO.getTestLineInstanceId());
                    trfTestLineDTO.setTestLineId(testLineBO.getTestLineId());
                    trfTestLineDTO.setEvaluationAlias(testLineBO.getEvaluationName());
//                    trfTestLineDTO.setTestLineSeq(testLineBO.getTestLineSeq());
                    CitationBO citation = testLineBO.getCitation();
                    if (Func.isNotEmpty(citation)) {
                        TrfCitationDTO trfCitationDTO = new TrfCitationDTO();
                        trfCitationDTO.setCitationId(citation.getCitationId());
//                        trfCitationDTO.setCitationType(citation.getCitationType());
                        trfCitationDTO.setCitationFullName(citation.getCitationFullName());
                        trfTestLineDTO.setCitation(trfCitationDTO);
                    }
                    List<PPTestLineRelBO> ppTestLineRelList = testLineBO.getPpTestLineRelList();
                    if (Func.isNotEmpty(ppTestLineRelList)) {
                        List<TrfPpTestLineDTO> trfPpTestLineDTOList = new ArrayList<>();
                        for (PPTestLineRelBO ppTestLineRelBO : ppTestLineRelList) {
                            TrfPpTestLineDTO trfPpTestLineDTO = new TrfPpTestLineDTO();

                            trfPpTestLineDTO.setPpNo(ppTestLineRelBO.getPpNo());
                            trfPpTestLineDTO.setPpName(ppTestLineRelBO.getPpName());
                            trfPpTestLineDTO.setAid(ppTestLineRelBO.getAid());
                            trfPpTestLineDTOList.add(trfPpTestLineDTO);
                        }
                        trfTestLineDTO.setPpTestLineRelList(trfPpTestLineDTOList);
                    }
                    trfTestLineDTO.setExternalInfo(new TrfTestItemExternalDTO());
                    trfTestLineDTOList.add(trfTestLineDTO);
                }
                context.getDomain().setTestLineList(trfTestLineDTOList);
            }

        }

    }

    private void buildTrfTestSample(SciTrfContext<GpoSciTrfSyncReq, SciTrfSyncBO> context) {
        GpoSciTrfSyncReq gpoSciTrfSyncReq = context.getParam();
        String objectType = gpoSciTrfSyncReq.getObjectType();
        List<TrfTestSampleDTO> trfTestSampleDTOList = new ArrayList<>();
        if (!StandardObjectType.Enquiry.check(objectType)) {
            List<OrderBO> orderList = context.getOrderList();
            if (Func.isNotEmpty(orderList)) {
                for (OrderBO orderBO : orderList) {
                    TestSampleQueryReq testSampleQueryReq = new TestSampleQueryReq();
                    testSampleQueryReq.setOrderNo(orderBO.getHeader().getOrderNo());
                    BaseResponse<List<TestSampleBO>> testSampleRes = testSampleService.queryV1(testSampleQueryReq);
                    if (testSampleRes.isSuccess() && Func.isNotEmpty(testSampleRes.getData())) {
                        List<TestSampleBO> testSamplePOList = testSampleRes.getData();
                        for (TestSampleBO testSampleBO : testSamplePOList) {
                            TrfTestSampleDTO trfTestSampleDTO = new TrfTestSampleDTO();
                            trfTestSampleDTO.setTestSampleInstanceId(testSampleBO.getId());
                            trfTestSampleDTO.setTestSampleNo(testSampleBO.getTestSampleNo());
                            List<TestSampleGroupBO> testSampleGroupList = testSampleBO.getTestSampleGroupList();
                            List<TrfTestSampleGroupDTO> trfTestSampleGroupList = new ArrayList<>();
                            if (Func.isNotEmpty(testSampleGroupList)) {
                                for (TestSampleGroupBO testSampleGroupBO : testSampleGroupList) {
                                    TrfTestSampleGroupDTO trfTestSampleGroupDTO = new TrfTestSampleGroupDTO();
                                    trfTestSampleGroupDTO.setTestSampleInstanceId(testSampleGroupBO.getTestSampleInstanceId());
                                    trfTestSampleGroupDTO.setMainSampleFlag(testSampleGroupBO.getMainSampleFlag());
                                    trfTestSampleGroupList.add(trfTestSampleGroupDTO);
                                }
                            }
                            trfTestSampleDTO.setTestSampleGroupList(trfTestSampleGroupList);
                            trfTestSampleDTO.setExternalSampleNo(testSampleBO.getExternalSampleNo());
                            trfTestSampleDTO.setTestSampleType(testSampleBO.getTestSampleType());
                            trfTestSampleDTO.setTestSampleSeq(testSampleBO.getTestSampleSeq());
                            TestSampleMaterialAttrBO materialAttr = testSampleBO.getMaterialAttr();
                            List<TrfMaterialAttrDTO> trfMaterialAttrDTOList = new ArrayList<>();
                            if(Func.isNotEmpty(materialAttr)){
                                TrfMaterialAttrDTO trfMaterialAttrDTO = new TrfMaterialAttrDTO();
                                trfMaterialAttrDTO.setMaterialColor(materialAttr.getMaterialColor());
                                trfMaterialAttrDTO.setMaterialDescription(materialAttr.getMaterialDescription());
                                trfMaterialAttrDTO.setMaterialEndUse(materialAttr.getMaterialEndUse());
                                trfMaterialAttrDTO.setMaterialTexture(materialAttr.getMaterialTexture());
                                trfMaterialAttrDTOList.add(trfMaterialAttrDTO);
                            }
                            trfTestSampleDTO.setMaterialAttrList(trfMaterialAttrDTOList);
                            trfTestSampleDTOList.add(trfTestSampleDTO);
                        }
                    }
                }
            }
        }

        context.getDomain().setTestSampleList(trfTestSampleDTOList);
    }


    private void buildTrfReport(SciTrfContext<GpoSciTrfSyncReq, SciTrfSyncBO> context) {
        GpoSciTrfSyncReq gpoSciTrfSyncReq = context.getParam();
        List<ReportPO> reportList = context.getReportList();
        List<ReportFilePO> reportFileList = context.getReportFileList();
        List<ReportMatrixRelBO> reportMatrixRelList = context.getReportMatrixRelList();
        List<TestMatrixBO> reportMatrixList = context.getReportMatrixList();
        List<ConclusionDTO> reportConclusionList = context.getReportConclusionList();
        List<ConclusionDTO> reportMatrixConclusionList = context.getReportMatrixConclusionList();
        String eventTypeStr = gpoSciTrfSyncReq.getEventType();
        EventType eventType = EventType.findEventType(eventTypeStr);

        if (Func.isEmpty(reportList)) {
            return;
        }
        String objectType = gpoSciTrfSyncReq.getObjectType();
        if (!StandardObjectType.Enquiry.check(objectType)) {

            List<TrfReportDTO> trfReportDTOList = new ArrayList<>();
            if (Func.isNotEmpty(reportList)) {
                if (StandardObjectType.Report.check(objectType)) {
                    reportList =  reportList.stream().filter(item->Func.equalsSafe(item.getReportNo(),gpoSciTrfSyncReq.getSourceNo())).collect(Collectors.toList());
                }
                for (ReportPO reportPO : reportList) {
                    TrfReportDTO trfReportDTO = new TrfReportDTO();
                    trfReportDTO.setOrderNo(context.getSyncTrfRootOrderNo());
                    trfReportDTO.setRealOrderNo(reportPO.getOrderNo());
                    trfReportDTO.setReportId(reportPO.getId());
                    trfReportDTO.setReportNo(reportPO.getActualReportNo());
                    trfReportDTO.setReportStatus(reportPO.getReportStatus());
                    trfReportDTO.setReportDueDate(reportPO.getReportDueDate());
                    trfReportDTO.setApproveBy(reportPO.getApproverBy());
                    trfReportDTO.setApproveDate(reportPO.getApproverDate());
                    trfReportDTO.setSoftCopyDeliveryDate(reportPO.getSoftcopyDeliveryDate());
                    trfReportDTO.setOriginalReportNo(reportPO.getParentReportNo());
                    //转换成外部号
                    if(Func.isNotEmpty(trfReportDTO.getOriginalReportNo())){
                        ReportQueryReq reportQueryReq = new ReportQueryReq();
                        reportQueryReq.setReportNo(trfReportDTO.getOriginalReportNo());
                        List<ReportPO> originReportList = reportService.select(reportQueryReq).getData();
                        if(Func.isNotEmpty(originReportList) && Func.isNotEmpty(originReportList.get(0).getActualReportNo())){
                            trfReportDTO.setOriginalReportNo(originReportList.get(0).getActualReportNo());
                        }
                    }
                    trfReportDTO.setReportVersion(reportPO.getReportVersion());
//                    trfReportDTO.setOriginalReportNo(reportPO);

                    if (Func.isNotEmpty(reportConclusionList)) {
                        ConclusionDTO reportConclusion = reportConclusionList.stream().filter(item -> Func.equalsSafe(item.getReportId(), reportPO.getId())).findAny().orElse(null);
                        if (Func.isNotEmpty(reportConclusion)) {
                            TrfConclusionDTO trfConclusionDTO = new TrfConclusionDTO();
                            trfConclusionDTO.setConclusionCode(reportConclusion.getConclusionCode());
                            trfConclusionDTO.setCustomerConclusion(reportConclusion.getDescription());
                            trfConclusionDTO.setConclusionRemark(reportConclusion.getConclusionRemark());
                            trfReportDTO.setConclusion(trfConclusionDTO);
                        }
                    }


                    if (Func.isNotEmpty(reportMatrixRelList)) {
                        List<ReportMatrixRelBO> reportMatrixRelBOList = reportMatrixRelList.stream().filter(item -> Func.equalsSafe(item.getReportId(), reportPO.getId())).collect(Collectors.toList());
                        if (Func.isNotEmpty(reportMatrixRelBOList)) {
                            List<TrfReportMatrixDTO> trfReportMatrixDTOList = new ArrayList<>();
                            for (ReportMatrixRelBO reportMatrixRelBO : reportMatrixRelBOList) {
                                TrfReportMatrixDTO trfReportMatrixDTO = new TrfReportMatrixDTO();
                                trfReportMatrixDTO.setTestMatrixId(reportMatrixRelBO.getTestMatrixId());
                                trfReportMatrixDTO.setTestSampleInstanceId(reportMatrixRelBO.getTestSampleId());
                                trfReportMatrixDTO.setTestLineInstanceId(reportMatrixRelBO.getTestLineInstanceId());
                                if (Func.isNotEmpty(reportMatrixConclusionList)) {
                                    ConclusionDTO reportMatrixConclusion = reportMatrixConclusionList.stream().filter(item -> Func.equalsSafe(item.getReportId(), reportPO.getId()) && Func.equalsSafe(item.getObjectId(), reportMatrixRelBO.getTestMatrixId())).findAny().orElse(null);
                                    if (Func.isNotEmpty(reportMatrixConclusion)) {
                                        TrfConclusionDTO trfRmConclusionDTO = new TrfConclusionDTO();
                                        trfRmConclusionDTO.setConclusionCode(reportMatrixConclusion.getConclusionCode());
                                        trfRmConclusionDTO.setCustomerConclusion(reportMatrixConclusion.getDescription());
                                        trfRmConclusionDTO.setConclusionRemark(reportMatrixConclusion.getConclusionRemark());
                                        trfReportMatrixDTO.setConclusion(trfRmConclusionDTO);
                                    }
                                }
//                                trfReportMatrixDTO.setTestMatrixFileList(Lists.newArrayList());
                                trfReportMatrixDTOList.add(trfReportMatrixDTO);
                            }
                            trfReportDTO.setReportMatrixList(trfReportMatrixDTOList);
                        }
                    }
                    List<ReportFilePO> matchReportFileList = new ArrayList<>();
                    if(Func.isNotEmpty(reportFileList)){
                        matchReportFileList = reportFileList.stream().filter(item -> Func.equalsSafe(item.getReportID(), reportPO.getId())).collect(Collectors.toList());
                    }
                    if (Func.isNotEmpty(matchReportFileList)) {
                        List<TrfFileDTO> trfFileDTOList = new ArrayList<>();
                        for (ReportFilePO reportFilePO : matchReportFileList) {
                            TrfFileDTO trfFileDTO = new TrfFileDTO();
                            trfFileDTO.setLanguageId(reportFilePO.getLanguageID());
                            trfFileDTO.setFileType(Func.toStr(reportFilePO.getReportFileType()));
                            trfFileDTO.setFileName(reportFilePO.getFilename());
                            trfFileDTO.setCloudId(reportFilePO.getCloudID());
                            trfFileDTO.setToCustomerFlag(1);

                            if (ReportFileType.check(reportFilePO.getReportFileType(), ReportFileType.PDF)) {
                                trfFileDTOList.add(trfFileDTO);
                            }
                        }
                        trfReportDTO.setReportFileList(trfFileDTOList);
                    }
//                    trfReportDTO.setTrfList(Lists.newArrayList());

                    if (Func.isNotEmpty(eventType)) {
                        switch (eventType) {
                            case ReviseReport:
                                if (ReportStatus.check(reportPO.getReportStatus(), ReportStatus.Reworked) || StringUtils.equalsIgnoreCase(Constants.SCI.URL.CAN_REVISE_REPORT, gpoSciTrfSyncReq.getSciUrl())) {
                                    trfReportDTOList.add(trfReportDTO);
                                }
                                break;
                            default:
                                if (ReportStatus.check(reportPO.getReportStatus(), ReportStatus.Completed)) {
                                    trfReportDTOList.add(trfReportDTO);
                                }
                                break;
                        }
                    }
                }
            }


            context.getDomain().setReportList(trfReportDTOList);
        }
    }

    private void buildTrfOrder(SciTrfContext<GpoSciTrfSyncReq, SciTrfSyncBO> context) {
        Set<String> orderNoList = context.getOrderNoList();
        TrfOrderDTO trfOrderDTO = new TrfOrderDTO();
        GpoSciTrfSyncReq param = context.getParam();
        String objectType = context.getParam().getObjectType();
        List<EnquiryPO> enquiryList = context.getEnquiryList();
        if(Func.isNotEmpty(enquiryList)){
            trfOrderDTO.setEnquiryNo(enquiryList.get(0).getEnquiryNo());
        }
        trfOrderDTO.setOrderNo(context.getSyncTrfRootOrderNo());
        trfOrderDTO.setRealOrderNo(param.getOrderNo());
        trfOrderDTO.setOrderId(context.getSciOrderId());
//        Enquiry
        if (!StandardObjectType.Enquiry.check(objectType)) {
            List<OrderBO> orderBOList = context.getOrderList();
            if (Func.isNotEmpty(orderBOList)) {
                OrderBO orderBO = orderBOList.get(0);
                trfOrderDTO.setProductCategory(orderBO.getHeader().getProductCategory());
                trfOrderDTO.setProductSubCategory(orderBO.getHeader().getProductSubCategory());
                trfOrderDTO.setServiceStartDate(orderBO.getHeader().getServiceStartDate());
                trfOrderDTO.setTestingStartDate(orderBO.getHeader().getTestingStartDate());
                trfOrderDTO.setTestingEndDate(orderBO.getHeader().getTestingEndDate());
                trfOrderDTO.setOrderExpectDueDate(orderBO.getHeader().getOrderExpectDueDate());

                OrderPaymentBO orderPayment = orderBO.getPayment();
                if (Func.isNotEmpty(orderPayment)) {
                    TrfPaymentDTO trfPaymentDTO = new TrfPaymentDTO();
                    trfPaymentDTO.setPaymentStatus(orderPayment.getPaymentStatus());
                    trfPaymentDTO.setCurrency(orderPayment.getCurrency());
                    trfPaymentDTO.setTotalAmount(Func.toStr(orderPayment.getTotalAmount()));
                    trfPaymentDTO.setMainCurrencyTotalAmount(Func.toStr(orderPayment.getMainCurrencyTotalAmount()));
                    trfOrderDTO.setPayment(trfPaymentDTO);
                }
                List<ContactPersonBO> orderContactPersonList = orderBO.getContactPersonList();
                if (Func.isNotEmpty(orderContactPersonList)) {
                    List<TrfOrderContactPersonDTO> trfOrderContactPersonDTOList = new ArrayList<>();
                    for (ContactPersonBO contactPersonBO : orderContactPersonList) {
                        TrfOrderContactPersonDTO trfOrderContactPersonDTO = new TrfOrderContactPersonDTO();
                        trfOrderContactPersonDTO.setContactId("");
                        trfOrderContactPersonDTO.setContactAddressId("");
                        trfOrderContactPersonDTO.setBossContactId(0L);
                        trfOrderContactPersonDTO.setBossSiteUseId(0L);
                        trfOrderContactPersonDTO.setContactUsage(OrderPersonType.getCode(contactPersonBO.getContactUsage()).getStatus());
                        trfOrderContactPersonDTO.setContactRegionAccount(contactPersonBO.getRegionAccount());
                        trfOrderContactPersonDTO.setContactName(contactPersonBO.getContactName());
                        trfOrderContactPersonDTO.setContactEmail(contactPersonBO.getContactEmail());
                        trfOrderContactPersonDTO.setContactPhone(contactPersonBO.getContactMobile());
                        trfOrderContactPersonDTO.setContactTelephone(contactPersonBO.getContactTelephone());
                        trfOrderContactPersonDTO.setContactFax(contactPersonBO.getContactFAX());
                        trfOrderContactPersonDTO.setResponsibleTeamCode(contactPersonBO.getResponsibleTeamCode());
                        trfOrderContactPersonDTOList.add(trfOrderContactPersonDTO);
                    }
                    trfOrderDTO.setContactPersonList(trfOrderContactPersonDTOList);
                }


                OrderFlagBO orderFlagBO = orderBO.getFlags();
                if (Func.isNotEmpty(orderFlagBO)) {
                    TrfOrderFlagsDTO trfOrderFlagsDTO = new TrfOrderFlagsDTO();
                    trfOrderFlagsDTO.setSelfTestFlag(orderFlagBO.getSelfTestFlag());
                    trfOrderFlagsDTO.setToDMFlag(orderFlagBO.getToDMFlag());
                    trfOrderDTO.setFlags(trfOrderFlagsDTO);
                }

                OrderOthersBO orderOthersBO = orderBO.getOthers();
                if (Func.isNotEmpty(orderOthersBO)) {
                    TrfOrderOthersDTO trfOrderOthersDTO = new TrfOrderOthersDTO();
                    if (Func.isNotEmpty(orderOthersBO.getPending())) {
                        TrfOrderPendingDTO trfOrderPendingDTO = new TrfOrderPendingDTO();
                        trfOrderPendingDTO.setPendingFlag(orderOthersBO.getPending().getPendingFlag());
                        trfOrderPendingDTO.setPendingType(Func.toStr(orderOthersBO.getPending().getPendingType()));
                        trfOrderPendingDTO.setPendingRemark(orderOthersBO.getPending().getPendingRemark());
                        trfOrderOthersDTO.setPending(trfOrderPendingDTO);
                    }
                    trfOrderOthersDTO.setOrderRemark(orderOthersBO.getOrderRemark());
                    trfOrderDTO.setOthers(trfOrderOthersDTO);
                }
                List<CustomerBO> orderCustomerList = orderBO.getCustomerList();
                if (Func.isNotEmpty(orderCustomerList)) {
                    List<TrfCustomerDTO> customerList = new ArrayList<>();
                    for (CustomerBO customerBO : orderCustomerList) {
                        TrfCustomerDTO trfCustomerDTO = new TrfCustomerDTO();
                        trfCustomerDTO.setCustomerInstanceId("");
                        trfCustomerDTO.setCustomerId(customerBO.getCustomerId());
                        trfCustomerDTO.setCustomerUsage(customerBO.getCustomerUsage());
                        trfCustomerDTO.setBossNo(customerBO.getBossNo());
                        trfCustomerDTO.setCustomerGroupCode(customerBO.getCustomerGroupCode());
                        trfCustomerDTO.setCustomerName(customerBO.getCustomerName());
                        trfCustomerDTO.setCustomerAddress(customerBO.getCustomerAddress());
                        trfCustomerDTO.setPaymentTerm(customerBO.getPaymentTerm());
                        trfCustomerDTO.setBlackFlag(0);
                        if(Func.equalsSafe(trfCustomerDTO.getCustomerUsage(), CustomerType.Buyer.getStatus())){
                            trfCustomerDTO.setMarketSegmentCode(orderOthersBO.getDepartmentCode());
                            trfCustomerDTO.setMarketSegmentName(orderOthersBO.getDepartmentCode());
                        }
                        List<CustomerContactBO> customerContactList = customerBO.getCustomerContactList();
                        if (Func.isNotEmpty(customerContactList)) {
                            List<TrfCustomerContactDTO> trfCustomerContactDTOList = new ArrayList<>();
                            for (CustomerContactBO customerContactBO : customerContactList) {
                                TrfCustomerContactDTO trfCustomerContactDTO = new TrfCustomerContactDTO();
                                trfCustomerContactDTO.setCustomerContactId(customerContactBO.getCustomerContactId());
                                trfCustomerContactDTO.setContactAddressId(customerContactBO.getCustomerContactAddressId());
                                trfCustomerContactDTO.setBossContactId(customerContactBO.getBossContactId());
                                trfCustomerContactDTO.setBossSiteUseId(customerContactBO.getBossSiteUseId());
//                        trfCustomerContactDTO.setContactUsage(customerContactBO.getBossContactId());
//                                trfCustomerContactDTO.setContactRegionAccount(customerContactBO.getRegionAccount());
                                trfCustomerContactDTO.setContactName(customerContactBO.getContactName());
                                trfCustomerContactDTO.setContactEmail(customerContactBO.getContactEmail());
                                trfCustomerContactDTO.setContactMobile(customerContactBO.getContactMobile());
                                trfCustomerContactDTO.setContactTelephone(customerContactBO.getContactTelephone());
                                trfCustomerContactDTO.setContactFax(customerContactBO.getContactFAX());
                                trfCustomerContactDTO.setSgsAccountCode(customerContactBO.getContactSgsMartAccount());
                                trfCustomerContactDTO.setSgsUserId(customerContactBO.getContactSgsMartUserId());
//                        trfCustomerContactDTO.setResponsibleTeamCode(customerContactBO.getContactEmail());
                                trfCustomerContactDTOList.add(trfCustomerContactDTO);
                            }
                            trfCustomerDTO.setCustomerContactList(trfCustomerContactDTOList);
                        }

                        List<CustomerLanguageBO> languageList = customerBO.getLanguageList();
                        if (Func.isNotEmpty(languageList)) {
                            List<TrfCustomerLangDTO> trfCustomerLangDTOList = new ArrayList<>();
                            for (CustomerLanguageBO customerLanguageBO : languageList) {
                                TrfCustomerLangDTO trfCustomerLangDTO = new TrfCustomerLangDTO();
                                trfCustomerLangDTO.setLanguageId(customerLanguageBO.getLanguageId());
                                trfCustomerLangDTO.setCustomerName(customerLanguageBO.getCustomerName());
                                trfCustomerLangDTO.setCustomerAddress(customerLanguageBO.getCustomerAddress());
                                trfCustomerLangDTOList.add(trfCustomerLangDTO);
                            }
                            trfCustomerDTO.setLanguageList(trfCustomerLangDTOList);
                        }
                        customerList.add(trfCustomerDTO);
                    }
                    trfOrderDTO.setCustomerList(customerList);
                }
                ServiceRequirementBO orderServiceRequirement = orderBO.getServiceRequirement();
                if (Func.isNotEmpty(orderServiceRequirement)) {
                    TrfServiceRequirementDTO trfServiceRequirementDTO = new TrfServiceRequirementDTO();
                    ServiceRequirementReportBO serviceRequirementReportBO = orderServiceRequirement.getReport();
                    if (Func.isNotEmpty(serviceRequirementReportBO)) {
                        TrfServiceRequirementReportDTO trfServiceRequirementReportDTO = new TrfServiceRequirementReportDTO();
                        trfServiceRequirementReportDTO.setReportLanguage(serviceRequirementReportBO.getReportLanguage());
//                trfServiceRequirementReportDTO.setReportForm(serviceRequirementReportBO.getReportForm());
                        trfServiceRequirementReportDTO.setReportHeader(serviceRequirementReportBO.getReportHeader());
                        trfServiceRequirementReportDTO.setReportAddress(serviceRequirementReportBO.getReportAddress());
                        trfServiceRequirementReportDTO.setLanguageList(Lists.newArrayList());
                        trfServiceRequirementReportDTO.setAccreditation(serviceRequirementReportBO.getQualificationType());
                        trfServiceRequirementReportDTO.setNeedConclusion(serviceRequirementReportBO.getNeedConclusion());
                        trfServiceRequirementReportDTO.setNeedPhoto(serviceRequirementReportBO.getNeedPhoto());
                        trfServiceRequirementReportDTO.setNeedDraft(serviceRequirementReportBO.getNeedDraft());
                        trfServiceRequirementDTO.setReport(trfServiceRequirementReportDTO);
                    }
                    trfServiceRequirementDTO.setOtherRequestRemark(orderServiceRequirement.getOtherRequestRemark());
                    trfOrderDTO.setServiceRequirement(trfServiceRequirementDTO);
                }
                List<AttachmentBO> orderAttachmentList = orderBO.getAttachmentList();
                if (Func.isNotEmpty(orderAttachmentList)) {
                    orderAttachmentList = orderAttachmentList.stream().filter(item->Func.equalsSafe(item.getToCp(),1)).collect(Collectors.toList());
                    List<TrfFileDTO> trfAttachmentList = new ArrayList<>();
                    for (AttachmentBO attachmentBO : orderAttachmentList) {
                        TrfFileDTO trfFileDTO = new TrfFileDTO();
//                trfFileDTO.setLanguageId(0);
                        trfFileDTO.setFileType(attachmentBO.getFileType());
                        trfFileDTO.setFileName(attachmentBO.getFileName());
                        trfFileDTO.setFilePath(attachmentBO.getFilePath());
                        trfFileDTO.setCloudId(attachmentBO.getCloudId());
                        trfFileDTO.setToCustomerFlag(1);
//                trfFileDTO.setFileSize(0L);
                        trfAttachmentList.add(trfFileDTO);
                    }
                    trfOrderDTO.setAttachmentList(trfAttachmentList);
                }

                //查询Order

                //查询ChildList
                List<GeneralOrderPO> rootOrderList = context.getChildOrderList();
                if (Func.isNotEmpty(rootOrderList)) {
                    rootOrderList = rootOrderList.stream().filter(item -> !orderNoList.contains(item.getRootOrderNo())).collect(Collectors.toList());
                    List<TrfOrderDTO> childOrderList = new ArrayList<>();
                    for (GeneralOrderPO generalOrderPO : rootOrderList) {
                        TrfOrderDTO rootOrderDto = new TrfOrderDTO();
                        trfOrderDTO.setRealOrderNo(generalOrderPO.getOrderNo());
                        rootOrderDto.setOrderNo(context.getSyncTrfRootOrderNo());
                        rootOrderDto.setOrderId(generalOrderPO.getId());
                        childOrderList.add(rootOrderDto);
                    }
                    trfOrderDTO.setChildOrderList(childOrderList);
                }
            }
        }
        context.getDomain().setOrder(trfOrderDTO);
    }

    @Override
    public BaseResponse before(SciTrfContext<GpoSciTrfSyncReq, SciTrfSyncBO> context) {
        GpoSciTrfSyncReq gpoSciTrfSyncReq = context.getParam();
        String orderNo = gpoSciTrfSyncReq.getOrderNo();
        String orderId = gpoSciTrfSyncReq.getOrderId();
        String objectType = gpoSciTrfSyncReq.getObjectType();
        String trfNo = gpoSciTrfSyncReq.getTrfNo();
        Integer refSystemId = gpoSciTrfSyncReq.getRefSystemId();
        String enquiryNo = "";
        String primaryLanguageCode = frameworkClient.getPrimaryLanguageCode(Func.isNotEmpty(gpoSciTrfSyncReq.getProductLineCode())?gpoSciTrfSyncReq.getProductLineCode():ProductLineContextHolder.getProductLineCode());
        int primaryLanguageId = LanguageType.findCode(primaryLanguageCode).getLanguageId();
        context.setPrimaryLanguageId(primaryLanguageId);
        if (StandardObjectType.Enquiry.check(objectType)) {
            context.setSyncTrfRootOrderNo(orderNo);
            enquiryNo = orderNo;
            EnquiryIdReq enquiryIdReq = new EnquiryIdReq();
            enquiryIdReq.setEnquiryNoList(Sets.newHashSet(enquiryNo));
            List<EnquiryPO> enquiryPOList = enquiryService.select(enquiryIdReq).getData();
            context.setEnquiryList(enquiryPOList);
            if (Func.isNotEmpty(enquiryPOList)) {
                context.setLabCode(enquiryPOList.get(0).getLabCode());
                context.setBuCode(enquiryPOList.get(0).getBuCode());
            }
        } else {
            OrderQueryReq orderQueryReq = new OrderQueryReq();
            orderQueryReq.setOrderNoList(Sets.newHashSet(orderNo));
            BaseResponse<List<OrderBO>> orderRsp = orderDomainService.queryBO(orderQueryReq);
            if (orderRsp.isFail() || Func.isEmpty(orderRsp.getData())) {
                log.info("SciTrfSync Fail: Not Find Order");
                return BaseResponse.newFailInstance("order.result.empty", null);
            }
            List<OrderBO> orderBOList = orderRsp.getData();
            LabBO lab = orderBOList.get(0).getLab();
            String labCode = lab.getLabCode();
            context.setLabCode(labCode);
            context.setBuCode(lab.getBuCode());
            context.setOrderList(orderBOList);

            Set<String> orderNoList = orderBOList.stream().map(OrderBO::getHeader).map(OrderHeaderBO::getOrderNo).collect(Collectors.toSet());
            context.setOrderNoList(orderNoList);

            //Test Line List
            OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
            orderTestLineReq.setOrderNo(orderNo);
            BaseResponse<List<TestLineBO>> testLineRsp = testLineDomainService.queryPPTestLine(orderTestLineReq);
            if (testLineRsp.isSuccess()) {
                context.setTestLineList(testLineRsp.getData());
            }
            //Test SampleList
            TestSampleQueryReq testSampleQueryReq = new TestSampleQueryReq();
            testSampleQueryReq.setOrderNo(orderNo);
            BaseResponse<List<TestSamplePO>> testSampleRsp = testSampleService.select(testSampleQueryReq);
            if (testSampleRsp.isSuccess()) {
                context.setTestSampleList(testSampleRsp.getData());
            }
            //Test Result List


            //Invoice List
            BaseResponse<List<BossOrderInvoiceDTO>> invoiceRsp = bossOrderInvoiceService.getBossInvoiceByOrderNo(orderNo);
            if (invoiceRsp.isSuccess()) {
                context.setInvoiceList(invoiceRsp.getData());
            }
            List<GeneralOrderPO> generalOrderPOList = generalOrderService.query2(orderQueryReq).getData();
            if (Func.isNotEmpty(generalOrderPOList)) {
                GeneralOrderPO generalOrderPO = generalOrderPOList.get(0);
                enquiryNo = generalOrderPO.getEnquiryNo();
                //设置同步sci的rootOrderNo
                String syncTrfRootOrderNo = generalOrderPO.getOrderNo();
                if(Func.isNotEmpty(enquiryNo)){
                    //查询enquiryNo
                    List<EnquiryTrfRelationshipPO> enquiryTrfRelationshipPOList = null;
                    if (Func.isNotEmpty(enquiryNo)) {
                        EnquiryIdReq enquiryIdReq = new EnquiryIdReq();
                        enquiryIdReq.setEnquiryNoList(Sets.newHashSet(enquiryNo));
                        List<EnquiryPO> enquiryPOList = enquiryService.select(enquiryIdReq).getData();
                        enquiryIdReq.setEnquiryIdList(Sets.newHashSet(enquiryPOList.stream().map(EnquiryPO::getId).filter(Func::isNotEmpty).collect(Collectors.toSet())));
                        enquiryTrfRelationshipPOList = enquiryTrfRelationshipService.select(enquiryIdReq).getData();
                    }
                    if (Func.isNotEmpty(enquiryTrfRelationshipPOList)) {
                        boolean existEnquiryTrfRel = enquiryTrfRelationshipPOList.stream().anyMatch(item -> RefIntegrationChannel.check(item.getIntegrationChannel(), RefIntegrationChannel.SCI));
                        if (existEnquiryTrfRel) {
                            syncTrfRootOrderNo = enquiryNo;
                        }
                    }
                }
                context.setSyncTrfRootOrderNo(syncTrfRootOrderNo);
                String rootOrderNo = generalOrderPO.getOrderNo();
                if(Func.isNotEmpty(generalOrderPO.getRootOrderNo())){
                    rootOrderNo = generalOrderPO.getRootOrderNo();
                }
                if (Func.isEmpty(rootOrderNo)) {
                    enquiryNo = generalOrderPO.getEnquiryNo();
                } else {
                    OrderQueryReq orderQueryReq1 = new OrderQueryReq();
                    orderQueryReq1.setOrderNoList(Sets.newHashSet(rootOrderNo));
                    List<GeneralOrderPO> rootOrderInfos = generalOrderService.query2(orderQueryReq1).getData();
                    if (Func.isNotEmpty(rootOrderInfos)) {
                        GeneralOrderPO rootOrder = rootOrderInfos.get(0);
                        enquiryNo = rootOrder.getEnquiryNo();
                        context.setRootOrderInfo(rootOrder);
                    }
                    OrderQueryReq rootOrderQuery = new OrderQueryReq();
                    rootOrderQuery.setOrderNoList(Sets.newHashSet(rootOrderNo));
                    List<GeneralOrderPO> rootOrderList = generalOrderService.queryByRootOrderNo(rootOrderQuery).getData();
                    rootOrderList.addAll(rootOrderInfos);
                    context.setChildOrderList(rootOrderList);
                }


                ProductSampleQueryReq productSampleQueryReq = new ProductSampleQueryReq();
                productSampleQueryReq.setOrderIdList(Sets.newHashSet(generalOrderPO.getId()));
                BaseResponse<List<ProductInstancePO>> orderProductSampleRsp = productInstanceService.queryOrderProductSample(productSampleQueryReq);
                if (orderProductSampleRsp.isSuccess()) {
                    List<ProductInstancePO> productInstancePOList = orderProductSampleRsp.getData();
                    productInstancePOList = productInstancePOList.stream().filter(item-> !Func.equalsSafe(item.getCancelFlag(),1)).collect(Collectors.toList());
                    context.setProductInstanceList(productInstancePOList);
                }
                List<ProductBO> productBOS = this.convertProduct(context);
                context.setOrderProductList(productBOS);

            }
            if (Func.isNotEmpty(enquiryNo)) {
                EnquiryIdReq enquiryIdReq = new EnquiryIdReq();
                enquiryIdReq.setEnquiryNoList(Sets.newHashSet(enquiryNo));
                List<EnquiryPO> enquiryPOList = enquiryService.select(enquiryIdReq).getData();
                context.setEnquiryList(enquiryPOList);
                enquiryIdReq.setEnquiryIdList(Sets.newHashSet(enquiryPOList.stream().map(EnquiryPO::getId).filter(Func::isNotEmpty).collect(Collectors.toSet())));
                BaseResponse<List<EnquiryTrfRelationshipPO>> enquiryTrfRsp = enquiryTrfRelationshipService.select(enquiryIdReq);
                if (enquiryTrfRsp.isSuccess() && Func.isNotEmpty(enquiryTrfRsp.getData())) {
                    context.setEnquiryTrfRelationshipPOList(enquiryTrfRsp.getData());
                }
            }
            //Report List
            OrderIdReq orderIdReq = new OrderIdReq();
            orderIdReq.setOrderNoList(Sets.newHashSet(gpoSciTrfSyncReq.getOrderNo()));
            BaseResponse<List<ReportPO>> reportRsp = reportService.queryReportByOrderNo(orderIdReq);
            if (reportRsp.isSuccess() && Func.isNotEmpty(reportRsp.getData())) {
                List<ReportPO> reportPOList = reportRsp.getData();
                context.setReportList(reportPOList);
                Set<String> reportNoList = reportPOList.stream().map(ReportPO::getReportNo).collect(Collectors.toSet());
                Set<String> reportIdList = reportPOList.stream().map(ReportPO::getId).collect(Collectors.toSet());
                //ReportConclusionList
                ReportIdReq reportIdReq = new ReportIdReq();
                reportIdReq.setReportIdList(reportIdList);
                BaseResponse<List<ConclusionDTO>> reportConclusionRsp = reportService.queryReportConclusionList(reportIdReq);
                if (reportConclusionRsp.isSuccess() && Func.isNotEmpty(reportConclusionRsp.getData())) {
                    context.setReportConclusionList(reportConclusionRsp.getData());
                }
                BaseResponse<List<ConclusionDTO>> reportMatrixConclusionRsp = reportService.queryReportMatrixConclusionList(reportIdReq);
                if (reportMatrixConclusionRsp.isSuccess() && Func.isNotEmpty(reportMatrixConclusionRsp.getData())) {
                    context.setReportMatrixConclusionList(reportMatrixConclusionRsp.getData());
                }
                //Report File List
                ReportFileQueryReq reportQueryReq = new ReportFileQueryReq();
                reportQueryReq.setReportNoList(reportNoList);
                BaseResponse<List<ReportFilePO>> reportFileRsp = reportFileService.query(reportQueryReq);
                if (reportFileRsp.isSuccess() && Func.isNotEmpty(reportFileRsp.getData())) {
                    context.setReportFileList(reportFileRsp.getData());
                }
                ReportMatrixQueryReq reportMatrixQueryReq = new ReportMatrixQueryReq();
                reportMatrixQueryReq.setReportNoList(reportNoList);
                BaseResponse<List<ReportMatrixRelBO>> reportMatrixRelRsp = reportMatrixRelService.queryReportMatrix(reportMatrixQueryReq);
                if (reportMatrixRelRsp.isSuccess() && Func.isNotEmpty(reportMatrixRelRsp.getData())) {
                    List<ReportMatrixRelBO> reportMatrixRelBOList = reportMatrixRelRsp.getData();
                    context.setReportMatrixRelList(reportMatrixRelBOList);
                    Set<String> testMatrixIdList = reportMatrixRelBOList.stream().map(ReportMatrixRelBO::getTestMatrixId).collect(Collectors.toSet());
                    TestMatrixQueryReq testMatrixQueryReq = new TestMatrixQueryReq();
                    testMatrixQueryReq.setTestMatrixIdList(testMatrixIdList);
                    BaseResponse<List<TestMatrixBO>> testMatrixRsp = testMatrixService.queryV1(testMatrixQueryReq);
                    if (testMatrixRsp.isSuccess() && Func.isNotEmpty(testMatrixRsp.getData())) {
                        context.setReportMatrixList(testMatrixRsp.getData());
                    }

                }
            }
            OrderTrfReq orderTrfReq = new OrderTrfReq();
            orderTrfReq.setOrderNoList(orderNoList);
            BaseResponse<List<OrderTrfBO>> orderTrfRes = orderTrfRelationshipService.queryOrderTrf(orderTrfReq);
            if (orderTrfRes.isSuccess() && Func.isNotEmpty(orderTrfRes.getData())) {
                context.setOrderTrfBOList(orderTrfRes.getData());
            }

            String eventTypeStr = gpoSciTrfSyncReq.getEventType();

            EventType eventType = EventType.findEventType(eventTypeStr);
            if (Func.isNotEmpty(eventType)) {
                switch (eventType) {
                    case Pending:
                    case UnPending:
                        //查询operationHistory
                        OrderIdReq orderIdReq1 = new OrderIdReq();
                        orderIdReq1.setOrderIdList(Sets.newHashSet(orderId));
                        BaseResponse<List<OrderOperationHistoryPO>> orderOperationRsp = orderOperationHistoryService.select(orderIdReq1);
                        if (orderOperationRsp.isSuccess()) {
                            context.setOrderOperationHistoryPOList(orderOperationRsp.getData());
                        }
                        break;
                    default:
                        break;
                }
            }

        }

        String sciOrderNo = orderNo;
        String sciOrderId = orderId;
        if (!StandardObjectType.Enquiry.check(objectType)) {
            if (Func.isNotEmpty(context.getEnquiryList()) && Func.isNotEmpty(context.getEnquiryTrfRelationshipPOList())) {
                EnquiryTrfRelationshipPO enquiryTrfRelationshipPO = context.getEnquiryTrfRelationshipPOList().stream().filter(item -> Func.isNotEmpty(item.getRefSystemId()) && Func.equalsSafe(item.getRefSystemId(), refSystemId)).findAny().orElse(null);
                if (Func.isNotEmpty(enquiryTrfRelationshipPO) && Func.equalsSafe(trfNo, enquiryTrfRelationshipPO.getRefNo())) {
                    sciOrderNo = context.getEnquiryList().get(0).getEnquiryNo();
                    sciOrderId = context.getEnquiryList().get(0).getId();
                }
            }
        }
        context.setSciOrderNo(sciOrderNo);
        context.setSciOrderId(sciOrderId);

        return super.before(context);
    }

    private List<ProductBO> convertProduct(SciTrfContext<GpoSciTrfSyncReq, SciTrfSyncBO> context) {
        List<ProductInstancePO> productInsList = context.getProductInstanceList();
        if (Func.isEmpty(productInsList)) {
            return null;
        }
//        productInsList = productInsList.stream().filter(i -> Func.isEmpty(i.getHeaderID())).collect(Collectors.toList());
        if (Func.isEmpty(productInsList)) {
            return Lists.newArrayList();
        }
        Set<String> formIdSets = productInsList.stream().map(ProductInstancePO::getDFFFormID).collect(Collectors.toSet());
        List<ProductBO> rdProductList = Lists.newArrayList();
        if (Func.isNotEmpty(formIdSets)) {
            String primaryLanguageCode = frameworkClient.getPrimaryLanguageCode(ProductLineContextHolder.getProductLineCode());
            int primaryLanguageId = LanguageType.findCode(primaryLanguageCode).getLanguageId();
            List<DffFormAttrDTO> allDffFormAttrDTOList = dffClient.getDffFormAttrByDffFormIdList(formIdSets);
            List<DffFormAttrDTO> dffFormAttrDTOListEn = new ArrayList<>();
            List<DffFormAttrDTO> dffFormAttrDTOListCn = new ArrayList<>();
            List<DffFormAttrDTO> defaultLanguageDffFormAttrDTOList = new ArrayList<>();
            if (Func.isNotEmpty(allDffFormAttrDTOList)) {
                //只过滤需要getDisplayInSystem包含1(sgsMart)的dff
//                allDffFormAttrDTOList = allDffFormAttrDTOList.stream().filter(item->Func.isNotEmpty(item.getDisplayInSystem()) && Func.toStrList(item.getDisplayInSystem()).contains("1")).collect(Collectors.toList());
                dffFormAttrDTOListEn = allDffFormAttrDTOList.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.English)).collect(Collectors.toList());
                dffFormAttrDTOListCn = allDffFormAttrDTOList.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.Chinese)).collect(Collectors.toList());
                defaultLanguageDffFormAttrDTOList = allDffFormAttrDTOList.stream().filter(i -> Func.isNotEmpty(i.getLanguageID()) && i.getLanguageID() == primaryLanguageId).collect(Collectors.toList());
                if (Func.isEmpty(defaultLanguageDffFormAttrDTOList)) {
                    defaultLanguageDffFormAttrDTOList = allDffFormAttrDTOList.stream().filter(i -> Func.isNotEmpty(i.getLanguageID()) && i.getLanguageID() != primaryLanguageId).collect(Collectors.toList());
                }
            }
            List<ProductInstancePO> productInstanceDTOListEn = productInsList.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.English)).collect(Collectors.toList());
            List<ProductInstancePO> productInstanceDTOListCn = productInsList.stream().filter(i -> LanguageType.check(i.getLanguageID(), LanguageType.Chinese)).collect(Collectors.toList());
            List<ProductInstancePO> defaultLanguageList = productInsList.stream().filter(i -> Func.isNotEmpty(i.getLanguageID()) && i.getLanguageID() == primaryLanguageId).collect(Collectors.toList());
            if (Func.isEmpty(defaultLanguageList)) {
                defaultLanguageList = productInsList.stream().filter(i -> Func.isNotEmpty(i.getLanguageID()) && i.getLanguageID() != primaryLanguageId).collect(Collectors.toList());
            }
            if (Func.isNotEmpty(productInsList)) {
                for (ProductInstancePO productInstancePO : productInsList) {
                    ProductBO rdProductDTO = new ProductBO();
                    rdProductDTO.setProductInstanceId(productInstancePO.getID());
                    rdProductDTO.setTemplateId(productInstancePO.getDFFFormID());
                    List<DFFAttrBO> rdProductAttrList = Lists.newArrayList();
                    Map<String, Object> defaultProductMap = BeanUtil.beanToMap(productInstancePO, false, true);
                    if (Func.isNotEmpty(defaultLanguageDffFormAttrDTOList)) {
                        List<DffFormAttrDTO> dffFormAttrDTOList = defaultLanguageDffFormAttrDTOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getdFFFormID(), productInstancePO.getDFFFormID())).collect(Collectors.toList());
                        dffFormAttrDTOList = dffFormAttrDTOList.stream().sorted(Comparator.comparing(item -> (Func.isEmpty(item.getSequence()) ? 0 : Integer.parseInt(item.getSequence())), Comparator.nullsLast(Integer::compareTo))).collect(Collectors.toList());
                        for (DffFormAttrDTO dffFormAttrDTO : dffFormAttrDTOList) {
                            DFFAttrBO sampleAttrDTO = new DFFAttrBO();
                            sampleAttrDTO.setLabelName(dffFormAttrDTO.getDispalyName());
                            sampleAttrDTO.setLabelCode(dffFormAttrDTO.getFieldCode());
                            sampleAttrDTO.setCustomerLabel("");
                            sampleAttrDTO.setValue(Func.toStr(defaultProductMap.getOrDefault(StrUtil.lowerFirst(dffFormAttrDTO.getFieldCode()), null)));
                            sampleAttrDTO.setSeq(Func.toInt(dffFormAttrDTO.getSequence()));
                            sampleAttrDTO.setDataType(dffFormAttrDTO.getFieldType());
                            List<DFFAttrLanguageBO> languageList = Lists.newArrayList();
                            //英文
                            List<ProductInstancePO> productInstancePOEns = productInstanceDTOListEn.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getDFFFormID(), productInstancePO.getDFFFormID())).collect(Collectors.toList());
                            ProductInstancePO productInstanceEn = productInstancePOEns.stream().filter(i -> Func.equals(i.getSampleID(), productInstancePO.getSampleID())).findAny().orElse(null);
                            if (Func.isNotEmpty(dffFormAttrDTOListEn) && Func.isNotEmpty(productInstanceEn)) {
                                DffFormAttrDTO dffFormAttrDTOEN = dffFormAttrDTOListEn.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getFieldCode(), dffFormAttrDTO.getFieldCode())).findAny().orElse(null);
                                if (Func.isNotEmpty(dffFormAttrDTOEN)) {
                                    Map<String, Object> productEnMap = BeanUtil.beanToMap(productInstanceEn, false, true);
                                    DFFAttrLanguageBO sampleAttrLanguage = new DFFAttrLanguageBO();
                                    sampleAttrLanguage.setLabelName(dffFormAttrDTOEN.getDispalyName());
                                    sampleAttrLanguage.setValue(Func.toStr(productEnMap.getOrDefault(StrUtil.lowerFirst(dffFormAttrDTOEN.getFieldCode()), null)));
                                    sampleAttrLanguage.setLanguageId(LanguageType.English.getLanguageId());
                                    languageList.add(sampleAttrLanguage);
                                }
                            }

                            //中文
                            List<ProductInstancePO> productInstancePOCns = productInstanceDTOListCn.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getDFFFormID(), productInstancePO.getDFFFormID())).collect(Collectors.toList());
                            ProductInstancePO productInstanceCn = productInstancePOCns.stream().filter(i -> Func.equals(i.getSampleID(), productInstancePO.getSampleID())).findAny().orElse(null);
                            if (Func.isNotEmpty(dffFormAttrDTOListCn) && Func.isNotEmpty(productInstanceCn)) {
                                DffFormAttrDTO dffFormAttrDTOCN = dffFormAttrDTOListCn.stream().filter(i -> StringUtils.equalsIgnoreCase(i.getFieldCode(), dffFormAttrDTO.getFieldCode())).findAny().orElse(null);
                                if (Func.isNotEmpty(dffFormAttrDTOCN)) {
                                    Map<String, Object> productCnMap = BeanUtil.beanToMap(productInstanceCn, false, true);
                                    DFFAttrLanguageBO sampleAttrLanguage = new DFFAttrLanguageBO();
                                    sampleAttrLanguage.setLabelName(dffFormAttrDTOCN.getDispalyName());
                                    sampleAttrLanguage.setValue(Func.toStr(productCnMap.getOrDefault(StrUtil.lowerFirst(dffFormAttrDTOCN.getFieldCode()), null)));
                                    sampleAttrLanguage.setLanguageId(LanguageType.Chinese.getLanguageId());
                                    languageList.add(sampleAttrLanguage);
                                }
                            }
                            sampleAttrDTO.setLanguageList(languageList);
                            rdProductAttrList.add(sampleAttrDTO);
                        }
                        //设置主语言的值
                        for (DFFAttrBO dffAttrBO : rdProductAttrList) {
                            List<DFFAttrLanguageBO> languageList = dffAttrBO.getLanguageList();
                            if(Func.isNotEmpty(languageList)){
                                DFFAttrLanguageBO primaryLanguageDFFBO = languageList.stream().filter(item -> Func.equalsSafe(primaryLanguageId,item.getLanguageId())).findAny().orElse(null);
                                if(Func.isNotEmpty(primaryLanguageDFFBO) && Func.isNotEmpty(primaryLanguageDFFBO.getValue())){
                                    dffAttrBO.setValue(primaryLanguageDFFBO.getValue());
                                }else{
                                    DFFAttrLanguageBO otherLanguageDFFBO = languageList.stream().filter(item -> !Func.equalsSafe(primaryLanguageId,item.getLanguageId())).findAny().orElse(null);
                                    if(Func.isNotEmpty(otherLanguageDFFBO) && Func.isNotEmpty(otherLanguageDFFBO.getValue())){
                                        dffAttrBO.setValue(otherLanguageDFFBO.getValue());
                                    }
                                }
                            }
                        }
                    }
                    rdProductDTO.setProductAttrList(rdProductAttrList);
                    rdProductList.add(rdProductDTO);
                }
            }
        }
        return rdProductList;
    }
}
