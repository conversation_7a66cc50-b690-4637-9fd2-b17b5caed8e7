package com.sgs.gpo.domain.service.otsnotes.report.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportFileHistoryPO;
import com.sgs.gpo.facade.model.report.req.ReportFileQueryReq;

import java.util.List;

/**
 */
public interface IReportFileHistoryService extends IService<ReportFileHistoryPO> {

    List<ReportFileHistoryPO> query(ReportFileQueryReq req);

}
