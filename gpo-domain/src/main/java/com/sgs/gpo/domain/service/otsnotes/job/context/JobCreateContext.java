package com.sgs.gpo.domain.service.otsnotes.job.context;

import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.order.order.OrderBO;
import com.sgs.framework.model.test.execution.JobBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.test.testline.TestLineExecutionBO;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.job.JobPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.job.JobTestLinePO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineExpectDueDateReq;
import com.sgs.gpo.integration.digital.rsp.CheckPackageTemplateRsp;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/7/3 09:02
 */
@Data
public class JobCreateContext<Input> extends BaseContext<Input, JobBO> {
    private Set<String> testLineInstanceIdListReq;
    private UserInfo userInfo;
    private OrderBO order;
    private String notesOrderId;
    private List<TestLineBO> testLineList;
    private String testLineGroupModel = Constants.OBJECT.JOB.TESTLINE_GROUP_MODE.LAB_SECTION;


    // Temp 相关属性
    private List<TestLineBO> unExecutionTestLineList;
    private List<JobPO> newStatusJobList;
    /**
     * Key:LabSectionBaseId / orderId,Value:JobNo
     */
    private Map<Object, String> testLineGroupMap;
    private Map<Object, String> newJobNoMap;
    private List<TestLineExecutionBO> existNewExecutionJobList;

    // DB 相关属性
    private List<JobPO> saveJobList;
    private List<JobTestLinePO> saveJobTestLineList;
    private List<TestLineExpectDueDateReq> updateTestLineExpectDueDateList;
    private Set<Object> needCreateJobGroupKeyList;

    //pp校验
    private List<CheckPackageTemplateRsp> checkPackageTemplateRspList;
}
