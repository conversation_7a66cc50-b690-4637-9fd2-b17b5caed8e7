package com.sgs.gpo.domain.service.preorder.enquiry.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.beust.jcommander.internal.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.PageBO;
import com.sgs.framework.model.order.enquiry.EnquiryBO;
import com.sgs.framework.model.order.enquiry.EnquiryIdBO;
import com.sgs.framework.open.platform.base.service.impl.AbstractBaseService;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.enquiry.EnquiryMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryPO;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryService;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/7 12:00
 */
@Service
@Slf4j
public class EnquiryServiceImpl extends
        AbstractBaseService<EnquiryBO, EnquiryPO, EnquiryIdBO, EnquiryMapper, EnquiryQueryReq> implements IEnquiryService {
    @Override
    public BaseResponse<List<EnquiryPO>> select(EnquiryIdReq enquiryIdReq) {
        if (Func.isEmpty(enquiryIdReq)) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        if (Func.isEmpty(enquiryIdReq.getEnquiryIdList()) && Func.isEmpty(enquiryIdReq.getEnquiryNoList())) {
            return BaseResponse.newFailInstance("common.param.miss", new Object[]{"EnquiryId/EnquiryNo"});
        }
        LambdaQueryWrapper<EnquiryPO> wrapper = Wrappers.lambdaQuery();
        if (Func.isNotEmpty(enquiryIdReq.getEnquiryIdList())) {
            wrapper.in(EnquiryPO::getId, enquiryIdReq.getEnquiryIdList());
        } else {
            wrapper.in(EnquiryPO::getEnquiryNo, enquiryIdReq.getEnquiryNoList());
        }
        return BaseResponse.newSuccessInstance(baseMapper.selectList(wrapper));
    }

    @Override
    public List<EnquiryPO> query(EnquiryQueryReq queryReq){
        if (Func.isEmpty(queryReq)) {
            return Lists.newArrayList();
        }
        if (Func.isEmpty(queryReq.getEnquiryIdList()) && Func.isEmpty(queryReq.getEnquiryNoList())) {
            return  Lists.newArrayList();
        }
        LambdaQueryWrapper<EnquiryPO> wrapper = Wrappers.lambdaQuery();
        if (Func.isNotEmpty(queryReq.getEnquiryIdList())) {
            wrapper.in(EnquiryPO::getId, queryReq.getEnquiryIdList());
        }
        if (Func.isNotEmpty(queryReq.getEnquiryNoList())) {
            wrapper.in(EnquiryPO::getEnquiryNo, queryReq.getEnquiryNoList());
        }
        return baseMapper.selectList(wrapper);
    }


    @Override
    public PageBO<EnquiryPO> page(EnquiryQueryReq enquiryQueryReq, Integer page, Integer rows) {
        IPage<EnquiryPO> iPage = baseMapper.selectPage(new Page(page, rows), enquiryQueryReq);
        PageBO<EnquiryPO> enquiryPOPage = Func.copy(iPage, PageBO.class);
        return enquiryPOPage;
    }




    @Override
    public List<EnquiryBO> convertToBO(Collection<EnquiryPO> poList) {
        return null;
    }

    @Override
    public List<EnquiryPO> convertToPO(Collection<EnquiryBO> boList) {
        return null;
    }

    @Override
    public LambdaQueryWrapper<EnquiryPO> createWrapper(EnquiryQueryReq queryReq) {
        return null;
    }
}
