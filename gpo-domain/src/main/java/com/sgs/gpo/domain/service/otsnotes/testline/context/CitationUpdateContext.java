package com.sgs.gpo.domain.service.otsnotes.testline.context;

import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.test.testline.v2.TestLineBO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testline.TestLineInstancePO;
import com.sgs.trimslocal.facade.model.testline.rsp.GetCitationBaseInfoRsp;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title: CitationUpdateContext
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/11/28 17:00
 */
@Data
public class CitationUpdateContext<Input> extends BaseContext<Input, TestLineBO> {
    private List<GetCitationBaseInfoRsp> citationBaseInfoRspList;
    private List<TestLineInstancePO> testLineInstancePOList;
}
