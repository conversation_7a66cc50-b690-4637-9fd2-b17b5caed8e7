package com.sgs.gpo.domain.service.dashboard.command;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.util.StringPool;
import com.sgs.framework.model.common.lab.LabBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.domain.service.dashboard.context.DashboardContext;
import com.sgs.gpo.dbstorages.mybatis.mapper.dashboard.DashboardMapper;
import com.sgs.gpo.domain.service.common.lab.ILabService;
import com.sgs.gpo.facade.model.dashboard.rsp.MatrixFuturePlanItem;
import com.sgs.gpo.facade.model.dashboard.req.DashBoardQueryRep;
import com.sgs.gpo.facade.model.dashboard.rsp.MatrixFuturePlanRsp;
import com.sgs.gpo.facade.model.lab.req.LabReq;
import com.sgs.gpo.facade.model.trims.labsection.rsp.LabSectionRsp;
import com.sgs.gpo.integration.trims.TrimsClient;
import com.sgs.trimslocal.facade.model.labsection.rsp.GetLabSectionBaseInfoRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;

@Service
@Slf4j
@Scope(value = "prototype")
public class MatrixFuturePlanQueryCMD extends BaseCommand<DashboardContext> {
    @Autowired
    private DashboardMapper dashboardMapper;
    @Autowired
    private TrimsClient trimsClient;
    @Autowired
    private ILabService labService;

    @Override
    public BaseResponse validateParam(DashboardContext context) {
        // VP1、校验入参不能为空
        if(Func.isEmpty(context)){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        DashBoardQueryRep dashBoardQueryRep = context.getParam();
        if(Func.isEmpty(dashBoardQueryRep)){
            dashBoardQueryRep = new DashBoardQueryRep();
        }
        // VP2、校验用户不能为空
        UserInfo userInfo = context.getUserInfo();
        if(Func.isEmpty(userInfo)){
            return BaseResponse.newFailInstance(ResponseCode.TokenExpire);
        }
        String labCode= userInfo.getCurrentLabCode();
        context.setLabCode(labCode);
        // 根据labCode获取labId
        LabReq labReq = new LabReq();
        labReq.setLabCode(labCode);
        BaseResponse<List<LabBO>> labListRes = labService.search(labReq);
        if(Func.isNotEmpty(labListRes) && Func.isNotEmpty(labListRes.getData())){
            context.setLabId(labListRes.getData().get(0).getLabId());
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(DashboardContext context) {
        // 初始化查询条件
        Date currentDate = new Date();
        DashBoardQueryRep dashBoardQueryRep = context.getParam();
        dashBoardQueryRep.setLabCode(context.getLabCode());
        dashBoardQueryRep.setCurrentDate(getDate(currentDate, LocalTime.MIN,1l));
        dashBoardQueryRep.setStartDate(getDate(currentDate, LocalTime.MIN,1l));
        dashBoardQueryRep.setEndDate(getDate(currentDate,LocalTime.MAX,7l));

        List<MatrixFuturePlanItem> matrixFuturePlanItems =dashboardMapper.selectMatrixFuturePlan(dashBoardQueryRep);
        MatrixFuturePlanRsp matrixFuturePlanRsp = new MatrixFuturePlanRsp();
        List<MatrixFuturePlanItem> result = Lists.newArrayList();
        if(Func.isNotEmpty(matrixFuturePlanItems)){
            matrixFuturePlanItems.forEach(matrixDashPoItem ->{
                if(matrixDashPoItem!=null){
                    if(StringPool.NULL.equals(matrixDashPoItem.getEngineer())||Func.isEmpty(matrixDashPoItem.getEngineer()))
                    {
                        matrixDashPoItem.setEngineer(StringPool.DASH);
                    }
                    matrixDashPoItem.setTotalQty(
                            Func.toInteger(matrixDashPoItem.getOneDayQty(),0) +
                            Func.toInteger(matrixDashPoItem.getTwoDayQty(),0) +
                            Func.toInteger(matrixDashPoItem.getThreeDayQty(),0) +
                            Func.toInteger(matrixDashPoItem.getFourDayQty(),0) +
                            Func.toInteger(matrixDashPoItem.getFiveDayQty(),0) +
                            Func.toInteger(matrixDashPoItem.getSixDayQty(),0) +
                            Func.toInteger(matrixDashPoItem.getSevenDayQty(),0)
                    );
                    result.add(matrixDashPoItem);
                }
            });

            Collections.sort(result, Comparator.comparing(MatrixFuturePlanItem::getTotalQty).reversed());
        }
        matrixFuturePlanRsp.setMatrixFuturePlanList(result);
        // 查询当前Lab下的labSection列表
        Set<Integer> labIds = Sets.newHashSet();
        labIds.add(context.getLabId());
        BaseResponse<List<GetLabSectionBaseInfoRsp>> trimsLabSectionRes = trimsClient.getLabSectionByLabId(labIds);
        if(Func.isNotEmpty(trimsLabSectionRes)&&Func.isNotEmpty(trimsLabSectionRes.getData())){
            List<LabSectionRsp> labSectionList = Func.copy(trimsLabSectionRes.getData(),GetLabSectionBaseInfoRsp.class,LabSectionRsp.class);
            matrixFuturePlanRsp.setLabSectionList(labSectionList);
        }
        return BaseResponse.newSuccessInstance(matrixFuturePlanRsp);
    }

    private Date getDate(Date date,LocalTime localTime,Long days){
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(localTime);
        if(days!=null){
            if(days>0){
                startOfDay = startOfDay.plusDays(days);
            }else if(days<0){
                startOfDay = startOfDay.minusDays(~(days));
            }
        }
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }
}
