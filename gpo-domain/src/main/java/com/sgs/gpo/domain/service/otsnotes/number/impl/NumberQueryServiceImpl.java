package com.sgs.gpo.domain.service.otsnotes.number.impl;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.domain.service.otsnotes.number.INumberQueryService;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.number.OtsNotesNumberMapper;
import com.sgs.gpo.facade.model.otsnotes.common.req.QueryOtsNotesNumberReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: NumberQueryServiceImpl
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/6 14:18
 */
@Service
public class NumberQueryServiceImpl implements INumberQueryService {
    @Autowired
    private OtsNotesNumberMapper otsNotesNumberMapper;

    @Override
    public BaseResponse<List<String>> queryOtsNotesNumberList(QueryOtsNotesNumberReq queryOtsNotesNumberReq) {
        queryOtsNotesNumberReq.setLabId(SystemContextHolder.getLab().getLabId());
        if (Func.isEmpty(queryOtsNotesNumberReq)) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        if (Func.isEmpty(queryOtsNotesNumberReq.getAttributeCode())) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"attributeCode"});
        }
        if (Func.isEmpty(queryOtsNotesNumberReq.getLabId())) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"labId"});
        }
        List<String> numberList = otsNotesNumberMapper.queryNumber(queryOtsNotesNumberReq);
        numberList = numberList.stream().filter(Func::isNotEmpty).collect(Collectors.toList());
        return BaseResponse.newSuccessInstance(numberList);
    }
}
