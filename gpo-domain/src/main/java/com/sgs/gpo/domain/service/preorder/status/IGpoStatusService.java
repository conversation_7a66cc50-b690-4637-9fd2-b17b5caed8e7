package com.sgs.gpo.domain.service.preorder.status;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.status.GpoStatusPO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;

import java.util.List;

/**
 * <AUTHOR>
 * @title: IGpnStatusService
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/26 16:35
 */
public interface IGpoStatusService extends IService<GpoStatusPO> {
    BaseResponse<List<GpoStatusPO>> queryOrderStatusByOrderNo(OrderIdReq orderIdReq);
}
