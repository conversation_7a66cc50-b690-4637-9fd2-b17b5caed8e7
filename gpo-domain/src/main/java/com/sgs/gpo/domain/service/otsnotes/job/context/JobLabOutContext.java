package com.sgs.gpo.domain.service.otsnotes.job.context;

import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.order.order.OrderBO;
import com.sgs.framework.model.test.execution.JobBO;
import com.sgs.gpo.facade.model.otsnotes.job.dto.JobTestLineDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/3 09:02
 */
@Data
public class JobLabOutContext<Input> extends BaseContext<Input, JobBO> {
    private List<OrderBO> orderBOList;
    private List<JobTestLineDTO> jobTestLineDTOList;
}
