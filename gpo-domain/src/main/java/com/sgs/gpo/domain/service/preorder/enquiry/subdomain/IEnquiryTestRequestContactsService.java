package com.sgs.gpo.domain.service.preorder.enquiry.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryTestRequestContactsPO;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;

import java.util.List;

public interface IEnquiryTestRequestContactsService extends IService<EnquiryTestRequestContactsPO> {

    List<EnquiryTestRequestContactsPO> select(EnquiryIdReq enquiryIdReq);
}
