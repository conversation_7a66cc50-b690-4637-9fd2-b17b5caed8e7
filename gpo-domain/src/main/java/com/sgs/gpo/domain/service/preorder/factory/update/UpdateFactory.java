package com.sgs.gpo.domain.service.preorder.factory.update;

import com.google.common.collect.Maps;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.annotation.Section;
import com.sgs.gpo.domain.service.preorder.factory.update.context.UpdateContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.SortedMap;

@Component
@Slf4j
public class UpdateFactory implements InitializingBean {

    @Autowired
    private List<UpdateBaseService> updateBaseServiceList;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Override
    public void afterPropertiesSet() {

    }
    public BaseResponse update(UpdateContext updateContext){
        if (Func.isEmpty(updateBaseServiceList)) {
            return BaseResponse.newFailInstance("common.empty",null);
        }
        List<String> sectionList = updateContext.getSectionList();
        String objectType = updateContext.getObjectType();
        if(Func.isEmpty(objectType)){
            return BaseResponse.newFailInstance("common.empty",new Object[]{"ObjectType"});
        }
        if(Func.isEmpty(sectionList)){
            return BaseResponse.newFailInstance("common.empty",new Object[]{"sectionList"});
        }
        // 待执行的Service
        SortedMap<Integer, UpdateBaseService> exeServiceMap = Maps.newTreeMap();
        // 按照order排序,根据PrintType过滤需要执行的Service
        for (UpdateBaseService updateBaseService : updateBaseServiceList) {
            Class<?> targetClass = AopUtils.getTargetClass(updateBaseService);

            Section section = AnnotationUtils.getAnnotation(targetClass, Section.class);
            if (section == null) {
                continue;
            }
            String attribute = section.attribute();
            if (Func.isEmpty(attribute) || !sectionList.contains(attribute)) {
                continue;
            }
            Integer order = section.order();
            exeServiceMap.put(order, updateBaseService);
        }
        if (Func.isEmpty(exeServiceMap)) {
            return BaseResponse.newSuccessInstance(true);
        }
        // 执行Service Update
        boolean flag = Boolean.TRUE.equals(transactionTemplate.execute((trans) -> {
            exeServiceMap.forEach((key, service) -> {
                service.update(updateContext);
            });
            return true;
        }));
        return BaseResponse.newSuccessInstance(true);
    }
}
