package com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testmatrix.TestMatrixExtMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.TestMatrixExtPO;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixExtService;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixExtQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service
@Slf4j
public class TestMatrixExtServiceImpl extends ServiceImpl<TestMatrixExtMapper, TestMatrixExtPO> implements ITestMatrixExtService {

    @Override
    public List<TestMatrixExtPO> query(TestMatrixExtQueryReq testMatrixExtQueryReq){
        LambdaQueryWrapper<TestMatrixExtPO> queryWrapper = new LambdaQueryWrapper<>();
        if(Func.isEmpty(testMatrixExtQueryReq) || Func.isEmpty(testMatrixExtQueryReq.getTestMatrixIdList())){
            return new ArrayList<>();
        }
        if(Func.isNotEmpty(testMatrixExtQueryReq.getTestMatrixIdList())){
            queryWrapper.in(TestMatrixExtPO::getTestMatrixId,testMatrixExtQueryReq.getTestMatrixIdList());
        }
        return baseMapper.selectList(queryWrapper);

    }
}
