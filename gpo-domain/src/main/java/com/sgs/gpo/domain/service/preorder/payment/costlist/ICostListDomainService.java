package com.sgs.gpo.domain.service.preorder.payment.costlist;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.facade.model.payment.costlist.bo.CostListBO;
import com.sgs.gpo.facade.model.payment.costlist.req.CostListQueryReq;

/**
 * <AUTHOR>
 * @title: ICostListService
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/6/1515:09
 */
public interface ICostListDomainService {
    BaseResponse<IPage<CostListBO>> page(CostListQueryReq costListQueryReq, Integer page, Integer rows);

}
