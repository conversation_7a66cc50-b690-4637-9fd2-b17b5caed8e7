package com.sgs.gpo.domain.service.otsnotes.execution;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.execution.ExecutionResubmissionRecordPO;
import com.sgs.gpo.facade.model.otsnotes.execution.req.ResubmissionQueryReq;
import com.sgs.gpo.facade.model.otsnotes.execution.req.ResubmissionUpdateReq;

import java.util.List;

public interface IExecutionResubmissionService extends IService<ExecutionResubmissionRecordPO> {

    BaseResponse<Boolean> batchUpdateStatusById(ResubmissionUpdateReq resubmissionUpdateReq);

    BaseResponse<List<ExecutionResubmissionRecordPO>> select(ResubmissionQueryReq resubmissionQueryReq);
}
