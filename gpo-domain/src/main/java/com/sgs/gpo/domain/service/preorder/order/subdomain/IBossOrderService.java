package com.sgs.gpo.domain.service.preorder.order.subdomain;


import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.payment.BossOrderInvoiceDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
public interface IBossOrderService {

    Boolean getBossOrderByTrueOrderNo(String trueOrderNo);
    GeneralOrderPO getOrderByBossOrderNo(@Param("bossOrderNo") String bossOrderNo);
    List<BossOrderInvoiceDTO> getBossOrderNoByOrderNo(String orderNo);
}
