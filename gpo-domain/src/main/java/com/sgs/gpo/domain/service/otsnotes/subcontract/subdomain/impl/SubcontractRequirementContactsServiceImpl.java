package com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.subcontract.SubcontractRequirementContactsMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractRequirementContactsPO;
import com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.ISubcontractRequirementContactsService;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractRequirementContactsQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class SubcontractRequirementContactsServiceImpl extends ServiceImpl<SubcontractRequirementContactsMapper, SubcontractRequirementContactsPO> implements ISubcontractRequirementContactsService {
    @Override
    public BaseResponse<List<SubcontractRequirementContactsPO>> query(SubcontractRequirementContactsQueryReq req) {
        if(Func.isEmpty(req)||Func.isEmpty(req.getSubContractIds())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        LambdaQueryWrapper<SubcontractRequirementContactsPO> wrapper= Wrappers.<SubcontractRequirementContactsPO>lambdaQuery()
                .in(SubcontractRequirementContactsPO::getSubcontractId,req.getSubContractIds());
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }
}
