package com.sgs.gpo.domain.service.preorder.status.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.status.GpoStatusMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.status.GpoStatusPO;
import com.sgs.gpo.domain.service.preorder.status.IGpoStatusService;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import com.sgs.preorder.facade.model.enums.ObjectType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @title: GpnStatusServiceImpl
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/26 16:36
 */
@Service
@Slf4j
public class GpoStatusServiceImpl extends ServiceImpl<GpoStatusMapper, GpoStatusPO> implements IGpoStatusService {

    @Override
    public BaseResponse<List<GpoStatusPO>> queryOrderStatusByOrderNo(OrderIdReq orderIdReq) {
        if(Func.isEmpty(orderIdReq) || Func.isEmpty(orderIdReq.getOrderNoList())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        QueryWrapper<GpoStatusPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(GpoStatusPO.COLUMN.OBJECT_NO,orderIdReq.getOrderNoList());
        queryWrapper.eq(GpoStatusPO.COLUMN.OBJECT_TYPE, ObjectType.Order.getCode());
        List<GpoStatusPO> gpoStatusPOS = baseMapper.selectList(queryWrapper);
        return BaseResponse.newSuccessInstance(gpoStatusPOS);
    }
}
