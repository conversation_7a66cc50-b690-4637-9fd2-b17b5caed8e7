package com.sgs.gpo.domain.service.preorder.trf.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.trf.TrfToDoMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.trf.TrfToDoPO;
import com.sgs.gpo.domain.service.preorder.trf.ITrfToDoService;
import com.sgs.gpo.facade.model.preorder.trf.TrfToDoListReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class TrfToDoServiceImpl extends ServiceImpl<TrfToDoMapper, TrfToDoPO> implements ITrfToDoService {
    @Override
    public List<TrfToDoPO> select(TrfToDoListReq trfToDoListReq) {
        if (Func.isEmpty(trfToDoListReq) || Func.isEmpty(trfToDoListReq.getTrfNoList())) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<TrfToDoPO> wrapper = Wrappers.lambdaQuery();
        wrapper.in(TrfToDoPO::getTrfNo, trfToDoListReq.getTrfNoList());
        if(Func.isNotEmpty(trfToDoListReq.getStatus())){
            wrapper.eq(TrfToDoPO::getStatus,trfToDoListReq.getStatus());
        }
        return baseMapper.selectList(wrapper);
    }
}
