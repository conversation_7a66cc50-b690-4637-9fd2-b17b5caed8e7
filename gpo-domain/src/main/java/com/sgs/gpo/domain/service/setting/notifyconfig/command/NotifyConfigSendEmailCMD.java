package com.sgs.gpo.domain.service.setting.notifyconfig.command;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.StringPool;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.notify.NotifyLogPO;
import com.sgs.gpo.dbstorages.mybatis.model.notify.NotifyConfigPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.job.JobPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.domain.service.setting.notifyconfig.INotifyConfigProcessService;
import com.sgs.gpo.domain.service.setting.notifyconfig.context.NotifyConfigContext;
import com.sgs.gpo.domain.service.setting.notifyconfig.subdomain.INotifyConfigService;
import com.sgs.gpo.domain.service.setting.notifyconfig.subdomain.INotifyLogService;
import com.sgs.gpo.domain.service.otsnotes.job.subdomain.IJobService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.setting.buparam.IBUParam;
import com.sgs.gpo.facade.model.buparam.PendingNotifyConfigRsp;
import com.sgs.gpo.facade.model.job.req.JobQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.gpo.integration.framework.FrameworkClient;
import com.sgs.gpo.integration.framework.NotificationClient;
import com.sgs.otsnotes.facade.model.enums.JobStatus;
import com.sgs.otsnotes.facade.model.enums.ReportStatus;
import com.sgs.preorder.facade.model.dto.holiday.HolidayDTO;
import com.sgs.preorder.facade.model.info.HolidayDateInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class NotifyConfigSendEmailCMD extends BaseCommand<NotifyConfigContext<String>>{
    @Autowired
    private INotifyConfigService  notifyConfigService;
    @Autowired
    private INotifyLogService notifyLogService;
    @Autowired
    private INotifyConfigProcessService notifyConfigProcessService;
    @Autowired
    private FrameworkClient frameworkClient;
    @Autowired
    private IReportService reportService;
    @Autowired
    private IJobService jobService;
    @Resource
    private NotificationClient notificationClient;
    @Autowired
    private IBUParam buParam;

    @Override
    public BaseResponse validateParam(NotifyConfigContext<String> context) {
        Assert.isTrue(Func.isNotEmpty(context.getParam()), "common.miss", new Object[]{"param"});
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    @Transactional
    @DS(Constants.DB.COMMON)
    public BaseResponse execute(NotifyConfigContext<String> context) {
        List<NotifyConfigPO> notifyConfigPOList = context.getNotifyConfigPOList();
        if(Func.isNotEmpty(notifyConfigPOList)) {
            notifyConfigService.updateBatchById(notifyConfigPOList);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(NotifyConfigContext<String> context) {
        String productLineCode = context.getProductLineCode();
        Date currDate = new Date();
        //查询类型，下次通知时间小于当前时间，待运行的tb_object_notification_config
        LambdaQueryWrapper<NotifyConfigPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NotifyConfigPO::getActiveIndicator, ActiveType.Enable.getStatus());
        wrapper.eq(NotifyConfigPO::getBuCode, productLineCode);
        wrapper.in(NotifyConfigPO::getObjectType, Lists.newArrayList(Constants.BUSINESS_TYPE.JOB,Constants.BUSINESS_TYPE.REPORT));
        wrapper.in(NotifyConfigPO::getEventType, Lists.newArrayList(ReportStatus.Pending.getMessage(), JobStatus.PENDING.getMessage()));
        wrapper.le(NotifyConfigPO::getNextNotifyDate, currDate);
        List<NotifyConfigPO> notifyConfigPOList = notifyConfigService.list(wrapper);
        if (Func.isEmpty(notifyConfigPOList)){
            return BaseResponse.newSuccessInstance(true);
        }

        //存放No与Status  键值对
        Map<String,Integer> map = new HashMap<>();
        //根据对象类型 查询report
        Set<String> reportNos = notifyConfigPOList.stream().filter(e -> Func.equalsSafe(e.getObjectType(), Constants.BUSINESS_TYPE.REPORT)).map(NotifyConfigPO::getObjectNo).collect(Collectors.toSet());
        ReportQueryReq reportQueryReq = new ReportQueryReq();
        reportQueryReq.setReportNoList(reportNos);
        BaseResponse<List<ReportPO>> reportPOListRes = reportService.select(reportQueryReq);
        if (reportPOListRes.isSuccess() && Func.isNotEmpty(reportPOListRes.getData())){
            map.putAll(reportPOListRes.getData().stream().collect(Collectors.toMap(ReportPO::getReportNo, ReportPO::getReportStatus)));
        }

        //查询job
        Set<String> jobNos = notifyConfigPOList.stream().filter(e -> Func.equalsSafe(e.getObjectType(), Constants.BUSINESS_TYPE.JOB)).map(NotifyConfigPO::getObjectNo).collect(Collectors.toSet());
        JobQueryReq jobQueryReq = new JobQueryReq();
        jobQueryReq.setJobNoList(jobNos);
        BaseResponse<List<JobPO>> oldJobListRes = jobService.select(jobQueryReq);
        if (oldJobListRes.isSuccess() && Func.isNotEmpty(oldJobListRes.getData())){
            map.putAll(oldJobListRes.getData().stream().collect(Collectors.toMap(JobPO::getJobNo, JobPO::getJobStatus)));
        }

        //存放buId-locationId 键 对应的节假日
        Map<String,List<Date>> holidayMap = notifyConfigPOList.stream().collect(Collectors.toMap(po->po.getBuId() + StringPool.DASH + po.getLocationId(), po -> new ArrayList<>(), (existing, replacement) -> existing));
        for (Map.Entry<String, List<Date>> entry : holidayMap.entrySet()) {
            String key = entry.getKey();  // 获取 buId-locationId 键
            String buId = key.split(StringPool.DASH)[0];
            String locationId = key.split(StringPool.DASH)[1];
            //获取节假日
            BaseResponse<String> holidayIdRes = frameworkClient.queryHolidayId(Func.toStr(buId), Func.toStr(locationId));
            if (holidayIdRes.isFail() || Func.isEmpty(holidayIdRes.getData())) {
                continue;
            }
            // 根据holidayId查询holidayDetail
            String holidayId = holidayIdRes.getData();
            List<HolidayDateInfo> holidayDateList = Lists.newArrayList();
            String[] holidayIdArray = holidayId.split(StringPool.COMMA);
            for(String id : holidayIdArray){
                BaseResponse<HolidayDTO> holidayRes = frameworkClient.queryHolidayDetail(id);
                if (holidayRes.isSuccess() && Func.isNotEmpty(holidayRes.getData())) {
                    holidayDateList.addAll(holidayRes.getData().getHolidayDateList());
                }
            }
            holidayDateList = holidayDateList.stream().sorted(Comparator.comparing(HolidayDateInfo::getHolidayDate)).collect(Collectors.toList());
            entry.setValue(holidayDateList.stream().map(HolidayDateInfo::getHolidayDate).collect(Collectors.toList()));
        }

        //查询Bu配置，如果未配置(已修改配置)，更新运行状态
        notifyConfigPOList.forEach(item->{
            item.setModifiedDate(currDate);
            //查询bu配置
            BaseResponse<PendingNotifyConfigRsp> notifyConfigParamRes = buParam.getNotifyConfig(productLineCode, item.getLocationCode());
            if (notifyConfigParamRes.isFail()){
                item.setActiveIndicator(ActiveType.Disable.getStatus());
                item.setRemark("Bu param invalid");
                return;
            }
            PendingNotifyConfigRsp notifyConfigParam = notifyConfigParamRes.getData();
            //没有查询到配置或者Report/Job表没查询到数据，只更新状态
            if(Func.isEmpty(notifyConfigParam) || !notifyConfigParam.getIsNotify() || Func.equalsSafe(notifyConfigParam.getIntervalHours(),0) || !map.containsKey(item.getObjectNo())){
                item.setActiveIndicator(ActiveType.Disable.getStatus());
                item.setRemark("Bu param invalid");
                return;
            }
            //如果是Report,非pending状态的，更新状态
            if (Func.equalsSafe(item.getObjectType(), Constants.BUSINESS_TYPE.REPORT) && !ReportStatus.check(map.get(item.getObjectNo()), ReportStatus.Pending)){
                item.setActiveIndicator(ActiveType.Disable.getStatus());
                item.setRemark("Report status: " + map.get(item.getObjectNo()));
                return;
            }
            //如果是Job,非pending状态的，更新状态
            if (Func.equalsSafe(item.getObjectType(), Constants.BUSINESS_TYPE.JOB) && !JobStatus.check(map.get(item.getObjectNo()), JobStatus.PENDING)){
                item.setActiveIndicator(ActiveType.Disable.getStatus());
                item.setRemark("Job status: " + map.get(item.getObjectNo()));
                return;
            }
            //计算下次发送时间，排除节假日
            Integer intervalHours = notifyConfigParam.getIntervalHours();
            List<Date> holidayDateInfos = holidayMap.get(item.getBuId() + StringPool.DASH + item.getLocationId());
            if (Func.isEmpty(holidayDateInfos)){
                item.setActiveIndicator(ActiveType.Disable.getStatus());
                item.setRemark("Holiday not found");
                return;
            }
            Date nextNotifyDate = notifyConfigProcessService.getNextNotifyDate(item.getNextNotifyDate(),intervalHours,holidayDateInfos).getData();
            item.setNextNotifyDate(nextNotifyDate);
            item.setNotifyCount(item.getNotifyCount() + 1);
        });
        context.setNotifyConfigPOList(notifyConfigPOList);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    @DS(Constants.DB.COMMON)
    public BaseResponse after(NotifyConfigContext<String> context) {
        List<NotifyConfigPO> notifyConfigPOList = context.getNotifyConfigPOList();
        if (Func.isEmpty(notifyConfigPOList)){
            return BaseResponse.newSuccessInstance(true);
        }
        Date currDate = new Date();
        List<NotifyLogPO> notifyConfigLogPOList = new ArrayList<>();
        //调用邮件重发
        List<NotifyConfigPO> sendEmailNotifyConfigList = notifyConfigPOList.stream().filter(item -> Func.equalsSafe(item.getActiveIndicator(), ActiveType.Enable.getStatus())).collect(Collectors.toList());
        sendEmailNotifyConfigList.forEach(item -> {
            String emailSendResult = notificationClient.sendMailForGeneral(item.getNotifyParams());
            NotifyLogPO notifyConfigLogPO = new NotifyLogPO();
            notifyConfigLogPO.setId(IdUtil.uuId());
            notifyConfigLogPO.setNotifyId(item.getId());
            notifyConfigLogPO.setObjectNo(item.getObjectNo());
            notifyConfigLogPO.setRequestBody(item.getNotifyParams());
            notifyConfigLogPO.setResponseBody(emailSendResult);
            notifyConfigLogPO.setNotifyTime(currDate);
            notifyConfigLogPO.setCreatedDate(currDate);
            notifyConfigLogPO.setModifiedDate(currDate);
            notifyConfigLogPOList.add(notifyConfigLogPO);
        });
        //插入到日志表tb_object_notify_log
        notifyLogService.saveBatch(notifyConfigLogPOList);
        return BaseResponse.newSuccessInstance(true);
    }
}
