package com.sgs.gpo.domain.service.otsnotes.report.command;

import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.customer.CustomerPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.domain.service.otsnotes.report.context.ReportContext;
import com.sgs.gpo.domain.service.preorder.customer.subdomain.ICustomerService;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IGeneralOrderService;
import com.sgs.gpo.domain.service.setting.buparam.IBUParam;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportWorkFlowReq;
import com.sgs.gpo.integration.framework.rsp.ReportWorkflowRsp;
import com.sgs.otsnotes.facade.model.enums.ReportWorkFlow;
import com.sgs.priceengine.facade.model.enums.CustomerUsage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 查询报告对应的WorkFlow
 */
@Service
@Slf4j
public class ReportWorkFlowQueryCMD extends BaseCommand<ReportContext<ReportWorkFlowReq>> {

    @Autowired
    private IBUParam buParamClient;
    @Autowired
    private IGeneralOrderService generalOrderService;
    @Autowired
    private ICustomerService customerService;


    @Override
    public BaseResponse validateParam(ReportContext<ReportWorkFlowReq> context) {
        ReportWorkFlowReq reportWorkFlowReq = context.getParam();
        // 校验OrderNo不能为空
        if(Func.isEmpty(reportWorkFlowReq.getOrderNo())) {
            return BaseResponse.newFailInstance("common.miss", new Object[]{"OrderNo"});
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(ReportContext<ReportWorkFlowReq> context) {
        String orderNo = context.getParam().getOrderNo();
        String buCode = context.getProductLineCode();
        Integer workFlow = ReportWorkFlow.None.getType();

        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setOrderNoList(Sets.newHashSet(orderNo));
        BaseResponse<List<GeneralOrderPO>> orderRes = generalOrderService.query2(orderQueryReq);
        GeneralOrderPO generalOrderPO = orderRes.getData().get(0);
        ReportWorkflowRsp buReportWorkflowDTO =  buParamClient.getReportWorkFlow(buCode,generalOrderPO.getLocationCode()).getData();
        if (Func.isEmpty(buReportWorkflowDTO)){
            return BaseResponse.newFailInstance("未查询出配置");
        }
        if (buReportWorkflowDTO.getTypingFinish()){
            workFlow = workFlow | ReportWorkFlow.TypingFinish.getType();
        }
        if (buReportWorkflowDTO.getReview()){
            workFlow = workFlow | ReportWorkFlow.Review.getType();
        }
        if (buReportWorkflowDTO.getConfirm()){
            workFlow = workFlow | ReportWorkFlow.Confirm.getType();
        }
        if (Func.isNotEmpty(buReportWorkflowDTO.getHostReview()) && Func.isNotEmpty(buReportWorkflowDTO.getHostReview().getFromProductLineCode())){
            String fromProductLineCode = buReportWorkflowDTO.getHostReview().getFromProductLineCode();
            String [] fromProductLineCodes = fromProductLineCode.split(",");
            if (fromProductLineCodes.length > 0){
                //查询订单
                OrderIdReq orderIdReq = new OrderIdReq();
                orderIdReq.setOrderIdList(Sets.newHashSet(generalOrderPO.getId()));
                BaseResponse<List<CustomerPO>> baseResponse = customerService.select(orderIdReq);
                //判断是否有oldOrder
                if (Func.isNotEmpty(baseResponse) && Func.isNotEmpty(baseResponse.getData())){
                    CustomerPO customerInstanceDTO = baseResponse.getData().stream().filter(e->CustomerUsage.check(e.getCustomerUsage(),CustomerUsage.SUBCONTRACTFROM)).findFirst().orElse(null);
                    if (Func.isNotEmpty(customerInstanceDTO)){
                        String[] split = customerInstanceDTO.getCustomerId().split(" ");
                        if (split.length == 2){
                            String subcontractFromBu = split[1];
                            for (String fromBuCode : fromProductLineCodes){
                                if (Func.equals(fromBuCode,subcontractFromBu)){
                                    workFlow = workFlow | ReportWorkFlow.HostReview.getType();
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
        return BaseResponse.newSuccessInstance(workFlow);
    }

    @Override
    public BaseResponse after(ReportContext<ReportWorkFlowReq> context) {
        return BaseResponse.newSuccessInstance(true);
    }

}
