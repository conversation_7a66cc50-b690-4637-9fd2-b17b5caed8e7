package com.sgs.gpo.domain.service.preorder.enquiry.impl;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.model.order.enquiry.EnquiryBO;
import com.sgs.framework.model.order.enquiry.EnquiryIdBO;
import com.sgs.framework.open.platform.base.service.impl.AbstractDomainService;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryCustomerPO;
import com.sgs.gpo.domain.service.preorder.enquiry.IEnquiryDomainService;
import com.sgs.gpo.domain.service.preorder.enquiry.command.EnquiryQueryCMD;
import com.sgs.gpo.domain.service.preorder.enquiry.context.EnquiryContext;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryProductService;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryService;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryCreateReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryCustomerReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryProductReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryQueryReq;
import com.sgs.gpo.facade.model.preorder.enquiry.rsp.EnquiryProductInstanceRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class EnquiryDomainServiceImpl extends AbstractDomainService<EnquiryBO, EnquiryIdBO, EnquiryQueryReq, IEnquiryService>
        implements IEnquiryDomainService {

    @Autowired
    private IEnquiryProductService enquiryProductService;

    @Override
    public BaseResponse<List<EnquiryCustomerPO>> queryCustomerList(EnquiryCustomerReq req) {
        return null;
    }

    @Override
    public BaseResponse<List<EnquiryProductInstanceRsp>> queryProductList(EnquiryProductReq req) {
        return BaseResponse.newSuccessInstance(enquiryProductService.queryEnquiryProductByDff(req));
    }

    @Override
    public BaseResponse createEnquiry(EnquiryCreateReq createReq) {
        return null;
    }

    @Override
    public BaseResponse<List<EnquiryBO>> queryBO(EnquiryQueryReq enquiryQueryReq) {
        EnquiryContext<EnquiryQueryReq> context = new EnquiryContext<>();
        context.setParam(enquiryQueryReq);
        context.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        context.setLab(SystemContextHolder.getLab());
        context.setToken(SystemContextHolder.getSgsToken());
        context.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        return BaseExecutor.start(EnquiryQueryCMD.class, context);
    }

}
