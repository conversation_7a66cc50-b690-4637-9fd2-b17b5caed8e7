package com.sgs.gpo.domain.service.otsnotes.testmatrix.impl;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.common.object.ObjectIdBO;
import com.sgs.framework.model.common.object.StatusControlBO;
import com.sgs.framework.model.common.object.process.ObjectProcessBO;
import com.sgs.framework.model.common.object.process.ObjectProcessHeaderBO;
import com.sgs.framework.model.enums.ReportEntryModeEnum;
import com.sgs.framework.model.test.testmatrix.TestMatrixBO;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixIdBO;
import com.sgs.framework.model.workflow.WorkflowRuleBO;
import com.sgs.framework.open.platform.model.req.ObjectStatusVerifyReq;
import com.sgs.framework.open.platform.model.req.StatusControlReq;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.MatrixActionEnums;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.order.GeneralOrderInstancePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testline.TestLineInstancePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.TestMatrixPO;
import com.sgs.gpo.domain.service.otsnotes.order.IGeneralOrderInstanceService;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.ITestLineService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.ITestMatrixProcessService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixService;
import com.sgs.gpo.domain.service.setting.object.IObjectProcessDomainService;
import com.sgs.gpo.domain.service.setting.workflowrule.IWorkflowRuleService;
import com.sgs.gpo.domain.service.setting.workflowrule.command.WorkflowRuleQueryCMD;
import com.sgs.gpo.domain.service.setting.workflowrule.context.WorkflowRuleContext;
import com.sgs.gpo.facade.model.otsnotes.common.rsp.StatusControlRsp;
import com.sgs.gpo.facade.model.otsnotes.order.OrderInstanceQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineUpdateStatusReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixProcessReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.gpo.facade.model.setting.object.submodel.process.req.ObjectProcessStatusReq;
import com.sgs.gpo.facade.model.workflow.req.WorkflowRuleReq;
import com.sgs.otsnotes.facade.TestLineFacade;
import com.sgs.otsnotes.facade.model.enums.MatrixStatus;
import com.sgs.otsnotes.facade.model.req.testLine.TestLineStatusUpdateReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: ITestMatrixProcessService
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/8/30 15:19
 */
@Service
@Slf4j
public class TestMatrixProcessServiceImpl implements ITestMatrixProcessService {
    @Autowired
    private ITestMatrixService testMatrixService;
    @Autowired
    private ITestLineService testLineService;
    @Autowired
    private IWorkflowRuleService workflowRuleService;
    @Autowired
    private TestLineFacade testLineFacade;
    @Autowired
    private IGeneralOrderInstanceService generalOrderInstanceService;
    @Autowired
    private IObjectProcessDomainService objectProcessDomainService;



    @Override
    public BaseResponse typing(TestMatrixProcessReq testMatrixProcessReq) {
        return processMatrix(testMatrixProcessReq);
    }

    @Override
    public BaseResponse enter(TestMatrixProcessReq testMatrixProcessReq) {
        return processMatrix(testMatrixProcessReq);
    }

    @Override
    public BaseResponse submit(TestMatrixProcessReq testMatrixProcessReq) {
        return processMatrix(testMatrixProcessReq);
    }

    @Override
    public BaseResponse complete(TestMatrixProcessReq testMatrixProcessReq) {
        return processMatrix(testMatrixProcessReq);
    }

    @Override
    public BaseResponse cancel(TestMatrixProcessReq testMatrixProcessReq) {
        return processMatrix(testMatrixProcessReq);
    }

    @Override
    public BaseResponse na(TestMatrixProcessReq testMatrixProcessReq) {
        return processMatrix(testMatrixProcessReq);
    }

    private BaseResponse processMatrix(TestMatrixProcessReq testMatrixProcessReq) {
        if(testMatrixProcessReq==null){
            return BaseResponse.newFailInstance("common.param.miss",new Object[]{Constants.TERM.REQUEST.getCode()});
        }
        StatusControlRsp statusControlRsp = new StatusControlRsp();
        String action = testMatrixProcessReq.getAction();
        //Protocol Data Entry . Save / Submit / Validate 时状态校验取消
        if(ReportEntryModeEnum.check(testMatrixProcessReq.getEntryMode(),ReportEntryModeEnum.PROTOCOL_DATA_ENTRY)
                && MatrixActionEnums.checkAction(action,MatrixActionEnums.Save,MatrixActionEnums.Submit,MatrixActionEnums.Validate)){
            statusControlRsp.setAllowObjectIdList(testMatrixProcessReq.getTestMatrixIdList());
        }else if(testMatrixProcessReq.isNeedStatusControl()){
            // Step 1: Check status control response
            BaseResponse<StatusControlRsp> statusControlRspBaseResponse = this.statusControl(testMatrixProcessReq);
            if (statusControlRspBaseResponse.isFail()) {
                return statusControlRspBaseResponse;
            }
            statusControlRsp = statusControlRspBaseResponse.getData();
            if (Func.isEmpty(statusControlRsp)) {
                return BaseResponse.newFailInstance("Status control response is empty");
            }
        }else{
            statusControlRsp.setAllowObjectIdList(testMatrixProcessReq.getTestMatrixIdList());
        }

        // Step 2: Update matrix status if allow object ID list is not empty
        Set<String> allowObjectIdList = statusControlRsp.getAllowObjectIdList();
        if (Func.isNotEmpty(allowObjectIdList)) {
            testMatrixProcessReq.setTestMatrixIdList(allowObjectIdList);
            BaseResponse<Boolean> updateStatusResponse = testMatrixService.updateMatrixStatus(testMatrixProcessReq);
            if (updateStatusResponse.isFail()) {
                return updateStatusResponse;
            }
        }
        // Step 3: Handle matrix status change
        if(testMatrixProcessReq.isTriggerTestLine()){
            BaseResponse matrixStatusChangeRsp = this.onMatrixStatusChange(testMatrixProcessReq);
            if (matrixStatusChangeRsp.isFail()) {
                log.error("Matrix {} Fail, {}, {}", action, matrixStatusChangeRsp.getMessage(), Func.toJson(testMatrixProcessReq));
                throw new BizException(matrixStatusChangeRsp.getMessage());
            }
        }
        // Step 4: Return success response
        return BaseResponse.newSuccessInstance(statusControlRsp);
    }


    private BaseResponse<StatusControlRsp> statusControl(TestMatrixProcessReq testMatrixProcessReq) {
        TestMatrixQueryReq testMatrixQueryReq = new TestMatrixQueryReq();
        testMatrixQueryReq.setTestMatrixIdList(testMatrixProcessReq.getTestMatrixIdList());
        List<TestMatrixPO> testMatrixPOList = testMatrixService.queryList(testMatrixQueryReq).getData();        //读取tb_workflow_rule，判断是否满足Status更新条件
        StatusControlRsp statusControlRsp = new StatusControlRsp();
        if (Func.isNotEmpty(testMatrixPOList)) {
            WorkflowRuleContext workflowRuleContext = new WorkflowRuleContext();
            WorkflowRuleReq workflowRuleReq = new WorkflowRuleReq();
            workflowRuleReq.setCurrentNode("");
            workflowRuleReq.setNextNode(Func.toStr(testMatrixProcessReq.getNewMatrixStatus()));
            workflowRuleReq.setProcessCode(Constants.OBJECT.MATRIX);
            workflowRuleReq.setAction(testMatrixProcessReq.getAction());
            workflowRuleReq.setLabCode(SystemContextHolder.getLabCode());
            workflowRuleContext.setParam(workflowRuleReq);
            BaseResponse<List<WorkflowRuleBO>> baseResponse = BaseExecutor.start(WorkflowRuleQueryCMD.class, workflowRuleContext);
            List<WorkflowRuleBO> workflowRuleBOList = baseResponse.getData();
            //过滤出允许进入下个节点的Matrix数据
            Set<String> currentNodeRuleList = workflowRuleBOList.parallelStream().map(WorkflowRuleBO::getCurrentNode).collect(Collectors.toSet());
            Set<String> allowNextTestMatrixList = testMatrixPOList.stream().filter(item -> currentNodeRuleList.contains(Func.toStr(item.getMatrixStatus()))).map(TestMatrixPO::getId).collect(Collectors.toSet());
            List<TestMatrixPO> notAllowNextList = testMatrixPOList.stream().filter(item -> !currentNodeRuleList.contains(Func.toStr(item.getMatrixStatus()))).collect(Collectors.toList());
            statusControlRsp.setAllowObjectIdList(allowNextTestMatrixList);
            Map<String, String> notAllowObjectIdMap = null;
            if (Func.isNotEmpty(notAllowNextList)) {
                notAllowObjectIdMap = new HashMap<>();
                for (TestMatrixPO testMatrixPO : notAllowNextList) {
                    notAllowObjectIdMap.put(
                            testMatrixPO.getId(),
                            String.format("Matrix Status is %s ,not Allow change to %s",
                                    Func.isNotEmpty(MatrixStatus.findStatus(testMatrixPO.getMatrixStatus())) ? MatrixStatus.findStatus(testMatrixPO.getMatrixStatus()).getMessage() : "",
                                    Func.isNotEmpty(MatrixStatus.findStatus(testMatrixProcessReq.getNewMatrixStatus())) ? MatrixStatus.findStatus(testMatrixProcessReq.getNewMatrixStatus()).getMessage() : ""));
                }
            }
            statusControlRsp.setNotAllowObjectIdMap(notAllowObjectIdMap);
        }
        return BaseResponse.newSuccessInstance(statusControlRsp);
    }

    private BaseResponse onMatrixStatusChange(TestMatrixProcessReq testMatrixProcessReq) {
        //查询Matrix关联的TL，再根据TL查询Matrix，如果全部的Matrix都进入到下一个状态，则更新TL的状态
        //回退：子对象有一个状态回退，父对象状态就回退
        TestMatrixQueryReq testMatrixQueryReq = new TestMatrixQueryReq();
        testMatrixQueryReq.setTestMatrixIdList(testMatrixProcessReq.getTestMatrixIdList());
        List<TestMatrixBO> testMatrixBOList = testMatrixService.queryV1(testMatrixQueryReq).getData();
        if (Func.isNotEmpty(testMatrixBOList)) {
            Set<String> testLineInstanceIdList = testMatrixBOList.stream().map(TestMatrixBO::getTestLineInstanceId).collect(Collectors.toSet());
            testMatrixQueryReq = new TestMatrixQueryReq();
            testMatrixQueryReq.setTestLineInstanceIdList(testLineInstanceIdList);
            List<TestMatrixPO> allTestMatrixList = testMatrixService.queryList(testMatrixQueryReq).getData();
            long count = -1;
            //回退
            if(Func.isNotEmpty(allTestMatrixList)){
                allTestMatrixList = allTestMatrixList.stream().filter(item->!MatrixStatus.check(item.getMatrixStatus(),MatrixStatus.Cancelled)).collect(Collectors.toList());
            }
            String action = testMatrixProcessReq.getAction();
            MatrixActionEnums matrixActionEnum = MatrixActionEnums.getAction(action);
            Set<String> orderNoList = new HashSet<>();
            BaseResponse<List<TestLineInstancePO>> queryTestLineBase = testLineService.queryTestLineBase(testLineInstanceIdList);
            if(queryTestLineBase.isSuccess()){
                List<TestLineInstancePO> testLineInstancePOList = queryTestLineBase.getData();
                BaseResponse resultResponse = BaseResponse.newSuccessInstance(true);
                switch (matrixActionEnum) {
                    case Submit:
                        //校验TL的StarDate是否有值
                        String collect = testLineInstancePOList.stream().filter(item -> Func.isEmpty(item.getTestStartDate())).map(TestLineInstancePO::getTestLineId).map(Func::toStr).collect(Collectors.joining(","));
                        if(Func.isNotEmpty(collect)){
                            resultResponse = BaseResponse.newFailInstance(String.format("[%s],%s", collect,"Test StartDate Can not be Empty."));
                        }
                        break;
                }
                if(resultResponse.isFail()){
                    return resultResponse;
                }
                Set<String> generalOrderInstanceIdList = testLineInstancePOList.stream().map(TestLineInstancePO::getGeneralOrderInstanceId).collect(Collectors.toSet());
                if(Func.isNotEmpty(generalOrderInstanceIdList)){
                    OrderInstanceQueryReq orderInstanceQueryReq = new OrderInstanceQueryReq();
                    orderInstanceQueryReq.setOrderIdList(generalOrderInstanceIdList);
                    BaseResponse<List<GeneralOrderInstancePO>> orderListResponse = generalOrderInstanceService.select(orderInstanceQueryReq);
                    if(orderListResponse.isSuccess()){
                        orderNoList = orderListResponse.getData().stream().map(GeneralOrderInstancePO::getOrderNo).collect(Collectors.toSet());
                    }
                }
            }else{
                return BaseResponse.newFailInstance("common.empty",new Object[]{"Test Line"});
            }
            //更新TL的状态:永远等于子对象状态最早的那个
            Set<Integer> allTestMatrixStatusList = allTestMatrixList.stream().map(TestMatrixPO::getMatrixStatus).filter(Func::isNotEmpty).distinct().collect(Collectors.toSet());
            Integer newMatrixStatus = testMatrixProcessReq.getNewMatrixStatus();
            List<ObjectProcessBO> objectProcessBOList = this.getProcess().getData();
            if (Func.isNotEmpty(objectProcessBOList) && Func.isNotEmpty(allTestMatrixStatusList)
            ) {
                ObjectProcessHeaderBO objectProcessHeaderBO = objectProcessBOList.stream().filter(item -> Func.isNoneEmpty(item.getHeader(), item.getHeader().getStatusId()) && allTestMatrixStatusList.contains(item.getHeader().getStatusId())).map(ObjectProcessBO::getHeader).min(Comparator.comparingInt(ObjectProcessHeaderBO::getSequence)).orElse(null);
                if (Func.isNotEmpty(objectProcessHeaderBO)) {
                    newMatrixStatus = objectProcessHeaderBO.getStatusId();
                }
            }

            TestLineUpdateStatusReq testLineUpdateStatusReq = new TestLineUpdateStatusReq();
            testLineUpdateStatusReq.setTestLineInstanceIdList(testLineInstanceIdList);
            testLineUpdateStatusReq.setTestLineStatus(newMatrixStatus);
            testLineUpdateStatusReq.setAction(action);
            testLineService.batchUpdateTestLineStatus(testLineUpdateStatusReq);

            //TL状态改变，联动其他对象改变
            if (Func.isNotEmpty(orderNoList)) {
                for (String orderNo : orderNoList) {
                    TestLineStatusUpdateReq testLineStatusUpdateReq = new TestLineStatusUpdateReq();
                    testLineStatusUpdateReq.setOrderNo(orderNo);
                    testLineStatusUpdateReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                    testLineStatusUpdateReq.setAction("dataEntry");
                    BaseResponse testLineChangeResponse = testLineFacade.onChange(testLineStatusUpdateReq);
                    if (testLineChangeResponse.isFail()) {
                        log.error("onMatrixStatusChange after TL Status Change error:{}", testLineChangeResponse.getMessage());
                    }
                }
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse<List<StatusControlBO<com.sgs.framework.model.test.testmatrix.v2.TestMatrixBO>>> statusControl(StatusControlReq<TestMatrixIdBO> statusControlReq) {
        return null;
    }

    @Override
    public BaseResponse<List<com.sgs.framework.model.test.testmatrix.v2.TestMatrixBO>> statusVerify(ObjectStatusVerifyReq<com.sgs.framework.model.test.testmatrix.v2.TestMatrixBO, TestMatrixIdBO> orderStatusVerifyReq) {
        return null;
    }

    @Override
    public BaseResponse<List<ObjectProcessBO>> getProcess() {
        ObjectProcessStatusReq orderObjectProcessStatusReq = new ObjectProcessStatusReq();
        orderObjectProcessStatusReq.setLabId(SystemContextHolder.getLab().getLabId());
        ObjectIdBO objectIdBO = new ObjectIdBO();
        objectIdBO.setObjectCode(Constants.OBJECT.TEST_MATRIX.OBJECT_CODE);
        orderObjectProcessStatusReq.setObject(objectIdBO);
        BaseResponse<List<ObjectProcessBO>> orderObjectProcess =objectProcessDomainService.queryStatus(orderObjectProcessStatusReq);
        return orderObjectProcess;
    }

    @Override
    public BaseResponse<List<com.sgs.framework.model.test.testmatrix.v2.TestMatrixBO>> queryStatus(List<TestMatrixIdBO> testMatrixIdBOS) {
        return null;
    }
}
