package com.sgs.gpo.domain.service.preorder.enquiry.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryTrfRelationshipPO;
import com.sgs.gpo.facade.model.preorder.enquiry.bo.EnquiryTrfBO;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryDelReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryTrfReq;

import java.util.List;

/**
 * <AUTHOR>
 * @title: IEnquiryTrfRelationshipService
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2024/1/16 22:32
 */
public interface IEnquiryTrfRelationshipService extends IService<EnquiryTrfRelationshipPO> {
    BaseResponse<List<EnquiryTrfRelationshipPO>> select(EnquiryIdReq enquiryIdReq);
    BaseResponse<List<EnquiryTrfBO>> queryEnquiryTrf(EnquiryTrfReq enquiryTrfReq);

    /**
     * 删除EnquiryTrf关系
     * @param enquiryDelReq
     * @return
     */
    Integer delete (EnquiryDelReq enquiryDelReq);

}
