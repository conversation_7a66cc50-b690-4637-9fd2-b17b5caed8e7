package com.sgs.gpo.domain.service.otsnotes.testmatrix.command;

import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.model.common.productsample.ProductLanguageBO;
import com.sgs.framework.model.common.productsample.ProductParallelBO;
import com.sgs.framework.model.enums.LanguageType;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.test.testmatrix.TestLineWithSampleBO;
import com.sgs.framework.model.test.testmatrix.TestMatrixBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.productinstance.ProductInstancePO;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineDomainService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.ITestMatrixDomainService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.context.TestMatrixContext;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixService;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.domain.service.preorder.productinstance.IProductInstanceService;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.preorder.productsample.req.ProductSampleQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/3 14:25
 */
@Service
@Slf4j
@Scope(value = "prototype")
public class TestLineWithSampleQueryCMD extends BaseCommand<TestMatrixContext<TestMatrixQueryReq, TestLineWithSampleBO>> {

    @Autowired
    private ITestMatrixService testMatrixService;
    @Autowired
    private ITestLineDomainService testLineDomainService;
    @Autowired
    private IOrderDomainService orderDomainService;
    @Autowired
    private IProductInstanceService productInstanceService;
    @Resource
    private ITestMatrixDomainService testMatrixDomainService;


    @Override
    public BaseResponse validateParam(TestMatrixContext<TestMatrixQueryReq, TestLineWithSampleBO> context) {
        // 校验入参不能为空
        if(Func.isEmpty(context)||Func.isEmpty(context.getParam())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(TestMatrixContext<TestMatrixQueryReq,TestLineWithSampleBO> context) {
        //1、查询TestMatrix列表
        BaseResponse<List<TestMatrixBO>> testMatrixResponse = testMatrixService.queryV1(context.getParam());
        List<TestMatrixBO> testMatrixList = testMatrixResponse.getData();
        if(Func.isEmpty(testMatrixList)){
            return BaseResponse.newSuccessInstance(testMatrixList);
        }
        //2、查询TestLine列表；
        Set<String> testLineInstanceIdList = testMatrixList.stream().map(TestMatrixBO::getTestLineInstanceId).collect(Collectors.toSet());
        OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
        orderTestLineReq.setTestLineInstanceIdList(testLineInstanceIdList);
        orderTestLineReq.setCrossDbQuery(true);
        BaseResponse<List<TestLineBO>> testLineResponse = testLineDomainService.queryPPTestLine(orderTestLineReq);
        List<TestLineBO> testLineList = testLineResponse.getData();
        if(Func.isEmpty(testLineList)){
            return BaseResponse.newSuccessInstance(testMatrixList);
        }
        //3、查询Sample列表
        List<ProductInstancePO> productInstanceList = null;
        Set<String> testSampleInstanceIdList = testMatrixList.stream().map(TestMatrixBO::getTestSampleInstanceId).collect(Collectors.toSet());
        ProductSampleQueryReq productSampleQueryReq = new ProductSampleQueryReq();
        productSampleQueryReq.setOrderIdList(testLineList.stream().map(TestLineBO::getPreOrderId).collect(Collectors.toSet()));
        productSampleQueryReq.setRefSampleIdList(testSampleInstanceIdList);
        BaseResponse<List<ProductInstancePO>> productInstanceRes = productInstanceService.queryOrderProductSample(productSampleQueryReq);
        if(productInstanceRes.isSuccess() && Func.isNotEmpty(productInstanceRes.getData())){
            productInstanceList = productInstanceRes.getData();
        }

        //  4 查询Order信息
        List<OrderBO> orderList = null;
        Set<String> orderIdList = testLineList.stream().map(TestLineBO::getPreOrderId).collect(Collectors.toSet());
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setOrderIdList(orderIdList);
        orderQueryReq.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        orderQueryReq.setBaseQuery(true);
        BaseResponse<List<OrderBO>> orderRes = orderDomainService.queryBO(orderQueryReq);
        if(orderRes.isSuccess() && Func.isNotEmpty(orderRes.getData())){
            orderList = orderRes.getData();
        }
        // 查询V2结构的matrix数据
        TestMatrixQueryReq testMatrixQueryReq = new TestMatrixQueryReq();
        testMatrixQueryReq.setTestMatrixIdList(testMatrixList.stream().map(TestMatrixBO::getTestMatrixId).collect(Collectors.toSet()));
        BaseResponse<List<com.sgs.framework.model.test.testmatrix.v2.TestMatrixBO>> testMatrixNewRes = testMatrixDomainService.queryBO(testMatrixQueryReq);
        if(testMatrixNewRes.isFail() || Func.isEmpty(testMatrixNewRes.getData())){
             return BaseResponse.newSuccessInstance(testMatrixList);
        }
        //5、TestLine 追加Sample信息
        List<TestLineWithSampleBO> testLineWithSampleList = Lists.newArrayList();
        for(TestLineBO testLine : testLineList){
            TestLineWithSampleBO testLineAndSampleList = new TestLineWithSampleBO();
            // testLine
            testLineAndSampleList.setTestLine(testLine);
            testLineWithSampleList.add(testLineAndSampleList);
            if(Func.isEmpty(orderList)){
                continue;
            }
            OrderBO testLineOrder = orderList.stream().filter(order -> order.getId().getOrderId().equals(testLine.getPreOrderId())).findFirst().orElse(null);
            if(Func.isEmpty(testLineOrder)){
                continue;
            }
            // Order
            testLineAndSampleList.setOrder(testLineOrder);
            if(Func.isEmpty(testMatrixList)){
                continue;
            }
            List<com.sgs.framework.model.test.testmatrix.v2.TestMatrixBO> testLineMatrixList = testMatrixNewRes.getData().stream().filter(testMatrix ->
                    testMatrix.getRelationship().getParent().getTestLine().getTestLineInstanceId().equals(testLine.getTestLineInstanceId())).collect(Collectors.toList());
            if(Func.isEmpty(testLineMatrixList)){
                continue;
            }
            // TestMatrix
            testLineAndSampleList.setTestMatrixList(testLineMatrixList);
            if(Func.isEmpty(productInstanceList)){
                continue;
            }
            Set<String> testLineSampleIdList = testLineMatrixList.stream()
                    .filter(testMatrix -> testMatrix.getRelationship().getParent().getTestLine().getTestLineInstanceId().equals(testLine.getTestLineInstanceId()))
                    .map(item-> item.getRelationship().getParent().getTestSample().getTestSampleInstanceId()).collect(Collectors.toSet());
            if(Func.isEmpty(testLineSampleIdList)){
                continue;
            }
            List<ProductInstancePO> testLineSampleList = productInstanceList.stream().filter(sample -> testLineSampleIdList.contains(sample.getRefSampleID())).collect(Collectors.toList());
            if(Func.isEmpty(testLineSampleList)){
                continue;
            }
            // sample
            List<ProductParallelBO> sampleList = Func.copy(testLineSampleList, ProductInstancePO.class, ProductParallelBO.class);
            ProductLanguageBO sampleLanguage = new ProductLanguageBO();
            sampleLanguage.setEn(sampleList.stream().filter(item-> LanguageType.check(item.getLanguageID(),LanguageType.English)).collect(Collectors.toList()));
            sampleLanguage.setCn(sampleList.stream().filter(item-> LanguageType.check(item.getLanguageID(),LanguageType.Chinese)).collect(Collectors.toList()));
            testLineAndSampleList.setSampleList(sampleLanguage);
        }
        return BaseResponse.newSuccessInstance(testLineWithSampleList);
    }

}
