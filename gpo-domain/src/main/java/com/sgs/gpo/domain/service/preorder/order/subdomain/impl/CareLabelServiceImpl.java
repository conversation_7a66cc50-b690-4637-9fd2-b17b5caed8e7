package com.sgs.gpo.domain.service.preorder.order.subdomain.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.common.productsample.CareLabelBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.CareLabelMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.CareLabelInstancePO;
import com.sgs.gpo.domain.service.preorder.order.subdomain.ICareLabelService;
import com.sgs.gpo.facade.model.preorder.order.req.CareLabelReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class CareLabelServiceImpl extends ServiceImpl<CareLabelMapper, CareLabelInstancePO> implements ICareLabelService {
    @Override
    public BaseResponse<List<CareLabelBO>> queryCareLabelList(CareLabelReq req) {
        if(Func.isEmpty(req) ||( Func.isEmpty(req.getOrderId()) && Func.isEmpty(req.getOrderIds()))){
            return BaseResponse.newFailInstance("缺少OrderId信息");
        }
        return BaseResponse.newSuccessInstance(this.baseMapper.queryCareLabelList(req));
    }
}
