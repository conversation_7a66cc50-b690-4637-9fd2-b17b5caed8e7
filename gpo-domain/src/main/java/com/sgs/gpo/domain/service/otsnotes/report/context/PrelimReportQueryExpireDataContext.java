package com.sgs.gpo.domain.service.otsnotes.report.context;


import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.gpo.dbstorages.mybatis.model.notify.NotifyConfigPO;
import lombok.Data;
import java.util.List;


@Data
public class PrelimReportQueryExpireDataContext<Input> extends BaseContext<Input, ReportBO> {
    private List<NotifyConfigPO> notifyConfigList;
}
