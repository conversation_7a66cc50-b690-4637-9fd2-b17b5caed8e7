package com.sgs.gpo.domain.convertor;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.common.dff.DFFAttrBO;
import com.sgs.framework.model.common.dff.DFFAttrLanguageBO;
import com.sgs.framework.model.common.productsample.ProductBO;
import com.sgs.framework.model.common.productsample.ProductSampleBO;
import com.sgs.framework.model.common.productsample.SampleBO;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryProductPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.productinstance.ProductInstancePO;
import com.sgs.preorder.facade.model.dto.dff.DffFormAttrDTO;
import com.sgs.preorder.facade.model.info.ProductInfo;
import com.sgs.preorder.facade.model.info.ProductSampleInfo;
import com.sgs.preorder.facade.model.rsp.ProductSampleRsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
public class ProductConvert {
    private static final Map<Class<?>, Map<String, Field>> FIELD_CACHE = new ConcurrentHashMap<>();
    public static ProductBO convertToProductBO(List<ProductSampleRsp> productSampleRspList, List<DffFormAttrDTO> allDffFormAttrDTOList) {
        if (Func.isAnyEmpty(productSampleRspList, allDffFormAttrDTOList)) {
            return null;
        }

        List<ProductInfo> productInfoList = productSampleRspList.stream()
                .map(ProductSampleRsp::getProduct)
                .collect(Collectors.toList());

        if (Func.isEmpty(productInfoList)) {
            return null;
        }

        ProductBO productBO = new ProductBO();
        Map<String, List<DffFormAttrDTO>> dffFormAttrDTOMap = buildDffFormAttrDTOMap(allDffFormAttrDTOList);

        List<DFFAttrBO> productAttrList = processProductInfoList(productInfoList, dffFormAttrDTOMap);
        productBO.setProductAttrList(productAttrList);
        productBO.setTemplateId(productInfoList.get(0).getdFFFormID());
        return productBO;
    }

    public static List<SampleBO> convertToSampleBO(List<ProductSampleRsp> productSampleRspList, List<DffFormAttrDTO> allDffFormAttrDTOList) {
        if (Func.isEmpty(productSampleRspList)) {
            return null;
        }

        List<ProductSampleInfo> productSampleInfoList = productSampleRspList.stream()
                .filter(tl -> Func.isNotEmpty(tl.getProductSamples()))
                .flatMap(tl -> tl.getProductSamples().stream())
                .collect(Collectors.toList());

        if (Func.isEmpty(productSampleInfoList)) {
            return null;
        }

        Map<String, List<ProductSampleInfo>> productSampleInfoMap = productSampleInfoList.stream()
                .collect(Collectors.groupingBy(ProductSampleInfo::getSampleID));

        Map<String, List<DffFormAttrDTO>> dffFormAttrDTOMap = buildDffFormAttrDTOMap(allDffFormAttrDTOList);

        List<SampleBO> sampleBOList = productSampleInfoMap.entrySet().stream()
                .map(entry -> {
                    SampleBO sampleBO = new SampleBO();
                    sampleBO.setSampleNo(entry.getKey());
                    List<DFFAttrBO> sampleAttrList = processProductInfoList(entry.getValue(), dffFormAttrDTOMap);
                    sampleBO.setSampleAttrList(sampleAttrList);
                    return sampleBO;
                })
                .collect(Collectors.toList());

        return sampleBOList;
    }

    private static Map<String, List<DffFormAttrDTO>> buildDffFormAttrDTOMap(List<DffFormAttrDTO> allDffFormAttrDTOList) {
        return allDffFormAttrDTOList.stream()
                .collect(Collectors.groupingBy(item -> item.getdFFFormID() + "-" + item.getLanguageID()));
    }

    private static List<DFFAttrBO> processProductInfoList(List<? extends ProductInfo> productInfoList, Map<String, List<DffFormAttrDTO>> dffFormAttrDTOMap) {
        List<DFFAttrBO> attrList = new ArrayList<>();
        for (ProductInfo productInfo : productInfoList) {
            String key = productInfo.getdFFFormID() + "-" + productInfo.getLanguageID();
            List<DffFormAttrDTO> dffFormAttrDTOList = dffFormAttrDTOMap.get(key);
            if (Func.isEmpty(dffFormAttrDTOList)) {
                continue;
            }

            dffFormAttrDTOList = dffFormAttrDTOList.stream()
                    .sorted(Comparator.comparing(item -> Func.toInt(item.getSequence(), 0), Comparator.nullsLast(Integer::compareTo)))
                    .collect(Collectors.toList());

            Map<String, Object> productValueMap = BeanUtil.beanToMap(productInfo, false, true);
            for (DffFormAttrDTO dffFormAttrDTO : dffFormAttrDTOList) {
                DFFAttrBO dffAttrBO = attrList.stream()
                        .filter(item -> Func.equalsSafe(item.getLabelCode(), dffFormAttrDTO.getFieldCode()))
                        .findAny()
                        .orElse(null);

                if (Func.isNull(dffAttrBO)) {
                    dffAttrBO = createDFFAttrBO(dffFormAttrDTO, productValueMap);
                    attrList.add(dffAttrBO);
                } else {
                    updateDFFAttrBO(dffAttrBO, dffFormAttrDTO, productValueMap, productInfo);
                }
            }
        }
        return attrList;
    }

    private static DFFAttrBO createDFFAttrBO(DffFormAttrDTO dffFormAttrDTO, Map<String, Object> productValueMap) {
        DFFAttrBO dffAttrBO = new DFFAttrBO();
        dffAttrBO.setLabelName(dffFormAttrDTO.getDispalyName());
        dffAttrBO.setLabelCode(dffFormAttrDTO.getFieldCode());
        dffAttrBO.setCustomerLabel("");
        dffAttrBO.setValue(Func.toStr(productValueMap.getOrDefault(StrUtil.lowerFirst(dffFormAttrDTO.getFieldCode()), null)));
        dffAttrBO.setSeq(Func.toInt(dffFormAttrDTO.getSequence()));
        dffAttrBO.setDataType(dffFormAttrDTO.getFieldType());
        dffAttrBO.setLanguageList(new ArrayList<>());

        DFFAttrLanguageBO dffAttrLanguageBO = new DFFAttrLanguageBO();
        dffAttrLanguageBO.setLabelName(dffFormAttrDTO.getDispalyName());
        dffAttrLanguageBO.setValue(Func.toStr(productValueMap.getOrDefault(StrUtil.lowerFirst(dffFormAttrDTO.getFieldCode()), null)));
        dffAttrLanguageBO.setLanguageId(dffFormAttrDTO.getLanguageID());
        dffAttrLanguageBO.setLangId(dffFormAttrDTO.getId());
        dffAttrBO.getLanguageList().add(dffAttrLanguageBO);

        return dffAttrBO;
    }

    private static void updateDFFAttrBO(DFFAttrBO dffAttrBO, DffFormAttrDTO dffFormAttrDTO, Map<String, Object> productValueMap, ProductInfo productInfo) {
        DFFAttrLanguageBO dffAttrLanguageBO = new DFFAttrLanguageBO();
        dffAttrLanguageBO.setLabelName(dffFormAttrDTO.getDispalyName());
        dffAttrLanguageBO.setValue(Func.toStr(productValueMap.getOrDefault(StrUtil.lowerFirst(dffFormAttrDTO.getFieldCode()), null)));
        dffAttrLanguageBO.setLanguageId(productInfo.getLanguageID());
        dffAttrLanguageBO.setLangId(productInfo.getId());
        dffAttrBO.getLanguageList().add(dffAttrLanguageBO);
    }

    public static List<ProductInstancePO> convertProductSampleBOToProductPO(ProductSampleBO productSampleBO) {
        if (Func.isEmpty(productSampleBO)) {
            return new ArrayList<>();
        }
        List<ProductInstancePO> productInstancePOList = new ArrayList<>();
        ProductBO productBO = productSampleBO.getProduct();
        List<SampleBO> sampleBOList = productSampleBO.getSampleList();
        List<ProductInstancePO> productInstancePOList_Form = convertProductBOToProductPO(productBO);
        List<ProductInstancePO> productInstancePOList_Grid = convertSampleBOToProductPO(sampleBOList);
        if (Func.isNotEmpty(productInstancePOList_Form)) {
            productInstancePOList.addAll(productInstancePOList_Form);
        }
        if (Func.isNotEmpty(productInstancePOList_Grid)) {
            productInstancePOList.addAll(productInstancePOList_Grid);
        }
        return productInstancePOList;
    }

    public static List<ProductInstancePO> convertProductBOToProductPO(ProductBO productBO) {
        List<ProductInstancePO> productInstancePOList = new ArrayList<>();
        if (Func.isEmpty(productBO)) {
            return productInstancePOList;
        }
        String dffFormId = productBO.getTemplateId();
        // Define the attributes to skip
        Map<Integer, ProductInstancePO> languageProductMap = new HashMap<>();
        Date now = DateUtil.now();
        String regionAccount = SystemContextHolder.getUserInfoFillSystem().getRegionAccount();
        for (DFFAttrBO dffAttrBO : productBO.getProductAttrList()) {
            if (dffAttrBO.getLanguageList() == null) {
                continue;
            }
            for (DFFAttrLanguageBO dffAttrLanguageBO : dffAttrBO.getLanguageList()) {
                Integer languageId = dffAttrLanguageBO.getLanguageId();
                String langId = dffAttrLanguageBO.getLangId();
                ProductInstancePO productInstancePO = languageProductMap.computeIfAbsent(languageId, k -> new ProductInstancePO());
                boolean newFlag = false;
                productInstancePO.setLanguageID(languageId);
                productInstancePO.setDFFFormID(dffFormId);
                if(Func.isEmpty(langId)){
                    langId = IdUtil.uuId();
                }
                if(Func.isEmpty(productInstancePO.getID())){
                    productInstancePO.setCreatedDate(now);
                    productInstancePO.setCreatedBy(regionAccount);
                    newFlag = true;
                }
                productInstancePO.setID(langId);
                productInstancePO.setModifiedDate(now);
                productInstancePO.setModifiedBy(regionAccount);
                Class<?> productInstancePOClass = productInstancePO.getClass();
                String labelCode = dffAttrBO.getLabelCode();
                String value = Func.toStr(dffAttrLanguageBO.getValue());
                // Skip attributes that are in the skipAttributes array
                if (isIgnoreFiled(labelCode)) {
                    continue;
                }
                try {
                    labelCode = Func.firstCharToLower(labelCode);
                    Field field = getCachedField(productInstancePOClass, labelCode);
                    if(Func.isEmpty(field)){
                        continue;
                    }
                    field.setAccessible(true);
                    if(Func.isNotEmpty(field.get(productInstancePO))){
                        continue;
                    }
                    if(Func.equalsSafe(labelCode.toLowerCase(), Constants.DFF.FILED.NO_OF_SAMPLE.toLowerCase())){
                        Integer sampleQty = Integer.valueOf(value);
                        field.set(productInstancePO, sampleQty);
                    } else {
                        field.set(productInstancePO, value);
                    }
                } catch (Exception e) {
                    log.warn("Failed to set attribute {} with value {}: {}", labelCode, value, e.getMessage());
                }
                if(newFlag){
                    productInstancePOList.add(productInstancePO);
                }
            }
        }
        return productInstancePOList;
    }
    public static List<ProductInstancePO> convertSampleBOToProductPO(List<SampleBO> sampleBOList) {
        List<ProductInstancePO> productInstancePOList = new ArrayList<>();
        if (Func.isEmpty(sampleBOList)) {
            return productInstancePOList;
        }
        Date now = DateUtil.now();
        String regionAccount = SystemContextHolder.getUserInfoFillSystem().getRegionAccount();
        for (SampleBO sampleBO : sampleBOList) {
            String dffFormId = sampleBO.getTemplateId();
            String sampleNo = sampleBO.getSampleNo();
            // Define the attributes to skip
            Map<String, ProductInstancePO> languageProductMap = new HashMap<>();
            for (DFFAttrBO dffAttrBO : sampleBO.getSampleAttrList()) {
                if (dffAttrBO.getLanguageList() == null) {
                    continue;
                }
                for (DFFAttrLanguageBO dffAttrLanguageBO : dffAttrBO.getLanguageList()) {
                    Integer languageId = dffAttrLanguageBO.getLanguageId();
                    String langId = dffAttrLanguageBO.getLangId();
                    String languageSampleKey = String.format("%s-%s", languageId, sampleNo);
                    ProductInstancePO productInstancePO = languageProductMap.computeIfAbsent(languageSampleKey, k -> new ProductInstancePO());
                    boolean newFlag = false;
                    productInstancePO.setLanguageID(languageId);
                    productInstancePO.setDFFFormID(dffFormId);
                    if(Func.isEmpty(langId)){
                        langId = IdUtil.uuId();
                    }
                    if(Func.isEmpty(productInstancePO.getID())){
                        productInstancePO.setCreatedDate(now);
                        productInstancePO.setCreatedBy(regionAccount);
                        newFlag = true;
                    }
                    productInstancePO.setID(langId);
                    productInstancePO.setSampleID(sampleNo);
                    productInstancePO.setModifiedDate(now);
                    productInstancePO.setModifiedBy(regionAccount);
                    Class<?> productInstancePOClass = productInstancePO.getClass();
                    String labelCode = dffAttrBO.getLabelCode();
                    String value = Func.toStr(dffAttrLanguageBO.getValue());
                    // Skip attributes that are in the skipAttributes array
                    if (isIgnoreFiled(labelCode)) {
                        continue;
                    }
                    try {
                        labelCode = Func.firstCharToLower(labelCode);
                        Field field = getCachedField(productInstancePOClass, labelCode);
                        if(Func.isEmpty(field)){
                            continue;
                        }
                        if(Func.isEmpty(field)){
                            continue;
                        }
                        field.setAccessible(true);
                        if(Func.isNotEmpty(field.get(productInstancePO))){
                            continue;
                        }
                        if(Func.equalsSafe(labelCode.toLowerCase(), Constants.DFF.FILED.NO_OF_SAMPLE.toLowerCase())){
                            Integer sampleQty = Integer.valueOf(value);
                            field.set(productInstancePO, sampleQty);
                        } else {
                            field.set(productInstancePO, value);
                        }
                    } catch (Exception e) {
                        log.warn("Failed to set attribute {} with value {}: {}", labelCode, value, e.getMessage());
                    }
                    if(newFlag){
                        productInstancePOList.add(productInstancePO);
                    }
                }
            }
        }
        return productInstancePOList;
    }

    public static List<EnquiryProductPO> convertProductSampleBOToEnquiryProductPO(ProductSampleBO productSampleBO) {
        if (Func.isEmpty(productSampleBO)) {
            return new ArrayList<>();
        }
        List<EnquiryProductPO> productInstancePOList = new ArrayList<>();
        ProductBO productBO = productSampleBO.getProduct();
        List<SampleBO> sampleBOList = productSampleBO.getSampleList();
        List<EnquiryProductPO> productInstancePOList_Form = convertProductBOToEnquiryProductPO(productBO);
        List<EnquiryProductPO> productInstancePOList_Grid = convertSampleBOToEnquiryProductPO(sampleBOList);
        if (Func.isNotEmpty(productInstancePOList_Form)) {
            productInstancePOList.addAll(productInstancePOList_Form);
        }
        if (Func.isNotEmpty(productInstancePOList_Grid)) {
            productInstancePOList.addAll(productInstancePOList_Grid);
        }
        return productInstancePOList;
    }

    public static List<EnquiryProductPO> convertProductBOToEnquiryProductPO(ProductBO productBO) {
        List<EnquiryProductPO> productInstancePOList = new ArrayList<>();
        if (Func.isEmpty(productBO)) {
            return productInstancePOList;
        }
        String dffFormId = productBO.getTemplateId();
        // Define the attributes to skip
        Map<Integer, EnquiryProductPO> languageProductMap = new HashMap<>();
        Date now = DateUtil.now();
        String regionAccount = SystemContextHolder.getUserInfoFillSystem().getRegionAccount();
        for (DFFAttrBO dffAttrBO : productBO.getProductAttrList()) {
            if (dffAttrBO.getLanguageList() == null) {
                continue;
            }
            for (DFFAttrLanguageBO dffAttrLanguageBO : dffAttrBO.getLanguageList()) {
                Integer languageId = dffAttrLanguageBO.getLanguageId();
                String langId = dffAttrLanguageBO.getLangId();
                EnquiryProductPO productInstancePO = languageProductMap.computeIfAbsent(languageId, k -> new EnquiryProductPO());
                boolean newFlag = false;
                productInstancePO.setLanguageId(languageId);
                productInstancePO.setDffFormId(dffFormId);
                if(Func.isEmpty(langId)){
                    langId = IdUtil.uuId();
                }
                if(Func.isEmpty(productInstancePO.getId())){
                    productInstancePO.setCreatedDate(now);
                    productInstancePO.setCreatedBy(regionAccount);
                    newFlag = true;
                }
                productInstancePO.setId(langId);
                productInstancePO.setModifiedDate(now);
                productInstancePO.setModifiedBy(regionAccount);
                Class<?> productInstancePOClass = productInstancePO.getClass();
                String labelCode = dffAttrBO.getLabelCode();
                String value = Func.toStr(dffAttrLanguageBO.getValue());
                // Skip attributes that are in the skipAttributes array
                if (isIgnoreFiled(labelCode)) {
                    continue;
                }
                try {
                    labelCode = Func.firstCharToLower(labelCode);
                    Field field = productInstancePOClass.getDeclaredField(labelCode);
                    field.setAccessible(true);
                    if(Func.isNotEmpty(field.get(productInstancePO))){
                        continue;
                    }
                    if(Func.equalsSafe(labelCode.toLowerCase(), Constants.DFF.FILED.NO_OF_SAMPLE.toLowerCase())){
                        Integer sampleQty = Integer.valueOf(value);
                        field.set(productInstancePO, sampleQty);
                    } else {
                        field.set(productInstancePO, value);
                    }
                } catch (Exception e) {
                    log.warn("Failed to set attribute {} with value {}: {}", labelCode, value, e.getMessage());
                }
                if(newFlag){
                    productInstancePOList.add(productInstancePO);
                }
            }
        }
        return productInstancePOList;
    }

    public static List<EnquiryProductPO> convertSampleBOToEnquiryProductPO(List<SampleBO> sampleBOList) {
        List<EnquiryProductPO> productInstancePOList = new ArrayList<>();
        if (Func.isEmpty(sampleBOList)) {
            return productInstancePOList;
        }
        Date now = DateUtil.now();
        String regionAccount = SystemContextHolder.getUserInfoFillSystem().getRegionAccount();
        for (SampleBO sampleBO : sampleBOList) {
            String dffFormId = sampleBO.getTemplateId();
            String sampleNo = sampleBO.getSampleNo();
            // Define the attributes to skip
            Map<String, EnquiryProductPO> languageProductMap = new HashMap<>();
            for (DFFAttrBO dffAttrBO : sampleBO.getSampleAttrList()) {
                if (dffAttrBO.getLanguageList() == null) {
                    continue;
                }
                for (DFFAttrLanguageBO dffAttrLanguageBO : dffAttrBO.getLanguageList()) {
                    Integer languageId = dffAttrLanguageBO.getLanguageId();
                    String langId = dffAttrLanguageBO.getLangId();
                    String languageSampleKey = String.format("%s-%s", languageId, sampleNo);
                    EnquiryProductPO productInstancePO = languageProductMap.computeIfAbsent(languageSampleKey, k -> new EnquiryProductPO());
                    boolean newFlag = false;
                    productInstancePO.setLanguageId(languageId);
                    productInstancePO.setDffFormId(dffFormId);
                    if(Func.isEmpty(langId)){
                        langId = IdUtil.uuId();
                    }
                    if(Func.isEmpty(productInstancePO.getId())){
                        productInstancePO.setCreatedDate(now);
                        productInstancePO.setCreatedBy(regionAccount);
                        newFlag = true;
                    }
                    productInstancePO.setId(langId);
                    productInstancePO.setSampleId(sampleNo);
                    productInstancePO.setModifiedDate(now);
                    productInstancePO.setModifiedBy(regionAccount);
                    Class<?> productInstancePOClass = productInstancePO.getClass();
                    String labelCode = dffAttrBO.getLabelCode();
                    String value = Func.toStr(dffAttrLanguageBO.getValue());
                    // Skip attributes that are in the skipAttributes array
                    if (isIgnoreFiled(labelCode)) {
                        continue;
                    }
                    try {
                        labelCode = Func.firstCharToLower(labelCode);
                        Field field = getCachedField(productInstancePOClass, labelCode);
                        if(Func.isEmpty(field)){
                            continue;
                        }
                        field.setAccessible(true);
                        if(Func.isNotEmpty(field.get(productInstancePO))){
                            continue;
                        }
                        if(Func.equalsSafe(labelCode.toLowerCase(), Constants.DFF.FILED.NO_OF_SAMPLE.toLowerCase())){
                            Integer sampleQty = Integer.valueOf(value);
                            field.set(productInstancePO, sampleQty);
                        } else {
                            field.set(productInstancePO, value);
                        }
                    } catch (Exception e) {
                        log.warn("Failed to set attribute {} with value {}: {}", labelCode, value, e.getMessage());
                    }
                    if(newFlag){
                        productInstancePOList.add(productInstancePO);
                    }
                }
            }
        }
        return productInstancePOList;
    }

    private static boolean isIgnoreFiled(String filedCode) {
        String[] ignoreFields = Constants.DFF.IGNORE_FIELD;
        return Arrays.stream(ignoreFields).anyMatch(item -> StringUtils.equalsIgnoreCase(item, filedCode));
    }
    private static Field getCachedField(Class<?> clazz, String fieldName) {
        return FIELD_CACHE.computeIfAbsent(clazz, k -> new ConcurrentHashMap<>())
                .computeIfAbsent(fieldName, fn -> {
                    try {
                        Field f = clazz.getDeclaredField(fn);
                        f.setAccessible(true);
                        return f;
                    } catch (NoSuchFieldException e) {
                        return null;
                    }
                });
    }
}