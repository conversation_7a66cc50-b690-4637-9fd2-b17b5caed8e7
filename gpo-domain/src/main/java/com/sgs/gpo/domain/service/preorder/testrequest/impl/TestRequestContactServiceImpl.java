package com.sgs.gpo.domain.service.preorder.testrequest.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.TestRequestContactMapper;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.TestRequestMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.testrequest.TestRequestContactPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.testrequest.TestRequestPO;
import com.sgs.gpo.domain.service.preorder.testrequest.ITestRequestContactService;
import com.sgs.gpo.domain.service.preorder.testrequest.ITestRequestService;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class TestRequestContactServiceImpl extends ServiceImpl<TestRequestContactMapper, TestRequestContactPO> implements ITestRequestContactService {


    @Override
    public BaseResponse<List<TestRequestContactPO>> select(OrderIdReq orderIdReq) {
        if(Func.isEmpty(orderIdReq)||Func.isEmpty(orderIdReq.getOrderIdList())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        LambdaQueryWrapper<TestRequestContactPO> wrapper= Wrappers.<TestRequestContactPO>lambdaQuery()
                .in(TestRequestContactPO::getOrderId,orderIdReq.getOrderIdList());
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }
}
