package com.sgs.gpo.domain.service.otsnotes.testline.command;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.core.enums.TestLineActionEnums;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineDomainService;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineProcessService;
import com.sgs.gpo.domain.service.otsnotes.testline.context.TestLineContext;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.ITestLineService;
import com.sgs.gpo.facade.model.otsnotes.common.rsp.StatusControlRsp;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineBatchUpdateReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineNaReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineProcessReq;
import com.sgs.otsnotes.facade.TestLineFacade;
import com.sgs.otsnotes.facade.model.enums.TestLineStatus;
import com.sgs.otsnotes.facade.model.req.testLine.TestLineStatusUpdateReq;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class TestLineNABizCMD extends BaseCommand<TestLineContext<TestLineNaReq>> {
    private ITestLineDomainService testLineDomainService;
    private ITestLineProcessService testLineProcessService;
    private TestLineFacade testLineFacade;
    private ITestLineService testLineService;
    @Override
    public BaseResponse validateParam(TestLineContext<TestLineNaReq> context) {
        Assert.isTrue(Func.isNotEmpty(context.getParam()), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(context.getParam().getTestLineInstanceIdList()), "common.param.miss", new Object[]{"testLineInstanceIdList"});
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(TestLineContext<TestLineNaReq> context) {
        TestLineNaReq param = context.getParam();
        Set<String> testLineInstanceIdList = param.getTestLineInstanceIdList();
//        String remark = param.getRemark();
        OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
        orderTestLineReq.setBaseQuery(true);
        orderTestLineReq.setTestLineInstanceIdList(testLineInstanceIdList);
        BaseResponse<List<TestLineBO>> testLineBORes = testLineDomainService.queryTestLine(orderTestLineReq);
        if(testLineBORes.isFail()){
            return testLineBORes;
        }
        List<TestLineBO> testLineBOList = testLineBORes.getData();
        Assert.isTrue(Func.isNotEmpty(testLineBOList), "common.records.empty", new Object[]{"TestLine"});
        //校验状态是否为Typing
        List<TestLineBO> unTypingTestLineList = testLineBOList.stream().filter(testLineBO -> !TestLineStatus.check(testLineBO.getTestLineStatus(), TestLineStatus.Typing)).collect(Collectors.toList());
        if (Func.isNotEmpty(unTypingTestLineList)) {
            String unTypingTestItemNo = unTypingTestLineList.stream().map(TestLineBO::getTestItemNo).map(Func::toStr).collect(Collectors.joining(","));
            return BaseResponse.newFailInstance("test.line.status.not.match", new Object[]{unTypingTestItemNo,TestLineStatus.Typing.getMessage()});
        }
        //是否已经创建了SubContract或Job
        List<TestLineBO> createSubContactTestLineList = testLineBOList.stream().filter(testLineBO ->Func.isNotEmpty(testLineBO.getSubcontractBO()) || Func.isNotEmpty(testLineBO.getTestLineJobBO())).collect(Collectors.toList());
        if (Func.isNotEmpty(createSubContactTestLineList)) {
            String createExecutionTestLineItemNo = createSubContactTestLineList.stream().map(TestLineBO::getTestItemNo).map(Func::toStr).collect(Collectors.joining(","));
            return BaseResponse.newFailInstance("test.line.already.created.execution", new Object[]{createExecutionTestLineItemNo});
        }
        context.setTestLineBOList(testLineBOList);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(TestLineContext<TestLineNaReq> context) {
        TestLineNaReq testLineNaReq = context.getParam();
        String orderTestLineRemark = testLineNaReq.getTestLineRemark();
        List<TestLineBO> testLineBOList = context.getTestLineBOList();
        Set<String> testLineInstanceIdList = testLineBOList.stream().map(TestLineBO::getId).collect(Collectors.toSet());
        TestLineProcessReq testLineProcessReq = new TestLineProcessReq();
        testLineProcessReq.setTriggerMatrix(true);
        testLineProcessReq.setObjectIdList(testLineInstanceIdList);
        testLineProcessReq.setProcessCode(Constants.OBJECT.TEST_LINE.OBJECT_CODE);
        testLineProcessReq.setAction(TestLineActionEnums.NA.getAction());
        testLineProcessReq.setNewStatus(TestLineStatus.NA.getStatus());
        testLineProcessReq.setNeedStatusControl(false);
        testLineProcessReq.setRemark(orderTestLineRemark);
        BaseResponse<StatusControlRsp> naResponse = testLineProcessService.toNa(testLineProcessReq);
        if(naResponse.isSuccess() && Func.isNotEmpty(naResponse.getData())){
            StatusControlRsp statusControlRsp = naResponse.getData();
            if(Func.isNotEmpty(statusControlRsp) && Func.isNotEmpty(statusControlRsp.getAllowObjectIdList())){
                Set<String> allowObjectIdList = statusControlRsp.getAllowObjectIdList();
                //更新Remark
                if(Func.isNotEmpty(orderTestLineRemark)){
                    //批量更新TestLine Remark
                    TestLineBatchUpdateReq testLineBatchUpdateReq = new TestLineBatchUpdateReq();
                    testLineBatchUpdateReq.setTestLineInstanceIdList(allowObjectIdList);
                    testLineBatchUpdateReq.setOrderTestLineRemark(orderTestLineRemark);
                    testLineService.updateTestLineRemark(testLineBatchUpdateReq);
                }
            }
        }
        log.info("TestLineNA,testLineInstanceIdList:{},naResponse:{}",testLineInstanceIdList,naResponse);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse after(TestLineContext<TestLineNaReq> context) {
        List<TestLineBO> testLineBOList = context.getTestLineBOList();
        Set<String> orderNoList = testLineBOList.stream().map(TestLineBO::getOrderNo).collect(Collectors.toSet());
        for (String orderNo : orderNoList) {
            TestLineStatusUpdateReq testLineStatusUpdateReq = new TestLineStatusUpdateReq();
            testLineStatusUpdateReq.setOrderNo(orderNo);
            testLineStatusUpdateReq.setProductLineCode(context.getProductLineCode());
            testLineStatusUpdateReq.setAction("dataEntry");
            BaseResponse testLineChangeResponse = testLineFacade.onChange(testLineStatusUpdateReq);
            if (testLineChangeResponse.isFail()) {
                log.error("on TestLineStatusChange error:{}", testLineChangeResponse.getMessage());
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }
}
