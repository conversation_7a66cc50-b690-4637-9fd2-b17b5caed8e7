package com.sgs.gpo.domain.service.otsnotes.report.subdomain.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.PageBO;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.report.ReportAutoDeliverMapper;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportAutoDeliverService;
import com.sgs.gpo.facade.model.report.bo.ReportAutoDeliverBO;
import com.sgs.gpo.facade.model.report.req.ReportAutoDeliverQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ReportAutoDeliverServiceImpl implements IReportAutoDeliverService {
    @Autowired
    private ReportAutoDeliverMapper reportAutoDeliverMapper;

    @Override
    public BaseResponse<PageBO<ReportAutoDeliverBO>> page(ReportAutoDeliverQueryReq queryReq, Integer page, Integer rows) {
        Assert.isTrue(Func.isNotEmpty(queryReq), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(queryReq.getLabId()), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        if(Func.isEmpty(page)){
            page = Constants.DEFAULT.PAGE;
        }
        if(Func.isEmpty(rows)){
            rows = Constants.DEFAULT.ROWS;
        }
        Page<ReportAutoDeliverBO> pageParam = new Page<>(page,rows);
        IPage<ReportAutoDeliverBO> iPage = reportAutoDeliverMapper.page(pageParam, queryReq);
        PageBO<ReportAutoDeliverBO> reportAutoDeliverBOPage = Func.copy(iPage, PageBO.class);
        return BaseResponse.newSuccessInstance(reportAutoDeliverBOPage);
    }
}
