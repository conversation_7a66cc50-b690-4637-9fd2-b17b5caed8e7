package com.sgs.gpo.domain.service.otsnotes.testmatrix;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixBO;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixIdBO;
import com.sgs.framework.open.platform.base.service.IProcessService;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixProcessReq;

/**
 * <AUTHOR>
 * @title: ITestMatrixProcessService
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/8/30 15:18
 */
public interface ITestMatrixProcessService extends IProcessService<TestMatrixBO, TestMatrixIdBO> {
    BaseResponse typing(TestMatrixProcessReq testMatrixProcessReq);
    BaseResponse enter(TestMatrixProcessReq testMatrixProcessReq);
    BaseResponse submit(TestMatrixProcessReq testMatrixProcessReq);
    BaseResponse complete(TestMatrixProcessReq testMatrixProcessReq);
    BaseResponse cancel(TestMatrixProcessReq testMatrixProcessReq);
    BaseResponse na(TestMatrixProcessReq testMatrixProcessReq);
}
