package com.sgs.gpo.domain.service.preorder.order.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.common.productsample.CareLabelBO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.CareLabelInstancePO;
import com.sgs.gpo.facade.model.preorder.order.req.CareLabelReq;

import java.util.List;

public interface ICareLabelService extends IService<CareLabelInstancePO> {

    BaseResponse<List<CareLabelBO>> queryCareLabelList(CareLabelReq req);
}
