package com.sgs.gpo.domain.service.otsnotes.ordercitation;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.ordercitation.OrderCitationPO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/6/30 10:17
 */
public interface IOrderCitationService extends IService<OrderCitationPO> {
    /**
     * 基于OrderID 查询订单测试标准信息
     *
     * @param orderIdList
     * @return
     */
    BaseResponse<List<OrderCitationPO>> queryByOrderId(Set<String> orderIdList);

}
