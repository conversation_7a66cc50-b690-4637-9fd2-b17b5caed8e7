package com.sgs.gpo.domain.service.otsnotes.attachment;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.attachment.ObjectAttachmentPO;
import com.sgs.gpo.facade.model.attachment.ObjectAttachmentQueryReq;

import java.util.List;

public interface IObjectAttachmentService extends IService<ObjectAttachmentPO> {

    List<ObjectAttachmentPO> query(ObjectAttachmentQueryReq queryReq);

}
