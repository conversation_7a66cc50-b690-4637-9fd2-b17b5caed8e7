package com.sgs.gpo.domain.service.preorder.enquiry.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.enquiry.EnquiryTrfRelationshipMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryTrfRelationshipPO;
import com.sgs.gpo.domain.service.preorder.enquiry.subdomain.IEnquiryTrfRelationshipService;
import com.sgs.gpo.facade.model.preorder.enquiry.bo.EnquiryTrfBO;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryDelReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryIdReq;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryTrfReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @title: EnquiryTrfRelationshipServiceImpl
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2024/1/16 22:33
 */
@Service
@Slf4j
public class EnquiryTrfRelationshipServiceImpl extends ServiceImpl<EnquiryTrfRelationshipMapper, EnquiryTrfRelationshipPO> implements IEnquiryTrfRelationshipService {

    @Override
    public BaseResponse<List<EnquiryTrfRelationshipPO>> select(EnquiryIdReq enquiryIdReq) {
        if (Func.isEmpty(enquiryIdReq)) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        if (Func.isEmpty(enquiryIdReq.getEnquiryIdList())) {
            return BaseResponse.newFailInstance("common.param.miss", new Object[]{"EnquiryId"});
        }
        LambdaQueryWrapper<EnquiryTrfRelationshipPO> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EnquiryTrfRelationshipPO::getEnquiryId, enquiryIdReq.getEnquiryIdList());
        return BaseResponse.newSuccessInstance(baseMapper.selectList(wrapper));
    }

    @Override
    public BaseResponse<List<EnquiryTrfBO>> queryEnquiryTrf(EnquiryTrfReq enquiryTrfReq) {
        if (Func.isEmpty(enquiryTrfReq) || (Func.isEmpty(enquiryTrfReq.getEnquiryIdList()) && Func.isEmpty(enquiryTrfReq.getEnquiryNoList()) && Func.isEmpty(enquiryTrfReq.getRefNo()) && Func.isEmpty(enquiryTrfReq.getExternalOrderNo()))) {
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        List<EnquiryTrfBO> orderTrfBOS = baseMapper.queryEnquiryTrf(enquiryTrfReq);
        return BaseResponse.newSuccessInstance(orderTrfBOS);
    }

    @Override
    public Integer delete(EnquiryDelReq enquiryDelReq) {
        if(Func.isEmpty(enquiryDelReq) || (Func.isEmpty(enquiryDelReq.getEnquiryIdList()))){
            return null;
        }
        LambdaQueryWrapper<EnquiryTrfRelationshipPO> wrapper = Wrappers.lambdaQuery();
        wrapper.in(EnquiryTrfRelationshipPO::getEnquiryId, enquiryDelReq.getEnquiryIdList());
        if(Func.isNotEmpty(enquiryDelReq.getRefSystemIdList())){
            wrapper.in(EnquiryTrfRelationshipPO::getRefSystemId, enquiryDelReq.getRefSystemIdList());
        }
        return baseMapper.delete(wrapper);
    }

}
