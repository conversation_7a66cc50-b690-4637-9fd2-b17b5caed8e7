package com.sgs.gpo.domain.service.preorder.enquiry.convertor;

import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.util.StandardCharPool;
import com.sgs.gpo.facade.model.preorder.enquiry.req.ServiceItemReq;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public abstract class ServiceItemConvertor {
    public static List<ServiceItemReq> batchConvertToServiceItemDto(List<String> serviceItemStrList) {
        if (Func.isEmpty(serviceItemStrList)) {
            return new ArrayList<>();
        }
        List<ServiceItemReq> serviceItemList = serviceItemStrList.stream().map(s -> {
            ServiceItemReq serviceItemDto = new ServiceItemReq();
            String[] namePair = StringUtils.split(s, StandardCharPool.GPO_SEPARATOR);
            if (ArrayUtils.getLength(namePair) > 0) {
                serviceItemDto.setServiceItemName(StringUtils.trimToEmpty(namePair[0]));
            }
            if (ArrayUtils.getLength(namePair) > 1) {
                serviceItemDto.setServiceItemNameCn(StringUtils.trimToEmpty(namePair[1]));
            }
            if (StringUtils.isBlank(serviceItemDto.getServiceItemNameCn())) {
                serviceItemDto.setServiceItemNameCn(serviceItemDto.getServiceItemName());
            }
            return serviceItemDto;
        }).collect(Collectors.toList());
        return serviceItemList;
    }
}
