package com.sgs.gpo.domain.service.otsnotes.subcontract.command;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.model.Lab;
import com.sgs.framework.model.enums.ReportLanguage;
import com.sgs.framework.model.enums.SgsSystem;
import com.sgs.framework.model.enums.TestLineStatus;
import com.sgs.framework.model.enums.TestLineType;
import com.sgs.framework.model.order.v2.TestLineIdRelBO;
import com.sgs.framework.model.report.report.v2.*;
import com.sgs.framework.model.test.citation.CitationBO;
import com.sgs.framework.model.test.pp.pptestline.v2.PPTestLineIdBO;
import com.sgs.framework.model.test.pp.pptestline.v2.PPTestLineRelBO;
import com.sgs.framework.model.test.pp.pptestline.v2.PPTestLineRelHeaderBO;
import com.sgs.framework.model.test.testline.v2.*;
import com.sgs.framework.model.test.testmatrix.v2.*;
import com.sgs.framework.model.test.testsample.v2.TestSampleIdBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportMatrixRelPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractTestLinePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.TestMatrixExtPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.TestMatrixPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.testrequest.TestRequestPO;
import com.sgs.gpo.domain.service.common.lab.ILabService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportMatrixRelService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.otsnotes.subcontract.context.SubcontractReportMatrixContext;
import com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.ISubcontractService;
import com.sgs.gpo.domain.service.otsnotes.subcontract.subdomain.ISubcontractTestLineService;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineDomainService;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.IPPTestLineRelationshipService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixExtService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixService;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.ITestSampleService;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IGeneralOrderService;
import com.sgs.gpo.domain.service.preorder.testrequest.ITestRequestService;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.PPTestLineQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixExtQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderQueryReq;
import com.sgs.gpo.facade.model.report.req.ReportQueryReq;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractQueryReq;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractReportMatrixQueryReq;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractTestLineQueryReq;
import com.sgs.gpo.facade.model.subcontract.rsp.SubcontractReportMatrixQueryRsp;
import com.sgs.gpo.integration.trims.TrimsClient;
import com.sgs.trimslocal.facade.model.pp.req.GetPpInfoReq;
import com.sgs.trimslocal.facade.model.pp.rsp.GetPpBaseInfoRsp;
import com.sgs.trimslocal.facade.model.testline.req.QueryPpTestLineReq;
import com.sgs.trimslocal.facade.model.testline.rsp.QueryPpTestLineRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@Scope(value = "prototype")
public class SubcontractReportMatrixCMD extends BaseCommand<SubcontractReportMatrixContext> {


    @Autowired
    private ITestMatrixExtService testMatrixExtService;

    @Autowired
    private ISubcontractTestLineService subcontractTestLineService;

    @Autowired
    private IReportMatrixRelService reportMatrixRelService;

    @Autowired
    private ITestSampleService testSampleService;

    @Autowired
    private TrimsClient trimsClient;

    @Autowired
    ITestMatrixService testMatrixService;

    @Autowired
    IReportService reportService;

    @Autowired
    ISubcontractService subcontractService;

    @Autowired
    ITestRequestService testRequestService;

    @Autowired
    IGeneralOrderService generalOrderService;

    @Autowired
    private ILabService labService;

    @Autowired
    private ITestLineDomainService testLineDomainService;

    @Autowired
    private IPPTestLineRelationshipService ppTestLineRelationshipService;

    /**
     * 查询参数校验
     * @param context
     * @return
     */
    @Override
    public BaseResponse validateParam(SubcontractReportMatrixContext context) {
        SubcontractReportMatrixQueryReq req = context.getParam();
        if(Func.isEmpty(req) || Func.isEmpty(req.getSubcontractId())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    /**
     * 根据分包单查询orderNo
     */
    @Override
    public BaseResponse before(SubcontractReportMatrixContext context){
        String subcontractId = context.getParam().getSubcontractId();
        //查询分包单信息
        SubcontractQueryReq subcontractQueryReq = new SubcontractQueryReq();
        subcontractQueryReq.setSubcontractId(subcontractId);
        BaseResponse<List<SubcontractPO>> subcontractResponse = subcontractService.select(subcontractQueryReq);
        if(subcontractResponse.isFail()|| Func.isEmpty(subcontractResponse.getData())){
            return BaseResponse.newFailInstance("未查询到分包单对应的Order信息！");
        }
        context.setOrderNo(subcontractResponse.getData().get(0).getOrderNo());
        //查询general OrderId
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setOrderNoList(Sets.newHashSet(subcontractResponse.getData().get(0).getOrderNo()));
        BaseResponse<List<GeneralOrderPO>> generalOrderPORsp = generalOrderService.query2(orderQueryReq);
        if(generalOrderPORsp.isFail() || Func.isEmpty(generalOrderPORsp.getData())){
            return BaseResponse.newFailInstance("未查询到分包单对应的Order信息");
        }
        String orderId = generalOrderPORsp.getData().get(0).getId();
        context.setOrderId(orderId);
        return super.before(context);
    }

    /**
     * 查询所需数据 拼装 分包单 -> TestLine -> Sample&Matrix -> Report
     * @param context
     * @return
     */
    @Override
    public BaseResponse execute(SubcontractReportMatrixContext context) {
        SubcontractReportMatrixQueryRsp rsp = new SubcontractReportMatrixQueryRsp();
        rsp.setSystemId(SgsSystem.GPO.getCode());
        //查询订单下的reportLanguage
        OrderIdReq orderIdReq = new OrderIdReq();
        orderIdReq.setOrderIdList(Sets.newHashSet(context.getOrderId()));
        BaseResponse<List<TestRequestPO>> testRequestPORsp = testRequestService.select(orderIdReq);
        if(testRequestPORsp.isFail() || Func.isEmpty(testRequestPORsp.getData())){
            return BaseResponse.newFailInstance("未查询到Report Language");
        }
        String langId = testRequestPORsp.getData().get(0).getReportLanguage();
        rsp.setLangId(langId);
        //查询Subcontract对应的TL集合
        SubcontractTestLineQueryReq subcontractTestLineQueryReq = new SubcontractTestLineQueryReq();
        subcontractTestLineQueryReq.setSubcontractIds(Sets.newHashSet(context.getParam().getSubcontractId()));
        BaseResponse<List<SubcontractTestLinePO>> subcontractTestLinePORsp =  subcontractTestLineService.query(subcontractTestLineQueryReq);
        if(subcontractTestLinePORsp.getStatus() != 200 || Func.isEmpty(subcontractTestLinePORsp.getData())){
            return BaseResponse.newFailInstance("未查到到分包单下存在TestLine!");
        }
        //TLInstanceId
        List<SubcontractTestLinePO> subcontractTestLinePOList = subcontractTestLinePORsp.getData();
        Set<String> testLineInstanceIds = new HashSet<>();
        for(SubcontractTestLinePO item : subcontractTestLinePOList ){
            if(Func.isNotEmpty(item.getTestLineInstanceId())){
                testLineInstanceIds.add(item.getTestLineInstanceId());
            }
        }
        log.info("根据分包单查找到的TL:{}",subcontractTestLinePOList);
        //查询TL
        List<TestLineBO> testLineBOList = getTestLine(testLineInstanceIds);
        rsp.setTestLineList(testLineBOList);
        //TestMatrix
        if(Func.isNotEmpty(testLineBOList)){
            Set<String> newTestLineIds = testLineBOList.stream().map(e -> e.getId().getTestLineInstanceId()).collect(Collectors.toSet());
            List<TestMatrixBO> testMatrixBOList = getTestMatrix(newTestLineIds);
            rsp.setTestMatrixList(testMatrixBOList);
            //Report
            Set<String> testMatrixIds = new HashSet<>();
            for(TestMatrixBO item : rsp.getTestMatrixList()){
                if(Func.isNotEmpty(item) && Func.isNotEmpty(item.getId())){
                    testMatrixIds.add(item.getId().getTestMatrixId());
                }
            }
            List<ReportBO> reportBOList = getReport(testMatrixIds);
            rsp.setReportList(reportBOList);
            //20231217 增加对CSPP的拆分
            SubcontractReportMatrixQueryRsp result = dealCSPP(rsp,context);
            return BaseResponse.newSuccessInstance(result);
        }
        return BaseResponse.newSuccessInstance(rsp);
    }

    private  List<TestLineBO> getTestLine(Set<String> testLineInstanceIds) {
        List<TestLineBO> testLineBOList = Lists.newArrayList();
        OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
        orderTestLineReq.setTestLineInstanceIdList(testLineInstanceIds);
        orderTestLineReq.setCrossDbQuery(false);
        List<com.sgs.framework.model.test.testline.TestLineBO> localTestLineList = testLineDomainService.queryTestLine(orderTestLineReq).getData();
        if (Func.isEmpty(localTestLineList)) {
            return testLineBOList;
        }
        List<com.sgs.framework.model.test.testline.TestLineBO> testLineList = new ArrayList<>();
        // 存在已经展开的CSPP -> 重新组装TL列表
        // 1.增加CSPP关联下的TL 2.删除此CSPP 3.展开的TL 判断是都存在在列表中，存在跳过 不存在加入
        List<com.sgs.framework.model.test.testline.TestLineBO> openedCSPP = localTestLineList.stream().filter(e -> Func.isNotEmpty(e.getTestLineType())
                && TestLineType.check(e.getTestLineType(),TestLineType.CSPP_OPEN)).collect(Collectors.toList());
        List<com.sgs.framework.model.test.testline.TestLineBO> testLines = localTestLineList.stream().filter(e -> Func.isNotEmpty(e.getTestLineType())
                && !TestLineType.check(e.getTestLineType(),TestLineType.CSPP_OPEN)).collect(Collectors.toList());
        if(Func.isNotEmpty(openedCSPP)){
            String orderNo = openedCSPP.get(0).getOrderNo();
            List<com.sgs.framework.model.test.testline.TestLineBO> testLineByCSPP = null;
            //查询订单下所有的展开的TL
            if(Func.isNotEmpty(orderNo)){
                OrderTestLineReq req = new OrderTestLineReq();
                req.setOrderNoList(Sets.newHashSet(orderNo));
                req.setCrossDbQuery(false);
                //订单下的TestLine
                List<com.sgs.framework.model.test.testline.TestLineBO> orderTestLineList = testLineDomainService.queryTestLine(req).getData();
                //订单下CSPP已经展开的TL
                testLineByCSPP = orderTestLineList.stream().filter(e -> Func.isNotEmpty(e.getTestLineType()) && TestLineType.check(e.getTestLineType(),TestLineType.CSPP_OPEN_TL)).collect(Collectors.toList());
            }
            List<com.sgs.framework.model.test.testline.TestLineBO> finalTestLineByCSPP = testLineByCSPP;
            testLineList.addAll(testLines);
            //遍历已经展开的cspp，替换为TL
            openedCSPP.stream().forEach(testLineBO -> {
                //查询出被展开的TL的ID
                PPTestLineQueryReq ppTestLineQueryReq = new PPTestLineQueryReq();
                ppTestLineQueryReq.setOrderInstanceId(testLineBO.getOrderId());
                ppTestLineQueryReq.setParentTestLineId(testLineBO.getTestLineInstanceId());
                Set<String> childTestLineIds = ppTestLineRelationshipService.getTestLineByCSPP(ppTestLineQueryReq);
                if(Func.isNotEmpty(childTestLineIds)){
                    childTestLineIds.stream().forEach(testLineId ->{
                        com.sgs.framework.model.test.testline.TestLineBO testLine = testLineList.stream().filter(e -> Func.equalsSafe(e.getTestLineInstanceId(),testLineId)).findAny().orElse(null);
                        //不存在列表中 加入
                        if(Func.isEmpty(testLine) && Func.isNotEmpty(finalTestLineByCSPP)){
                            testLine = finalTestLineByCSPP.stream().filter(e -> Func.equalsSafe(e.getTestLineInstanceId(),testLineId)).findAny().orElse(null);
                            if(Func.isNotEmpty(testLine)){
                                testLineList.add(testLine);
                            }
                        }
                    });
                }
            });
        } else {
            testLineList.addAll(localTestLineList);
        }
        testLineList.stream().forEach(localTestLine -> {
            TestLineBO testLineBO = new TestLineBO();
            if(TestLineStatus.check(localTestLine.getTestLineStatus(),TestLineStatus.Cancelled)){
                return;
            }
            //id
            TestLineIdBO id = new TestLineIdBO();
            Func.copy(localTestLine, id);
            testLineBO.setId(id);
            //relationship
            assembleRelationship(testLineBO);
            //ppTestLineRelList
            assemblePPTestLineRelList(testLineBO, localTestLine);
            //header
            testLineBO.setHeader(Func.copy(localTestLine, TestLineHeaderBO.class));
            //citation
            testLineBO.setCitation(localTestLine.getCitation());
            testLineBOList.add(testLineBO);
        });
        return testLineBOList.stream().sorted(Comparator.comparing(item -> (Func.isEmpty(item.getId().getTestLineId())?0:item.getId().getTestLineId()), Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());
    }

    private void assembleRelationship(TestLineBO testLineBO) {
        TestLineRelationshipBO relationship = new TestLineRelationshipBO();
        // children
        TestLineChildrenBO children = new TestLineChildrenBO();
        relationship.setChildren(children);
        // parallel
        TestSampleQueryReq testSampleQueryReq = new TestSampleQueryReq();
        testSampleQueryReq.setTestLineInstanceId(Sets.newHashSet(testLineBO.getId().getTestLineInstanceId()));
        BaseResponse<List<TestLineSampleBO>> testSampleResponse = testSampleService.queryListByTestLine(testSampleQueryReq);
        if (testSampleResponse.isSuccess() && Func.isNotEmpty(testSampleResponse.getData())) {
            TestLineParallelBO parallel = new TestLineParallelBO();
            parallel.setTestSampleList(testSampleResponse.getData());
            relationship.setParallel(parallel);
        }
        testLineBO.setRelationship(relationship);
    }

    private void assemblePPTestLineRelList(TestLineBO testLineBO, com.sgs.framework.model.test.testline.TestLineBO localTestLine) {
        if (Func.isEmpty(localTestLine.getPpTestLineRelList())) {
            return;
        }
        Set<Long> ppBaseIds = new HashSet<>();
        //批量查询ppNo,ppVersionId
        for(com.sgs.framework.model.test.pp.pptestline.PPTestLineRelBO item : localTestLine.getPpTestLineRelList()){
            ppBaseIds.add(item.getPpBaseId());
        }
        GetPpInfoReq ppInfoReq = new GetPpInfoReq();
        ppInfoReq.setCallerBU(ProductLineContextHolder.getProductLineCode());
        ppInfoReq.setPpBaseIds(ppBaseIds);
        List<GetPpBaseInfoRsp> ppBaseInfoRspList = trimsClient.getPpBaseInfo(ppInfoReq).getData();
        List<PPTestLineRelBO> ppTestLineRelList = Lists.newArrayList();
        localTestLine.getPpTestLineRelList().stream().forEach(ppRel -> {
            PPTestLineRelBO ppTestLineRelBO = new PPTestLineRelBO();
            GetPpBaseInfoRsp ppBaseInfo = ppBaseInfoRspList.stream().filter(e -> e.getPpBaseId().equals(ppRel.getPpBaseId())).findFirst().orElse(null);
            ppTestLineRelBO.setId(Func.copy(ppRel, PPTestLineIdBO.class));
            if(Func.isNotEmpty(ppBaseInfo)){
                PPTestLineIdBO id = ppTestLineRelBO.getId();
                id.setPpNo(ppBaseInfo.getPpNo());
                id.setPpVersionId(ppBaseInfo.getPpVersionId());
                ppTestLineRelBO.setId(id);
            }
            ppTestLineRelBO.setHeader(Func.copy(ppRel, PPTestLineRelHeaderBO.class));
            ppTestLineRelList.add(ppTestLineRelBO);
        });
        testLineBO.setPpTestLineRelList(ppTestLineRelList);
    }

    private List<TestMatrixBO> getTestMatrix(Set<String> testLineInstanceIds) {
        List<TestMatrixBO> testMatrixList = Lists.newArrayList();
        TestMatrixQueryReq testMatrixQueryReq = new TestMatrixQueryReq();
        testMatrixQueryReq.setTestLineInstanceIdList(testLineInstanceIds);
        BaseResponse<List<TestMatrixPO>> testMatrixResponse = testMatrixService.queryList(testMatrixQueryReq);
        if(testMatrixResponse.isFail() || Func.isEmpty(testMatrixResponse.getData())){
            return testMatrixList;
        }
        List<TestMatrixPO> testMatrixPOList = testMatrixResponse.getData();
        Set<String> testMatrixId = testMatrixPOList.stream().map(e -> e.getId()).collect(Collectors.toSet());
        TestMatrixExtQueryReq testMatrixExtQueryReq = new TestMatrixExtQueryReq();
        testMatrixExtQueryReq.setTestMatrixIdList(testMatrixId);
        List<TestMatrixExtPO> testMatrixExtPOList = testMatrixExtService.query(testMatrixExtQueryReq);
        testMatrixPOList.stream().forEach(testMatrix -> {
            TestMatrixBO testMatrixBO = new TestMatrixBO();
            //1.id
            TestMatrixIdBO id = new TestMatrixIdBO();
            id.setTestMatrixId(testMatrix.getId());
            id.setTestMatrixNo(testMatrix.getMatrixNo());
            testMatrixBO.setId(id);

            //2.relationship
            assembleRelationship(testMatrixBO,testMatrix);

            //3.header
            TestMatrixHeaderBO header = new TestMatrixHeaderBO();
            header.setTestMatrixGroupId(testMatrix.getMatrixGroupId());
            //TODO 转化数组
            if(Func.isNotEmpty(testMatrixExtPOList)){
                TestMatrixExtPO testMatrixExtPO = testMatrixExtPOList.stream().filter(e -> e.getTestMatrixId().equals(testMatrix.getId())).findAny().orElse(null);
                if(Func.isNotEmpty(testMatrixExtPO) && Func.isNotEmpty(testMatrixExtPO.getApplicationFactorId()) && !testMatrixExtPO.getApplicationFactorId().equals("null")){
                    JSONArray jsonArray = JSONArray.parseArray(testMatrixExtPO.getApplicationFactorId());
                    header.setApplicationFactorIds(JSONObject.parseArray(jsonArray.toJSONString(),Long.class));
                }
            }
            testMatrixBO.setHeader(header);
            testMatrixList.add(testMatrixBO);
        });
        // 获取本地的TestMatrix数据
        return testMatrixList;
    }

    private void assembleRelationship(TestMatrixBO testMatrixBO,TestMatrixPO testMatrixPO){
        if(Func.isEmpty(testMatrixPO)){
            return;
        }
        TestMatrixRelationshipBO relationship = new TestMatrixRelationshipBO();
        TestMatrixParentBO parent = new TestMatrixParentBO();
        // testConditionGroupList
        if(Func.isNotEmpty(testMatrixPO.getTestConditionGroupID())){
            List<TestLineConditionGroupIdBO> testConditionGroupList = Lists.newArrayList();
            TestLineConditionGroupIdBO testLineConditionGroupId = new TestLineConditionGroupIdBO();
            testLineConditionGroupId.setTestConditionGroupId(testMatrixPO.getTestConditionGroupID());
            testConditionGroupList.add(testLineConditionGroupId);
            parent.setTestConditionGroupList(testConditionGroupList);
        }
        // testLineList
        TestLineIdRelBO testLineIdRel = new TestLineIdRelBO();
        testLineIdRel.setTestLineInstanceId(testMatrixPO.getTestLineInstanceID());
        parent.setTestLine(testLineIdRel);
        // testSampleList
        TestSampleIdBO testSampleID = new TestSampleIdBO();
        testSampleID.setTestSampleInstanceId(testMatrixPO.getTestSampleID());
        parent.setTestSample(testSampleID);
        relationship.setParent(parent);
        //
        testMatrixBO.setRelationship(relationship);
    }

    protected List<ReportBO> getReport(Set<String> testMatrixIds) {
        List<ReportBO> reportList = Lists.newArrayList();
        //Report Matrix信息
        ReportQueryReq reportIdReq = new ReportQueryReq();
        reportIdReq.setTestMatrixIds(testMatrixIds);
        BaseResponse<List<ReportMatrixRelPO>> reportMatrixRelPORsp = reportMatrixRelService.select(reportIdReq);
        if(reportMatrixRelPORsp.isFail() || Func.isEmpty(reportMatrixRelPORsp.getData())){
            return reportList;
        }
        Set<String> reportIds = new HashSet<>();
        for(ReportMatrixRelPO item : reportMatrixRelPORsp.getData()){
            reportIds.add(item.getReportId());
        }
        //查询report
        reportIdReq.setTestMatrixIds(null);
        reportIdReq.setReportIdList(reportIds);
        ReportQueryReq reportQueryReq = new ReportQueryReq();
        reportQueryReq.setReportIdList(reportIds);
        BaseResponse<List<ReportPO>> gpnReportPORsp = reportService.select(reportQueryReq);
        if(gpnReportPORsp.isFail() || Func.isEmpty(gpnReportPORsp.getData())){
            return reportList;
        }
        List<ReportPO> reportPOList = gpnReportPORsp.getData();
        reportPOList.stream().forEach(localReport->{
            ReportBO reportBO = new ReportBO();
            // 1.id
            ReportIdBO id = new ReportIdBO();
            id.setReportId(localReport.getId());
            id.setReportNo(localReport.getReportNo());
            id.setParentReportNo(localReport.getParentReportNo());
            reportBO.setId(id);
            // 2.relationship
            assembleRelationship(reportBO,reportMatrixRelPORsp.getData());
            reportList.add(reportBO);
        });
        return reportList;
    }
    private void assembleRelationship(ReportBO reportBO, List<ReportMatrixRelPO> reportMatrixList){
        ReportRelationBO relationship = new ReportRelationBO();
        ReportChildrenBO children = new ReportChildrenBO();
        // report matrix
        if(Func.isNotEmpty(reportMatrixList)){
            List<ReportMatrixRelPO> currentReportMatrixList = reportMatrixList.stream().filter(r->Func.equalsSafe(r.getReportId(),reportBO.getId().getReportId()))
                    .collect(Collectors.toList());
            if(Func.isNotEmpty(currentReportMatrixList)){
                List<ReportMatrixRelBO> reportMatrixRelList = Func.copy(currentReportMatrixList,ReportMatrixRelPO.class,ReportMatrixRelBO.class);
                children.setReportMatrixList(reportMatrixRelList);
            }
        }
        relationship.setChildren(children);
        reportBO.setRelationship(relationship);
    }

    private SubcontractReportMatrixQueryRsp dealCSPP(SubcontractReportMatrixQueryRsp rsp,SubcontractReportMatrixContext context){
        SubcontractReportMatrixQueryRsp result = new SubcontractReportMatrixQueryRsp();
        Lab lab = context.getLab();
        Func.copy(rsp,result);
        List<TestLineBO> testLineListResult = new ArrayList<>();
        //记录旧的tl instanceId 和 新的 tl instanceId 的关系 key -> old  value -> new
        Map<String, List<String>> newTestLineInstanceMap = new HashMap<>();
        Map<String, List<String>> newTestMatrixIdMap = new HashMap<>();
        //首先获取TLList 判断是否为csPP
        if(Func.isNotEmpty(result.getTestLineList())) {
            List<TestLineBO> testLineList = result.getTestLineList();
            List<TestLineBO> csPPTestLine = testLineList.stream().filter(
                    e -> Func.isNotEmpty(e.getHeader().getTestLineType())
                    && TestLineType.check(e.getHeader().getTestLineType(),TestLineType.CSPP)).collect(Collectors.toList());
            List<TestLineBO> noCsPPTestLine = testLineList.stream().filter(
                    e -> Func.isNotEmpty(e.getHeader().getTestLineType())
                            && !TestLineType.check(e.getHeader().getTestLineType(),TestLineType.CSPP)).collect(Collectors.toList());
            log.info("csPPList为:{}",csPPTestLine);
            if(Func.isNotEmpty(csPPTestLine)){
                //对每一个csPP TestLine处理
                for(TestLineBO cspp : csPPTestLine){
                    //获取csPP关联的testLine列表
                    QueryPpTestLineReq queryPpTestLineReq = new QueryPpTestLineReq();
                    queryPpTestLineReq.setCallerBU(context.getProductLineCode());
                    queryPpTestLineReq.setLabId(lab.getLabId());
                    queryPpTestLineReq.setPpVersionId(cspp.getId().getTestLineVersionId());
                    List<QueryPpTestLineRsp> csPPTestLineList = trimsClient.getPpTestLineList(queryPpTestLineReq);
                    List<String> newTLInstanceIds = new ArrayList<>();
                    //对csPP进行拆解为TestLine
                    for(QueryPpTestLineRsp item : csPPTestLineList){
                        TestLineBO testLineBO = new TestLineBO();
                        //TestLineId节点
                        TestLineIdBO id = new TestLineIdBO();
                        id.setTestLineInstanceId(UUID.randomUUID().toString());
                        newTLInstanceIds.add(id.getTestLineInstanceId());
                        id.setTestLineBaseId(item.getTestLineBaseId());
                        id.setTestLineId(item.getTestLineId());
                        id.setTestLineVersionId(item.getTestLineVersionId());
                        id.setTestItemNo(cspp.getId().getTestItemNo());
                        testLineBO.setId(id);
                        //parallel节点
                        TestLineRelationshipBO relationship = cspp.getRelationship();
                        testLineBO.setRelationship(relationship);
                        //ppTestLineRel节点 -- 重新组装
                        List<PPTestLineRelBO> ppTestLineRelList = new ArrayList<>();
                        PPTestLineRelBO ppTestLineRelBO = new PPTestLineRelBO();
                        //ppTestLineRel  - id节点
                        PPTestLineIdBO ppTestLineId = new PPTestLineIdBO();
                        ppTestLineId.setPpVersionId(item.getPpVersionId());
                        ppTestLineId.setPpBaseId(item.getPpBaseId());
                        ppTestLineId.setRootPPBaseId(item.getRootPpBaseId());
                        ppTestLineId.setPpNo(item.getPpNo());
                        ppTestLineRelBO.setId(ppTestLineId);
                        //ppTestLineRel  - header节点
                        PPTestLineRelHeaderBO header = new PPTestLineRelHeaderBO();
                        //header.setPpNo(item.getPpNo());
                        header.setPpNotes(item.getPpNotes());
                        header.setPpName(item.getPpName());
                        header.setAid(item.getAid());
                        ppTestLineRelBO.setHeader(header);
                        ppTestLineRelList.add(ppTestLineRelBO);
                        testLineBO.setPpTestLineRelList(ppTestLineRelList);
                        //header不组装
                        //citation重新组装
                        CitationBO citation = new CitationBO();
                        citation.setCitationBaseId(item.getCitationBaseId());
                        citation.setCitationId(item.getCitationId());
                        citation.setCitationVersionId(item.getCitationVersionId());
                        citation.setCitationType(item.getCitationType());
                        citation.setCitationSectionId(item.getCitationSectionId());
                        citation.setCitationSectionName(item.getCitationSectionName());
                        citation.setCitationName(item.getCitationName());
                        citation.setCitationFullName(item.getCitationFullName());
                        testLineBO.setCitation(citation);
                        //TestLine 整体节点
                        testLineListResult.add(testLineBO);
                    }
                    newTestLineInstanceMap.put(cspp.getId().getTestLineInstanceId(),newTLInstanceIds);
                }
                testLineListResult.addAll(noCsPPTestLine);
                result.setTestLineList(testLineListResult);
                //处理TestMatrix List
                List<TestMatrixBO> testMatrixListResult = new ArrayList<>();
                if(Func.isNotEmpty(result.getTestMatrixList())){
                    List<TestMatrixBO> testMatrixList = result.getTestMatrixList();
                    //重新组装 TestMatrix
                    for(TestMatrixBO item : testMatrixList){
                        String testLineInstanceId = item.getRelationship().getParent().getTestLine().getTestLineInstanceId();
                        if(newTestLineInstanceMap.containsKey(testLineInstanceId)){
                            List<String> newTestMatrixIds = new ArrayList<>();
                            //判断是否为cspp拆分的TL 如果是，这个matrix根据 newTL instance 拆分成多个Matrix
                            List<String> newTestLineInstanceIds = newTestLineInstanceMap.get(testLineInstanceId);
                            for(String newTLInstanceId : newTestLineInstanceIds){
                                TestMatrixBO testMatrixBO = new TestMatrixBO();
                                //重组 TestMatrix id 节点
                                TestMatrixIdBO id = new TestMatrixIdBO();
                                id.setTestMatrixId(UUID.randomUUID().toString());
                                newTestMatrixIds.add(id.getTestMatrixId());
                                testMatrixBO.setId(id);
                                //重组 TestMatrix header节点
                                TestMatrixHeaderBO header = item.getHeader();
                                testMatrixBO.setHeader(header);
                                //重组 TestMatrix relationship 节点
                                TestMatrixRelationshipBO relationship = new TestMatrixRelationshipBO();
                                TestMatrixParentBO parent = new TestMatrixParentBO();
                                TestLineIdRelBO testLine = new TestLineIdRelBO();
                                testLine.setTestLineInstanceId(newTLInstanceId);
                                parent.setTestLine(testLine);
                                parent.setTestSample(item.getRelationship().getParent().getTestSample());
                                relationship.setParent(parent);
                                testMatrixBO.setRelationship(relationship);
                                testMatrixListResult.add(testMatrixBO);
                            }
                            newTestMatrixIdMap.put(item.getId().getTestMatrixId(),newTestMatrixIds);
                        } else {
                            testMatrixListResult.add(item);
                        }
                    }
                }
                result.setTestMatrixList(testMatrixListResult);
                //处理Report List
                List<ReportBO> reportListResult = new ArrayList<>();
                if(Func.isNotEmpty(result.getReportList())){
                    List<ReportBO> reportList = result.getReportList();
                    for(ReportBO item : reportList){
                        //对每一个Report特殊处理
                        if(Func.isNotEmpty(item.getRelationship()) && Func.isNotEmpty(item.getRelationship().getChildren()) && Func.isNotEmpty(item.getRelationship().getChildren().getReportMatrixList())){
                            List<ReportMatrixRelBO> matrixList = item.getRelationship().getChildren().getReportMatrixList();
                            List<ReportMatrixRelBO> newMatrixList = new ArrayList<>();
                            for(ReportMatrixRelBO reportMatrix : matrixList){
                                if(Func.isNotEmpty(reportMatrix.getTestMatrixId())){
                                    String matrixId = reportMatrix.getTestMatrixId();
                                    //存在拆分的matrix - 替换
                                    if(newTestMatrixIdMap.containsKey(matrixId)){
                                        List<String> newTestMatrixIds = newTestMatrixIdMap.get(matrixId);
                                        for(String newMatrixId : newTestMatrixIds){
                                            ReportMatrixRelBO reportMatrixRelBO = new ReportMatrixRelBO();
                                            reportMatrixRelBO.setTestMatrixId(newMatrixId);
                                            newMatrixList.add(reportMatrixRelBO);
                                        }
                                    } else{
                                        newMatrixList.add(reportMatrix);
                                    }
                                }
                                item.getRelationship().getChildren().setReportMatrixList(newMatrixList);
                            }
                        }
                        reportListResult.add(item);
                    }
                }
                result.setReportList(reportListResult);
            }
        }
        return result;
    }


}
