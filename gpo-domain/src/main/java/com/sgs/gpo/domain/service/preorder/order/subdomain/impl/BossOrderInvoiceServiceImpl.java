package com.sgs.gpo.domain.service.preorder.order.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.BossOrderInvoiceMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.BossOrderInvoicePO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.payment.BossOrderInvoiceDTO;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IBossOrderInvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;


@Service
@Slf4j
public class BossOrderInvoiceServiceImpl extends ServiceImpl<BossOrderInvoiceMapper, BossOrderInvoicePO> implements IBossOrderInvoiceService {

    @Override
    public BaseResponse<List<BossOrderInvoicePO>> getInvoiceByOrderNo(String orderNo) {
        if(Func.isEmpty(orderNo)){
            return BaseResponse.newSuccessInstance(true);
        }
        List<BossOrderInvoicePO> bossOrderInvoicePOS = baseMapper.getInvoiceByOrderNo(orderNo);
        return BaseResponse.newSuccessInstance(bossOrderInvoicePOS);
    }

    @Override
    public BaseResponse<List<BossOrderInvoiceDTO>> getBossInvoiceByOrderNo(String orderNo) {
        if(Func.isEmpty(orderNo)){
            return BaseResponse.newSuccessInstance(true);
        }
        List<BossOrderInvoiceDTO> bossOrderInvoicePOS = baseMapper.getInvoiceInfoByOrderNo(orderNo);
        return BaseResponse.newSuccessInstance(bossOrderInvoicePOS);
    }
    @Override
    public Long getNoInvoiceQuotationCount(String orderNo) {
        if(Func.isEmpty(orderNo)){
            return null;
        }
        Long noInvoiceQuotationCount = baseMapper.getNoInvoiceQuotationCount(orderNo);
        return noInvoiceQuotationCount;
    }

    @Override
    public int batchUpdatePaidAmountByInvoiceNo(List<BossOrderInvoicePO> bossOrderInvoicePOList) {
        return baseMapper.batchUpdatePaidAmountByInvoiceNo(bossOrderInvoicePOList);
    }

    @Override
    public List<String> getOrderNoByInvoice(List<String> invoiceNos) {
        return baseMapper.getOrderNoByInvoiceNo(invoiceNos);
    }

    @Override
    public int insertBatch(List<BossOrderInvoicePO> bossOrderInvoiceInfoPOS) {
        return baseMapper.insertBatch(bossOrderInvoiceInfoPOS);
    }

    @Override
    public Boolean getDataByBossOrderAndInvoice(String bossOrderNo, String invoiceNo) {
        return baseMapper.getDataByBossOrderAndInvoice(bossOrderNo,invoiceNo);
    }

    @Override
    public int updatePaidAmountByInvoiceNoBatch(List<BossOrderInvoicePO> bossOrderInvoicePOList) {
        return baseMapper.updatePaidAmountByInvoiceNoBatch(bossOrderInvoicePOList);
    }

    @Override
    public List<BossOrderInvoicePO> getBossOrderNoByInvoice(String invoiceNo) {
        if(Func.isNotEmpty(invoiceNo)){
            LambdaQueryWrapper<BossOrderInvoicePO> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(BossOrderInvoicePO::getInvoiceNo,invoiceNo);
            return baseMapper.selectList(queryWrapper);
        }
        return null;
    }

    @Override
    public List<BossOrderInvoiceDTO> getBossInvoiceByOrderNos(Set<String> orderNoList) {
        if(Func.isEmpty(orderNoList)){
            return Lists.newArrayList();
        }
        return baseMapper.getInvoiceInfoByOrderNos(orderNoList);
    }
}
