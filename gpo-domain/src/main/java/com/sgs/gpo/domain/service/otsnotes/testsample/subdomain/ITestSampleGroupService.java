package com.sgs.gpo.domain.service.otsnotes.testsample.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.TestSampleGroupPO;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;

import java.util.List;

public interface ITestSampleGroupService extends IService<TestSampleGroupPO> {

    BaseResponse<List<TestSampleGroupPO>> query(TestSampleQueryReq testSampleQueryReq);

}
