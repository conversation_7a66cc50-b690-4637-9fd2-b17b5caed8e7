package com.sgs.gpo.domain.service.preorder.order.subdomain;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.SLOrderPO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;

import java.util.List;

/**
 * <AUTHOR>
 * @title: ISlOrderService
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/10 17:02
 */
public interface ISLOrderService extends IService<SLOrderPO> {
    BaseResponse<List<SLOrderPO>> select(OrderIdReq orderIdReq);
}
