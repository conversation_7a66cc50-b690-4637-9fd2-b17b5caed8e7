package com.sgs.gpo.domain.service.otsnotes.orderlanguage.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.orderlanguage.OrderLanguageMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.orderlanguage.OrderLanguagePO;
import com.sgs.gpo.domain.service.otsnotes.orderlanguage.IOrderLanguageService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/6/30 10:17
 */
@Service
public class OrderLanguageServiceImpl extends ServiceImpl<OrderLanguageMapper, OrderLanguagePO> implements IOrderLanguageService {

    @Override
    public BaseResponse<List<OrderLanguagePO>> queryByOrderId(Set<String> orderIdList) {
        if(Func.isEmpty(orderIdList)){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"orderIdList"});
        }
        QueryWrapper<OrderLanguagePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(OrderLanguagePO.COLUMN.ORDER_ID,orderIdList);
        return BaseResponse.newSuccessInstance(baseMapper.selectList(queryWrapper));
    }

}
