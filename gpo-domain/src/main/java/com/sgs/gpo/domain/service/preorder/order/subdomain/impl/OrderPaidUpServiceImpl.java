package com.sgs.gpo.domain.service.preorder.order.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.payment.paidup.OrderPaidUpMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderPaidUpPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.PaidUpPO;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IOrderPaidUpService;
import org.springframework.stereotype.Service;

import java.util.Set;


@Service
public class OrderPaidUpServiceImpl extends ServiceImpl<OrderPaidUpMapper, OrderPaidUpPO> implements IOrderPaidUpService {

    @Override
    public void inactiveByIdList(Set<String> idList) {
        if(Func.isEmpty(idList)){
            return;
        }
        LambdaUpdateWrapper<OrderPaidUpPO> updateWrapper = new LambdaUpdateWrapper<>();
        OrderPaidUpPO orderPaidUpPO = new OrderPaidUpPO();
        orderPaidUpPO.setActiveIndicator(ActiveType.Disable.getStatus());
        updateWrapper.in(OrderPaidUpPO::getPaidUpId,idList);
        baseMapper.update(orderPaidUpPO,updateWrapper);
    }
}