package com.sgs.gpo.domain.service.otsnotes.testline.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Sets;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testline.PPTestLineRelationshipMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testline.PPTestLineRelationshipPO;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.IPPTestLineRelationshipService;
import com.sgs.gpo.facade.model.otsnotes.testline.req.PPTestLineQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;


@Service
@Slf4j
public class PPTestLineRelationshipServiceImpl extends ServiceImpl<PPTestLineRelationshipMapper, PPTestLineRelationshipPO> implements IPPTestLineRelationshipService {

    @Override
    public Set<String> getTestLineByCSPP(PPTestLineQueryReq ppTestLineQueryReq) {

        Set<String> testLineIds = Sets.newHashSet();
        if(Func.isEmpty(ppTestLineQueryReq.getOrderInstanceId()) || Func.isEmpty(ppTestLineQueryReq.getParentTestLineId())){
            return testLineIds;
        }
        LambdaQueryWrapper<PPTestLineRelationshipPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PPTestLineRelationshipPO::getGeneralOrderInstanceID,ppTestLineQueryReq.getOrderInstanceId());
        List<PPTestLineRelationshipPO> ppTestLineRelationshipPOList = baseMapper.selectList(wrapper);
        if(Func.isNotEmpty(ppTestLineRelationshipPOList)){
            String parentTestLineId = ppTestLineQueryReq.getParentTestLineId();
            ppTestLineRelationshipPOList.stream().forEach(ppTestLine ->{
                String extFields = ppTestLine.getExtFields();
                if(Func.isNotEmpty(extFields)){
                    //解析
                    JSONObject jsonObject = JSON.parseObject(extFields);
                    String parentId = jsonObject.getString("parentTestLineInstanceId");
                    if(Func.isNotEmpty(parentId) && parentId.equals(parentTestLineId)){
                        testLineIds.add(ppTestLine.getTestLineInstanceID());
                    }
                }
            });
        }
        return testLineIds;
    }

    @Override
    public List<PPTestLineRelationshipPO> getPPTestLineRelationshipPOList(Set<String> quotationIds) {
        if (Func.isEmpty(quotationIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PPTestLineRelationshipPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PPTestLineRelationshipPO::getQuotationTestlineInstanceID, quotationIds);
        return baseMapper.selectList(wrapper);
    }
}
