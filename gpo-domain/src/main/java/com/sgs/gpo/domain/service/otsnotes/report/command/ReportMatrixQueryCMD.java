package com.sgs.gpo.domain.service.otsnotes.report.command;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.test.citation.CitationBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.trims.labsection.LabSectionBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.TestSamplePO;
import com.sgs.gpo.domain.service.otsnotes.report.context.ReportContext;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportMatrixRelService;
import com.sgs.gpo.domain.service.otsnotes.report.subdomain.IReportService;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineDomainService;
import com.sgs.gpo.domain.service.otsnotes.testsample.subdomain.ITestSampleService;
import com.sgs.gpo.facade.model.otsnotes.reportmatrix.vo.ReportMatrixVO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;
import com.sgs.gpo.facade.model.report.bo.ReportMatrixRelBO;
import com.sgs.gpo.facade.model.report.req.ReportMatrixQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: ReportMatrixQueryCMD
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2024/3/25 15:16
 */
@Service
@Slf4j
public class ReportMatrixQueryCMD extends BaseCommand<ReportContext<ReportMatrixQueryReq>> {
    @Autowired
    private IReportMatrixRelService reportMatrixRelService;
    @Autowired
    private ITestLineDomainService testLineDomainService;
    @Autowired
    private IReportService reportService;
    @Autowired
    private ITestSampleService testSampleService;

    @Override
    public BaseResponse validateParam(ReportContext<ReportMatrixQueryReq> context) {
        Assert.isTrue(Func.isNotEmpty(context.getParam()), "common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        Assert.isTrue(Func.isNotEmpty(context.getParam().getReportNoList()), "common.param.miss", new Object[]{"ReportId"});
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(ReportContext<ReportMatrixQueryReq> context) {
        List<ReportMatrixRelBO> reportMatrixRelBOList = context.getReportMatrixRelBOList();
        List<TestLineBO> testLineBOList = context.getTestLineBOList();
        List<TestSamplePO> testSamplePOList = context.getTestSamplePOList();
        List<ReportMatrixVO> reportMatrixVOList = new ArrayList<>();
        if (Func.isNotEmpty(reportMatrixRelBOList)) {
            List<ReportMatrixRelBO> distinctList = reportMatrixRelBOList.stream()
                    .collect(Collectors.toMap(e -> e.getTestLineInstanceId(), e -> e, (e1, e2) -> e1, LinkedHashMap::new))
                    .values()
                    .stream()
                    .collect(Collectors.toList());
            for (ReportMatrixRelBO reportMatrixRelBO : distinctList) {
                String testLineInstanceId = reportMatrixRelBO.getTestLineInstanceId();
//            testLineReportMatrixMap.forEach((key, matrixRelBOList) -> {
                ReportMatrixVO reportMatrixVO = new ReportMatrixVO();
                reportMatrixVO.setTestLineInstanceId(testLineInstanceId);
                List<ReportMatrixRelBO> matrixRelBOList = reportMatrixRelBOList.stream().filter(item -> Func.equalsSafe(item.getTestLineInstanceId(), reportMatrixRelBO.getTestLineInstanceId())).collect(Collectors.toList());
                if (Func.isNotEmpty(testLineBOList)) {
                    TestLineBO testLineBO = testLineBOList.stream().filter(item -> Func.equalsSafe(item.getTestLineInstanceId(), testLineInstanceId)).findAny().orElse(null);
                    reportMatrixVO.setOrderNo(testLineBO.getOrderNo());
                    if (Func.isNotEmpty(testLineBO)) {
                        CitationBO citation = testLineBO.getCitation();
                        if (Func.isNotEmpty(citation)) {
                            reportMatrixVO.setCitationFullName(citation.getCitationFullName());
                        }
                        reportMatrixVO.setTestItemNo(testLineBO.getTestItemNo());
                        reportMatrixVO.setTestItemName(testLineBO.getEvaluationName());
                        reportMatrixVO.setTestLineStatus(testLineBO.getTestLineStatus());
                        reportMatrixVO.setTestLineStatusDisplay(com.sgs.otsnotes.facade.model.enums.TestLineStatus.getMessage(testLineBO.getTestLineStatus()));
                        if (Func.isNotEmpty(testSamplePOList) && Func.isNotEmpty(matrixRelBOList)) {
                            Set<String> testSampleInstanceIdList = matrixRelBOList.stream().map(ReportMatrixRelBO::getTestSampleId).filter(Func::isNotEmpty).collect(Collectors.toSet());
                            if (Func.isNotEmpty(testSampleInstanceIdList)) {
                                String sampleNos = testSamplePOList.stream().filter(item -> testSampleInstanceIdList.contains(item.getId())).map(TestSamplePO::getSampleNo).sorted().collect(Collectors.joining(","));
                                reportMatrixVO.setSampleNos(sampleNos);
                            }
                        }
                        List<LabSectionBO> labSectionList = testLineBO.getLabSectionList();
                        if (Func.isNotEmpty(labSectionList)) {
                            String labSectionCodes = labSectionList.stream().sorted(Comparator.comparing(LabSectionBO::getLabSectionSeq, Comparator.nullsLast(Integer::compareTo))).map(LabSectionBO::getLabSectionCode).collect(Collectors.joining(","));
                            reportMatrixVO.setLabSectionCodes(labSectionCodes);
                        }
                    }
                }
                reportMatrixVOList.add(reportMatrixVO);
            }
        }
        return BaseResponse.newSuccessInstance(reportMatrixVOList);
    }

    @Override
    public BaseResponse before(ReportContext<ReportMatrixQueryReq> context) {
        ReportMatrixQueryReq reportMatrixQueryReq = context.getParam();

        BaseResponse<List<ReportMatrixRelBO>> reportMatrixBORsp = reportMatrixRelService.queryReportMatrix(reportMatrixQueryReq);
        if (reportMatrixBORsp.isSuccess()) {
            List<ReportMatrixRelBO> reportMatrixRelBOList = reportMatrixBORsp.getData();
            context.setReportMatrixRelBOList(reportMatrixRelBOList);
            Set<String> testSampleInstanceIdList = reportMatrixRelBOList.stream().map(ReportMatrixRelBO::getTestSampleInstanceId).collect(Collectors.toSet());
            if (Func.isNotEmpty(testSampleInstanceIdList)) {
                TestSampleQueryReq testSampleQueryReq = new TestSampleQueryReq();
                testSampleQueryReq.setTestSampleInstanceIdList(testSampleInstanceIdList);
                BaseResponse<List<TestSamplePO>> testSamplePORsp = testSampleService.select(testSampleQueryReq);
                if (testSamplePORsp.isSuccess()) {
                    List<TestSamplePO> testSamplePOList = testSamplePORsp.getData();
                    context.setTestSamplePOList(testSamplePOList);
                }
            }


            Set<String> testLineInstanceIdList = reportMatrixRelBOList.stream().map(ReportMatrixRelBO::getTestLineInstanceId).collect(Collectors.toSet());
            if (Func.isNotEmpty(testLineInstanceIdList)) {
                OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
                orderTestLineReq.setTestLineInstanceIdList(testLineInstanceIdList);
                BaseResponse<List<TestLineBO>> testLineRsp = testLineDomainService.queryTestLineLabSection(orderTestLineReq);
                if (testLineRsp.isSuccess()) {
                    context.setTestLineBOList(testLineRsp.getData());
                }
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }
}
