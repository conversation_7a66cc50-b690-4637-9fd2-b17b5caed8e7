package com.sgs.gpo.domain.service.otsnotes.report.context;


import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.report.ReportTemplatePO;
import com.sgs.gpo.facade.model.digital.req.GenerateReportRequest;
import com.sgs.gpo.integration.digital.req.v2.DigitalReportReq;
import com.sgs.preorder.facade.model.dto.customer.CustomerInstanceDTO;
import com.sgs.preorder.facade.model.dto.order.OrderAllDTO;
import com.sgs.preorder.facade.model.dto.order.OrderTrfDTO;
import com.sgs.preorder.facade.model.dto.order.ProductInstanceDTO;
import com.sgs.preorder.facade.model.info.TestRequestInfo;
import com.sgs.preorder.facade.model.rsp.ReportReceiverRsp;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class ReportGenerateContext<Input> extends BaseContext<Input, ReportBO> {

    /**
     * 生成报告使用的上下文参数
     */
    private ReportTemplatePO reportTemplateCn;
    private ReportTemplatePO reportTemplateEn;
    private OrderBO order;

    private List<DigitalReportReq> digitalReportReqList;
    // Report关联的TestLine的集合
    private Set<String> testLineInsIdList;
    // Report关联的TestMatrix的集合
    private Set<String> testMatrixIdList;
    // Report关联的TestSampleId的集合
    private Set<String> testSampleIdList;
    protected boolean chineseFlag = false;

}
