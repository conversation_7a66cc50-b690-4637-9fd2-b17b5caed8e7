package com.sgs.gpo.domain.service.preorder.order.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.OrderReportReceiverMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderReportReceiverPO;
import com.sgs.gpo.domain.service.preorder.order.subdomain.IOrderReportReceiverService;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class OrderReportReceiverServiceImpl extends ServiceImpl<OrderReportReceiverMapper, OrderReportReceiverPO>
        implements IOrderReportReceiverService {

    @Override
    public BaseResponse<List<OrderReportReceiverPO>> select(OrderIdReq orderIdReq) {
        if(Func.isEmpty(orderIdReq)||Func.isEmpty(orderIdReq.getOrderIdList())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        LambdaQueryWrapper<OrderReportReceiverPO> wrapper= Wrappers.<OrderReportReceiverPO>lambdaQuery()
                .in(OrderReportReceiverPO::getGeneralOrderId,orderIdReq.getOrderIdList());
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }
}
