package com.sgs.gpo.domain.service.otsnotes.aitask.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.aitask.AiTaskMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.aitask.AiTaskPO;
import com.sgs.gpo.domain.service.otsnotes.aitask.IAiTaskService;
import com.sgs.gpo.facade.model.aitask.AiTaskQueryReq;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class AiTaskServiceImpl extends ServiceImpl<AiTaskMapper, AiTaskPO> implements IAiTaskService {

    @Override
    public BaseResponse<List<AiTaskPO>> select(AiTaskQueryReq taskQueryReq) {
        if (Func.isEmpty(taskQueryReq) || Func.isAllEmpty(taskQueryReq.getObjectNo(), taskQueryReq.getObjectNoList())) {
            return BaseResponse.newFailInstance("Param  missing.");
        }
        LambdaQueryWrapper<AiTaskPO> wrapper = Wrappers.lambdaQuery();
        if (Func.isNotEmpty(taskQueryReq.getObjectNoList())) {
            wrapper.in(AiTaskPO::getObjectNo, taskQueryReq.getObjectNoList());
        }
        if (Func.isNotEmpty(taskQueryReq.getObjectNo())) {
            wrapper.eq(AiTaskPO::getObjectNo, taskQueryReq.getObjectNo());
        }
        if (Func.isNotEmpty(taskQueryReq.getLabId())) {
            wrapper.eq(AiTaskPO::getLabId, taskQueryReq.getLabId());
        }
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }
}
