package com.sgs.gpo.domain.service.otsnotes.testline.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.i18n.util.MessageUtil;
import com.sgs.framework.model.common.object.ObjectIdBO;
import com.sgs.framework.model.common.object.StatusControlBO;
import com.sgs.framework.model.common.object.process.ObjectProcessBO;
import com.sgs.framework.model.common.object.process.ObjectProcessHeaderBO;
import com.sgs.framework.model.test.testline.v2.TestLineBO;
import com.sgs.framework.model.test.testline.v2.TestLineHeaderBO;
import com.sgs.framework.model.test.testline.v2.TestLineIdBO;
import com.sgs.framework.model.workflow.WorkflowRuleBO;
import com.sgs.framework.open.platform.model.req.ObjectStatusVerifyReq;
import com.sgs.framework.open.platform.model.req.StatusControlReq;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testline.TestLineInstancePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.TestMatrixPO;
import com.sgs.gpo.domain.service.otsnotes.testline.ITestLineProcessService;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.ITestLineService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.ITestMatrixDomainService;
import com.sgs.gpo.domain.service.otsnotes.testmatrix.subdomain.ITestMatrixService;
import com.sgs.gpo.domain.service.setting.object.IObjectProcessDomainService;
import com.sgs.gpo.domain.service.setting.workflowrule.command.WorkflowRuleQueryCMD;
import com.sgs.gpo.domain.service.setting.workflowrule.context.WorkflowRuleContext;
import com.sgs.gpo.facade.model.otsnotes.common.rsp.StatusControlRsp;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineProcessReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineUpdateStatusReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixProcessReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.gpo.facade.model.setting.object.submodel.process.req.ObjectProcessStatusReq;
import com.sgs.gpo.facade.model.workflow.req.WorkflowRuleReq;
import com.sgs.otsnotes.facade.model.enums.TestLineStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/10 09:39
 */
@Service
@Slf4j
public class TestLineProcessServiceImpl implements ITestLineProcessService {

    @Autowired
    private IObjectProcessDomainService objectProcessDomainService;
    @Autowired
    private MessageUtil messageUtil;
    @Autowired
    private ITestLineService testLineService;
    @Autowired
    private ITestMatrixDomainService testMatrixDomainService;
    @Autowired
    private ITestMatrixService testMatrixService;

    @Override
    public BaseResponse<List<StatusControlBO<TestLineBO>>> statusControl(StatusControlReq<TestLineIdBO> statusControlReq) {
        return null;
    }

    @Override
    public BaseResponse<List<TestLineBO>> statusVerify(ObjectStatusVerifyReq<TestLineBO, TestLineIdBO> orderStatusVerifyReq) {
        BaseResponse<List<ObjectProcessBO>> objectProcessRsp = this.getProcess();

        Set<Integer> statusList = objectProcessRsp.getData().stream().map(ObjectProcessBO::getHeader)
                .filter(header -> Func.isEmpty(orderStatusVerifyReq.getStatusTypeList()) ? true : orderStatusVerifyReq.getStatusTypeList().contains(header.getStatusType()))
                .filter(header -> Func.isEmpty(orderStatusVerifyReq.getEditableFlag()) ? true : Func.equalsSafe(orderStatusVerifyReq.getEditableFlag(), header.getEditableFlag()))
                .filter(header -> Func.isEmpty(orderStatusVerifyReq.getValidFlag()) ? true : Func.equalsSafe(orderStatusVerifyReq.getValidFlag(), header.getValidFlag()))
                .map(ObjectProcessHeaderBO::getStatusId).collect(Collectors.toSet());

        List<TestLineBO> errorList = orderStatusVerifyReq.getBoList().stream().filter(orderBO -> statusList.contains(orderBO.getHeader().getTestLineStatus())).collect(Collectors.toList());

        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setData(errorList);
        if (Func.isEmpty(errorList)) {
            baseResponse.setStatus(ResponseCode.SUCCESS.getCode());
        } else {
            baseResponse.setStatus(ResponseCode.FAIL.getCode());
            Set<String> errorNoList = errorList.stream().map(TestLineBO::getId).map(TestLineIdBO::getTestItemNo).collect(Collectors.toSet());
            baseResponse.setMessage(messageUtil.get("common.process.status.not.allowed", new Object[]{errorNoList.toString()}));
        }
        return baseResponse;
    }

    @Override
    public BaseResponse<List<ObjectProcessBO>> getProcess() {
        ObjectProcessStatusReq orderObjectProcessStatusReq = new ObjectProcessStatusReq();
        orderObjectProcessStatusReq.setLabId(SystemContextHolder.getLab().getLabId());
        ObjectIdBO objectIdBO = new ObjectIdBO();
        objectIdBO.setObjectCode(Constants.OBJECT.TEST_LINE.OBJECT_CODE);
        orderObjectProcessStatusReq.setObject(objectIdBO);
        BaseResponse<List<ObjectProcessBO>> orderObjectProcess = objectProcessDomainService.queryStatus(orderObjectProcessStatusReq);
        return orderObjectProcess;
    }

    @Override
    public BaseResponse<List<TestLineBO>> queryStatus(List<TestLineIdBO> testLineIdBOS) {
        Assert.isTrue(Func.isNotEmpty(testLineIdBOS), "common.param.miss", new Object[]{"orderId/orderNo"});

        List<TestLineBO> testLineBOList = Lists.newArrayList();

        Set<String> testLineIdList = testLineIdBOS.stream().map(TestLineIdBO::getTestLineInstanceId).collect(Collectors.toSet());
        Set<String> testItemNoList = testLineIdBOS.stream().map(TestLineIdBO::getTestItemNo).collect(Collectors.toSet());

        LambdaQueryWrapper<TestLineInstancePO> queryWrapper = new LambdaQueryWrapper<>();
        if (Func.isNotEmpty(testLineIdList)) {
            queryWrapper.in(TestLineInstancePO::getId, testLineIdList);
        } else if (Func.isNotEmpty(testItemNoList)) {
            queryWrapper.in(TestLineInstancePO::getTestItemNo, testItemNoList);
        }
        List<TestLineInstancePO> testLineInstancePOList = testLineService.list(queryWrapper);
        if (Func.isNotEmpty(testLineInstancePOList)) {
            testLineInstancePOList.forEach(testLineInstancePO -> {
                TestLineBO testLineBO = new TestLineBO();

                TestLineIdBO testLineIdBO = new TestLineIdBO();
                testLineIdBO.setTestLineInstanceId(testLineInstancePO.getId());
                testLineIdBO.setTestItemNo(testLineInstancePO.getTestItemNo());
                testLineBO.setId(testLineIdBO);

                TestLineHeaderBO testLineHeaderBO = new TestLineHeaderBO();
                testLineHeaderBO.setTestLineStatus(testLineInstancePO.getTestLineStatus());
                testLineBO.setHeader(testLineHeaderBO);

                testLineBOList.add(testLineBO);
            });
        }
        return BaseResponse.newSuccessInstance(testLineBOList);
    }

    @Override
    public BaseResponse<StatusControlRsp> toNa(TestLineProcessReq testLineProcessReq) {
        return processTestLine(testLineProcessReq);
    }

    private BaseResponse<StatusControlRsp> processTestLine(TestLineProcessReq testLineProcessReq) {
        if (testLineProcessReq == null || Func.isEmpty(testLineProcessReq.getObjectIdList())) {
            return BaseResponse.newFailInstance("common.param.miss", new Object[]{Constants.TERM.REQUEST.getCode()});
        }
        StatusControlRsp statusControlRsp = new StatusControlRsp();
        String action = testLineProcessReq.getAction();
        if(testLineProcessReq.isNeedStatusControl()){
            BaseResponse<StatusControlRsp> statusControlRspBaseResponse = this.statusControl(testLineProcessReq);
            if (statusControlRspBaseResponse.isFail()) {
                return statusControlRspBaseResponse;
            }
            statusControlRsp = statusControlRspBaseResponse.getData();
        }else{
            statusControlRsp.setAllowObjectIdList(testLineProcessReq.getObjectIdList());
        }

        if (Func.isEmpty(statusControlRsp)) {
            return BaseResponse.newFailInstance("Status control response is empty");
        }

        Set<String> allowObjectIdList = statusControlRsp.getAllowObjectIdList();
        testLineProcessReq.setObjectIdList(allowObjectIdList);
        if (Func.isNotEmpty(allowObjectIdList)) {
            TestLineUpdateStatusReq testLineUpdateStatusReq = new TestLineUpdateStatusReq();
            testLineUpdateStatusReq.setTestLineInstanceIdList(allowObjectIdList);
            testLineUpdateStatusReq.setTestLineStatus(testLineProcessReq.getNewStatus());
            testLineUpdateStatusReq.setTrigger(testLineProcessReq.getTrigger());
            testLineUpdateStatusReq.setAction(testLineProcessReq.getAction());
            testLineUpdateStatusReq.setRemark(testLineProcessReq.getRemark());
            BaseResponse<List<TestLineInstancePO>> updateStatusResponse = testLineService.batchUpdateTestLineStatus(testLineUpdateStatusReq);
            if (updateStatusResponse.isFail()) {
                return BaseResponse.newFailInstance(updateStatusResponse.getMessage());
            }
        }
        // Step 3: Handle  status change
        BaseResponse matrixStatusChangeRsp = this.onTestLineStatusChange(testLineProcessReq);
        if (matrixStatusChangeRsp.isFail()) {
            log.error("TestLine {} Fail, {}, {}", action, matrixStatusChangeRsp.getMessage(), Func.toJson(testLineProcessReq));
        }

        // Step 4: Return success response
        return BaseResponse.newSuccessInstance(statusControlRsp);
    }

    private BaseResponse onTestLineStatusChange(TestLineProcessReq testLineProcessReq) {
        if (Func.isEmpty(testLineProcessReq) || Func.isEmpty(testLineProcessReq.getObjectIdList())) {
            return BaseResponse.newSuccessInstance(true);
        }
        triggerMatrixStatusChange(testLineProcessReq);
        return BaseResponse.newSuccessInstance(true);
    }

    private BaseResponse triggerMatrixStatusChange(TestLineProcessReq testLineProcessReq) {
        if (!testLineProcessReq.isTriggerMatrix()) {
            return BaseResponse.newSuccessInstance(true);
        }
        Set<String> testLineInstanceIdList = testLineProcessReq.getObjectIdList();
        TestMatrixQueryReq testMatrixQueryReq = new TestMatrixQueryReq();
        testMatrixQueryReq.setTestLineInstanceIdList(testLineInstanceIdList);
        List<TestMatrixPO> testMatrixPOList = testMatrixService.queryList(testMatrixQueryReq).getData();
        if (Func.isNotEmpty(testMatrixPOList)) {
            Set<String> testMatrixIdList = testMatrixPOList.stream().map(TestMatrixPO::getId).collect(Collectors.toSet());
            //触发MatrixProcess状态变化
            TestMatrixProcessReq testMatrixProcessReq = new TestMatrixProcessReq();
            testMatrixProcessReq.setTestMatrixIdList(testMatrixIdList);
            testMatrixProcessReq.setAction(testLineProcessReq.getAction());
            testMatrixProcessReq.setNewMatrixStatus(testLineProcessReq.getNewStatus());
            testMatrixProcessReq.setTriggerTestLine(false);
            testMatrixProcessReq.setNeedStatusControl(false);
            BaseResponse updateMatrixStatusRes = testMatrixDomainService.updateMatrixStatus(testMatrixProcessReq);
            if (updateMatrixStatusRes.isFail()) {
                log.error("TestLine Change Trigger Matrix Change Fail, {}, {}", updateMatrixStatusRes.getMessage(), Func.toJson(testMatrixProcessReq));
            }
        }
        return BaseResponse.newSuccessInstance(true);
    }

    private BaseResponse<StatusControlRsp> statusControl(TestLineProcessReq testLineProcessReq) {
        if (Func.isEmpty(testLineProcessReq) || Func.isEmpty(testLineProcessReq.getObjectIdList())) {
//            return BaseResponse.newSuccessInstance();
        }
        List<TestLineInstancePO> testLinePOList = testLineService.queryTestLineBase(testLineProcessReq.getObjectIdList()).getData();        //读取tb_workflow_rule，判断是否满足Status更新条件
        StatusControlRsp statusControlRsp = new StatusControlRsp();
        if (Func.isNotEmpty(testLinePOList)) {
            WorkflowRuleContext workflowRuleContext = new WorkflowRuleContext();
            WorkflowRuleReq workflowRuleReq = new WorkflowRuleReq();
            workflowRuleReq.setCurrentNode("");
            workflowRuleReq.setNextNode(Func.toStr(testLineProcessReq.getNewStatus()));
            workflowRuleReq.setProcessCode(Constants.OBJECT.TEST_LINE.OBJECT_CODE);
            workflowRuleReq.setAction(testLineProcessReq.getAction());
            workflowRuleReq.setLabCode(SystemContextHolder.getLabCode());
            workflowRuleContext.setParam(workflowRuleReq);
            BaseResponse<List<WorkflowRuleBO>> baseResponse = BaseExecutor.start(WorkflowRuleQueryCMD.class, workflowRuleContext);
            List<WorkflowRuleBO> workflowRuleBOList = baseResponse.getData();
            //过滤出允许进入下个节点的数据
            Set<String> currentNodeRuleList = workflowRuleBOList.parallelStream().map(WorkflowRuleBO::getCurrentNode).collect(Collectors.toSet());
            Set<String> allowNextList = testLinePOList.stream().filter(item -> currentNodeRuleList.contains(Func.toStr(item.getTestLineStatus()))).map(TestLineInstancePO::getId).collect(Collectors.toSet());
            List<TestLineInstancePO> notAllowNextList = testLinePOList.stream().filter(item -> !currentNodeRuleList.contains(Func.toStr(item.getTestLineStatus()))).collect(Collectors.toList());
            statusControlRsp.setAllowObjectIdList(allowNextList);
            Map<String, String> notAllowObjectIdMap = null;
            if (Func.isNotEmpty(notAllowNextList)) {
                notAllowObjectIdMap = new HashMap<>();
                for (TestLineInstancePO testLineInstancePO : notAllowNextList) {
                    notAllowObjectIdMap.put(
                            testLineInstancePO.getId(),
                            String.format("TestLine %s Status is %s ,not Allow change to %s",
                                    testLineInstancePO.getTestItemNo(),
                                    Func.isNotEmpty(TestLineStatus.findStatus(testLineInstancePO.getTestLineStatus())) ? TestLineStatus.findStatus(testLineInstancePO.getTestLineStatus()).getMessage() : "",
                                    Func.isNotEmpty(TestLineStatus.findStatus(testLineProcessReq.getNewStatus())) ? TestLineStatus.findStatus(testLineProcessReq.getNewStatus()).getMessage() : ""));
                }
            }
            statusControlRsp.setNotAllowObjectIdMap(notAllowObjectIdMap);
        }
        return BaseResponse.newSuccessInstance(statusControlRsp);
    }
}
