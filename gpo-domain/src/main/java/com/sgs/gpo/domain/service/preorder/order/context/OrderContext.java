package com.sgs.gpo.domain.service.preorder.order.context;

import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.model.common.productsample.CareLabelBO;
import com.sgs.framework.model.order.enquiry.EnquiryBO;
import com.sgs.framework.model.order.order.OrderBO;
import com.sgs.framework.model.report.report.v2.ReportBO;
import com.sgs.framework.model.test.testline.TestLineBO;
import com.sgs.framework.model.test.testmatrix.TestMatrixBO;
import com.sgs.framework.model.test.testsample.TestSampleBO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.attachment.OrderAttachmentPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.customer.CustomerPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryProductPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryReportReceiverPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.LabInstancePO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderCrossLabPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderPersonPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderReportReceiverPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.SLOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.testrequest.TestRequestPO;
import com.sgs.gpo.facade.model.preorder.enquiry.req.UserLabDTO;
import com.sgs.gpo.facade.model.preorder.externalno.bo.ExternalNoBO;
import com.sgs.gpo.facade.model.preorder.order.rsp.DFFDiffRsp;
import com.sgs.gpo.facade.model.scantool.rsp.ScanToolFile;
import com.sgs.gpo.facade.model.sci.dto.TrfDTO;
import com.sgs.gpo.integration.sgsmart.rsp.TrfTemplateRsp;
import com.sgs.gpo.integration.usermanagement.rsp.CompanyEmployeeRsp;
import com.sgs.preorder.facade.model.dto.dm.CustomerDepartmentDTO;
import com.sgs.preorder.facade.model.dto.order.OrderDetailDto;
import com.sgs.preorder.facade.model.rsp.ProductSampleRsp;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/5 15:34
 */
@Data
public class OrderContext<Input> extends BaseContext<Input, OrderBO> {

    private List<ScanToolFile> scanToolFiles;

    private Map<String, List<TestLineBO>> testLineBOMap;
    private Map<String, List<TestMatrixBO>> testMatrixBOMap;
    private Map<String, List<TestSampleBO>> testSampleBOMap;

    private List<GeneralOrderPO> generalOrderList;
    private List<LabInstancePO> labList;
    private List<SLOrderPO> slOrderList;
    private List<OrderPersonPO> orderPersonList;
    private List<CustomerPO> customerList;
    private List<TestRequestPO> testRequestList;
    private List<OrderReportReceiverPO> orderReportReceiverList;
    private List<OrderAttachmentPO> orderAttachmentList;
    private List<OrderCrossLabPO> orderCrossLabList;
    private List<ExternalNoBO> externalNoBOList;
    private List<CareLabelBO> careLabelList;

    private List<OrderBO> orderList;
    private List<com.sgs.framework.model.order.v2.OrderBO> orderBOList;

    private String paymentTermName;
    private GeneralOrderPO orderPO;

    //to SGSMart 下拉选项
    private List<CustomerDepartmentDTO> customerDepartmentDTOList;
    private TrfTemplateRsp trfTemplateRsp;
    private List<CompanyEmployeeRsp> companyEmployeeRspList;
    private com.sgs.framework.model.order.v2.OrderBO orderBO;
    boolean deptIsRequired = false;

    //Bind Trf 数据
    private TrfDTO trfDTO;
    private OrderDetailDto orderDetailDto;
    private EnquiryBO enquiryBO;
    private EnquiryProductPO productPO;
    private ReportBO reportBO;
    private List<ProductSampleRsp> dbProductSampleRsps;
    private List<DFFDiffRsp> dffDiffRspList;
    private boolean bindFlag;

    private Integer copyFcm;

    // AFL新增的sales
    private String salesPerson;
    UserLabDTO saleUserLabDTO;

    private Integer copyNum;

    private String regular;

    private boolean copyQuotationFlag;

    // 区分新增编辑
    private Boolean isNew;

    private EnquiryBO originalEnquiry;

    private EnquiryReportReceiverPO enquiryReportReceiver;
}
