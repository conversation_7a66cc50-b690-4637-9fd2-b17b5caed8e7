package com.sgs.gpo.domain.service.otsnotes.testline.command;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.model.enums.JobStatus;
import com.sgs.framework.model.enums.TestLineStatus;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.enums.MatrixActionEnums;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testline.TestLineMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testline.TestLineInstancePO;
import com.sgs.gpo.domain.service.otsnotes.job.subdomain.IJobTestLineService;
import com.sgs.gpo.domain.service.otsnotes.testline.context.TestLineSubmitContext;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.ITestLineService;
import com.sgs.gpo.facade.model.otsnotes.job.dto.JobTestLineDTO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.QueryJobTestLineReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineSubmitReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.TestLineUpdateStatusReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TestLineSubmitCMD extends BaseCommand<TestLineSubmitContext> {

    @Autowired
    protected TestLineMapper testLineMapper;

    @Autowired
    private ITestLineService testLineService;

    @Autowired
    private IJobTestLineService jobTestLineService;

    @Override
    public BaseResponse validateParam(TestLineSubmitContext context) {
        TestLineSubmitReq testLineSubmitReq = context.getParam();
        if(Func.isEmpty(testLineSubmitReq) || Func.isEmpty(testLineSubmitReq.getTestLineInstanceIdList())){
            return BaseResponse.newFailInstance("common.param.miss", null);
        }
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse execute(TestLineSubmitContext context) {
        TestLineUpdateStatusReq testLineUpdateStatusReq = new TestLineUpdateStatusReq();
        testLineUpdateStatusReq.setTestLineInstanceIdList(context.getParam().getTestLineInstanceIdList());
        testLineUpdateStatusReq.setTestLineStatus(TestLineStatus.Submit.getStatus());
        testLineUpdateStatusReq.setTrigger("TestLine Submit");
        testLineUpdateStatusReq.setAction(MatrixActionEnums.Submit.getAction());
        testLineService.batchUpdateTestLineStatus(testLineUpdateStatusReq);
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(TestLineSubmitContext context){
        TestLineSubmitReq testLineSubmitReq = context.getParam();
        //Typing校验
        List<TestLineInstancePO> testLineInstancePOList = testLineService.queryTestLineBase(testLineSubmitReq.getTestLineInstanceIdList()).getData();
        if(Func.isEmpty(testLineInstancePOList)){
            return BaseResponse.newFailInstance("未查询到要提交的testLine信息");
        }
        List<TestLineInstancePO> testLineNotTyping = testLineInstancePOList.stream().filter(
                e -> Func.isNotEmpty(e.getTestLineStatus()) && !e.getTestLineStatus().equals(TestLineStatus.Typing.getStatus())).collect(Collectors.toList());
        if(Func.isNotEmpty(testLineNotTyping)){
            List<Integer> testLineIds = testLineNotTyping.stream().map(e -> e.getTestLineId()).collect(Collectors.toList());
            StringBuilder message = new StringBuilder();
            for(Integer item : testLineIds){
                message.append(item).append(",");
            }
            return BaseResponse.newFailInstance("存在TestLine Status不为Typing的TestLine:" + message.substring(0,message.length() -1));
        }
        //校验 Test Start Date 不能为空
        List<TestLineInstancePO> testLineNoStartDate = testLineInstancePOList.stream().filter(
                e -> Func.isEmpty(e.getTestStartDate())).collect(Collectors.toList());
        if(Func.isNotEmpty(testLineNoStartDate)){
            List<Integer> testLineIds = testLineNoStartDate.stream().map(e -> e.getTestLineId()).collect(Collectors.toList());
            StringBuilder message = new StringBuilder();
            for(Integer item : testLineIds){
                message.append(item).append(",");
            }
            return BaseResponse.newFailInstance("存在Test Start Date为空的TestLine:" + message.substring(0,message.length() -1));
        }
        //Assign Engineer校验 jobStatus
        QueryJobTestLineReq queryJobTestLineReq = new QueryJobTestLineReq();
        queryJobTestLineReq.setTestLineInstanceIdList(testLineSubmitReq.getTestLineInstanceIdList());
        BaseResponse<List<JobTestLineDTO>> jobTestLineDtoRsp = jobTestLineService.queryJobTestLineDTO(queryJobTestLineReq);
        if(jobTestLineDtoRsp.isFail() || Func.isEmpty(jobTestLineDtoRsp.getData())){
            return BaseResponse.newFailInstance("未查询到要提交的testLine对应job信息");
        }
        List<JobTestLineDTO> jobTestLinePOList = jobTestLineDtoRsp.getData();
        //校验JobStatus 是否为test
        List<JobTestLineDTO> jobTestLineNotTesting = jobTestLinePOList.stream().filter(
                e -> Func.isNotEmpty(e.getJobStatus()) && !e.getJobStatus().equals(JobStatus.Testing.getStatus())).collect(Collectors.toList());
        if(Func.isNotEmpty(jobTestLineNotTesting)){
            Set<String> testLineIds = new HashSet<>();
            for(JobTestLineDTO item : jobTestLineNotTesting){
                for(int i = 0; i < item.getTestLineDTOList().size(); i++){
                    testLineIds.add(item.getTestLineDTOList().get(i).getTestLineId().toString());
                }
            }
            return BaseResponse.newFailInstance("存在Job Status不为Testing的TestLine:" + String.join(",",testLineIds));
        }
        StringBuilder message = new StringBuilder();
        for(JobTestLineDTO item : jobTestLinePOList){
            for(int i = 0; i < item.getTestLineDTOList().size(); i++){
                if(Func.isEmpty(item.getTestLineDTOList().get(i).getEngineer())){
                    message.append(item.getTestLineDTOList().get(i).getTestLineId()).append(",");
                }
            }
        }
        if(Func.isNotEmpty(message)){
            return BaseResponse.newFailInstance("存在Engineer为空的TestLine:" + message.substring(0,message.length() -1));
        }
        return super.before(context);
    }
}
