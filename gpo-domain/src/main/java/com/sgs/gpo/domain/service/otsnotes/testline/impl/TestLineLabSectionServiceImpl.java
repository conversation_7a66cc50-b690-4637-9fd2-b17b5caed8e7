package com.sgs.gpo.domain.service.otsnotes.testline.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.enums.JobStatus;
import com.sgs.framework.model.enums.TestLineStatus;
import com.sgs.framework.model.trims.labsection.LabSectionBO;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testline.TestLineLabSectionRelationshipMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.job.JobPO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.order.GeneralOrderInstancePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testline.TestLineInstancePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testline.TestLineLabSectionRelationshipPO;
import com.sgs.gpo.domain.service.otsnotes.job.subdomain.IJobService;
import com.sgs.gpo.domain.service.otsnotes.order.IGeneralOrderInstanceService;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.ITestLineLabSectionService;
import com.sgs.gpo.domain.service.otsnotes.testline.subdomain.ITestLineService;
import com.sgs.gpo.domain.service.trims.labsection.ILabSectionService;
import com.sgs.gpo.facade.model.job.req.JobQueryReq;
import com.sgs.gpo.facade.model.otsnotes.labSection.req.QueryLabSectionReq;
import com.sgs.gpo.facade.model.otsnotes.labSection.rsp.ReportTestLineLabSectionRsp;
import com.sgs.gpo.facade.model.otsnotes.order.OrderInstanceQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import com.sgs.gpo.facade.model.trims.labsection.req.LabSectionReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/30 10:57
 */
@Service
public class TestLineLabSectionServiceImpl  extends ServiceImpl<TestLineLabSectionRelationshipMapper,TestLineLabSectionRelationshipPO>  implements ITestLineLabSectionService {
    @Autowired
    private IGeneralOrderInstanceService orderInstanceService;
    @Autowired
    private ILabSectionService labSectionService;
    @Autowired
    private ITestLineService testLineService;
    @Autowired
    private IJobService jobService;
    @Override
    public BaseResponse<List<TestLineLabSectionRelationshipPO>> queryByOrderTestLine(OrderTestLineReq orderTestLineReq) {
        if(Func.isEmpty(orderTestLineReq)){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"request"});
        }
        Set<String> orderIdList = orderTestLineReq.getOrderIdList();
        Set<String> testLineInstanceIdList = orderTestLineReq.getTestLineInstanceIdList();
        if(Func.isEmpty(orderIdList) && Func.isEmpty(testLineInstanceIdList)){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"order / test line"});
        }
        QueryWrapper<TestLineLabSectionRelationshipPO> queryWrapper = new QueryWrapper<>();
        if(Func.isNotEmpty(orderIdList)){
            queryWrapper.in(TestLineLabSectionRelationshipPO.COLUMN.ORDER_ID,orderIdList);
        }
        if(Func.isNotEmpty(orderTestLineReq.getTestLineInstanceIdList())) {
            queryWrapper.in(TestLineLabSectionRelationshipPO.COLUMN.TEST_LINE_INSTANCE_ID, orderTestLineReq.getTestLineInstanceIdList());
        }
        return BaseResponse.newSuccessInstance(baseMapper.selectList(queryWrapper));
    }

    @Override
    public BaseResponse<List<LabSectionBO>> queryLabSection(QueryLabSectionReq queryLabSectionReq) {
        if(Func.isEmpty(queryLabSectionReq) || Func.isEmpty(queryLabSectionReq.getOrderNoList())){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"request"});
        }
        OrderInstanceQueryReq orderInstanceQueryReq = new OrderInstanceQueryReq();
        orderInstanceQueryReq.setOrderNoList(queryLabSectionReq.getOrderNoList());
        BaseResponse<List<GeneralOrderInstancePO>> orderRsp = orderInstanceService.select(orderInstanceQueryReq);
        if(orderRsp.isFail()){
            return BaseResponse.newFailInstance(orderRsp.getMessage());
        }
        Set<String> orderIdList = orderRsp.getData().parallelStream().map(GeneralOrderInstancePO::getId).collect(Collectors.toSet());
        OrderTestLineReq orderTestLineReq = new OrderTestLineReq();
        orderTestLineReq.setOrderIdList(orderIdList);
        BaseResponse<List<TestLineLabSectionRelationshipPO>> listBaseResponse = this.queryByOrderTestLine(orderTestLineReq);
        if(listBaseResponse.isFail()){
            return BaseResponse.newFailInstance(listBaseResponse.getMessage());
        }
        //排除已Cancelled NC 的TL
        List<TestLineLabSectionRelationshipPO> testLineLabSectionRelationshipPOList = listBaseResponse.getData();
        List<TestLineInstancePO> testLineInstancePOList = testLineService.queryTestLineBaseByOrderId(orderIdList).getData();
        Set<String> invalidTestLineIdList = new HashSet<>();
        if(Func.isNotEmpty(testLineInstancePOList)){
            invalidTestLineIdList = testLineInstancePOList.stream().filter(item-> TestLineStatus.check(item.getTestLineStatus(),TestLineStatus.Cancelled,TestLineStatus.NC,TestLineStatus.NA))
                    .map(TestLineInstancePO::getId).collect(Collectors.toSet());
        }
        Set<String> finalInvalidTestLineIdList = invalidTestLineIdList;
        testLineLabSectionRelationshipPOList = testLineLabSectionRelationshipPOList.stream().filter(item->!finalInvalidTestLineIdList.contains(item.getTestLineInstanceId())).collect(Collectors.toList());
        Set<Long> labSectionBaseIdList = testLineLabSectionRelationshipPOList.parallelStream().map(TestLineLabSectionRelationshipPO::getLabSectionBaseId).filter(Func::isNotEmpty).collect(Collectors.toSet());
       if(Func.isEmpty(labSectionBaseIdList)){
           labSectionBaseIdList = testLineInstancePOList.parallelStream().map(TestLineInstancePO::getLabSectionBaseId).collect(Collectors.toSet());
       }
       // 如果labSectionBaseIdList 为空直接返回空的结果
       if(Func.isEmpty(labSectionBaseIdList)){
            return BaseResponse.newSuccessInstance(Lists.newArrayList());
       }
        LabSectionReq labSectionBaseIdReq = new LabSectionReq();
        labSectionBaseIdReq.setLabSectionBaseIdList(labSectionBaseIdList);
        List<LabSectionBO> labSectionBOList = labSectionService.queryLabSection(labSectionBaseIdReq).getData();
        return BaseResponse.newSuccessInstance(labSectionBOList.stream().sorted(Comparator.comparing(LabSectionBO::getLabSectionSeq))
                .collect(Collectors.toList()));
    }

    @Override
    public BaseResponse<List<LabSectionBO>> queryJobLabSection(QueryLabSectionReq queryLabSectionReq) {
        if(Func.isEmpty(queryLabSectionReq) || Func.isEmpty(queryLabSectionReq.getOrderNoList())){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"request"});
        }
        // 查询订单下有效的JobList
        JobQueryReq jobQueryReq = new JobQueryReq();
        jobQueryReq.setOrderNoList(queryLabSectionReq.getOrderNoList());
        List<JobPO> jobPOList = jobService.query(jobQueryReq);
        if(Func.isEmpty(jobPOList)){
            return BaseResponse.newSuccessInstance(Lists.newArrayList());
        }
        Set<Long> labSectionBaseIdList = jobPOList.stream().filter(job-> !JobStatus.check(job.getJobStatus(),JobStatus.Cancelled))
                .map(JobPO::getLabSectionBaseId).collect(Collectors.toSet());
        if(Func.isEmpty(labSectionBaseIdList)){
            return BaseResponse.newSuccessInstance(Lists.newArrayList());
        }
        LabSectionReq labSectionBaseIdReq = new LabSectionReq();
        labSectionBaseIdReq.setLabSectionBaseIdList(labSectionBaseIdList);
        List<LabSectionBO> labSectionBOList = labSectionService.queryLabSection(labSectionBaseIdReq).getData();
        return BaseResponse.newSuccessInstance(labSectionBOList.stream().sorted(Comparator.comparing(LabSectionBO::getLabSectionSeq))
                .collect(Collectors.toList()));
    }

    @Override
    public BaseResponse<List<ReportTestLineLabSectionRsp>> queryReportTestLineLabSection(QueryLabSectionReq queryLabSectionReq) {
        Assert.isTrue(Func.isNotEmpty(queryLabSectionReq),"common.param.miss",new Object[]{Constants.TERM.REQUEST.getCode()});
        if(Func.isAllEmpty(queryLabSectionReq.getReportIdList(), Func.isEmpty(queryLabSectionReq.getReportNoList()),queryLabSectionReq.getTestLineInstanceIdList())){
            return BaseResponse.newFailInstance("common.param.miss",new Object[]{"ReportId/ReportNo/TestLineInstanceId"});
        }
        List<ReportTestLineLabSectionRsp> reportTestLineLabSectionRspList = baseMapper.selectTestLineLabSectionByReport(queryLabSectionReq);
        return BaseResponse.newSuccessInstance(reportTestLineLabSectionRspList);
    }

    @Override
    public BaseResponse delete(OrderTestLineReq orderTestLineReq) {
        if(Func.isEmpty(orderTestLineReq)){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"request"});
        }
        Set<String> orderIdList = orderTestLineReq.getOrderIdList();
        Set<String> testLineInstanceIdList = orderTestLineReq.getTestLineInstanceIdList();
        if(Func.isEmpty(orderIdList) && Func.isEmpty(testLineInstanceIdList)){
            return BaseResponse.newFailInstance("common.miss",new Object[]{"order / test line"});
        }
        QueryWrapper<TestLineLabSectionRelationshipPO> queryWrapper = new QueryWrapper<>();
        if(Func.isNotEmpty(orderIdList)){
            queryWrapper.in(TestLineLabSectionRelationshipPO.COLUMN.ORDER_ID,orderIdList);
        }
        if(Func.isNotEmpty(orderTestLineReq.getTestLineInstanceIdList())) {
            queryWrapper.in(TestLineLabSectionRelationshipPO.COLUMN.TEST_LINE_INSTANCE_ID, orderTestLineReq.getTestLineInstanceIdList());
        }
        return BaseResponse.newSuccessInstance(baseMapper.delete(queryWrapper));
    }
}
