package com.sgs.gpo.domain.service.preorder.customer.subdomain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.dbstorages.mybatis.mapper.preorder.customer.CustomerMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.customer.CustomerPO;
import com.sgs.gpo.domain.service.preorder.customer.subdomain.ICustomerService;
import com.sgs.gpo.facade.model.customer.req.CustomerQueryReq;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class CustomerServiceImpl extends ServiceImpl<CustomerMapper, CustomerPO> implements ICustomerService {
    @Override
    public BaseResponse<List<CustomerPO>> select(OrderIdReq orderIdReq) {
        if(Func.isEmpty(orderIdReq)||Func.isEmpty(orderIdReq.getOrderIdList())){
            return BaseResponse.newFailInstance("common.param.miss",null);
        }
        LambdaQueryWrapper<CustomerPO> wrapper= Wrappers.<CustomerPO>lambdaQuery()
                .in(CustomerPO::getGeneralOrderId,orderIdReq.getOrderIdList());
        return BaseResponse.newSuccessInstance(this.list(wrapper));
    }

    @Override
    public List<CustomerPO> queryByName(CustomerQueryReq req) {
        Lists.newArrayList();
        if(Func.isEmpty(req) || Func.isEmpty(req.getCustomerName())){
            return Lists.newArrayList();
        }
        return baseMapper.queryByName(req);
    }

    @Override
    public int checkPayerIsSame(List<String> orderIds) {
        return baseMapper.checkPayerIsSame(orderIds);
    }
}
