package com.sgs.gpo.domain.service.preorder.order;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.model.common.object.StatusControlBO;
import com.sgs.framework.model.order.v2.OrderBO;
import com.sgs.framework.model.order.v2.OrderIdBO;
import com.sgs.framework.open.platform.base.service.IProcessService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/5 15:31
 */
public interface IOrderProcessService extends IProcessService<OrderBO,OrderIdBO> {

    /**
     * 更新订单状态为Testing
     * @param orderIdBOList
     * @return
     */
    BaseResponse<List<StatusControlBO<OrderBO>>> testing(List<OrderIdBO> orderIdBOList);

}
