package com.sgs.gpo.domain.service.otsnotes.testsample.context;

import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.model.test.testsample.v2.TestSampleBO;
import com.sgs.framework.model.test.testsample.v2.TestSampleTestLineBO;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.*;
import lombok.Data;

import java.util.List;

@Data
public class TestSampleContext<Input> extends BaseContext<Input, TestSampleBO> {

    List<TestSamplePO> samplePOList;
    List<LimitGroupPO> limitGroupList;
    List<TestSampleLanguagePO> testSampleLanguagePOList;
    List<ReferDataRelationshipPO> referSampleList;
    List<TestSampleTestLineBO> testSampleTestLineBOList;
    List<TestSampleGroupPO> testSampleGroupPOList;

    public TestSampleContext(Input input){
        this.setLab(SystemContextHolder.getLab());
        this.setToken(SecurityContextHolder.getSgsToken());
        this.setUserInfo(SecurityContextHolder.getUserInfo());
        this.setParam(input);
    }
}
