package com.sgs.gpo.domain.otsnotes.common.service.analyte;

import com.sgs.framework.model.test.analyte.v2.AnalyteBO;
import com.sgs.framework.model.test.analyte.v2.AnalyteIdBO;
import com.sgs.framework.open.platform.base.service.IBaseService;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.analyte.AnalytePO;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;

public interface ITestAnalyteService extends IBaseService<AnalyteBO, AnalytePO, AnalyteIdBO, OrderTestLineReq> {
}
