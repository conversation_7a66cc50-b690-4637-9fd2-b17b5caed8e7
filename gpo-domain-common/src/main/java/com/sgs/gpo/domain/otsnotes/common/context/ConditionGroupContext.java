package com.sgs.gpo.domain.otsnotes.common.context;


import com.sgs.framework.core.base.command.BaseContext;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.model.test.condition.conditiongroup.v2.ConditionGroupBO;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.condition.TestConditionGroupLanguagePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.condition.TestConditionGroupPO;
import lombok.Data;

import java.util.List;

@Data
public class ConditionGroupContext<Input> extends BaseContext<Input, ConditionGroupBO> {

    private List<TestConditionGroupPO> conditionGroupList;
    private List<TestConditionGroupLanguagePO> conditionGroupLanList;

    private List<ConditionGroupBO> conditionGroupBOList;

    public ConditionGroupContext(Input input){
        this.setLab(SystemContextHolder.getLab());
        this.setToken(SecurityContextHolder.getSgsToken());
        this.setUserInfo(SecurityContextHolder.getUserInfo());
        this.setParam(input);
    }

}
