package com.sgs.gpo.domain.otsnotes.common.service.analyte.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.context.SystemContextHolder;
import com.sgs.framework.core.exception.Assert;
import com.sgs.core.domain.UserInfo;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.test.analyte.v2.*;
import com.sgs.framework.model.test.testline.v2.TestLineIdBO;
import com.sgs.framework.model.test.testmatrix.v2.TestMatrixIdBO;
import com.sgs.framework.open.platform.base.service.impl.AbstractBaseService;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.analyte.AnalyteMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.analyte.AnalytePO;
import com.sgs.gpo.domain.otsnotes.common.service.analyte.ITestAnalyteService;
import com.sgs.gpo.facade.model.otsnotes.testline.req.OrderTestLineReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class TestAnalyteServiceImpl extends AbstractBaseService<AnalyteBO, AnalytePO, AnalyteIdBO, AnalyteMapper, OrderTestLineReq> implements ITestAnalyteService {

    @Override
    public List<AnalyteBO> convertToBO(Collection<AnalytePO> poList) {
        List<AnalyteBO> analyteBOList = Lists.newArrayList();
        if(Func.isNotEmpty(poList)){
            poList.forEach(analytePO -> {
                AnalyteBO analyteBO = new AnalyteBO();
                //ID
                AnalyteIdBO analyteIdBO = new AnalyteIdBO();
                analyteIdBO.setAnalyteBaseId(analytePO.getAnalyteBaseId());
                analyteIdBO.setAnalyteId(analytePO.getAnalyteId());
                analyteIdBO.setAnalyteInstanceId(analytePO.getId());
                analyteIdBO.setId(analytePO.getId());
                analyteBO.setId(analyteIdBO);
                //Relationship
                AnalyteRelationshipBO relationshipBO = new AnalyteRelationshipBO();
                AnalyteParentBO parentBO = new AnalyteParentBO();

                TestLineIdBO testLineIdBO = new TestLineIdBO();
                testLineIdBO.setTestLineInstanceId(analytePO.getTestLineInstanceId());
                parentBO.setTestLineId(testLineIdBO);

                TestMatrixIdBO testMatrixIdBO = new TestMatrixIdBO();
//                testMatrixIdBO.setTestMatrixId(analytePO.getTestMatrixId());
                parentBO.setTestMatrixId(testMatrixIdBO);

                relationshipBO.setParent(parentBO);
                analyteBO.setRelationship(relationshipBO);

                //Header
                AnalyteHeaderBO analyteHeaderBO = new AnalyteHeaderBO();
                analyteHeaderBO.setAnalyteName(analytePO.getTestAnalyteName());
                analyteHeaderBO.setAnalyteSeq(analytePO.getTestAnalyteSeq());
                analyteHeaderBO.setCasNo(analytePO.getCasNo());

                //Unit
                analyteHeaderBO.setUnitBaseId(analytePO.getUnitBaseId());
                analyteHeaderBO.setReportUnit(analytePO.getReportUnit());

                //Data Tag
//                analyteHeaderBO.setDataSource(analytePO.getDataSource());
//                analyteHeaderBO.setObjectType(analytePO.getObjectType());

                analyteBO.setHeader(analyteHeaderBO);
                analyteBOList.add(analyteBO);
            });
        }
        return analyteBOList;
    }

    @Override
    public List<AnalytePO> convertToPO(Collection<AnalyteBO> boList) {
        List<AnalytePO> analytePOList = Lists.newArrayList();
        if(Func.isNotEmpty(boList)){
            UserInfo userInfo = SystemContextHolder.getUserInfo();
            boList.forEach(analyteBO -> {
                AnalytePO analytePO = new AnalytePO();
                //ID
                AnalyteIdBO analyteIdBO = analyteBO.getId();
                if(Func.isNotEmpty(analyteIdBO) && Func.isNotEmpty(analyteIdBO.getAnalyteInstanceId())){
                    analytePO.setId(analyteIdBO.getAnalyteInstanceId());

                    analytePO.setModifiedBy(userInfo.getRegionAccount());
                    analytePO.setModifiedDate(new Date());
                }else{
                    analytePO.setId(IdUtil.uuId());
                    analytePO.setCreatedBy(userInfo.getRegionAccount());
                    analytePO.setCreatedDate(new Date());
                }

                analytePO.setAnalyteId(analyteIdBO.getAnalyteId());
                analytePO.setAnalyteBaseId(analyteIdBO.getAnalyteBaseId());

                //Relationship
                AnalyteRelationshipBO relationshipBO = analyteBO.getRelationship();
                try{
                    AnalyteParentBO parentBO = relationshipBO.getParent();
                    analytePO.setTestLineInstanceId(parentBO.getTestLineId().getTestLineInstanceId());
//                    analytePO.setTestMatrixId(parentBO.getTestMatrixId().getTestMatrixId());
                }catch (Exception e){
                    //TODO
                }


                //Header
                AnalyteHeaderBO analyteHeaderBO = analyteBO.getHeader();
                if(Func.isNotEmpty(analyteHeaderBO)){
                    analytePO.setTestAnalyteName(analyteHeaderBO.getAnalyteName());
                    analytePO.setTestAnalyteSeq(analyteHeaderBO.getAnalyteSeq());
                    //Unit
                    analytePO.setUnitBaseId(analyteHeaderBO.getUnitBaseId());
                    analytePO.setReportUnit(analyteHeaderBO.getReportUnit());

//                    analytePO.setDataSource(analyteHeaderBO.getDataSource());
//                    analytePO.setObjectType(analyteHeaderBO.getObjectType());
                }
                analytePOList.add(analytePO);
            });
        }
        return analytePOList;
    }

    @Override
    public LambdaQueryWrapper<AnalytePO> createWrapper(OrderTestLineReq queryReq) {
        Assert.notNull(queryReq);
        Assert.isTrue(!(Func.isNotEmpty(queryReq.getTestLineInstanceIdList())
                &&Func.isNotEmpty(queryReq.getOrderIdList())
                &&Func.isNotEmpty(queryReq.getTestMatrixIdList())), ResponseCode.PARAM_MISS);
        // TestLineInstanceId 查询
        LambdaQueryWrapper<AnalytePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(Func.isNotEmpty(queryReq.getTestLineInstanceIdList())){
            lambdaQueryWrapper.in(AnalytePO::getTestLineInstanceId,queryReq.getTestLineInstanceIdList());
//            lambdaQueryWrapper.eq(AnalytePO::getObjectType, Constants.OBJECT.PP_TEST_LINE.OBJECT_CODE);
        }
        //TestMatrixId 查询
//        if(Func.isNotEmpty(queryReq.getTestMatrixIdList())){
//            lambdaQueryWrapper.in(AnalytePO::getTestMatrixId,queryReq.getTestMatrixIdList());
//            lambdaQueryWrapper.eq(AnalytePO::getObjectType, Constants.OBJECT.TEST_MATRIX.OBJECT_CODE);
//        }
        //Order Id 查询；
        if(Func.isNotEmpty(queryReq.getOrderIdList())){
            lambdaQueryWrapper.in(AnalytePO::getGeneralOrderInstanceId,queryReq.getOrderIdList());
        }
        return lambdaQueryWrapper;
    }
}
