package com.sgs.gpo.domain.otsnotes.common.command;

import com.google.common.collect.Lists;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseCommand;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.model.test.condition.conditiongroup.ConditionGroupLanguageBO;
import com.sgs.framework.model.test.condition.conditiongroup.v2.*;
import com.sgs.framework.tool.utils.Func;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.condition.TestConditionGroupLanguagePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.condition.TestConditionGroupPO;
import com.sgs.gpo.domain.otsnotes.common.context.ConditionGroupContext;
import com.sgs.gpo.domain.otsnotes.common.subdomain.ITestConditionGroupLanguageService;
import com.sgs.gpo.domain.otsnotes.common.subdomain.ITestConditionGroupService;
import com.sgs.gpo.facade.model.condition.req.ConditionGroupQueryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/28 09:35
 * @Description ConditionGroup查询输出标准结构ConditionGroupBO
 */
@Primary
@Service
@Slf4j
public class ConditionGroupQueryCMD extends BaseCommand<ConditionGroupContext<ConditionGroupQueryReq>> {

    @Autowired
    private ITestConditionGroupService conditionGroupService;
    @Autowired
    private ITestConditionGroupLanguageService conditionGroupLanguageService;

    @Override
    public BaseResponse validateParam(ConditionGroupContext<ConditionGroupQueryReq> context) {
        Assert.isTrue(Func.isNotEmpty(context.getParam()),"common.param.miss",new Object[]{Constants.TERM.REQUEST.getCode()});
        //Assert.isTrue(Func.isNotEmpty(context.getParam().getLab()),"common.param.miss",new Object[]{Constants.TERM.LAB.getCode()});
        return BaseResponse.newSuccessInstance(true);
    }

    @Override
    public BaseResponse before(ConditionGroupContext<ConditionGroupQueryReq> context) {
        ConditionGroupQueryReq conditionGroupQueryReq = context.getParam();
        List<TestConditionGroupPO> conditionGroupList = Lists.newArrayList();
        // 查询订单下的conditionGroupList
        BaseResponse<List<TestConditionGroupPO>> conditionGroupResponse = conditionGroupService.queryV1(conditionGroupQueryReq);
        if (Func.isNotEmpty(conditionGroupResponse.getData())) {
            conditionGroupList = conditionGroupResponse.getData();
        }
        context.setConditionGroupList(conditionGroupList);
        // 多语言查询
        List<TestConditionGroupLanguagePO> conditionGroupLanList = Lists.newArrayList();
        conditionGroupQueryReq.setTestConditionGroupIdList(conditionGroupList.stream().map(TestConditionGroupPO::getId)
                .collect(Collectors.toSet()));
        BaseResponse<List<TestConditionGroupLanguagePO>> conditionGroupLanResponse = conditionGroupLanguageService.query(conditionGroupQueryReq);
        if(conditionGroupLanResponse.isSuccess() && Func.isNotEmpty(conditionGroupLanResponse.getData())){
            conditionGroupLanList = conditionGroupLanResponse.getData();
        }
        context.setConditionGroupLanList(conditionGroupLanList);
        return super.before(context);
    }

    @Override
    public BaseResponse buildDomain(ConditionGroupContext<ConditionGroupQueryReq> context) {
        List<ConditionGroupBO> conditionGroupBOList = Lists.newArrayList();
        List<TestConditionGroupPO> conditionGroupList = context.getConditionGroupList();
        List<TestConditionGroupLanguagePO> conditionGroupLanList = context.getConditionGroupLanList();
        conditionGroupList.stream().forEach(localConditionGroup->{
            ConditionGroupBO conditionGroup = new ConditionGroupBO();
            //id
            ConditionGroupIdBO id = new ConditionGroupIdBO();
            id.setConditionGroupId(localConditionGroup.getId());
            conditionGroup.setId(id);
            //relationship
            ConditionGroupRelationBO relationship = new ConditionGroupRelationBO();
            ConditionGroupParentBO parent = new ConditionGroupParentBO();
            parent.setTestLineInstanceId(localConditionGroup.getTestLineInstanceId());
            relationship.setParent(parent);
            conditionGroup.setRelationship(relationship);
            //header
            ConditionGroupHeaderBO header = new ConditionGroupHeaderBO();
            header.setCombinedConditionDescription(localConditionGroup.getCombinedConditionDescription());
            //languageList
            List<ConditionGroupLanguageBO> languageList = Lists.newArrayList();
            if(Func.isNotEmpty(conditionGroupLanList)){
                List<TestConditionGroupLanguagePO> testConditionGroupLanguageList =
                        conditionGroupLanList.stream().filter(item->Func.equalsSafe(item.getTestConditionGroupId(),localConditionGroup.getId())).collect(Collectors.toList());
                if(Func.isNotEmpty(testConditionGroupLanguageList)){
                    testConditionGroupLanguageList.stream().forEach(item->{
                        ConditionGroupLanguageBO conditionGroupLanguageBO = new ConditionGroupLanguageBO();
                        conditionGroupLanguageBO.setLanguageId(item.getLanguageId());
                        conditionGroupLanguageBO.setCombinedConditionDescription(item.getCombinedConditionDescription());
                        languageList.add(conditionGroupLanguageBO);
                    });
                }
            }
            header.setLanguageList(languageList);
            conditionGroup.setHeader(header);
            conditionGroupBOList.add(conditionGroup);
        });
        context.setConditionGroupBOList(conditionGroupBOList);
        return BaseResponse.newSuccessInstance(context.getConditionGroupBOList());
    }

    @Override
    public BaseResponse execute(ConditionGroupContext<ConditionGroupQueryReq> context) {
        return this.buildDomain(context);
    }
}
