<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.BossOrderTaxInvoiceMapper" >

    <update id="updateByInvoiceNoAndTaxBatch" parameterType="java.util.List">
        <foreach collection="bossOrderTaxInvoiceInfoPOS" item="item" index="index" separator=";">
            UPDATE tb_boss_order_tax_invoice
            SET tax_invoice_delivered_awb_no = #{item.taxInvoiceDeliveredAwbNo},
            tax_invoice_delivered_by = #{item.taxInvoiceDeliveredBy},
            tax_invoice_delivered_date = #{item.taxInvoiceDeliveredDate}
            WHERE invoice_no = #{item.invoiceNo}
            AND tax_invoice_no =#{item.taxInvoiceNo}
        </foreach>
    </update>
</mapper>