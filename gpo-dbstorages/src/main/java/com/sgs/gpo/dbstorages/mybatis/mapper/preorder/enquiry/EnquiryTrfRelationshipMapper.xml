<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.preorder.enquiry.EnquiryTrfRelationshipMapper" >

    <select id="queryEnquiryTrf" resultType="com.sgs.gpo.facade.model.preorder.enquiry.bo.EnquiryTrfBO">
        SELECT
            e.id AS enquiryId,
            e.enquiry_no AS enquiryNo,
            trfrel.RefNo AS refNo,
            trfrel.RefSystemId AS refSystemId,
            trfrel.ExternalOrderNo AS externalOrderNo,
            trfrel.IntegrationChannel as integrationChannel

        FROM
            tb_enquiry e
        INNER JOIN tb_enquiry_trf_relationship trfrel ON trfrel.EnquiryId = e.id
        <where>
            e.enquiry_status != 30
            <if test="refSystemIdList != null and refSystemIdList.size()>0">
               AND trfrel.RefSystemId IN
                <foreach collection="refSystemIdList" item="refSystemId" open="(" separator="," close=")">
                    #{refSystemId}
                </foreach>
            </if>
            <if test="enquiryIdList != null and enquiryIdList.size()>0">
                AND e.id IN
                <foreach collection="enquiryIdList" item="enquiryId" open="(" separator="," close=")">
                    #{enquiryId}
                </foreach>
            </if>
            <if test="enquiryNoList != null and enquiryNoList.size()>0">
                AND e.enquiry_no IN
                <foreach collection="enquiryNoList" item="enquiryNo" open="(" separator="," close=")">
                    #{enquiryNo}
                </foreach>
            </if>
            <if test="labCode != null and labCode != ''">
                AND e.lab_code = #{labCode}
            </if>
            <if test="refNo != null and refNo != ''">
                AND trfrel.RefNo = #{refNo}
            </if>
            <if test="externalOrderNo != null and externalOrderNo != ''">
                AND trfrel.ExternalOrderNo = #{externalOrderNo}
            </if>
        </where>
    </select>
</mapper>