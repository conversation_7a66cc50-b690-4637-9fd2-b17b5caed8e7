<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.OrderIndexMapper" >

    <select id="selectSourceList" parameterType="com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderIndexPO" resultType="com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderIndexPO" >
        select
            go.id orderId,
            goi.id notesOrderId,
            go.orderNo,
            enr.externalOrderNo,
            go.buId,
            go.LocationCode locationCode,
            concat('lab', go.LabId) hostLabId,
            concat('lab', (select li.labId from tb_lab_instance li where li.labCode = clr.toLab limit 1)) topLabId,
            otr.ExternalOrderNo subcontractOrderNo,
            opp.RefNo oppNo,
            go.createdBy,
            go.createdDate,
            go.RootOrderNo rootOrderNo,
            go.OldOrderNo parentOrderNo,
            go.ExpectedOrderDueDate dueDate,
            go.orderStatus,
            go.operationMode,
            so.CaseType orderType,
            so.CSName cs,
            ts.RegionAccount ts,
            subReportReviewer.RegionAccount subreportReviewer,
            sales.RegionAccount sales,
            csa.RegionAccount csa,
            so.customerRefNo customerReferenceNo,
            so.ProductCategory productCategory,
            go.ServiceLevel serviceType,
            tr.DraftReportRequired needDraftFlag,
            tr.ReportRequirement reportFileType,
            go.EnableDelivery enableDeliveryFlag,
            go.DeliveryApproveStatus as deliveryApproveFlag,
            go.toTestFlag,
            go.remark remark,
            so.PaymentStatus paymentStatus,
            so.EnquiryNo enquiryNo,
            applicant.BossNumber applicantNo,
            applicant.CustomerID applicantId,
            concat('[{"cn":', JSON_QUOTE(ifnull(applicant.CustomerNameCN,'')), ',"en":', JSON_QUOTE(ifnull(applicant.CustomerNameEN,'')), '}]') applicantName,
            buyer.CustomerID buyerId,
            buyer.BossNumber buyerNo,
            concat('[{"cn":', JSON_QUOTE(ifnull(buyer.CustomerNameCN,'')), ',"en":', JSON_QUOTE(ifnull(buyer.CustomerNameEN,'')), '}]') buyerName,
            supplier.BossNumber supplierNo,
            concat('[{"cn":', JSON_QUOTE(ifnull(supplier.CustomerNameCN,'')), ',"en":', JSON_QUOTE(ifnull(supplier.CustomerNameEN,'')), '}]') supplierName,
            manufacture.BossNumber manufactureNo,
            concat('[{"cn":', JSON_QUOTE(ifnull(manufacture.CustomerNameCN,'')), ',"en":', JSON_QUOTE(ifnull(manufacture.CustomerNameEN,'')), '}]') manufactureName,
            payer.BossNumber orderPayerNo,
            concat('lab', (select li.labId from tb_lab_instance li where li.labCode = subcontract.customerId limit 1)) subcontractLabId
        from
            tb_general_order go
            inner join tb_sl_order so on
            go.id = so.GeneralOrderID
            left join gpn.tb_general_order_instance goi on
            go.OrderNo = goi.OrderNo
            left join tb_external_no_rel enr on
            enr.GeneralOrderId = go.ID
            left join tb_order_trf_relationship otr on
            otr.OrderId = go.id
            and otr.ExternalOrderNo is not null
            left join tb_order_trf_relationship opp on
            opp.OrderId = go.id
            and opp.RefSystemId =10015 and opp.RefNo is not null
            left join tb_order_person ts on
            ts.GeneralOrderID = go.id
            and ts.PersonType = 'ts'
            left join tb_order_person subReportReviewer on
            subReportReviewer.GeneralOrderID = go.id
            and subReportReviewer.PersonType = 'subReportReviewer'
            left join tb_order_person sales on
            sales.GeneralOrderID = go.id
            and sales.PersonType = 'sales'
            left join tb_order_person csa on
            csa.GeneralOrderID = go.id
            and csa.PersonType = 'csa'
            left join tb_test_request tr on
            go.id = tr.GeneralOrderID
            left join tb_order_cross_lab_rel clr on
            go.OrderNo = clr.orderNo
            left join gpo.tb_customer_instance applicant on
            go.id = applicant.GeneralOrderID
            and applicant.CustomerUsage = 'applicant'
            left join gpo.tb_customer_instance buyer on
            go.id = buyer.GeneralOrderID
            and buyer.CustomerUsage = 'buyer'
            left join gpo.tb_customer_instance subcontract on
            go.id = subcontract.GeneralOrderID
            and subcontract.CustomerUsage = 'subcontractFrom'
            left join gpo.tb_customer_instance supplier on
            go.id = supplier.GeneralOrderID
            and supplier.CustomerUsage = 'supplier'
            left join gpo.tb_customer_instance manufacture on
            go.id = manufacture.GeneralOrderID
            and manufacture.CustomerUsage = 'manufacture'
            left join gpo.tb_customer_instance payer on
            go.id = payer.GeneralOrderID
            and payer.CustomerUsage = 'payer'
        <where>
            <if test="orderNo != null and orderNo != ''">
                go.orderno =#{orderNo}
            </if>
            <if test="orderId != null and orderId != ''">
                go.id =#{orderId}
            </if>
        </where>
    </select>

    <select id="selectTPSourceList" parameterType="com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderIndexPO" resultType="com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderIndexPO" >
        select
        go.id orderId,
        goi.id notesOrderId,
        go.orderNo,
        enr.externalOrderNo,
        go.buId,
        concat('lab', go.LabId) hostLabId,
        concat('lab', (select li.labId from tb_lab_instance li where li.labCode = clr.toLab limit 1)) topLabId,
        otr.ExternalOrderNo subcontractOrderNo,
        go.createdBy,
        go.createdDate,
        go.RootOrderNo rootOrderNo,
        go.OldOrderNo parentOrderNo,
        go.ExpectedOrderDueDate dueDate,
        go.orderStatus,
        go.operationMode,
        so.CaseType orderType,
        so.CSName cs,
        ts.RegionAccount ts,
        subReportReviewer.RegionAccount subreportReviewer,
        sales.RegionAccount sales,
        csa.RegionAccount csa,
        so.customerRefNo customerReferenceNo,
        go.ServiceLevel serviceType,
        tr.DraftReportRequired needDraftFlag,
        tr.ReportRequirement reportFileType,
        go.EnableDelivery enableDeliveryFlag,
        go.toTestFlag,
        applicant.BossNumber applicantNo,
        concat('[{"cn":', JSON_QUOTE(applicant.CustomerNameCN), ',"en":', JSON_QUOTE(applicant.CustomerNameEN), '}]') applicantName,
        buyer.BossNumber buyerNo,
        concat('[{"cn":', JSON_QUOTE(buyer.CustomerNameCN), ',"en":', JSON_QUOTE(buyer.CustomerNameEN), '}]') buyerName,
        concat('lab', (select li.labId from tb_lab_instance li where li.labCode = subcontract.customerId limit 1)) subcontractLabId
        from
        tb_general_order go
        inner join tb_sl_order so on
        go.id = so.GeneralOrderID
        left join gpn_tp.tb_general_order_instance goi on
        go.OrderNo = goi.OrderNo
        left join tb_external_no_rel enr on
        enr.GeneralOrderId = go.ID
        left join tb_order_trf_relationship otr on
        otr.OrderId = go.id
        and otr.ExternalOrderNo is not null
        left join tb_order_person ts on
        ts.GeneralOrderID = go.id
        and ts.PersonType = 'ts'
        left join tb_order_person subReportReviewer on
        subReportReviewer.GeneralOrderID = go.id
        and subReportReviewer.PersonType = 'subReportReviewer'
        left join tb_order_person sales on
        sales.GeneralOrderID = go.id
        and sales.PersonType = 'sales'
        left join tb_order_person csa on
        csa.GeneralOrderID = go.id
        and csa.PersonType = 'csa'
        left join tb_test_request tr on
        go.id = tr.GeneralOrderID
        left join tb_order_cross_lab_rel clr on
        go.OrderNo = clr.orderNo
        left join gpo_tp.tb_customer_instance applicant on
        go.id = applicant.GeneralOrderID
        and applicant.CustomerUsage = 'applicant'
        left join gpo_tp.tb_customer_instance buyer on
        go.id = buyer.GeneralOrderID
        and buyer.CustomerUsage = 'buyer'
        left join gpo_tp.tb_customer_instance subcontract on
        go.id = subcontract.GeneralOrderID
        and subcontract.CustomerUsage = 'subcontractFrom'
        <where>
            <if test="orderNo != null and orderNo != ''">
                go.orderno =#{orderNo}
            </if>
            <if test="orderId != null and orderId != ''">
                go.id =#{orderId}
            </if>
        </where>
    </select>

    <select id="getPayerByOrder" parameterType="java.lang.String" resultType="com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderIndexPO">
        SELECT
            tt.GeneralOrderID orderId,
            GROUP_CONCAT( tt.CustomerID ) payerId,
            GROUP_CONCAT( tt.BossNumber ) payerNo,
            concat('[',GROUP_CONCAT('{"cn":', JSON_QUOTE(ifnull(tt.CustomerNameCN,'')), ',"en":', JSON_QUOTE(ifnull(tt.CustomerNameEN,'')) , '}'),']') payerName,
            GROUP_CONCAT( tt.PaymentTermName ) paymentTerm
        FROM
            (
                SELECT
                    ci.GeneralOrderID,
                    ci.CustomerID,
                    ci.BossNumber,
                    ci.CustomerNameCN,
                    ci.CustomerNameEN,
                    ci.PaymentTermName
                FROM
                    tb_customer_instance ci
                WHERE
                    ci.CustomerUsage = 'payer'
                    AND ci.GeneralOrderID in
                    <foreach collection="orderIdList" item="item" index="index" separator=","  open="(" close=")" >
                        #{item}
                    </foreach>
                UNION
                SELECT
                    pci.order_id,
                    pci.customer_id,
                    pci.boss_number,
                    pci.customer_name_cn,
                    pci.customer_name_en,
                    NULL PaymentTermName
                FROM
                    tb_pe_customer_instance pci
                WHERE
                    pci.order_id  in
                    <foreach collection="orderIdList" item="item" index="index" separator=","  open="(" close=")" >
                        #{item}
                    </foreach>
            ) tt
        GROUP BY
            tt.GeneralOrderID
    </select>

    <select id="getProductByOrder" parameterType="java.lang.String" resultType="com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderIndexPO">
        SELECT GeneralOrderID orderId,
        GROUP_CONCAT(concat(
            ifnull(poNo,'') ,
            '#',
            ifnull(lotNo,'') ,
            '#',
            ifnull(ItemNo,'') ,
            '#',
            ifnull(StyleNo,'') ,
            '#',
            ifnull(OtherSampleInformation,'') ,
            '#',
            ifnull(ProductDescription,'')
            ) SEPARATOR '#')  product
        FROM gpo.tb_product_instance x
        WHERE GeneralOrderID in
        <foreach collection="orderIdList" item="item" index="index" separator=","  open="(" close=")" >
            #{item}
        </foreach>
        AND (HeaderID is null or HeaderID = '')
        GROUP BY GeneralOrderID
    </select>

    <select id="hasIndex" resultType="java.lang.Integer">
        select count(1) from tb_order_index where order_id = #{orderId}
    </select>
</mapper>