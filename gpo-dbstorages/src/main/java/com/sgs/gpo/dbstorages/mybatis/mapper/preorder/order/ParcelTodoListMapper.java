package com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.ParcelTodoListPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ParcelTodoListMapper extends BaseMapper<ParcelTodoListPO> {

    int updateInvoiceNoByOrderNoBatch(@Param("list") List<ParcelTodoListPO> parcelTodoListPOList);

}