package com.sgs.gpo.dbstorages.mybatis.mapper.preorder.attachment;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.attachment.OrderAttachmentPO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderAttachmentItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/6 09:51
 */

public interface OrderAttachmentMapper extends BaseMapper<OrderAttachmentPO> {

    int updateSequence(@Param("attachmentList") List<OrderAttachmentItem> attachmentList);

}
