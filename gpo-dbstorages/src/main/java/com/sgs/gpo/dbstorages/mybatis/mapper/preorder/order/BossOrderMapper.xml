<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.BossOrderMapper" >

    <select id="getBossOrderByTrueOrderNo" resultType="java.lang.Boolean">
        SELECT count(0)
        FROM tb_boss_order bo
        WHERE bo.TrueOrderNo = #{trueOrderNo}
            LIMIT 1
    </select>

    <select id="getOrderByBossOrderNo" resultType="com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO">
        SELECT tgo.id,tgo.OrderNo orderNo
        FROM tb_general_order tgo
                 INNER JOIN tre_order_relationship tor ON tgo.ID = tor.GeneralOrderID
                 INNER JOIN tb_boss_order tbo ON tor.BOSSOrderID = tbo.ID
        WHERE tbo.TrueOrderNo = #{bossOrderNo}
        LIMIT 1
    </select>

    <insert id="createBossInvoiceRel" parameterType="com.sgs.gpo.dbstorages.mybatis.model.preorder.order.BossOrderPO">
        INSERT INTO tre_boss_invoice_relationship (
        ID,
        TrueOrderNo,
        BOSSInvoiceNo,
        BOSSInvoiceDate,
        CreatedBy,
        CreatedDate,
        ModifiedBy,
        ModifiedDate
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.relID},
            #{item.trueOrderNo},
            #{item.bossInvoiceNo},
            #{item.bossInvoiceDate},
            #{item.createdBy},
            #{item.createdDate},
            #{item.modifiedBy},
            #{item.modifiedDate}
            )
        </foreach>
    </insert>

    <select id="getBossOrderNoByOrderNo" resultType="com.sgs.gpo.dbstorages.mybatis.model.preorder.payment.BossOrderInvoiceDTO">
        SELECT DISTINCT
        qh.quotation_no as quotationNo,
        bo.TrueOrderNo as bossOrderNo
        FROM
        tb_general_order o
        LEFT JOIN tb_pe_quotation_head qh ON qh.order_id = o.id
        LEFT JOIN tb_pe_quotation_head_history qhh ON qhh.quotation_head_id = qh.id
        LEFT JOIN tb_pe_boss_order_relationship bor ON bor.quotation_head_history_Id = qhh.id
        LEFT JOIN tb_boss_order bo ON bo.id = bor.boss_order_id
        WHERE
            qh.`status` != 4
        AND qhh.`status` != 4
        AND IF( qh.STATUS = 1 AND qhh.quotation_flag <![CDATA[&]]> 1 = 1, 1, 0 ) != 1
        and o.OrderNo = #{orderNo}
    </select>


</mapper>