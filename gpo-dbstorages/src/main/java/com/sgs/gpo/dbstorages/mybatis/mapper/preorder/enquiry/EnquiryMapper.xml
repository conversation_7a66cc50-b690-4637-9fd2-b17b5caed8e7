<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.preorder.enquiry.EnquiryMapper" >


    <sql id="queryEnquiryPageColumns">
        e.id id,
        e.enquiry_no enquiryNo,
        e.bu_code buCode,
        e.enquiry_status enquiryStatus,
        e.created_date createdDateShow,
        e.sales_person salesName,
        e.created_by createdBy,
        e.net_amount netAmount,
        e.after_tax_amount afterTaxAmount,
        e.final_amount finalAmount,
        e.currency_code currencyCode,
        e.lab_code labCode
    </sql>

    <sql id="queryEnquiryPageTables">
        tb_enquiry e
        left join tb_enquiry_customer ecb on e.id = ecb.enquiry_id and ecb.customer_usage = 'buyer'
        left join tb_enquiry_customer eca on e.id = eca.enquiry_id and eca.customer_usage = 'applicant'
        left join tb_enquiry_customer ecp on e.id = ecp.enquiry_id and ecp.customer_usage = 'payer'
        left join tb_object_tags ot on  e.id= ot.object_id
        left join tb_enquiry_person per on e.id = per.enquiry_id and per.person_type = 'csa'
        left  join tb_pe_quotation_service_item tpqsi on tpqsi.order_id = e.id
        <if test="sampleInfo != null and sampleInfo != '' ">
            left join tb_enquiry_product tep on e.id = tep.enquiry_id
        </if>
        LEFT JOIN tb_enquiry_trf_relationship trfRel ON trfRel.EnquiryId = e.id
    </sql>

    <sql id="queryEnquiryPageWhere">
        <where>
            <if test="labCode != null and labCode != '' and (labCodeList == null or labCodeList.size() == 0)" >
                and e.lab_code = #{labCode}
            </if>
            <if test="labCodeList != null and labCodeList.size() > 0" >
                and e.lab_code in
                <foreach collection="labCodeList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="enquiryNo != null and enquiryNo != '' ">
                and e.enquiry_no = #{enquiryNo}
            </if>
            <if test="productCategory != null and productCategory != '' ">
                and e.product_category = #{productCategory}
            </if>
            <if test="productSubCategory != null and productSubCategory != '' ">
                and e.product_sub_category = #{productSubCategory}
            </if>
            <if test="templateFlag != null">
                and e.template_flag = #{templateFlag}
            </if>
            <if test="serviceItemList != null and serviceItemList.size() > 0" >
                and
                (
                tpqsi.charge_name in
                <foreach collection="serviceItemList" open="(" close=")" separator="," item="item">
                    #{item.serviceItemName}
                </foreach>
                or
                tpqsi.charge_name in
                <foreach collection="serviceItemList" open="(" close=")" separator="," item="item">
                    #{item.serviceItemNameCn}
                </foreach>
                or
                tpqsi.charge_name_cn in
                <foreach collection="serviceItemList" open="(" close=")" separator="," item="item">
                    #{item.serviceItemNameCn}
                </foreach>
                )
            </if>
            <if test="sampleInfo != null and sampleInfo != '' ">
                and (tep.style_no like concat('%',#{sampleInfo},'%') or
                tep.lot_no like concat('%',#{sampleInfo},'%') or
                tep.po_no like concat('%',#{sampleInfo},'%') or
                tep.other_sample_information like concat('%',#{sampleInfo},'%') or
                tep.Item_No like concat('%',#{sampleInfo},'%') or
                tep.product_description like concat('%',#{sampleInfo},'%'))
            </if>
            <if test="createdDateStart != null and createdDateStart != '' ">
                and e.created_date BETWEEN #{createdDateStart} and #{createdDateEnd}
            </if>
            <if test="enquiryStatus != null and enquiryStatus==99">
                and e.pending_flag = 1
            </if>
            <if test="enquiryStatus != null and enquiryStatus!=99">
                and e.enquiry_status = #{enquiryStatus}
            </if>
            <if test="createdBy != null and createdBy != '' ">
                and e.created_by = #{createdBy}
            </if>
            <if test="cSName != null and cSName != '' ">
                and e.cs_name= #{cSName}
            </if>
            <if test="csa != null and csa != '' ">
                and per.region_account = #{csa}
            </if>
            <if test="referenceEnquiryNo != null and referenceEnquiryNo != '' ">
                and e.reference_enquiry_no = #{referenceEnquiryNo}
            </if>
            <if test="salesName != null and salesName != '' and (salesNameList == null or salesNameList.size() == 0)">
                and e.sales_person = #{salesName}
            </if>
            <if test="salesNameList != null and salesNameList.size() > 0" >
                and (e.sales_person in
                <foreach collection="salesNameList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
                or e.created_by = #{regionAccount})
            </if>
            <if test="enquiryType != null and enquiryType != '' ">
                and e.enquiry_type = #{enquiryType}
            </if>
            <if test="remark != null and remark != '' ">
                and e.remark like concat('%',#{remark},'%')
            </if>
            <if test="productCategory != null and productCategory != '' ">
                and e.product_category = #{productCategory}
            </if>
            <if test="trfNo != null and trfNo != '' ">
                and trfRel.RefNo = #{trfNo}
            </if>
            <if test="subcontractOrderNo != null and subcontractOrderNo != '' " >
                and trfRel.ExternalOrderNo = #{subcontractOrderNo}
            </if>
            <if test="enquiryNoList != null and enquiryNoList.size() > 0" >
                and e.enquiry_no in
                <foreach collection="enquiryNoList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="applicant != null">
                <choose>
                    <when test="applicant.customerNo != null and applicant.customerNo != '' ">
                        AND eca.boss_number = #{applicant.customerNo}
                    </when>
                    <when test="applicant.customerNumberList != null and applicant.customerNumberList.size() >0">
                        AND eca.boss_number in
                        <foreach collection="applicant.customerNumberList" open="(" close=")" separator="," item="item">
                            #{item}
                        </foreach>
                    </when>

                    <when test="applicant.customerCnNameList != null and applicant.customerCnNameList.size() >0 and applicant.customerEnNameList != null and applicant.customerEnNameList.size() >0">
                        AND (eca.customer_name_cn in
                        <foreach collection="applicant.customerCnNameList" open="(" close=")" separator="," item="item">
                            #{item}
                        </foreach>
                        or
                        eca.customer_name_en in
                        <foreach collection="applicant.customerEnNameList" open="(" close=")" separator="," item="item">
                            #{item}
                        </foreach>
                        )
                    </when>

                    <when test="applicant.customerCnNameList != null and applicant.customerCnNameList.size() >0">
                        AND eca.customer_name_cn in
                        <foreach collection="applicant.customerCnNameList" open="(" close=")" separator="," item="item">
                            #{item}
                        </foreach>
                    </when>
                    <when test="applicant.customerEnNameList != null and applicant.customerEnNameList.size() >0">
                        AND eca.customer_name_en in
                        <foreach collection="applicant.customerEnNameList" open="(" close=")" separator="," item="item">
                            #{item}
                        </foreach>
                    </when>
                </choose>
            </if>


            <if test="payer != null">
                <choose>
                    <when test="payer.customerNo != null and payer.customerNo != '' ">
                        AND ecp.boss_number = #{payer.customerNo}
                    </when>
                    <when test="payer.customerNumberList != null and payer.customerNumberList.size() >0">
                        AND ecp.boss_number in
                        <foreach collection="payer.customerNumberList" open="(" close=")" separator="," item="item">
                            #{item}
                        </foreach>
                    </when>


                    <when test="payer.customerCnNameList != null and payer.customerCnNameList.size() >0 and payer.customerEnNameList != null and payer.customerEnNameList.size() >0">
                        AND (ecp.customer_name_cn in
                        <foreach collection="payer.customerCnNameList" open="(" close=")" separator="," item="item">
                            #{item}
                        </foreach>
                        or ecp.customer_name_en in
                        <foreach collection="payer.customerEnNameList" open="(" close=")" separator="," item="item">
                            #{item}
                        </foreach>
                        )
                    </when>

                    <when test="payer.customerCnNameList != null and payer.customerCnNameList.size() >0">
                        AND ecp.customer_name_cn in
                        <foreach collection="payer.customerCnNameList" open="(" close=")" separator="," item="item">
                            #{item}
                        </foreach>
                    </when>
                    <when test="payer.customerEnNameList != null and payer.customerEnNameList.size() >0">
                        AND ecp.customer_name_en in
                        <foreach collection="payer.customerEnNameList" open="(" close=")" separator="," item="item">
                            #{item}
                        </foreach>
                    </when>
                </choose>
            </if>

            <if test="buyer != null">
                <choose>
                    <when test="buyer.customerNo != null and buyer.customerNo != '' ">
                        AND ecb.boss_number = #{buyer.customerNo}
                    </when>
                    <when test="buyer.customerNumberList != null and buyer.customerNumberList.size() >0">
                        AND ecb.boss_number in
                        <foreach collection="buyer.customerNumberList" open="(" close=")" separator="," item="item">
                            #{item}
                        </foreach>
                    </when>

                    <when test="buyer.customerCnNameList != null and buyer.customerCnNameList.size() >0 and buyer.customerEnNameList != null and buyer.customerEnNameList.size() >0">
                        AND (ecb.customer_name_cn in
                        <foreach collection="buyer.customerCnNameList" open="(" close=")" separator="," item="item">
                            #{item}
                        </foreach>
                        or
                        ecb.customer_name_en in
                        <foreach collection="buyer.customerEnNameList" open="(" close=")" separator="," item="item">
                            #{item}
                        </foreach>
                        )
                    </when>

                    <when test="buyer.customerCnNameList != null and buyer.customerCnNameList.size() >0">
                        AND ecb.customer_name_cn in
                        <foreach collection="buyer.customerCnNameList" open="(" close=")" separator="," item="item">
                            #{item}
                        </foreach>
                    </when>
                    <when test="buyer.customerEnNameList != null and buyer.customerEnNameList.size() >0">
                        AND ecb.customer_name_en in
                        <foreach collection="buyer.customerEnNameList" open="(" close=")" separator="," item="item">
                            #{item}
                        </foreach>
                    </when>
                </choose>
            </if>

            <if test="tagValueSelectDTOS != null and tagValueSelectDTOS.size() >0">
                AND
                <foreach collection="tagValueSelectDTOS" item="tag" index="index" open="(" close=")" separator="and" >
                    <if test="tag.selectedValue != null and tag.selectedValue.size() >0">
                        <foreach collection="tag.selectedValue" item="va" index="v_index" open="(" close=")" separator="or" >
                            json_contains (
                            ot.DATA,
                            JSON_OBJECT ('selectedValue', #{va})
                            )
                        </foreach>
                    </if>
                </foreach>
            </if>
            <if test="(enquiryNo == null or enquiryNo == '') and enquiryStatus == null and (enquiryNoBatchList == null or enquiryNoBatchList.size() == 0)">
                AND e.enquiry_status != 30
            </if>
        </where>
    </sql>


    <select id="selectPage" resultType="com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryPO"
            parameterType="com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryQueryReq">
        select distinct
        <include refid="queryEnquiryPageColumns"></include>
        from
        <include refid="queryEnquiryPageTables"></include>
        <include refid="queryEnquiryPageWhere"></include>
        order by e.enquiry_no desc
    </select>

</mapper>