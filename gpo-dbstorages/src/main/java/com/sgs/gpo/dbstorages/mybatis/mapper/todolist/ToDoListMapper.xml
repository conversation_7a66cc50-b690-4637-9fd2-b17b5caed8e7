<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.todolist.ToDoListMapper" >

    <sql id="queryPageColumns">
        t.id,
        t.type,
        t.lab_code labCode,
        t.object_no objectNo,
        t.object_id objectId,
        t.to_lab_code toLabCode,
        t.product_line_code productLineCode,
        t.to_product_line_code toProductLineCode,
        t.contact,
        t.contact_email contactEmail,
        t.contact_tel contactTel,
        t.expect_due_date expectDueDate,
        t.parcel_nos parcelNos,
        t.sample_receive_date sampleReceiveDate,
        t.sample_receive_by sampleReceiveBy,
        t.status,
        t.pend_flag pendFlag,
        t.pe,
        t.pe_email peEmail,
        t.tat,
        t.cs,
        t.service_type serviceType,
        t.source,
        t.created_date createdDate,
        t.data,
        t.system_id systemId
    </sql>

    <sql id="queryPageTables">
        tb_todo_list t
    </sql>

    <sql id="queryPageWhere">

        <where>
            t.to_lab_code = #{req.labCode} and (t.system_id != 35 or t.system_id is null or t.system_id = '')
            <if test="req.type != null and req.type != '' ">
                and t.type = #{req.type}
            </if>
            <if test="req.from != null and req.from != '' ">
                and t.lab_code = #{req.from}
            </if>
            <if test="req.orderNo != null and req.orderNo != '' ">
                and t.`data` -> '$.orderNo' = #{req.orderNo}
            </if>
            <if test="req.referenceNo != null and req.referenceNo != '' ">
                and t.object_no = #{req.referenceNo}
            </if>
            <if test="req.referenceNoList != null and req.referenceNoList.size() >0">
                AND t.object_no in
                <foreach collection="req.referenceNoList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="req.contact != null and req.contact != '' ">
                and (t.contact  like  CONCAT('%',#{req.contact},'%') or t.`data` -> '$.subContractContract' like  CONCAT('%',#{req.contact},'%'))
            </if>
            <if test="req.status != null and req.status != '' ">
                and t.status = #{req.status}
            </if>
            <if test="req.pendFlag != null and req.pendFlag != '' ">
                and t.pend_flag = #{req.pendFlag}
            </if>
            <if test="req.statusList != null and req.statusList.size() >0">
                AND t.status in
                <foreach collection="req.statusList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="req.createdDateStart != null and req.createdDateStart != '' ">
                and t.created_date BETWEEN #{req.createdDateStart} and #{req.createdDateEnd}
            </if>

            <if test="req.dueDateStart != null and req.dueDateStart != '' ">
            <![CDATA[
                and ((t.expect_due_date BETWEEN #{req.dueDateStart} and #{req.dueDateEnd}) or
                (t.`data` -> '$.subContractExpectDueDate' >= #{req.dueDateStartLong}
                and t.`data` -> '$.subContractExpectDueDate' <= #{req.dueDateEndLong}))
                    ]]>
            </if>

        </where>
    </sql>

    <sql id="queryPageWhere_RSTS">

        <where>
            t.to_lab_code = #{req.labCode} and t.system_id = 35
            <if test="req.type != null and req.type != '' ">
                and t.type = #{req.type}
            </if>
            <if test="req.from != null and req.from != '' ">
                and t.lab_code = #{req.from}
            </if>
            <if test="req.orderNo != null and req.orderNo != '' ">
                and t.object_no = #{req.orderNo}
            </if>
            <if test="req.referenceNo != null and req.referenceNo != '' ">
                and t.object_id = #{req.referenceNo}
            </if>
            <if test="req.referenceNoList != null and req.referenceNoList.size() >0">
                AND t.object_id in
                <foreach collection="req.referenceNoList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="req.contact != null and req.contact != '' ">
                and (t.contact  like  CONCAT('%',#{req.contact},'%') or t.`data` -> '$.subContractContract' like  CONCAT('%',#{req.contact},'%'))
            </if>
            <if test="req.status != null and req.status != '' ">
                and t.status = #{req.status}
            </if>
            <if test="req.pendFlag != null and req.pendFlag != '' ">
                and t.pend_flag = #{req.pendFlag}
            </if>
            <if test="req.statusList != null and req.statusList.size() >0">
                AND t.status in
                <foreach collection="req.statusList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="req.createdDateStart != null and req.createdDateStart != '' ">
                and t.created_date BETWEEN #{req.createdDateStart} and #{req.createdDateEnd}
            </if>

            <if test="req.dueDateStart != null and req.dueDateStart != '' ">
                <![CDATA[
                and ((t.expect_due_date BETWEEN #{req.dueDateStart} and #{req.dueDateEnd}) or
                (t.`data` -> '$.subContractExpectDueDate' >= #{req.dueDateStartLong}
                and t.`data` -> '$.subContractExpectDueDate' <= #{req.dueDateEndLong}))
                    ]]>
            </if>

        </where>
    </sql>

    <select id="selectPage" resultType="com.sgs.gpo.dbstorages.mybatis.model.todolist.ToDoListPO"
            parameterType="com.sgs.gpo.facade.model.todolist.ToDoListQueryReq">
        select
        <include refid="queryPageColumns"></include>
        from
        <include refid="queryPageTables"></include>
        <include refid="queryPageWhere"></include>
        union
        select
        <include refid="queryPageColumns"></include>
        from
        <include refid="queryPageTables"></include>
        <include refid="queryPageWhere_RSTS"></include>
        order by createdDate desc
    </select>

    <update id="updateStatus" parameterType="com.sgs.gpo.dbstorages.mybatis.model.todolist.ToDoListPO">
        update tb_todo_list
        <set>
            status=#{status},
            <if test="modifiedBy != null  and  modifiedBy != '' ">
                modified_by=#{modifiedBy},
            </if>
            <if test="data != null  and  data != '' ">
                data=#{data},
            </if>
            modified_date=#{modifiedDate}
        </set>
        <where>
            object_no=#{objectNo}
            <if test="object_id != null  and  object_id != '' ">
                and object_id=#{objectId}
            </if>
        </where>
    </update>



</mapper>