<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.noticeboard.NoticeRecordMapper" >

    <select id="getUnReadCount" parameterType="com.sgs.gpo.facade.model.setting.notice.req.NoticeRecordQueryReq" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            tb_notice tn
        WHERE
            tn.system_id = #{req.systemId}
          AND NOT EXISTS (
                SELECT
                    rr.notice_id
                FROM
                    tb_notice_read_record rr
                WHERE
                    tn.id = rr.notice_id
                  AND rr.region_account = #{req.regionAccount})
    </select>

</mapper>