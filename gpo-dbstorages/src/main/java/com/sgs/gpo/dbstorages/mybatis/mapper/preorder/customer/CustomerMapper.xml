<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.preorder.customer.CustomerMapper">
    <resultMap id="BaseResultMap" type="com.sgs.gpo.dbstorages.mybatis.model.preorder.customer.CustomerPO" >
        <id column="ID" property="id" jdbcType="VARCHAR" />
        <result column="GeneralOrderID" property="generalOrderId" jdbcType="VARCHAR" />
        <result column="CustomerID" property="customerId" jdbcType="VARCHAR" />
        <result column="CustomerGroupID" property="customerGroupId" jdbcType="VARCHAR" />
        <result column="CustomerAddressCN" property="customerAddressCn" jdbcType="VARCHAR" />
        <result column="CustomerAddressEN" property="customerAddressEn" jdbcType="VARCHAR" />
        <result column="CustomerNameCN" property="customerNameCn" jdbcType="VARCHAR" />
        <result column="CustomerNameEN" property="customerNameEn" jdbcType="VARCHAR" />
        <result column="ContactPersonEmail" property="contactPersonEmail" jdbcType="VARCHAR" />
        <result column="ContactPersonFax" property="contactPersonFax" jdbcType="VARCHAR" />
        <result column="ContactPersonPhone1" property="contactPersonPhone1" jdbcType="VARCHAR" />
        <result column="ContactPersonName" property="contactPersonName" jdbcType="VARCHAR" />
        <result column="ContactPersonRemark" property="contactPersonRemark" jdbcType="VARCHAR" />
        <result column="ContactPersonPhone2" property="contactPersonPhone2" jdbcType="VARCHAR" />
        <result column="CustomerCredit" property="customerCredit" jdbcType="VARCHAR" />
        <result column="CustomerUsage" property="customerUsage" jdbcType="VARCHAR" />
        <result column="AccountID" property="accountId" jdbcType="BIGINT" />
        <result column="BossNumber" property="bossNumber" jdbcType="BIGINT" />
        <result column="ContactAddressID" property="contactAddressId" jdbcType="VARCHAR" />
        <result column="BuyerGroup" property="buyerGroup" jdbcType="VARCHAR" />
        <result column="BuyerGroupName" property="buyerGroupName" jdbcType="VARCHAR" />
        <result column="SupplierNo" property="supplierNo" jdbcType="VARCHAR" />
        <result column="LogoCloudID" property="logoCloudId" jdbcType="VARCHAR" />
        <result column="OrganizationName" property="organizationName" jdbcType="VARCHAR" />
        <result column="IsAsApplicant" property="isAsApplicant" jdbcType="TINYINT" />
        <result column="ReportDeliveredTo" property="reportDeliveredTo" jdbcType="VARCHAR" />
        <result column="FailedReportDeliveredTo" property="failedReportDeliveredTo" jdbcType="VARCHAR" />
        <result column="BossSiteUseID" property="bossSiteUseId" jdbcType="BIGINT" />
        <result column="BossContactID" property="bossContactId" jdbcType="BIGINT" />

        <result column="PrimaryFlag" property="primaryFlag" jdbcType="VARCHAR" />
        <result column="MonthlyPayment" property="monthlyPayment" jdbcType="VARCHAR" />
        <result column="BossLocationCode" property="bossLocationCode" jdbcType="VARCHAR" />
        <result column="PaymentTermName" property="paymentTermName" jdbcType="VARCHAR" />

        <result column="ModifiedDate" property="modifiedDate" jdbcType="TIMESTAMP" />
        <result column="ModifiedBy" property="modifiedBy" jdbcType="VARCHAR" />

    </resultMap>

    <sql id="Base_Column_List" >
        ID, GeneralOrderID, CustomerID, CustomerGroupID, CustomerAddressCN, CustomerAddressEN,
        CustomerNameCN, CustomerNameEN, ContactPersonEmail, ContactPersonFax, ContactPersonPhone1,
        ContactPersonName, ContactPersonRemark, ContactPersonPhone2, CustomerCredit, CustomerUsage,
        AccountID, BossNumber, ContactAddressID, BuyerGroup, BuyerGroupName, SupplierNo,
        LogoCloudID, ActiveIndicator, OrganizationName,
        IsAsApplicant, ReportDeliveredTo, FailedReportDeliveredTo, BossSiteUseID, BossContactID,
        PrimaryFlag,MonthlyPayment,BossLocationCode,PaymentTermName,ModifiedBy,ModifiedDate
    </sql>


    <select id="queryByName" resultType="com.sgs.gpo.dbstorages.mybatis.model.preorder.customer.CustomerPO">
        SELECT distinct
            c.CustomerNameEN as customerNameEn,c.CustomerNameCN as customerNameCn,IFNULL(c.BossNumber,0) as bossBumber
        FROM tb_customer_instance c  FORCE INDEX(idx_usage_customer_name)
        INNER JOIN tb_lab_instance lab ON lab.GeneralOrderID = c.GeneralOrderID
        where
        lab.LabCode = #{req.lab.labCode}
        and c.CustomerUsage = #{req.customerUsage}
        and (c.CustomerNameCN like concat('%',#{req.customerName},'%') or c.CustomerNameEN like concat('%',#{req.customerName},'%'))
    </select>

    <select id="checkPayerIsSame" resultType="java.lang.Integer">
        SELECT
        count(DISTINCT t.CustomerID)
        FROM
        `tb_customer_instance` t
        WHERE
        t.CustomerUsage = 'Payer'
        AND t.GeneralOrderID IN (
        <foreach collection="orderIds" item="item" index="index" separator =",">
            #{item}
        </foreach >
        )
    </select>
</mapper>