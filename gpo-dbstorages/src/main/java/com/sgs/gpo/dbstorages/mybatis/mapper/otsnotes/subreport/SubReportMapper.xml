<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.subreport.SubReportMapper">

    <sql id = "select">
        select distinct
            sr.ID,
            sr.GeneralOrderInstanceID,
            sr.ObjectType,
            sr.ObjectNo,
            sr.CloudID,
            sr.Filename,
            sr.LanguageCode,
            sr.ConclusionId,
            sr.status,
            sr.SoftcopyDeliveryDate,
            sr.ReportSource,
            sr.AwbNo,
            sr.IsLastReport,
            sr.FileType,
            sr.SubReportNo,
            sr.ReportNo,
            sr.ReportFileType,
            sr.LanguageId,
            sr.OldSubReportId,
            sr.SubcontractId,
            sr.<PERSON>ndicat<PERSON>,
            sr.<PERSON>,
            sr.<PERSON>,
            sr.<PERSON>,
            sr.<PERSON>,
            sr.<PERSON>
        from tb_sub_report sr
        inner JOIN tre_report_sub_report_relationship rsrr ON sr.id = rsrr.sub_report_id
        LEFT JOIN tb_report tr ON rsrr.report_id = tr.id
        <where>
            <if test="reportNo != null and  reportNo != '' ">
                and tr.reportNo = #{reportNo}
            </if>
            <if test="languageId != null and  languageId != '' ">
                and sr.LanguageId = #{languageId}
            </if>
        </where>
    </sql>



    <select id="select" resultType="com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subreport.SubReportPO">
        <include refid="select"></include>
    </select>


    <select id="selectSubReportsNoMatrix" resultType="com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subreport.SubReportPO">
        SELECT
            sr.ObjectNo,
            sr.id,
            sr.subReportNo,
            sr.ObjectType,
            sr.Filename,
            sr.CloudID,
            sr.LanguageId,
            sr.ReportFileType,
            sr.CreatedDate,
            sr.CreatedBy,
            sr.STATUS,
            sr.SubcontractId,
            sr.CombineFlag
        FROM
            tb_sub_report sr
                INNER JOIN tb_general_order_instance goi ON goi.id = sr.GeneralOrderInstanceID
                LEFT JOIN tre_report_sub_report_relationship rsrr ON sr.id = rsrr.sub_report_id
        WHERE
            goi.OrderNo = #{orderNo}
          AND sr.ReportFileType = 1501
          AND sr.`status` = 208
          AND rsrr.id IS NULL
        ORDER BY
            sr.CreatedDate
    </select>

</mapper>