<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.objectsamplequantity.ObjectSampleQuantityMapper" >

    <select  id="selectPage" resultType="com.sgs.gpo.dbstorages.mybatis.model.otsnotes.objectsamplequantity.ObjectSampleQuantityPO">
        SELECT * FROM tb_object_sample_quantity
        <where>
            AND active_indicator = 1
            <if test="req.objectNoList != null and req.objectNoList.size() > 0" >
                AND object_no in
                 <foreach item="item" collection="req.objectNoList" separator="," open="(" close=")" index="index">
                    #{item}
                </foreach>
            </if>
            <if test="req.objectIdList != null and req.objectIdList.size() > 0">
                AND object_id in
                 <foreach item="item" collection="req.objectIdList" separator="," open="(" close=")" index="index">
                     #{item}
                 </foreach>
            </if>
        </where>
        order by created_date desc
    </select>

    <update  id="updateActiveIndicatorByIds">
        UPDATE tb_object_sample_quantity
        SET active_indicator = #{activeIndicator}
        <where>
            <choose>
                <when test="ids != null and ids.size() > 0">
                    AND id IN
                    <foreach item="item" collection="ids" separator="," open="(" close=")" index="index">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    1!=1
                </otherwise>
            </choose>
        </where>
    </update>
</mapper>