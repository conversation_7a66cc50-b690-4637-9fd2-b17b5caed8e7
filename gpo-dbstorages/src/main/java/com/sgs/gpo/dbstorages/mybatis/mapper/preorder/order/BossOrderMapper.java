package com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.BossOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.payment.BossOrderInvoiceDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BossOrderMapper extends BaseMapper<BossOrderPO> {

    Boolean getBossOrderByTrueOrderNo(@Param("trueOrderNo") String trueOrderNo);

    GeneralOrderPO getOrderByBossOrderNo(@Param("bossOrderNo") String bossOrderNo);

   int createBossInvoiceRel(List<BossOrderPO> list);

    List<BossOrderInvoiceDTO> getBossOrderNoByOrderNo(@Param("orderNo") String orderNo);
}
