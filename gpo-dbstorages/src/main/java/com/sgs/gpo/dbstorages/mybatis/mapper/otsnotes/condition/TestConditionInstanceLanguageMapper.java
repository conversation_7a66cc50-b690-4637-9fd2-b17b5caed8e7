package com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.condition;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.condition.TestConditionInstanceLanguagePO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.condition.TestConditionInstancePO;

/**
 * <AUTHOR>
 * @date 2023/6/28 13:10
 */
public interface TestConditionInstanceLanguageMapper extends BaseMapper<TestConditionInstanceLanguagePO> {

}
