package com.sgs.gpo.dbstorages.mybatis.mapper.preorder.enquiry;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryPO;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryQueryReq;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2023/7/6 09:51
 */
public interface EnquiryMapper extends BaseMapper<EnquiryPO> {

    /**
     * 查询 enquiry 标准对象列表
     * @param enquiryQueryReq
     * @return
     */
    IPage<EnquiryPO> selectPage(@Param("page") IPage page, @Param("req") EnquiryQueryReq enquiryQueryReq);


}
