package com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.GeneralOrderPO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.quotation.QuotationHeaderPO;
import com.sgs.gpo.facade.model.payment.costlist.req.ActualFeeSaveReq;
import com.sgs.gpo.facade.model.preorder.order.dto.OrderQuotationDTO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderSyncTrfReq;
import com.sgs.gpo.facade.model.preorder.productsample.req.QuotationQueryReq;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface GeneralOrderMapper extends BaseMapper<GeneralOrderPO> {

    BigDecimal getAllTotalAmountByPaidUp(@Param("orderNo") String orderNo);

    List<OrderQuotationDTO> getQuotationOrderForOrderId(@Param("orderId") String orderId);

    List<String> getOrderByBossOrderNos(@Param("list") List<String> bossOrderNoList);

    List<String> getOrderNoListByInvoiceNo(@Param("invoiceNos") List<String> invoiceNos);

    /**
     * 查询tb_pe_quotation_head 表
     */
    List<QuotationHeaderPO> selectQuotationList(QuotationQueryReq quotationQueryReq);
    List<Map<String,String>> selectCpToSgsMartData(OrderSyncTrfReq orderSyncTrfReq);

    void updateActualFeeByOrderNo(@Param("list") List<ActualFeeSaveReq> request);
    int updateForOdc();
}
