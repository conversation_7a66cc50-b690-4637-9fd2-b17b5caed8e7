<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.CareLabelMapper">

    <resultMap id="careLabelResult" type="com.sgs.framework.model.common.productsample.CareLabelBO">
        <id column="ID" property="careLabelInstanceId" jdbcType="VARCHAR" />
        <result column="CareInstruction" property="careInstruction" jdbcType="VARCHAR" />
        <result column="CareLabelSeq" property="careLabelSeq" jdbcType="INTEGER" />
        <result column="CloudID" property="cloudId" jdbcType="VARCHAR" />
        <result column="GeneralOrderID" property="generalOrderId" jdbcType="VARCHAR" />
        <collection property="sampleNos" column="ID"
                    javaType="List" ofType="java.lang.String">
            <result column="SampleID" jdbcType="VARCHAR" />
        </collection>
    </resultMap>


    <select id="queryCareLabelList" resultMap="careLabelResult">
        SELECT
            ci.ID,
            ci.CareInstruction,
            ci.CareLabelSeq,
            ci.GeneralOrderID,
            p.SampleID,
            oa.CloudID
        FROM
            tb_carelabel_instance ci
                INNER JOIN tb_product_carelabel_relationship cr ON cr.CareLabelInstanceID = ci.ID
                INNER JOIN tb_product_instance p ON p.ID = cr.ProductInstanceID
                LEFT JOIN tb_order_attachment oa ON oa.ObjectID = ci.ID
                AND oa.BusinessType = 'CareInstruction'
        <where>
            <if test="orderId != null and orderId!=''">
                AND ci.GeneralOrderID = #{orderId}
            </if>
            <if test="orderIds != null and orderIds.size>0">
                AND ci.GeneralOrderID in
                <foreach collection="orderIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        order by p.SampleID
    </select>

</mapper>