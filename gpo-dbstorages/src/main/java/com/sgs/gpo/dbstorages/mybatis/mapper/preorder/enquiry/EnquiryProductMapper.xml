<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.preorder.enquiry.EnquiryProductMapper" >

    <resultMap id="enquiryProductDtoMap" type="com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryProductPO" >
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="enquiry_id" property="enquiryId" jdbcType="VARCHAR" />
        <result column="product_library_id" property="productLibraryId" jdbcType="VARCHAR" />
        <result column="careLabel_instance_id" property="carelabelInstanceId" jdbcType="VARCHAR" />
        <result column="communication_log_id" property="communicationLogId" jdbcType="VARCHAR" />
        <result column="buyer_organnization1" property="buyerOrgannization1" jdbcType="VARCHAR" />
        <result column="buyer_organnization2" property="buyerOrgannization2" jdbcType="VARCHAR" />
        <result column="buyer_organnization3" property="buyerOrgannization3" jdbcType="VARCHAR" />
        <result column="buyer_organnization_code1" property="buyerOrgannizationCode1" jdbcType="VARCHAR" />
        <result column="buyer_organnization_code2" property="buyerOrgannizationCode2" jdbcType="VARCHAR" />
        <result column="buyer_organnization_code3" property="buyerOrgannizationCode3" jdbcType="VARCHAR" />
        <result column="buyer_aliase" property="buyerAliase" jdbcType="VARCHAR" />
        <result column="buyer_sourcing_office" property="buyerSourcingOffice" jdbcType="VARCHAR" />
        <result column="country_of_origin" property="countryOfOrigin" jdbcType="VARCHAR" />
        <result column="country_of_destination" property="countryOfDestination" jdbcType="VARCHAR" />
        <result column="dff_form_id" property="dffFormId" jdbcType="VARCHAR" />
        <result column="supplier" property="supplier" jdbcType="VARCHAR" />
        <result column="supplier_no" property="supplierNo" jdbcType="VARCHAR" />
        <result column="factory_id" property="factoryId" jdbcType="VARCHAR" />
        <result column="factory_name" property="factoryName" jdbcType="VARCHAR" />
        <result column="first_fpu_no" property="firstFpuNo" jdbcType="VARCHAR" />
        <result column="first_pass_fpu_no" property="firstPassFpuNo" jdbcType="VARCHAR" />
        <result column="first_time_application_flag" property="firstTimeApplicationFlag" jdbcType="VARCHAR" />
        <result column="fpu_no" property="fpuNo" jdbcType="VARCHAR" />
        <result column="fpu_report_no" property="fpuReportNo" jdbcType="VARCHAR" />
        <result column="gpu_no" property="gpuNo" jdbcType="VARCHAR" />
        <result column="lot_no" property="lotNo" jdbcType="VARCHAR" />
        <result column="no_of_sample" property="noOfSample" jdbcType="INTEGER" />
        <result column="peformance_code" property="peformanceCode" jdbcType="VARCHAR" />
        <result column="po_no" property="poNo" jdbcType="VARCHAR" />
        <result column="previous_report_no" property="previousReportNo" jdbcType="VARCHAR" />
        <result column="trim_report_no" property="trimReportNo" jdbcType="VARCHAR" />
        <result column="fabric_report" property="fabricReport" jdbcType="VARCHAR" />
        <result column="product_category1" property="productCategory1" jdbcType="VARCHAR" />
        <result column="product_category2" property="productCategory2" jdbcType="VARCHAR" />
        <result column="style_no" property="styleNo" jdbcType="VARCHAR" />
        <result column="product_color" property="productColor" jdbcType="VARCHAR" />
        <result column="production_stage" property="productionStage" jdbcType="VARCHAR" />
        <result column="sample_id" property="sampleId" jdbcType="VARCHAR" />
        <result column="sample_received_date" property="sampleReceivedDate" jdbcType="TIMESTAMP" />
        <result column="age_group" property="ageGroup" jdbcType="VARCHAR" />
        <result column="end_use1" property="endUse1" jdbcType="VARCHAR" />
        <result column="construction" property="construction" jdbcType="VARCHAR" />
        <result column="yarn_count" property="yarnCount" jdbcType="VARCHAR" />
        <result column="thread_count" property="threadCount" jdbcType="VARCHAR" />
        <result column="fiber_composition" property="fiberComposition" jdbcType="VARCHAR" />
        <result column="fiber_weight" property="fiberWeight" jdbcType="VARCHAR" />
        <result column="fabric_width" property="fabricWidth" jdbcType="VARCHAR" />
        <result column="season" property="season" jdbcType="VARCHAR" />
        <result column="size" property="size" jdbcType="VARCHAR" />
        <result column="special_finishing" property="specialFinishing" jdbcType="VARCHAR" />
        <result column="collection" property="collection" jdbcType="VARCHAR" />
        <result column="care_label_flag" property="careLabelFlag" jdbcType="VARCHAR" />
        <result column="care_label" property="careLabel" jdbcType="VARCHAR" />
        <result column="care_label_wording" property="careLabelWording" jdbcType="VARCHAR" />
        <result column="header_id" property="headerId" jdbcType="VARCHAR" />
        <result column="product_type" property="productType" jdbcType="VARCHAR" />
        <result column="product_item_no" property="productItemNo" jdbcType="VARCHAR" />
        <result column="cancel_flag" property="cancelFlag" jdbcType="BIT" />
        <result column="ref_sample_id" property="refSampleId" jdbcType="VARCHAR" />
        <result column="language_id" property="languageId" jdbcType="INTEGER" />
        <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
        <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
        <result column="active_indicator" property="activeIndicator" jdbcType="INTEGER" />
        <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
        <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
        <result column="Vendor_No" property="vendorNo" jdbcType="VARCHAR" />
        <result column="other_sample_information" property="otherSampleInformation" jdbcType="LONGVARCHAR" />
        <result column="ref_code1" property="refCode1" jdbcType="LONGVARCHAR" />
        <result column="ref_code2" property="refCode2" jdbcType="LONGVARCHAR" />
        <result column="ref_code3" property="refCode3" jdbcType="LONGVARCHAR" />
        <result column="ref_code4" property="refCode4" jdbcType="LONGVARCHAR" />
        <result column="ref_code5" property="refCode5" jdbcType="LONGVARCHAR" />
        <result column="ref_code6" property="refCode6" jdbcType="LONGVARCHAR" />
        <result column="ref_code7" property="refCode7" jdbcType="LONGVARCHAR" />
        <result column="ref_code8" property="refCode8" jdbcType="LONGVARCHAR" />
        <result column="ref_code9" property="refCode9" jdbcType="LONGVARCHAR" />
        <result column="ref_code10" property="refCode10" jdbcType="LONGVARCHAR" />
        <result column="product_description" property="productDescription" jdbcType="LONGVARCHAR" />
        <result column="special_customer_attribute1" property="specialCustomerAttribute1" jdbcType="LONGVARCHAR" />
        <result column="special_customer_attribute2" property="specialCustomerAttribute2" jdbcType="LONGVARCHAR" />
        <result column="special_customer_attribute3" property="specialCustomerAttribute3" jdbcType="LONGVARCHAR" />
        <result column="special_customer_attribute4" property="specialCustomerAttribute4" jdbcType="LONGVARCHAR" />
        <result column="special_customer_attribute5" property="specialCustomerAttribute5" jdbcType="LONGVARCHAR" />
        <result column="special_customer_attribute6" property="specialCustomerAttribute6" jdbcType="LONGVARCHAR" />
        <result column="special_customer_attribute7" property="specialCustomerAttribute7" jdbcType="LONGVARCHAR" />
        <result column="special_customer_attribute8" property="specialCustomerAttribute8" jdbcType="LONGVARCHAR" />
        <result column="special_customer_attribute9" property="specialCustomerAttribute9" jdbcType="LONGVARCHAR" />
        <result column="special_customer_attribute10" property="specialCustomerAttribute10" jdbcType="LONGVARCHAR" />
        <result column="special_customer_attribute11" property="specialCustomerAttribute11" jdbcType="LONGVARCHAR" />
        <result column="special_customer_attribute12" property="specialCustomerAttribute12" jdbcType="LONGVARCHAR" />
        <result column="special_customer_attribute13" property="specialCustomerAttribute13" jdbcType="LONGVARCHAR" />
        <result column="special_customer_attribute14" property="specialCustomerAttribute14" jdbcType="LONGVARCHAR" />
        <result column="special_customer_attribute15" property="specialCustomerAttribute15" jdbcType="LONGVARCHAR" />
        <result column="special_customer_attribute16" property="specialCustomerAttribute16" jdbcType="LONGVARCHAR" />
        <result column="special_customer_attribute17" property="specialCustomerAttribute17" jdbcType="LONGVARCHAR" />
        <result column="special_customer_attribute18" property="specialCustomerAttribute18" jdbcType="LONGVARCHAR" />
        <result column="special_product_attribute1" property="specialProductAttribute1" jdbcType="LONGVARCHAR" />
        <result column="special_product_attribute2" property="specialProductAttribute2" jdbcType="LONGVARCHAR" />
        <result column="special_product_attribute3" property="specialProductAttribute3" jdbcType="LONGVARCHAR" />
        <result column="special_product_attribute4" property="specialProductAttribute4" jdbcType="LONGVARCHAR" />
        <result column="special_product_attribute5" property="specialProductAttribute5" jdbcType="LONGVARCHAR" />
        <result column="special_product_attribute6" property="specialProductAttribute6" jdbcType="LONGVARCHAR" />
        <result column="special_product_attribute7" property="specialProductAttribute7" jdbcType="LONGVARCHAR" />
        <result column="special_product_attribute8" property="specialProductAttribute8" jdbcType="LONGVARCHAR" />
        <result column="special_product_attribute9" property="specialProductAttribute9" jdbcType="LONGVARCHAR" />
        <result column="special_product_attribute10" property="specialProductAttribute10" jdbcType="LONGVARCHAR" />
        <result column="Item_No" property="itemNo" jdbcType="LONGVARCHAR" />
    </resultMap>


    <select id="queryEnquiryProductByDff" resultMap="enquiryProductDtoMap">
        select distinct dff.enquiry_id,dff.language_id
        <if test="dffColumns != null and dffColumns.size() > 0" >
            <foreach collection="dffColumns" item="item" index="i" >
                ,${item}
            </foreach>
        </if>
        FROM tb_enquiry_product dff
        WHERE
        (dff.header_id IS NULL OR dff.header_id = '')
        and enquiry_id in
        <foreach collection="enquiryIds" item="enquiryId" separator="," open="(" close=")">
            #{enquiryId}
        </foreach>

    </select>


</mapper>