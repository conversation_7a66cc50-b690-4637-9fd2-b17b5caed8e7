<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.GeneralOrderMapper" >

    <select id="getAllTotalAmountByPaidUp" resultType="java.math.BigDecimal">
        select sum(a.totalPrice)
        from (
                 SELECT distinct
                     IFNULL( oo.id, o.id )   AS orderId,
                     IFNULL( oo.TotalPrice, o.TotalPrice ) AS totalPrice
                 FROM tb_general_order o
                          LEFT JOIN tb_order_paid_up op ON op.order_id = o.id
                     AND op.active_indicator = 1
                          LEFT JOIN tb_order_paid_up opp ON opp.paid_up_id = op.paid_up_id
                     AND opp.active_indicator = 1
                          LEFT JOIN tb_general_order oo ON oo.id = opp.order_id
                 WHERE o.OrderNo = #{orderNo}
             ) a
    </select>

    <select id="getQuotationOrderForOrderId" resultType="com.sgs.gpo.facade.model.preorder.order.dto.OrderQuotationDTO">
        SELECT
            tpqh.`id` quotationHeadId,
            tpqhh.`id` quotationHeadHistoryId,
            if(tpqhh.quotation_flag<![CDATA[&]]>1=1,1,0) freeQuotation,
            tpqhh.quotation_flag quotationFlag
        FROM
            tb_pe_quotation_head tpqh
                LEFT JOIN tb_pe_quotation_head_history tpqhh
                          ON tpqhh.quotation_head_id = tpqh.id
                              AND tpqhh.quotation_version = tpqh.quotation_version
        WHERE tpqh.status != 4
        AND tpqh.order_id = #{orderId}
    </select>

    <select id="getOrderByBossOrderNos" resultType="java.lang.String">
        SELECT tgo.OrderNo orderNo
        FROM tb_general_order tgo
        INNER JOIN tre_order_relationship tor ON tgo.ID = tor.GeneralOrderID
        INNER JOIN tb_boss_order tbo ON tor.BOSSOrderID = tbo.ID
        WHERE
        tbo.TrueOrderNo IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getOrderNoListByInvoiceNo" resultType="java.lang.String">
        SELECT
        tgo.`OrderNo` orderNo
        FROM
        `tb_general_order` tgo
        INNER JOIN `tre_order_relationship` tor ON tgo.`ID` = tor.`GeneralOrderID`
        INNER JOIN `tb_boss_order` tbo ON tor.`BOSSOrderID` = tbo.`ID`
        INNER JOIN `tb_boss_order_invoice` tboi ON tbo.`TrueOrderNo` = tboi.`boss_order_no`
        WHERE tgo.`OrderStatus` != 7
        <if test="invoiceNos!=null and invoiceNos.size>0">
            AND tboi.`invoice_no` in
            <foreach collection="invoiceNos" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="selectQuotationList" resultType="com.sgs.gpo.dbstorages.mybatis.model.preorder.quotation.QuotationHeaderPO">
        SELECT
            peh.order_id orderId,
            peh.to_boss_flag toBossFlag
        FROM
            tb_pe_quotation_head peh
        WHERE
        peh.order_id = #{orderId}
        <if test="toBossFlagList!=null and toBossFlagList.size>0">
            AND peh.to_boss_flag in
            <foreach collection="toBossFlagList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectCpToSgsMartData" resultType="java.util.Map">
        select distinct tso.version, tgo.OrderChannelType , tso.CaseType , tgo.OrderNo as orderNo,tgo.OrderStatus ,totr.RefNo ,totr.RefSystemId  from tb_general_order tgo
        left join tb_order_trf_relationship totr on tgo.id = totr.OrderId
        LEFT JOIN tb_sl_order tso on tso.GeneralOrderID  = tgo.ID
        inner join tb_customer_instance tci on tci.GeneralOrderID  = tgo.ID and tci.CustomerUsage ='buyer'
        where  (totr.RefSystemId  in(1,2) or totr.RefSystemId is null) and tgo.OrderStatus not in (1,5,7,10)
        and tgo.ToDMFlag  = 1
        and tgo.BUCode != 'SL'
        <choose>
            <when test="syncType=='syncToTrf'">
                and tso.version = 1
            </when>
            <otherwise>
                and tso.version is null
                and not exists (
                select id from tb_order_trf_relationship totr2 where tgo.id = totr2.OrderId and totr2.RefSystemId =2
                )
            </otherwise>
        </choose>
        <choose>
            <when test="(buyerGroupCodeList!= null and buyerGroupCodeList.size()>0) or (bossNumberList!= null and bossNumberList.size()>0)">
                <if test="buyerGroupCodeList!=null and buyerGroupCodeList.size()>0">
                    and tci.BuyerGroup in
                    <foreach collection="buyerGroupCodeList" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="bossNumberList!=null and bossNumberList.size()>0">
                    and tci.BossNumber in
                    <foreach collection="bossNumberList" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                </if>
            </when>
            <otherwise>
                1!=1
            </otherwise>
        </choose>
        order by  tgo.CreatedDate  desc
    </select>

    <update id="updateActualFeeByOrderNo">
        <foreach collection="list" item="item"  index="index" separator=";">
            update tb_general_order set actualFee = #{item.newActualFee}, actualFeeCurrency = #{item.newCurrency}
            where orderNo = #{item.orderNo}
        </foreach>
    </update>


    <update id="updateForOdc">
        update tb_general_order set LastModifiedTimestamp = '2024-12-05 18:15:00.000'
        where BUCode ='MR' and orderNo in (
        'GZMR2410032142',
        'TJMR2410006152',
        'TJMR2411006439',
        'SHMR2411022047',
        'TJMR2411006333',
        'SHMR2410021238',
        'GZMR2411033422',
        'TJMR2411006531',
        'TJMR2409005678',
        'TJMR2411006335',
        'SHMR2411022535',
        'GZMR2411034084',
        'GZMR2411032861',
        'GZMR2411033092',
        'GZMR2410031664',
        'GZMR2410029826',
        'GZMR2410032136',
        'SHMR2411021894',
        'SHMR2411022246',
        'TJMR2410006052',
        'GZMR2411032957',
        'SHMR2410021652',
        'SUZMR2410006514',
        'GZMR2410031771',
        'SHMR2410021452',
        'TJMR2410006238',
        'SHMR2409019723',
        'GZMR2409027575',
        'GZMR2410031900',
        'GZMR2411032654',
        'SHMR2410021160',
        'TJMR2410006182',
        'GZMR2408025153',
        'SHMR2411022059',
        'GZMR2411032812',
        'TJMR2411006602',
        'SHMR2411021919',
        'SHMR2411023096',
        'GZMR2410031772',
        'GZMR2411033484',
        'TJMR2410006208',
        'SHMR2410020920',
        'GZMR2410032348',
        'GZMR2411033416',
        'TJMR2411006506',
        'TJMR2410006060',
        'GZMR2411033082',
        'SUZMR2408004573',
        'GZMR2411033709',
        'GZMR2410030565',
        'GZMR2411034237',
        'TJMR2410006096',
        'SUZMR2410006725',
        'SUZMR2411007275',
        'SHMR2409018904',
        'TJMR2411006354',
        'GZMR2411034255',
        'GZMR2411032700',
        'SHMR2411022556',
        'SHMR2410020624',
        'GZMR2411033353',
        'GZMR2411034128',
        'GZMR2411033998',
        'TJMR2410006255',
        'SHMR2410021480',
        'GZMR2410032119',
        'SHMR2411022607',
        'TJMR2411006441',
        'GZMR2411034143',
        'GZMR2411034120',
        'TJMR2410006290',
        'SHMR2410021684',
        'GZMR2410032011',
        'SHMR2411022007',
        'GZMR2411032639',
        'SHMR2411021864',
        'TJMR2410006099',
        'SHMR2411022346',
        'TJMR2411006337',
        'SHMR2410021542',
        'TJMR2408004840',
        'SHMR2410021230',
        'SHMR2408016746',
        'TJMR2410006246',
        'TJMR2411006643',
        'TJMR2410005904',
        'TJMR2410006207',
        'GZMR2409029302',
        'TJMR2411006334',
        'GZMR2411034398',
        'SHMR2410021645',
        'TJMR2410006158',
        'TJMR2409005522',
        'SHMR2411022683',
        'TJMR2408005071',
        'GZMR2410032019',
        'GZMR2411033279',
        'SHMR2411022100',
        'GZMR2411034212',
        'GZMR2411032854',
        'SHMR2411021978',
        'GZMR2411033711',
        'SHMR2410020324',
        'GZMR2410032291',
        'SHMR2410021045',
        'GZMR2410031925',
        'GZMR2410031777',
        'SHMR2411022402',
        'SHMR2411022283',
        'GZMR2411034177',
        'TJMR2409005603',
        'SHMR2410020825',
        'GZMR2409026622',
        'GZMR2411035745',
        'GZMR2411033146',
        'GZMR2411034399',
        'SHMR2411021995',
        'SHMR2411022783',
        'TJMR2410006090',
        'GZMR2411034822',
        'GZMR2410030574',
        'GZMR2409028543',
        'GZMR2410031549',
        'SHMR2410020855',
        'GZMR2410032426',
        'SHMR2410021369',
        'SUZMR2410006666',
        'GZMR2410031905',
        'GZMR2408023772',
        'GZMR2410031727',
        'GZMR2411032716',
        'GZMR2410032408',
        'GZMR2410031662',
        'GZMR2411034354',
        'GZMR2408022548',
        'TJMR2410006110',
        'GZMR2411033290',
        'GZMR2411033184',
        'GZMR2410031871',
        'GZMR2411033486',
        'TJMR2411006375',
        'GZMR2410029539',
        'SHMR2411023222',
        'GZMR2411033941',
        'TJMR2410006211',
        'TJMR2411006658',
        'GZMR2411033847',
        'GZMR2411034416',
        'GZMR2411033984',
        'SHMR2408015117',
        'TJMR2411006433',
        'SHMR2411021967',
        'GZMR2411034651',
        'TJMR2411006482',
        'SHMR2411022636',
        'TJMR2411006636',
        'GZMR2410031975',
        'GZMR2411033457',
        'GZMR2411033344',
        'GZMR2410031820',
        'SHMR2410020692',
        'GZMR2411033958',
        'TJMR2411006449',
        'SHMR2411022624',
        'GZMR2410032005',
        'GZMR2411032616',
        'SHMR2410021488',
        'GZMR2411032913',
        'SHMR2410021631'
        )

    </update>
</mapper>