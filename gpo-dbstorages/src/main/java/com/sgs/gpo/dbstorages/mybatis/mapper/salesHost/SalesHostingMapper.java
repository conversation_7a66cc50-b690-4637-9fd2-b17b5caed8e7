package com.sgs.gpo.dbstorages.mybatis.mapper.salesHost;

import com.sgs.gpo.facade.model.salesHost.SalesHostingInfo;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

@Mapper
public interface SalesHostingMapper {
    SalesHostingInfo selectById(String id);
    List<SalesHostingInfo> selectAll();
    List<SalesHostingInfo> selectByCondition(SalesHostingInfo condition);
    int insert(SalesHostingInfo salesHosting);
    int update(SalesHostingInfo salesHosting);
    int deleteById(String id);
}
