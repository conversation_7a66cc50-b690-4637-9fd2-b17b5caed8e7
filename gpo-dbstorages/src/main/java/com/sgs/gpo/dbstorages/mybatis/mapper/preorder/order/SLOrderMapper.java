package com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.SLOrderPO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderIdReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @title: SLOrderMapper
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/10 17:20
 */

public interface SLOrderMapper extends BaseMapper<SLOrderPO> {
    List<SLOrderPO> select(@Param("req") OrderIdReq orderIdReq);
}
