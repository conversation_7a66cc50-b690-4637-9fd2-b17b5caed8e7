<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testsample.ReferDataRelationshipMapper" >

    <select id="selectReferDataByOrder" parameterType="com.sgs.gpo.facade.model.otsnotes.testsample.req.ReferDataRelationShipReq"
            resultType="com.sgs.gpo.facade.model.otsnotes.testsample.bo.ReferDataBO">
        select
            trdr.CurrentOrderNo ,
            trdr.CurrentSampleID ,
            trdr.SourceSampleId,
            trdr.SourceOrderNo,
            ttli.TestLineID
        from
            tb_refer_data_relationship trdr
                inner join tb_test_matrix ttm on
                trdr.SourceSampleId = ttm.TestSampleID
                inner join tb_test_line_instance ttli on
                ttli.ID = ttm.TestLineInstanceID
        where
        <if test="currentOrderNoList != null and currentOrderNoList.size() > 0">
            trdr.CurrentOrderNo in
            <foreach collection="currentOrderNoList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

    </select>


</mapper>