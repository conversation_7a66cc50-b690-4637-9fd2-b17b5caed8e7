package com.sgs.gpo.dbstorages.mybatis.mapper.workflow;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.workflow.WorkflowRulePO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/20 15:13
 */
@DS(Constants.DB.COMMON)
public interface WorkflowRuleMapper extends BaseMapper<WorkflowRulePO> {
    /**
     * 查询列表
     * @param workflowRulePO
     * @return
     */
    List<WorkflowRulePO> select(WorkflowRulePO workflowRulePO);
}
