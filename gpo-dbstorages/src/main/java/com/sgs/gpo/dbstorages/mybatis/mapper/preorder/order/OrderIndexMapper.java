package com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.OrderIndexPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface OrderIndexMapper extends BaseMapper<OrderIndexPO> {
    /**
     * 基于ID查看详情
     * @param orderIndex
     * @return
     */
    List<OrderIndexPO> selectSourceList(OrderIndexPO orderIndex);
    List<OrderIndexPO> selectTPSourceList(OrderIndexPO orderIndex);

    /**
     * 基于OrderID查询Payer信息
     * @param orderIdList
     * @return
     */
    List<OrderIndexPO> getPayerByOrder(@Param("orderIdList") Set<String> orderIdList);

    /**
     * 基于OrderID查询Product信息
     * @param orderIdList
     * @return
     */
    List<OrderIndexPO> getProductByOrder(@Param("orderIdList") Set<String> orderIdList);

    /**
     * 查看是否存在索引
     * @param orderId
     * @return
     */
    Integer hasIndex(@Param("orderId") String orderId);
}
