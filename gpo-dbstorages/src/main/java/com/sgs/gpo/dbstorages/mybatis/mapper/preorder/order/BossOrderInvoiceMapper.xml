<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.BossOrderInvoiceMapper" >

    <resultMap id="BaseResultMap" type="com.sgs.gpo.dbstorages.mybatis.model.preorder.order.BossOrderInvoicePO" >
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="boss_order_no" property="bossOrderNo" jdbcType="VARCHAR" />
        <result column="invoice_no" property="invoiceNo" jdbcType="VARCHAR" />
        <result column="invoice_date" property="invoiceDate" jdbcType="TIMESTAMP" />
        <result column="currency_code" property="currencyCode" jdbcType="VARCHAR" />
        <result column="invoice_amount" property="invoiceAmount" jdbcType="DECIMAL" />
        <result column="paid_amount" property="paidAmount" jdbcType="DECIMAL" />
        <result column="original_invoice_no" property="originalInvoiceNo" jdbcType="VARCHAR" />
        <result column="original_boss_order_no" property="originalBossOrderNo" jdbcType="VARCHAR" />
        <result column="functional_currency_code" property="functionalCurrencyCode" jdbcType="VARCHAR" />
        <result column="pre_tax_amount_functiona_currency" property="preTaxAmountFunctionaCurrency" jdbcType="DECIMAL" />
        <result column="tax_amount_functiona_currency" property="taxAmountFunctionaCurrency" jdbcType="DECIMAL" />
        <result column="balance_amount" property="balanceAmount" jdbcType="DECIMAL" />
        <result column="invoice_due_date" property="invoiceDueDate" jdbcType="VARCHAR" />
        <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
        <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
        <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
        <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
        <result column="active_indicator" property="activeIndicator" jdbcType="INTEGER" />
        <result column="invoice_line" property="invoiceLine" jdbcType="VARCHAR" />
    </resultMap>

    <select id="getInvoiceByOrderNo" resultMap="BaseResultMap">
        SELECT DISTINCT
            boi.*
        FROM
            tb_general_order o
                LEFT JOIN tb_pe_quotation_head qh ON qh.order_id = o.id
                LEFT JOIN tb_pe_quotation_head_history qhh ON qhh.quotation_head_id = qh.id
                LEFT JOIN tb_pe_boss_order_relationship bor ON bor.quotation_head_history_Id = qhh.id
                LEFT JOIN tb_boss_order bo ON bo.id = bor.boss_order_id
                LEFT JOIN tb_boss_order_invoice boi ON boi.boss_order_no = bo.TrueOrderNo
                LEFT JOIN tb_boss_order_invoice cdi ON boi.invoice_no = cdi.original_invoice_no
        WHERE
            ( cdi.original_invoice_no IS NULL OR cdi.original_invoice_no = '' )
                AND qh.`status` != 4
                AND qhh.`status` != 4
            AND IF( qh.STATUS = 1 AND qhh.quotation_flag <![CDATA[&]]> 1 = 1, 1, 0 ) != 1
            AND boi.id IS NOT NULL
            and o.OrderNo = #{orderNo}
    </select>


    <select id="getInvoiceInfoByOrderNo" resultType="com.sgs.gpo.dbstorages.mybatis.model.preorder.payment.BossOrderInvoiceDTO">
        SELECT DISTINCT
        o.OrderNo as orderNo,
        qh.quotation_no as quotationNo,
        boi.invoice_no as invoice_no,
        boi.id as invoiceId,
        boi.currency_code as currencyCode,
        boi.invoice_amount as invoiceAmount,
        bo.BOSSOrderNo as bossOrderNo
        FROM
        tb_general_order o
        LEFT JOIN tb_pe_quotation_head qh ON qh.order_id = o.id
        LEFT JOIN tb_pe_quotation_head_history qhh ON qhh.quotation_head_id = qh.id
        LEFT JOIN tb_pe_boss_order_relationship bor ON bor.quotation_head_history_Id = qhh.id
        LEFT JOIN tb_boss_order bo ON bo.id = bor.boss_order_id
        LEFT JOIN tb_boss_order_invoice boi ON boi.boss_order_no = bo.TrueOrderNo
        LEFT JOIN tb_boss_order_invoice cdi ON boi.invoice_no = cdi.original_invoice_no
        WHERE
        ( cdi.original_invoice_no IS NULL OR cdi.original_invoice_no = '' )
        AND qh.`status` != 4
        AND qhh.`status` != 4
        AND IF( qh.STATUS = 1 AND qhh.quotation_flag <![CDATA[&]]> 1 = 1, 1, 0 ) != 1
        AND boi.id IS NOT NULL
        and o.OrderNo = #{orderNo}
    </select>

    <select id="getNoInvoiceQuotationCount" resultType="java.lang.Long">
        SELECT
            count( 1 ) AS count
        FROM
            tb_general_order o
            LEFT JOIN tb_pe_quotation_head qh ON qh.order_id = o.id
            LEFT JOIN tb_pe_quotation_head_history qhh ON qhh.quotation_head_id = qh.id
            LEFT JOIN tb_pe_boss_order_relationship bor ON bor.quotation_head_history_Id = qhh.id
            LEFT JOIN tb_boss_order bo ON bo.id = bor.boss_order_id
            LEFT JOIN tb_boss_order_invoice boi ON boi.boss_order_no = bo.TrueOrderNo
            LEFT JOIN tb_boss_order_invoice cdi ON boi.invoice_no = cdi.original_invoice_no
        WHERE
            qh.`status` != 4
          AND qhh.`status` != 4
          AND IF( qh.STATUS = 1 AND qhh.quotation_flag <![CDATA[&]]> 1 = 1, 1, 0 ) != 1
          AND (boi.id IS NULL or cdi.original_invoice_no IS not  NULL OR cdi.original_invoice_no != '' )
          AND o.OrderNo = #{orderNo}
    </select>

    <select id="getDataByBossOrderAndInvoice" resultType="java.lang.Boolean">
        select count(0)
        from tb_boss_order_invoice boi
        where boi.boss_order_no = #{bossOrderNo}
        and boi.invoice_no = #{invoiceNo}
        limit 1
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO tb_boss_order_invoice (
        id, boss_order_no, invoice_no,
        invoice_date, currency_code, invoice_amount,
        paid_amount, original_invoice_no, original_boss_order_no,
        functional_currency_code, pre_tax_amount_functiona_currency,
        tax_amount_functiona_currency, balance_amount,
        invoice_due_date, created_date, created_by,
        modified_by, modified_date, active_indicator,invoice_line
        )
        VALUES
        <foreach collection="bossOrderInvoiceInfoPOS" item="item" separator="," >
            (
            #{item.id},
            #{item.bossOrderNo},
            #{item.invoiceNo},
            #{item.invoiceDate},
            #{item.currencyCode},
            #{item.invoiceAmount},
            #{item.paidAmount},
            #{item.originalInvoiceNo},
            #{item.originalBossOrderNo},
            #{item.functionalCurrencyCode},
            #{item.preTaxAmountFunctionaCurrency},
            #{item.taxAmountFunctionaCurrency},
            #{item.balanceAmount},
            #{item.invoiceDueDate},
            now(),
            #{item.createdBy},
            #{item.modifiedBy},
            #{item.modifiedDate},
            #{item.activeIndicator},#{item.invoiceLine})
        </foreach>
    </insert>

    <!--批量更新 -->
    <update id="batchUpdatePaidAmountByInvoiceNo" parameterType="java.util.List">
        <foreach collection="bossOrderInvoiceList" item="item" index="index" separator=";">
            update tb_boss_order_invoice
            set paid_amount = #{item.paidAmount},balance_amount = #{item.balanceAmount},modified_date = now()
            where invoice_no = #{item.invoiceNo}
        </foreach>
    </update>

    <select id="getOrderNoByInvoiceNo" resultType="java.lang.String">
        SELECT
        tgo.`OrderNo` orderNo
        FROM
        tb_general_order tgo
        INNER JOIN tb_sl_order tso ON tgo.`ID` = tso.`GeneralOrderID`
        INNER JOIN tre_order_relationship tor ON tgo.`ID` = tor.`GeneralOrderID`
        INNER JOIN tb_boss_order tbo ON tor.`BOSSOrderID` = tbo.`ID`
        INNER JOIN tb_boss_order_invoice tboi ON tbo.`TrueOrderNo` = tboi.`boss_order_no`
        WHERE tgo.`OrderStatus` != 7
        <if test="invoiceNos!=null and invoiceNos.size>0">
            AND tboi.`invoice_no` in
            <foreach collection="invoiceNos" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="updatePaidAmountByInvoiceNoBatch" parameterType="java.util.List">
        <foreach collection="bossOrderInvoiceInfo" item="item" index="index" separator=";">
            update tb_boss_order_invoice
            set paid_amount = #{item.paidAmount},balance_amount = #{item.balanceAmount},modified_date = now()
            where invoice_no = #{item.invoiceNo}
        </foreach>
    </update>

    <select id="getInvoiceInfoByOrderNos" resultType="com.sgs.gpo.dbstorages.mybatis.model.preorder.payment.BossOrderInvoiceDTO">
        SELECT DISTINCT
        o.OrderNo as orderNo,
        qh.quotation_no as quotationNo,
        boi.invoice_no as invoice_no,
        boi.id as invoiceId,
        boi.currency_code as currencyCode,
        boi.invoice_amount as invoiceAmount,
        bo.BOSSOrderNo as bossOrderNo
        FROM
        tb_general_order o
        LEFT JOIN tb_pe_quotation_head qh ON qh.order_id = o.id
        LEFT JOIN tb_pe_quotation_head_history qhh ON qhh.quotation_head_id = qh.id
        LEFT JOIN tb_pe_boss_order_relationship bor ON bor.quotation_head_history_Id = qhh.id
        LEFT JOIN tb_boss_order bo ON bo.id = bor.boss_order_id
        LEFT JOIN tb_boss_order_invoice boi ON boi.boss_order_no = bo.TrueOrderNo
        WHERE boi.id IS NOT NULL
        <if test="orderNoList!=null and orderNoList.size>0">
            and o.OrderNo in
            <foreach collection="orderNoList" open="(" close=")" item="item" separator=",">
            #{item}
            </foreach>
        </if>
    </select>

</mapper>