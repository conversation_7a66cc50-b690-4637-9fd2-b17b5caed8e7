package com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.objectsamplequantity;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.objectsamplequantity.ObjectSampleQuantityPO;
import com.sgs.gpo.facade.model.otsnotes.objectsamplequantity.req.ObjectSampleQuantityQueryReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ObjectSampleQuantityMapper extends BaseMapper<ObjectSampleQuantityPO> {
    IPage<ObjectSampleQuantityPO> selectPage(@Param("page") IPage page, @Param("req") ObjectSampleQuantityQueryReq objectSampleQuantityQueryReq);

    int updateActiveIndicatorByIds(@Param("ids") List<String> ids, @Param("activeIndicator") Integer activeIndicator);

}
