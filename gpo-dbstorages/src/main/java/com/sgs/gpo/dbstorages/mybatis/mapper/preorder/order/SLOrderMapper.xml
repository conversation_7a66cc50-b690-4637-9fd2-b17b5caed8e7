<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.SLOrderMapper" >

    <select id="select" resultType="com.sgs.gpo.dbstorages.mybatis.model.preorder.order.SLOrderPO">
        select
	    *
        from
        tb_sl_order sl
        inner join tb_general_order tgo on tgo.id = sl.GeneralOrderID
        where  1=1
        <if test="req.orderIdList != null and req.orderIdList != '' and req.orderIdList.size >0">
            and sl.GeneralOrderID in
            <foreach collection="req.orderIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="req.orderNoList != null and req.orderNoList != '' and req.orderNoList.size >0">
            and tgo.orderNo in
            <foreach collection="req.orderNoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>