package com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testsample;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.framework.model.test.testline.v2.TestLineSampleBO;
import com.sgs.framework.model.test.testsample.TestSampleBO;
import com.sgs.framework.model.test.testsample.v2.TestSampleTestLineBO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testsample.TestSamplePO;
import com.sgs.gpo.facade.model.otsnotes.testsample.req.TestSampleQueryReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/3 15:39
 */
public interface TestSampleMapper extends BaseMapper<TestSamplePO> {

    List<TestSampleBO> query(TestSampleQueryReq testSampleQueryReq);

    List<TestLineSampleBO> queryListByTestLine(TestSampleQueryReq testSampleQueryReq);

    List<TestSampleTestLineBO> querySampleTestLineList(TestSampleQueryReq testSampleQueryReq);
}
