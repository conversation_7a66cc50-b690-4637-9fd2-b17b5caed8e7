package com.sgs.gpo.dbstorages.mybatis.mapper.preorder.ordertrfrel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.ordertrfrel.OrderTrfRelationshipPO;
import com.sgs.gpo.facade.model.preorder.order.bo.OrderTrfBO;
import com.sgs.gpo.facade.model.preorder.order.req.OrderTrfReq;

import java.util.List;

/**
 * <AUTHOR>
 * @title: OrderTrfRelationshipMapper
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2023/7/4 11:12
 */
public interface OrderTrfRelationshipMapper extends BaseMapper<OrderTrfRelationshipPO> {
    List<OrderTrfBO> queryOrderTrf(OrderTrfReq orderTrfReq);
}
