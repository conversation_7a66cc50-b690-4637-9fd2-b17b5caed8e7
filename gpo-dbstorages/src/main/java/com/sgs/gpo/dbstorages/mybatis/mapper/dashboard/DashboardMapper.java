package com.sgs.gpo.dbstorages.mybatis.mapper.dashboard;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.gpo.facade.model.dashboard.rsp.MatrixTodayProgressItem;
import com.sgs.gpo.facade.model.dashboard.rsp.MatrixFuturePlanItem;
import com.sgs.gpo.facade.model.dashboard.rsp.ReportTaskDetailRsp;
import com.sgs.gpo.facade.model.dashboard.rsp.StatusTodaySummaryRsp;
import com.sgs.gpo.facade.model.dashboard.req.DashBoardQueryRep;
import com.sgs.gpo.facade.model.report.req.ReportDashBoardReq;

import java.util.List;

public interface DashboardMapper  extends BaseMapper<StatusTodaySummaryRsp> {

    /**
     * 获取饼图的report数据
     * @param dashBoardQueryRep
     * @return
     */
    List<StatusTodaySummaryRsp> selectReportTodaySummary(DashBoardQueryRep dashBoardQueryRep);

    /**
     * 获取饼图的order数据
     * @param dashBoardQueryRep
     * @return
     */
    List<StatusTodaySummaryRsp> selectOrderTodaySummary(DashBoardQueryRep dashBoardQueryRep);

    /**
     * 获取饼图的Matrix数据
     * @param dashBoardQueryRep
     * @return
     */
    List<StatusTodaySummaryRsp> selectMatrixTodaySummary(DashBoardQueryRep dashBoardQueryRep);

    /**
     * 获取testMatrix分布看板数据
     * @param dashBoardQueryRep
     * @return
     */
    List<MatrixTodayProgressItem> selectMatrixTodayProgress(DashBoardQueryRep dashBoardQueryRep);
    List<MatrixTodayProgressItem> selectEngineerDelaySummary(DashBoardQueryRep dashBoardQueryRep);

    /**
     * 获取testMatrix未来分布看板数据
     * @param dashBoardQueryRep
     * @return
     */
    List<MatrixFuturePlanItem>  selectMatrixFuturePlan(DashBoardQueryRep dashBoardQueryRep);

    List<ReportTaskDetailRsp> selectReportTaskList(DashBoardQueryRep dashBoardQueryRep);

}
