package com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.subcontract;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractPO;
import com.sgs.gpo.facade.model.otsnotes.subcontract.req.SubContractPageReq;
import com.sgs.gpo.facade.model.otsnotes.subcontract.vo.SubContractPageVO;
import com.sgs.gpo.facade.model.subcontract.req.SubcontractQueryReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 */
public interface SubContractMapper extends BaseMapper<SubcontractPO> {
    IPage<SubContractPageVO> querySubContractPage(IPage<SubContractPageVO> page, @Param("req") SubContractPageReq subContractPageReq);
    Long querySubContractPageCount(@Param("req") SubContractPageReq subContractPageReq);

    List<SubcontractPO> select(@Param("req")SubcontractQueryReq subcontractQueryReq);


    SubcontractPO queryOrderIsSubOrder(@Param("orderNo") String orderNo);

    List<SubcontractPO> selectSubcontractByTlInstanceIdList(@Param("req") SubcontractQueryReq subcontractQueryReq);
}
