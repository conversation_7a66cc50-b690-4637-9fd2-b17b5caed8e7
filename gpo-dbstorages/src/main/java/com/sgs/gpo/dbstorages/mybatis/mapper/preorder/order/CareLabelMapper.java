package com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.framework.model.common.productsample.CareLabelBO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.CareLabelInstancePO;
import com.sgs.gpo.facade.model.preorder.order.req.CareLabelReq;

import java.util.List;

public interface CareLabelMapper extends BaseMapper<CareLabelInstancePO> {


    List<CareLabelBO> queryCareLabelList(CareLabelReq req);

}
