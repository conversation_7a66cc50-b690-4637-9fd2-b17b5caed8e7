<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order.ParcelTodoListMapper" >

  <select id="selectList" resultType="com.sgs.gpo.dbstorages.mybatis.model.preorder.order.ParcelTodoListPO">
    select  distinct
    t.ID,
    t.order_no,
    t.resp_cs_name,
    t.report_deliver_date,
    t.company_name,
    t.address,
    t.contact,
    t.express_content,
    t.remark,
    t.parcel_no,
    t.`status`,
    t.express_no,
    t.send_date,
    t.lab_code,
    t.bu_code,
    t.location_code,
    t.created_date,
    t.created_by,
    t.modified_date,
    t.modified_by,
    t.tax_invoice_no,
    t.invoice_no,
    t.order_id,
    t.contract_tel,
    t.report_no,
    o.OperationMode,
    CASE t.express_content
    WHEN 1 THEN
    IFNULL(tr.ReturnTestedSampleRemark, tr.ReturnResidueSampleRemark)
    WHEN 2 THEN
    tr.InvoiceDeliverWay
    WHEN 3 THEN
    tr.HardCopyReportDeliverWay
    end as 'deliverWay'
    from tb_parcel_todo_list t
    inner join tb_general_order o on o.OrderNo = t.order_no
    inner join tb_test_request tr on tr.GeneralOrderID = o.id
    <where>
    <if test="orderNo != null and orderNo != ''">
      AND t.order_no = #{orderNo}
    </if>
    <if test="csName != null and csName != ''">
      AND t.resp_cs_name = #{csName}
    </if>
    <if test="reportNo != null and reportNo != ''">
      AND t.report_no = #{reportNo}
    </if>
    <if test="companyName != null and companyName != ''">
      AND t.company_name = #{companyName}
    </if>

    <if test="deliverWay != null and deliverWay != ''">
    AND
      <choose>
      <when test="deliverWay=='Other'">
        CASE
        t.express_content
        WHEN 1 THEN
          tr.ReturnTestedSampleRemark NOT IN ( 'Express-Freight Prepaid', 'Express-Freight Collect', 'Client Pick Up', 'To Be Determined' )
          AND tr.ReturnResidueSampleRemark NOT IN ( 'Express-Freight Prepaid', 'Express-Freight Collect', 'Client Pick Up', 'To Be Determined' )
        WHEN 2 THEN
            tr.InvoiceDeliverWay  NOT IN ( 'Express-Freight Prepaid', 'Express-Freight Collect', 'Client Pick Up', 'To Be Determined' )
        WHEN 3 THEN
            tr.HardCopyReportDeliverWay NOT IN ( 'Express-Freight Prepaid', 'Express-Freight Collect', 'Client Pick Up', 'To Be Determined' )
        END
      </when>
      <otherwise>
        CASE
        t.express_content
        WHEN 1 THEN
        tr.ReturnTestedSampleRemark = #{deliverWay}
        OR tr.ReturnResidueSampleRemark = #{deliverWay}
        WHEN 2 THEN
        tr.InvoiceDeliverWay  = #{deliverWay}
        WHEN 3 THEN
        tr.HardCopyReportDeliverWay = #{deliverWay}
        END
      </otherwise>
      </choose>
    </if>

    <if test="parcelCompanyInfo != null">
        <if test="(parcelCompanyInfo.customerName != null and parcelCompanyInfo.customerName != '') or (parcelCompanyInfo.customerNameCn != null and parcelCompanyInfo.customerNameCn != '') or (parcelCompanyInfo.customerNameEn != null and parcelCompanyInfo.customerNameEn != '')">
          and
        </if>
      <trim prefix="(" suffix=")" prefixOverrides="or" >
        <if test="(parcelCompanyInfo.customerName != null and parcelCompanyInfo.customerName != '')">
          or t.company_name like concat('%',#{parcelCompanyInfo.customerName},'%')
        </if>
        <if test="(parcelCompanyInfo.customerNameCn != null and parcelCompanyInfo.customerNameCn != '')">
          or t.company_name like concat('%',#{parcelCompanyInfo.customerNameCn},'%')
        </if>
        <if test="(parcelCompanyInfo.customerNameEn != null and parcelCompanyInfo.customerNameEn != '')">
          or t.company_name like concat('%',#{parcelCompanyInfo.customerNameEn},'%')
        </if>
      </trim>
    </if>

    <if test="expressContent != null and expressContent != ''">
      AND t.express_content = #{expressContent}
    </if>
    <if test="status != null and status != ''">
      AND t.status = #{status}
    </if>
    <if test="status == null and cancelStatus != null and cancelStatus != '' ">
      AND t.status != #{cancelStatus}
    </if>
    <if test="buCode != null and buCode != ''">
      AND t.bu_code = #{buCode}
    </if>
    <if test="labCode != null and labCode != ''">
      AND t.lab_code = #{labCode}
    </if>
    <if test="deliverStartDate != null and deliverStartDate!=''">
      AND t.report_deliver_date <![CDATA[  >=   ]]> #{deliverStartDate}
    </if>
    <if test="deliverEndDate != null and deliverEndDate!=''">
          AND t.report_deliver_date <![CDATA[  <   ]]> #{deliverEndDate}
    </if>
    <if test="idList != null and idList.size() > 0">
      AND t.id IN
      <foreach collection="idList" item="item" index="index" separator=","  open="(" close=")" >
        #{item}
      </foreach>
    </if>
    </where>
    order by t.report_deliver_date desc,t.order_no desc
  </select>

  <!--批量更新 -->
  <update id="updateInvoiceNoByOrderNoBatch" parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" separator=";">
      update
      tb_parcel_todo_list ptl
      <set >
        <if test="item.invoiceNo != null" >ptl.invoice_no = #{item.invoiceNo}</if>
        <if test="item.taxInvoiceNo != null" >ptl.tax_invoice_no = #{item.taxInvoiceNo}</if>
      </set>
      where ptl.order_no = #{item.orderNo}
    </foreach>
  </update>
</mapper>