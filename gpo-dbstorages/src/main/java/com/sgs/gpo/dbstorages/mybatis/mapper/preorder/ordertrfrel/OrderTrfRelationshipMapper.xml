<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.preorder.ordertrfrel.OrderTrfRelationshipMapper" >

    <select id="queryOrderTrf" resultType="com.sgs.gpo.facade.model.preorder.order.bo.OrderTrfBO">
        SELECT
            o.id AS id,
            o.OrderNo AS orderNo,
            trfrel.RefNo AS refNo,
            trfrel.RefSystemId AS refSystemId,
            trfrel.ExternalOrderNo AS externalOrderNo,
            trfrel.TrfSourceType as trfSourceType,
            trfrel.IntegrationChannel as integrationChannel

        FROM
            tb_general_order o
        INNER JOIN tb_lab_instance lab ON lab.GeneralOrderID = o.id
        INNER JOIN tb_order_trf_relationship trfrel ON trfrel.OrderId = o.id
        <where>
            o.OrderStatus != 7
            <if test="refSystemIdList != null and refSystemIdList.size()>0">
               AND trfrel.RefSystemId IN
                <foreach collection="refSystemIdList" item="refSystemId" open="(" separator="," close=")">
                    #{refSystemId}
                </foreach>
            </if>
            <if test="orderIdList != null and orderIdList.size()>0">
                AND o.id IN
                <foreach collection="orderIdList" item="orderId" open="(" separator="," close=")">
                    #{orderId}
                </foreach>
            </if>
            <if test="orderNoList != null and orderNoList.size()>0">
                AND o.OrderNo IN
                <foreach collection="orderNoList" item="orderNo" open="(" separator="," close=")">
                    #{orderNo}
                </foreach>
            </if>
            <if test="labCode != null and labCode != ''">
                AND lab.LabCode = #{labCode}
            </if>
            <if test="refNo != null and refNo != ''">
                AND trfrel.RefNo = #{refNo}
            </if>
            <if test="externalOrderNo != null and externalOrderNo != ''">
                AND trfrel.ExternalOrderNo = #{externalOrderNo}
            </if>
        </where>
    </select>
</mapper>