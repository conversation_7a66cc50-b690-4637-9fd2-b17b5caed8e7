package com.sgs.gpo.dbstorages.mybatis.mapper.preorder.trf;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.trf.IntegrationCustomerConfigPO;
import com.sgs.gpo.facade.model.trf.req.QueryTrfConfigReq;

import java.util.List;

public interface IntegrationCustomerConfigInfoMapper extends BaseMapper<IntegrationCustomerConfigPO> {
    List<IntegrationCustomerConfigPO> queryTrfConfigList(QueryTrfConfigReq queryTrfConfigReq);
}
