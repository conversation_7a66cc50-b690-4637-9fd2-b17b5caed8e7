<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.preorder.enquiry.EnquiryIndexMapper" >

    <select id="selectSourceList" parameterType="com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryIndexPO" resultType="com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryIndexPO" >
        select
            e.id enquiryId,
            e.enquiry_no enquiryNo,
            e.lab_code labCode,
            e.bu_id buId,
            e.bu_code buCode,
            e.enquiry_status enquiryStatus,
            e.product_category productCategory,
            e.product_sub_category productSubCategory,
            e.template_flag templateFlag,
            e.created_date createdDate,
            e.pending_flag pendingFlag,
            e.created_by createdBy,
            e.cs_name cs,
            e.reference_enquiry_no referenceEnquiryNo,
            e.sales_person sales,
            e.enquiry_type enquiryType,
            e.remark remark,
            e.currency_code currencyCode,
            e.net_amount netAmount,
            e.after_tax_amount afterTaxAmount,
            e.final_amount finalAmount,
            applicant.boss_number applicantNo,
            applicant.customer_id applicantId,
            concat('{"cn":', JSON_QUOTE(ifnull(applicant.customer_name_cn,'')), ',"en":', JSON_QUOTE(ifnull(applicant.customer_name_en,'')), '}') applicantName,
            buyer.boss_number buyerNo,
            buyer.customer_id buyerId,
            concat('{"cn":', JSON_QUOTE(ifnull(buyer.customer_name_cn,'')), ',"en":', JSON_QUOTE(ifnull(buyer.customer_name_en,'')), '}') buyerName,
            payer.boss_number payerNo,
            payer.customer_id payerId,
            concat('{"cn":', JSON_QUOTE(ifnull(payer.customer_name_cn,'')), ',"en":', JSON_QUOTE(ifnull(payer.customer_name_en,'')), '}') payerName
        from
            tb_enquiry e
            left join gpo.tb_enquiry_customer applicant
            on e.id = applicant.enquiry_id and applicant.customer_usage = 'applicant'
            left join gpo.tb_enquiry_customer buyer
            on e.id = buyer.enquiry_id and buyer.customer_usage = 'buyer'
            left join gpo.tb_enquiry_customer payer
            on e.id = payer.enquiry_id and payer.customer_usage = 'payer'
        <where>
            <if test="enquiryId != null and enquiryId != ''">
                and e.id = #{enquiryId}
            </if>
            <if test="enquiryNo != null and enquiryNo != ''">
                and e.enquiry_no = #{enquiryNo}
            </if>
        </where>
    </select>


    <select id="hasIndex" resultType="java.lang.Integer">
        select count(1) from tb_enquiry_index where enquiry_id = #{enquiryId}
    </select>
</mapper>