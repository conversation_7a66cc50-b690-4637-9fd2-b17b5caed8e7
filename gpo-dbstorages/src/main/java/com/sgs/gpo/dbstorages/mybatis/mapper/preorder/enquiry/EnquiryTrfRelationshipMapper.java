package com.sgs.gpo.dbstorages.mybatis.mapper.preorder.enquiry;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryTrfRelationshipPO;
import com.sgs.gpo.facade.model.preorder.enquiry.bo.EnquiryTrfBO;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryTrfReq;

import java.util.List;

/**
 * <AUTHOR>
 * @title: EnquiryTrfRelationshipMapper
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2024/1/16 22:31
 */
public interface EnquiryTrfRelationshipMapper  extends BaseMapper<EnquiryTrfRelationshipPO> {
    List<EnquiryTrfBO> queryEnquiryTrf(EnquiryTrfReq enquiryTrfReq);
}
