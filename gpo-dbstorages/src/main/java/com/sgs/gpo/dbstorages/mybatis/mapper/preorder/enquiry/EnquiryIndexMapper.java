package com.sgs.gpo.dbstorages.mybatis.mapper.preorder.enquiry;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryIndexPO;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface EnquiryIndexMapper extends BaseMapper<EnquiryIndexPO> {
    /**
     * 基于ID查看详情
     * @param enquiryIndex
     * @return
     */
    List<EnquiryIndexPO> selectSourceList(EnquiryIndexPO enquiryIndex);

    /**
     * 查看是否存在索引
     * @param enquiryId
     * @return
     */
    Integer hasIndex(@Param("enquiryId") String enquiryId);
}
