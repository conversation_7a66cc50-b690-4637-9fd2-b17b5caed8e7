package com.sgs.gpo.dbstorages.mybatis.mapper.noticeboard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.gpo.dbstorages.mybatis.model.setting.notice.NoticeRecordPO;
import com.sgs.gpo.facade.model.setting.notice.req.NoticeRecordQueryReq;
import org.apache.ibatis.annotations.Param;

public interface NoticeRecordMapper extends BaseMapper<NoticeRecordPO> {

    /**
     * 获取登录用户的未读通知
     * */
    Integer getUnReadCount(@Param("req")NoticeRecordQueryReq  noticeRecordQueryReq);
}
