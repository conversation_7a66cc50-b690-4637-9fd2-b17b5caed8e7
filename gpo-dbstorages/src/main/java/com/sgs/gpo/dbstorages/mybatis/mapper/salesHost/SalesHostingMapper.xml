<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.salesHost.SalesHostingMapper">

    <resultMap id="BaseResultMap" type="com.sgs.gpo.facade.model.salesHost.SalesHostingInfo">
        <id column="ID" property="id"/>
        <result column="RegionAccount" property="regionAccount"/>
        <result column="SalesPerson" property="salesPerson"/>
        <result column="HostingID" property="hostingId"/>
        <result column="HostingCustomerNo" property="hostingCustomerNo"/>
        <result column="HostingCustomerNameEn" property="hostingCustomerNameEn"/>
        <result column="HostingCustomerNameCn" property="hostingCustomerNameCn"/>
        <result column="CreatedBy" property="createdBy"/>
        <result column="CreatedDate" property="createdDate"/>
        <result column="ModifiedBy" property="modifiedBy"/>
        <result column="ModifiedDate" property="modifiedDate"/>
        <result column="BuCode" property="buCode"/>
        <result column="LocationCode" property="locationCode"/>
        <result column="deleted" property="deleted"/>
        <result column="IsMainHost" property="isMainHost"/>
    </resultMap>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM tb_sales_hosting
        WHERE ID = #{id} AND deleted = 'NOT_DELETED'
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT * FROM tb_sales_hosting
        WHERE deleted = 'NOT_DELETED'
    </select>

    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT * FROM tb_sales_hosting
        WHERE deleted = 'NOT_DELETED'
        <if test="regionAccount != null">
            AND RegionAccount = #{regionAccount}
        </if>
        <if test="buCode != null">
            AND BuCode = #{buCode}
        </if>
        <if test="locationCode != null">
            AND LocationCode = #{locationCode}
        </if>
        <if test="hostingCustomerNo != null">
            AND HostingCustomerNo = #{hostingCustomerNo}
        </if>
    </select>

    <insert id="insert">
        INSERT INTO tb_sales_hosting (
        ID, RegionAccount, SalesPerson, HostingID, HostingCustomerNo,
        HostingCustomerNameEn, HostingCustomerNameCn, CreatedBy,
        CreatedDate, ModifiedBy, ModifiedDate, BuCode, LocationCode,
        deleted, IsMainHost
        ) VALUES (
        #{id}, #{regionAccount}, #{salesPerson}, #{hostingId}, #{hostingCustomerNo},
        #{hostingCustomerNameEn}, #{hostingCustomerNameCn}, #{createdBy},
        #{createdDate}, #{modifiedBy}, #{modifiedDate}, #{buCode}, #{locationCode},
        #{deleted}, #{isMainHost}
        )
    </insert>

    <update id="update">
        UPDATE tb_sales_hosting
        SET
        RegionAccount = #{regionAccount},
        SalesPerson = #{salesPerson},
        HostingID = #{hostingId},
        HostingCustomerNo = #{hostingCustomerNo},
        HostingCustomerNameEn = #{hostingCustomerNameEn},
        HostingCustomerNameCn = #{hostingCustomerNameCn},
        ModifiedBy = #{modifiedBy},
        ModifiedDate = #{modifiedDate},
        BuCode = #{buCode},
        LocationCode = #{locationCode},
        deleted = #{deleted},
        IsMainHost = #{isMainHost}
        WHERE ID = #{id}
    </update>

    <update id="deleteById">
        UPDATE tb_sales_hosting
        SET deleted = 'DELETED'
        WHERE ID = #{id}
    </update>

</mapper>