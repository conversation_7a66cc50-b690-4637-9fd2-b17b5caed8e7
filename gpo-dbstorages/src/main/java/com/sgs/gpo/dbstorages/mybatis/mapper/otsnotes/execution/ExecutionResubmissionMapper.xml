<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.execution.ExecutionResubmissionMapper">
    <update id="batchUpdateStatusById">
        update tb_execution_resubmission_record set
        status = #{status},
        modified_by = #{modifiedBy},
        modified_date = #{modifiedDate}
        <where>
            <choose>
                <when test="resubmissionIdList != null and resubmissionIdList.size()>0">
                    id in
                    <foreach collection="resubmissionIdList" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    1!=1
                </otherwise>
            </choose>
        </where>
    </update>

</mapper>