<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.subcontract.SubContractMapper">

    <resultMap id="subContractPageResultMap" type="com.sgs.gpo.facade.model.otsnotes.subcontract.vo.SubContractPageVO">
        <id column="ID" property="id" jdbcType="VARCHAR" />
        <result column="SubContractNo" property="subContractNo" jdbcType="VARCHAR" />
        <result column="OrderNo" property="orderNo" jdbcType="VARCHAR" />
        <result column="generalOrderInstanceId" property="generalOrderInstanceId" jdbcType="VARCHAR" />
        <result column="SubContractLabCode" property="subContractLabCode" jdbcType="VARCHAR" />
        <result column="SubContractLabName" property="subContractLabName" jdbcType="VARCHAR" />
        <result column="SubContractLabId" property="subContractLabId" jdbcType="INTEGER" />
        <result column="ExternalNo" property="referenceNo" jdbcType="VARCHAR" />
        <result column="ExternalOrderDueDate" property="referenceDueDate" jdbcType="TIMESTAMP" />
        <result column="ExternalOrderConfirmDate" property="referenceConfirmDate" jdbcType="TIMESTAMP" />
        <result column="ExternalOrderCs" property="referenceCs" jdbcType="VARCHAR" />
        <result column="CSName" property="responsibleCs" jdbcType="VARCHAR" />
        <result column="Status" property="subContractStatus" jdbcType="INTEGER" />
        <result column="SyncStatus" property="syncStatus" jdbcType="INTEGER" />
        <result column="OperationModel" property="operationModel" jdbcType="INTEGER" />
        <result column="SubContractExpectDueDate" property="subContractExpectDueDate" jdbcType="TIMESTAMP" />
        <result column="CreatedDate" property="createdDate" jdbcType="TIMESTAMP" />
        <result column="StartDate" property="startDate" jdbcType="TIMESTAMP" />
        <result column="CompleteDate" property="completedDate" jdbcType="TIMESTAMP" />
        <result column="SubContractRemark" property="remark" jdbcType="VARCHAR" />
        <result column="SubcontractFee" property="subContractFee" jdbcType="DECIMAL" />
        <result column="SubcontractFeeCurrency" property="subContractFeeCurrency" jdbcType="VARCHAR" />
        <result column="SubContractOrder" property="SubContractOrder" jdbcType="INTEGER" />
    </resultMap>
    
    <sql id="subContractPageTables">
        tb_sub_contract sc
        LEFT JOIN tb_general_order_instance goi ON sc.OrderNo = goi.OrderNo
        LEFT JOIN tb_order_index toi on toi.order_no = goi.OrderNo
        LEFT JOIN tb_subcontract_external_relationship serel ON serel.SubContractNo = sc.SubContractNo
        LEFT JOIN tb_subcontract_requirement tsr ON tsr.SubContractID = sc.ID
    </sql>
    <sql id="subContractCountTables">
        tb_sub_contract sc
        LEFT JOIN tb_general_order_instance goi ON sc.OrderNo = goi.OrderNo
        LEFT JOIN tb_order_index toi on toi.order_no = goi.OrderNo
        LEFT JOIN tb_subcontract_external_relationship serel ON serel.SubContractNo = sc.SubContractNo
    </sql>
    <sql id="subContractPageColumns">
        sc.ID,
        sc.SubContractNo,
        sc.OrderNo,
        sc.SubContractLabName,
        sc.SubContractLabCode,
        sc.SubContractLabId,
        sc.Status,
        sc.SubContractExpectDueDate,
        sc.CompleteDate,
        sc.StartDate,
        sc.ErrorMsg,
        sc.SyncStatus,
        sc.DataLock,
        sc.SubContractRemark,
        sc.CreatedDate,
        sc.SubContractOrder,
        sc.OperationModel as operationModel,
        goi.ID  as generalOrderInstanceId,
        goi.CSName,
        serel.ExternalOrderDueDate,
        serel.ExternalOrderCs,
        serel.ExternalNo,
        serel.ExternalOrderConfirmDate,
        tsr.SubcontractFee,
        tsr.SubcontractFeeCurrency
    </sql>
    <sql id="subContractPageWhere">
        <where>
            <!--权限处理,含3段信息：1、queryAuth：固定值；2、权限，多个逗号分割：0-默认全部；1、自己创建的 2、销售=自己的 3、销售=自己host的 4、客户=自己host的;3、host表关联的客户类型：applicant\buyer\payer等-->
            <if test="req.productLineCode=='AFL' and req.pageType != 'getStarLimsFolders'">
                [queryAuth:0]
            </if>
            <if test="req.subcontractNo!=null and req.subcontractNo!=''">
                and sc.SubContractNo like  CONCAT('%',#{req.subcontractNo},'%')
            </if>
            <if test="req.subContractRemark!=null and req.subContractRemark!=''">
                and sc.SubContractRemark like  CONCAT('%',#{req.subContractRemark},'%')
            </if>
            <if test="req.subContractOrder!=null and req.subContractOrder!=''">
                and sc.SubContractOrder = #{req.subContractOrder}
            </if>
            <if test="req.orderNo!=null and req.orderNo!=''">
                and goi.OrderNo = #{req.orderNo}
            </if>
            <if test="req.orderNoList != null and req.orderNoList.size() > 0" >
                and goi.OrderNo in
                <foreach collection="req.orderNoList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="req.subcontractNoList != null and req.subcontractNoList.size() > 0" >
                and sc.SubContractNo in
                <foreach collection="req.subcontractNoList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="req.cSNameList!=null and req.cSNameList.size()>0">
                and goi.CSName in
                <foreach collection="req.cSNameList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="req.subcontractStatusList!=null and req.subcontractStatusList.size()>0">
                and sc.Status  in
                <foreach collection="req.subcontractStatusList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="req.createBeginDate != null and req.createBeginDate!=''">
                AND sc.CreatedDate  <![CDATA[>=]]>#{req.createBeginDate,jdbcType=TIMESTAMP}
            </if>
            <if test="req.createEndDate != null and req.createEndDate!=''">
                AND sc.CreatedDate  <![CDATA[<=]]>#{req.createEndDate,jdbcType=TIMESTAMP}
            </if>

            <if test="req.expectBeginDate != null and req.expectBeginDate!=''">
                AND sc.SubContractExpectDueDate  <![CDATA[>=]]>#{req.expectBeginDate,jdbcType=TIMESTAMP}
            </if>
            <if test="req.expectEndDate != null and req.expectEndDate!=''">
                AND sc.SubContractExpectDueDate  <![CDATA[<=]]>#{req.expectEndDate,jdbcType=TIMESTAMP}
            </if>

            <if test="req.referenceBeginDueDate != null and req.referenceBeginDueDate!=''">
                AND serel.ExternalOrderDueDate  <![CDATA[>=]]>#{req.referenceBeginDueDate,jdbcType=TIMESTAMP}
            </if>
            <if test="req.referenceEndDueDate != null and req.referenceEndDueDate!=''">
                AND serel.ExternalOrderDueDate  <![CDATA[<=]]>#{req.referenceEndDueDate,jdbcType=TIMESTAMP}
            </if>

            <!--<if test="req.labCode != null and req.labCode!=''">
                AND goi.LabCode=#{req.labCode}
            </if>-->
            <if test="req.blockTops==null or req.blockTops == ''">
                <choose>
                    <when test="crossLabFlag != null and crossLabFlag == 1">
                        AND goi.orderNo in (select rel.orderNo from gpo.tb_order_cross_lab_rel rel where rel.execType=1 and rel.toLab = #{req.labCode})
                    </when>
                    <when test="req.labId != null and req.labId != ''">
                        AND toi.host_lab_id = CONCAT('lab',#{req.labId})
                    </when>
                </choose>
            </if>
            <if test="req.blockTops!=null and req.blockTops!='' and req.labId != null and req.labId != ''">
                AND toi.host_lab_id =  CONCAT('lab',#{req.labId})
            </if>
            <if test="req.pageType == 'orderDetailHasToMyLab' ">
                and
                (
                goi.LabCode = goi.LabCode or sc.SubContractLabCode  in
                <foreach collection="req.subcontractLabCodeList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="req.pageType != 'orderDetailHasToMyLab' and req.subcontractLabCodeList!=null and req.subcontractLabCodeList.size()>0">
                and sc.SubContractLabCode  in
                <foreach collection="req.subcontractLabCodeList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test="req.ignoreCancelled != null and req.ignoreCancelled == true">
                and sc.Status != 3
            </if>
            <if test='req.externalOrderDueDateMatch!=null and req.externalOrderDueDateMatch == "Y".toString()'>
                and serel.ExternalOrderDueDate  <![CDATA[<=]]> sc.SubContractExpectDueDate
            </if>
            <if test='req.externalOrderDueDateMatch!=null and req.externalOrderDueDateMatch == "N".toString()'>
                and serel.ExternalOrderDueDate  <![CDATA[>]]> sc.SubContractExpectDueDate
            </if>
            <if test="req.createdBy != null and req.createdBy!=''">
                AND sc.CreatedBy = #{req.createdBy}
            </if>
            <if test="req.referenceNo != null and req.referenceNo!=''">
                AND serel.ExternalNo like CONCAT('%',#{req.referenceNo},'%')
            </if>
            <if test="req.applicant != null">
                <choose>
                    <when test="req.applicant.customerNo != null and req.applicant.customerNo != '' ">
                        AND toi.applicant_no = #{req.applicant.customerNo}
                    </when>
                    <when test="req.applicant.customerName != null and req.applicant.customerName != '' ">
                        AND toi.applicant_name LIKE CONCAT('%',#{req.applicant.customerName},'%')
                    </when>
                </choose>
            </if>
        </where>
    </sql>
    <sql id = "select">
        select
            sc.ID,
            sc.OrderNo,
            sc.SubContractNo,
            sc.SubContractLabName,
            sc.SubContractServiceType,
            sc.SubContractExpectDueDate,
            sc.AdditionalInfo,
            sc.SubContractRemark,
            sc.CreatedBy,
            sc.CreatedDate,
            sc.ModifiedBy,
            sc.ModifiedDate,
            sc.SubContractLabCode,
            sc.SlimJobNo,
            sc.Status,
            sc.ErrorMsg,
            sc.SyncStatus,
            sc.StartDate,
            sc.CompleteDate,
            sc.SubContractContract,
            sc.SubContractContractTel,
            sc.SubContractContractEmail,
            sc.ToSlimBy,
            sc.LastModifiedTimestamp,
            sc.SubContractOrder,
            sc.DataLock,
            sc.SubcontractTAT,
            sc.SubContractLabId,
            sc.OperationModel,
            sc.ExtData
        from tb_sub_contract sc
        left join tb_general_order_instance goi on sc.orderNo = goi.orderNo
        left join tb_order_index toi on toi.order_no = goi.OrderNo
        <where>
            <if test="req.blockTops!=null and req.blockTops!=''">
                <if test="req.lab != null and req.lab.labId != null and req.lab.labId !=''">
                AND toi.host_lab_id = CONCAT('lab',#{req.labId})
                </if>
            </if>
            <if test="req.blockTops==null or req.blockTops == ''" >
                <if test="req.lab.labId != null and req.lab.labId != ''">
                AND MATCH (toi.host_lab_id,toi.top_lab_id) AGAINST (CONCAT('lab',#{req.lab.labId}) IN BOOLEAN Mode)
                </if>
            </if>
            <if test="req.orderNoList != null and req.orderNoList.size() > 0">
                and sc.OrderNo in
                <foreach item="orderNo" collection="req.orderNoList" open="(" separator="," close=")">
                    #{orderNo}
                </foreach>
            </if>
            <if test="req.subcontractIdList != null and req.subcontractIdList.size() > 0">
                and sc.ID in
                <foreach item="subcontractId" collection="req.subcontractIdList" open="(" separator="," close=")">
                    #{subcontractId}
                </foreach>
            </if>
            <if test="req.subcontractNoList != null and req.subcontractNoList.size() > 0">
                and sc.SubContractNo in
                <foreach item="subcontractNo" collection="req.subcontractNoList" open="(" separator="," close=")">
                    #{subcontractNo}
                </foreach>
            </if>
        </where>
        group by sc.ID
        order by sc.SubContractNo desc
    </sql>

    <select id="querySubContractPageCount" resultType="java.lang.Long">
        select count(1)
        from (
        select distinct
        subcontractId,id  from(
            SELECT distinct
            sc.id subcontractId, serel.id  from
        <bind name="crossLabFlag" value="0"></bind>
        <include refid="subContractCountTables"/>
            <include refid="subContractPageWhere"/>
            <if test="req.blockTops==null or req.blockTops == ''">
                union
                <bind name="crossLabFlag" value="1"></bind>
                select distinct
                sc.id subcontractId, serel.id
                from
                <include refid="subContractPageTables"/>
                <include refid="subContractPageWhere"/>
            </if>
        ) TEMP
        ) table_count
    </select>

    <select id="querySubContractPage" resultMap="subContractPageResultMap">
        select
        temp.*
        from(
        select distinct
        <bind name="crossLabFlag" value="0"></bind>
        <include refid="subContractPageColumns"/>
            from
            <include refid="subContractPageTables"/>
            <include refid="subContractPageWhere"/>
        <if test="req.blockTops==null or req.blockTops == ''">
            union
            <bind name="crossLabFlag" value="1"></bind>
            select distinct
            <include refid="subContractPageColumns"/>
            from
            <include refid="subContractPageTables"/>
            <include refid="subContractPageWhere"/>
        </if>
        )TEMP
        <choose>
            <when test="req.productLineCode=='AFL'">
                order by temp.CreatedDate desc
            </when>
            <otherwise>
                order by temp.SubContractNo desc
            </otherwise>
        </choose>


    </select>

    <select id="select" resultType="com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractPO">
        <include refid="select"></include>
    </select>

    <select id="queryOrderIsSubOrder" parameterType="string" resultType="com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractPO">
        select b.OrderNo, a.SubContractNo, b.SubContractOrder,b.DataLock from
            tb_subcontract_external_relationship a
                join tb_sub_contract b
                     on a.SubContractNo = b.SubContractNo
        where a.ExternalNo = #{orderNo}
          and a.SubContractType in (1, 16, 32)
            limit 1
    </select>
    <select id="selectSubcontractByTlInstanceIdList"
            resultType="com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subcontract.SubcontractPO">
        select distinct tsc.ID id, tsc.OrderNo orderNo, tsc.SubContractNo subContractNo, tsc.SubContractLabName subContractLabName,
            tsc.subContractServiceType subContractServiceType, tsc.SubContractExpectDueDate subContractExpectDueDate,
            tsc.AdditionalInfo additionalInfo, tsc.SubContractRemark subContractRemark, tsc.CreatedBy createdBy,
            tsc.CreatedDate createdDate, tsc.ModifiedBy modifiedBy, tsc.ModifiedDate modifiedDate, tsc.SubContractLabCode subContractLabCode,
            tsc.Status status, tsc.SubContractContract subContractContract
        from tb_sub_contract tsc
        inner join tb_sub_contract_test_line_mapping tsctlm  on tsc.ID = tsctlm.SubContractID
        <where>
            <choose>
                <when test="req.testLineInstanceIdList!= null and req.testLineInstanceIdList.size()>0">
                    and tsctlm.TestLineInstanceID in
                    <foreach collection="req.testLineInstanceIdList" open="(" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    and 1!=1
                </otherwise>
            </choose>
        </where>
    </select>

</mapper>