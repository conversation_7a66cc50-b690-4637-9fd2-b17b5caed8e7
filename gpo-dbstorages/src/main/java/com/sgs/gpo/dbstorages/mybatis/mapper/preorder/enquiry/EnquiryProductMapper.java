package com.sgs.gpo.dbstorages.mybatis.mapper.preorder.enquiry;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.enquiry.EnquiryProductPO;
import com.sgs.gpo.facade.model.preorder.enquiry.req.EnquiryProductReq;

import java.util.List;

public interface EnquiryProductMapper extends BaseMapper<EnquiryProductPO> {

    /**
     * 基于DFF配置的字段动态查询
     * @return
     */
    List<EnquiryProductPO> queryEnquiryProductByDff(EnquiryProductReq productReq);

}
