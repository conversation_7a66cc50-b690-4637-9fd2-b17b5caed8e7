<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.preorder.attachment.OrderAttachmentMapper" >

    <update id="updateSequence" parameterType="java.util.List">
        <foreach collection="attachmentList" item="item" index="index" open="" close="" separator=";">
            UPDATE
                tb_order_attachment
            set
              `sequence` = #{item.sequence}
            where
            id = #{item.orderAttachmentId}
        </foreach>
    </update>

</mapper>