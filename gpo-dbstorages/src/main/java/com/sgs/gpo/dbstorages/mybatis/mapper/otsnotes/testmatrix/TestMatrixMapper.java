package com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testmatrix;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.framework.model.test.testmatrix.TestMatrixBO;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.testmatrix.TestMatrixPO;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.req.TestMatrixQueryReq;
import com.sgs.gpo.facade.model.otsnotes.testmatrix.rsp.TestMatrixSampleRsp;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/3 10:51
 */
public interface TestMatrixMapper extends BaseMapper<TestMatrixPO> {

    /**
     * 查询TestMatrix业务对象集合
     * @param testMatrixQueryReq
     * @return
     */
    List<TestMatrixBO> select(TestMatrixQueryReq testMatrixQueryReq);

    List<TestMatrixSampleRsp> selectTestMatrixSample(TestMatrixQueryReq testMatrixQueryReq);
}
