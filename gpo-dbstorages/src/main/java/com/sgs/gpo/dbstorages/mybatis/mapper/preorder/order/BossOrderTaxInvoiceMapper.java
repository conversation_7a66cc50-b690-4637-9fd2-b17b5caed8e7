package com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.BossOrderTaxInvoicePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BossOrderTaxInvoiceMapper extends BaseMapper<BossOrderTaxInvoicePO> {

    int updateByInvoiceNoAndTaxBatch(@Param("bossOrderTaxInvoiceInfoPOS") List<BossOrderTaxInvoicePO> bossOrderTaxInvoiceInfoPOS);

}
