<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.dashboard.DashboardMapper">

    <resultMap id="BaseResultMap" type="com.sgs.gpo.facade.model.dashboard.rsp.StatusTodaySummaryRsp" >
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="qty" property="qty" jdbcType="INTEGER" />
    </resultMap>

    <resultMap id="matrixProgressMap" type="com.sgs.gpo.facade.model.dashboard.rsp.MatrixTodayProgressItem" >
        <result column="engineer" property="engineer" jdbcType="VARCHAR" />
        <result column="delayCount" property="delayQty" jdbcType="INTEGER" />
        <result column="amCount" property="todayAMQty" jdbcType="INTEGER" />
        <result column="pmCount" property="todayPMQty" jdbcType="INTEGER" />
    </resultMap>


    <resultMap id="matrixPlanMap" type="com.sgs.gpo.facade.model.dashboard.rsp.MatrixFuturePlanItem" >
        <result column="engineer" property="engineer" jdbcType="VARCHAR" />
        <result column="oneDayQty" property="oneDayQty" jdbcType="INTEGER" />
        <result column="twoDayQty" property="twoDayQty" jdbcType="INTEGER" />
        <result column="threeDayQty" property="threeDayQty" jdbcType="INTEGER" />
        <result column="fourDayQty" property="fourDayQty" jdbcType="INTEGER" />
        <result column="fiveDayQty" property="fiveDayQty" jdbcType="INTEGER" />
        <result column="sixDayQty" property="sixDayQty" jdbcType="INTEGER" />
        <result column="sevenDayQty" property="sevenDayQty" jdbcType="INTEGER" />
    </resultMap>

    <select id="selectReportTodaySummary"  resultMap="BaseResultMap" >
        SELECT
            tre.ReportStatus AS status,
            count( 1 ) AS qty
        FROM
            gpn.tb_report tre
            LEFT JOIN gpn.tb_general_order_instance goi ON tre.OrderNo = goi.OrderNo
        WHERE
            tre.ReportDueDate BETWEEN #{startDate}  and #{endDate}
            AND goi.LabCode = #{labCode}
        GROUP BY tre.ReportStatus
    </select>

    <select id="selectOrderTodaySummary" resultMap="BaseResultMap" >
        SELECT
            go.OrderStatus AS status,
            count( 1 ) AS qty
        FROM
            gpo.tb_general_order go
	        LEFT JOIN gpo.tb_lab_instance li ON go.id = li.GeneralOrderID
        WHERE
            go.ExpectedOrderDueDate BETWEEN #{startDate}  and #{endDate}
            AND li.LabCode = #{labCode}
        GROUP BY go.OrderStatus
    </select>

    <select id="selectMatrixTodaySummary" resultMap="BaseResultMap" >
        SELECT
            IF (tli.PendingFlag = 0,tli.TestLineStatus,799) as status,
            count( 1 ) as qty
        FROM
            gpn.tb_test_matrix tm
            INNER join gpn.tb_test_line_instance tli on tm.TestLineInstanceID = tli.id
            INNER join gpn.tb_order_index toi on tli.orderNo = toi.order_no
            LEFT JOIN gpn.tb_testline_labsection_relationship tlr ON tlr.test_line_instance_id = tli.id
        WHERE
            tli.TestDueDate BETWEEN #{startDate}  and #{endDate}
        AND toi.host_lab_id =  (CONCAT('lab',#{labId}))
        AND toi.operation_mode &lt;&gt; 8
            <if test="labSectionBaseIdList!=null and labSectionBaseIdList.size()>0 ">
                and tlr.lab_section_base_id  in
                <foreach collection="labSectionBaseIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        GROUP BY status
    </select>


    <select id="selectEngineerDelaySummary" resultMap="matrixProgressMap" parameterType="com.sgs.gpo.facade.model.dashboard.req.DashBoardQueryRep">
        SELECT
        Engineer,
        count( distinct tm.id ) as delayCount
        FROM
        gpn.tb_test_line_instance tli
        LEFT JOIN gpn.tb_test_matrix tm ON tli.id = tm.TestLineInstanceID
        LEFT JOIN gpn.tb_testline_labsection_relationship tlr ON tlr.test_line_instance_id = tli.id
        LEFT JOIN gpn.tre_report_matrix_relationship rmrel ON rmrel.TestMatrixID = tm.id
        LEFT JOIN gpn.tb_report r ON r.id = rmrel.ReportID
        INNER JOIN gpn.tb_general_order_instance goi ON goi.id = tli.GeneralOrderInstanceID
        INNER JOIN gpo.tb_general_order o ON o.OrderNo = goi.OrderNo
        LEFT JOIN gpn.tre_job_test_line_relationship jtlrel ON jtlrel.TestLineInstanceID = tli.id
        LEFT JOIN gpn.tb_job job ON job.id = jtlrel.JobID
        WHERE
        tli.TestDueDate &lt; #{currentDate}
        AND tli.TestLineStatus in ('701','702','705')
        AND tli.PendingFlag is false
        AND goi.LabCode = #{labCode}
        AND o.OrderStatus not in ( 5, 6, 7, 10)
        -- 20230927 jira GPO2-12831 显示report pending的数据
        -- AND r.ReportStatus &lt;&gt; 209
        AND job.JobStatus &lt;&gt; 1106
        AND tli.TestLineType &amp;2 = 0
        AND o.OperationMode != 8
        <if test="engineerList!=null and engineerList.size()>0 ">
            and tli.Engineer in
            <foreach collection="engineerList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="labSectionBaseIdList!=null and labSectionBaseIdList.size()>0 ">
            and tlr.lab_section_base_id  in
            <foreach collection="labSectionBaseIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        tli.Engineer;
    </select>

    <select id="selectMatrixTodayProgress" resultMap="matrixProgressMap" parameterType="com.sgs.gpo.facade.model.dashboard.req.DashBoardQueryRep">
        SELECT
            summary.Engineer,
            sum( summary.amCount ) amCount,
            sum( summary.pmCount ) pmCount
        FROM
        (
            SELECT
                tli.Engineer,
                tli.TestDueDate,
            IF ( DATE_FORMAT( tli.TestDueDate, '%H:%i:%s' ) &gt;= '00:00:00'
                &amp;&amp;  DATE_FORMAT( tli.TestDueDate, '%H:%i:%s' ) &lt;= '12:00:00', 1, 0 ) amCount,
            IF ( DATE_FORMAT( tli.TestDueDate, '%H:%i:%s' ) &gt; '12:00:00'
                &amp;&amp;  DATE_FORMAT( tli.TestDueDate, '%H:%i:%s' ) &lt;= '23:59:59', 1,0 ) pmCount
            FROM
                gpn.tb_test_line_instance tli
                LEFT JOIN gpn.tb_test_matrix tm ON tli.id = tm.TestLineInstanceID
                LEFT JOIN gpn.tb_testline_labsection_relationship tlr ON tlr.test_line_instance_id = tli.id
                LEFT JOIN gpn.tre_report_matrix_relationship rmrel ON rmrel.TestMatrixID = tm.id
                LEFT JOIN gpn.tb_report r ON r.id = rmrel.ReportID
                LEFT JOIN gpn.tb_general_order_instance goi ON tli.GeneralOrderInstanceID = goi.id
                LEFT JOIN gpo.tb_general_order gor ON gor.OrderNo = goi.OrderNo
                LEFT JOIN gpn.tre_job_test_line_relationship jtlrel ON jtlrel.TestLineInstanceID = tli.id
                LEFT JOIN gpn.tb_job job ON job.id = jtlrel.JobID
            WHERE
                goi.LabCode = #{labCode}
                and tli.TestLineStatus in ('701','702','705')
                and tli.PendingFlag is false
                AND tli.TestDueDate BETWEEN #{startDate} AND #{endDate}
                AND gor.OrderStatus NOT IN (5, 6, 7, 10)
                -- 20230927 jira GPO2-12831 显示report pending的数据
                -- AND r.ReportStatus &lt;&gt; 209
                AND job.JobStatus &lt;&gt; 1106
                AND tli.TestLineType &amp;2 = 0
                <if test="engineerList!=null and engineerList.size()>0 ">
                    and tli.Engineer in
                    <foreach collection="engineerList" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="labSectionBaseIdList!=null and labSectionBaseIdList.size()>0 ">
                    and tlr.lab_section_base_id  in
                    <foreach collection="labSectionBaseIdList" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            group by tm.id
        ) summary
        GROUP BY summary.Engineer
    </select>

    <select id="selectMatrixFuturePlan" resultMap="matrixPlanMap" parameterType="com.sgs.gpo.facade.model.dashboard.req.DashBoardQueryRep">
        select aa.Engineer,sum(aa.oneamount) oneDayQty,sum(aa.twoamount) twoDayQty,sum(aa.threeamount) threeDayQty,
               sum(aa.fouramount) fourDayQty,sum(aa.friamount) fiveDayQty,sum(satamount) sixDayQty,sum(sunamount) sevenDayQty
        from (
                 SELECT A.Engineer,
                        IF ( DATEDIFF( A.TestDueDate,CURRENT_DATE) = 1, 1, 0 ) oneamount,
                        IF ( DATEDIFF( A.TestDueDate,CURRENT_DATE)  = 2, 1, 0 ) twoamount,
                        IF ( DATEDIFF( A.TestDueDate,CURRENT_DATE)  = 3, 1, 0 ) threeamount,
                        IF ( DATEDIFF( A.TestDueDate,CURRENT_DATE)  = 4, 1, 0 ) fouramount,
                        IF ( DATEDIFF( A.TestDueDate,CURRENT_DATE)  = 5, 1, 0 ) friamount,
                        IF ( DATEDIFF( A.TestDueDate,CURRENT_DATE)  = 6, 1, 0 ) satamount,
                        IF ( DATEDIFF( A.TestDueDate,CURRENT_DATE)  = 7, 1, 0 ) sunamount
                 FROM (
                          SELECT
                              tli.Engineer,
                              tli.TestDueDate,
                              tli.id
                          FROM
                              gpn.tb_test_line_instance tli
                                  LEFT JOIN gpn.tb_testline_labsection_relationship tlr ON tlr.test_line_instance_id = tli.id
                                  LEFT JOIN gpn.tb_test_matrix tm ON tli.id = tm.TestLineInstanceID
                                  LEFT JOIN gpn.tb_general_order_instance goi ON tli.GeneralOrderInstanceID = goi.id
                                  LEFT JOIN gpo.tb_general_order gor ON gor.OrderNo = goi.OrderNo
                                  LEFT JOIN gpn.tre_job_test_line_relationship jtlrel ON jtlrel.TestLineInstanceID = tli.id
                                  LEFT JOIN gpn.tb_job job ON job.id = jtlrel.JobID
                          WHERE
                              goi.LabCode = #{labCode}
                                AND tli.TestLineStatus in ('701','702','705')
                                AND gor.OrderStatus NOT IN (5, 6, 7)
                                and tli.PendingFlag is false
                                AND tli.TestDueDate BETWEEN #{startDate}  AND #{endDate}
                                AND job.JobStatus &lt;&gt; 1106
                                AND tli.TestLineType &amp;2 = 0
                                <if test="engineerList!=null and engineerList.size()>0 ">
                                    and tli.Engineer in
                                    <foreach collection="engineerList" item="item" open="(" separator="," close=")">
                                        #{item}
                                    </foreach>
                                </if>
                                <if test="labSectionBaseIdList!=null and labSectionBaseIdList.size()>0 ">
                                    and tlr.lab_section_base_id  in
                                    <foreach collection="labSectionBaseIdList" item="item" open="(" separator="," close=")">
                                        #{item}
                                    </foreach>
                                </if>
                        group by tm.id
                          order by  tli.TestDueDate
                      )	A
             )aa
            group by aa.Engineer
    </select>

    <select id="selectReportTaskList" parameterType="com.sgs.gpo.facade.model.dashboard.req.DashBoardQueryRep" resultType="com.sgs.gpo.facade.model.dashboard.rsp.ReportTaskDetailRsp">
        SELECT
        tre.reportNo,
        tre.reportStatus,
        tre.ActualReportNo as extReportNo,
        go.id as orderId,
        go.orderNo,
        tci.customerId as subLab,
        so.CSName as tse,
        tre.ReportDueDate as reportDueDate,
        IF
        ( DATE_FORMAT( tre.ReportDueDate, '%H:%i:%s' ) &gt;= '00:00:00' &amp;&amp; DATE_FORMAT( tre.ReportDueDate, '%H:%i:%s' ) &lt;= '12:00:00', 'AM', 'PM' ) timeSlot,
        IF
        ( rdh.delivery_type = 'DraftReport', 1, 0 ) sendDraft,
        d.delay_type_code as reportDelayType ,
        tli.id as tliId,
        tli.TestLineStatus as testLineStatus,
        tlr.lab_section_base_id as labSectionBaseId,
        tsl.activeIndicator as isPending,
        status.ObjectNo as beenApproved,
        sc.SubContractLabCode as subContractLabCode
        FROM
        gpn.tb_report tre
        JOIN gpn.tb_general_order_instance goi ON tre.OrderNo = goi.OrderNo
        JOIN gpo.tb_general_order go ON tre.OrderNo = go.OrderNo
        JOIN gpo.tb_sl_order so ON go.id = so.GeneralOrderID
        JOIN gpo.tb_test_request tr ON go.id = tr.GeneralOrderID
        left join gpo.tb_customer_instance tci on tci.GeneralOrderID = go.id and tci.customerUsage = 'subcontractFrom'
        left join gpn.tre_report_matrix_relationship trmr on trmr.ReportID = tre.id
        left join gpn.tb_test_matrix tm on tm.id = trmr.TestMatrixID
        left join gpn.tb_test_line_instance tli on tli.id = tm.TestLineInstanceID
        left join gpn.tb_sub_contract_test_line_mapping tstlm on tstlm.TestLineInstanceID = tli.id
        left join gpn.tb_sub_contract sc on sc.id = tstlm.SubContractID
        left join gpn.tre_job_test_line_relationship tjtlr on tjtlr.TestLineInstanceID = tli.id
        left join gpn.tb_job job on job.id = tjtlr.JobID
        left join gpn.tb_testline_labsection_relationship tlr on tlr.test_line_instance_id = tli.id
        LEFT JOIN gpn.tb_report_delivery_history rdh ON rdh.report_id = tre.id
        LEFT JOIN gpn.tb_delay d ON d.object_id = tre.id
        left join gpn.tb_status_log tsl on tsl.reportId = tre.id
        left join gpn.tb_status status on status.ObjectNo = tre.ReportNo and status.ObjectType = 4 and status.OldStatus = 203
        WHERE
        tre.ReportDueDate BETWEEN #{startDate}  AND #{endDate}
        AND goi.LabCode = #{labCode}
        AND tre.reportStatus = #{reportStatus}
    </select>
</mapper>