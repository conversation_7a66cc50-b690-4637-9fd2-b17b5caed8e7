package com.sgs.gpo.dbstorages.mybatis.mapper.preorder.order;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.order.BossOrderInvoicePO;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.payment.BossOrderInvoiceDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface BossOrderInvoiceMapper extends BaseMapper<BossOrderInvoicePO> {

    List<BossOrderInvoicePO> getInvoiceByOrderNo(String orderNo);
    List<BossOrderInvoiceDTO> getInvoiceInfoByOrderNo(String orderNo);
    List<BossOrderInvoiceDTO> getInvoiceInfoByOrderNos(@Param("orderNoList") Set<String> orderNoList);

    Long getNoInvoiceQuotationCount(@Param("orderNo") String orderNo);

    Boolean getDataByBossOrderAndInvoice(@Param("bossOrderNo") String bossOrderNo,@Param("invoiceNo") String invoiceNo);

    int insertBatch(@Param("bossOrderInvoiceInfoPOS")List<BossOrderInvoicePO> bossOrderInvoiceInfoPOS);

    int batchUpdatePaidAmountByInvoiceNo(@Param("bossOrderInvoiceList") List<BossOrderInvoicePO> bossOrderInvoicePOList);

    List<String> getOrderNoByInvoiceNo(@Param("invoiceNos") List<String> invoiceNos);

    int updatePaidAmountByInvoiceNoBatch(@Param("bossOrderInvoiceInfo") List<BossOrderInvoicePO> bossOrderInvoicePOList);



}
