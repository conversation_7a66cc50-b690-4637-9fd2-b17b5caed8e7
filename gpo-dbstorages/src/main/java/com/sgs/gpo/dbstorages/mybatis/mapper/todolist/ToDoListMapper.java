package com.sgs.gpo.dbstorages.mybatis.mapper.todolist;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sgs.gpo.core.constants.Constants;
import com.sgs.gpo.dbstorages.mybatis.model.todolist.ToDoListPO;
import com.sgs.gpo.facade.model.todolist.ToDoListQueryReq;
import org.apache.ibatis.annotations.Param;

@DS(Constants.DB.COMMON)
public interface ToDoListMapper extends BaseMapper<ToDoListPO> {

    /**
     * 查询 TODOList 标准对象列表
     *
     * @param toDoListQueryReq
     * @return
     */
    IPage<ToDoListPO> selectPage(@Param("page") IPage page, @Param("req") ToDoListQueryReq toDoListQueryReq);
    /**
     * 更新待办事项状态
     *
     * @param toDoListPO
     * @return
     */
    int updateStatus(ToDoListPO toDoListPO);



}
