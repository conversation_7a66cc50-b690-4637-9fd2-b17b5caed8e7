package com.sgs.gpo.dbstorages.mybatis.mapper.preorder.customer;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.gpo.dbstorages.mybatis.model.preorder.customer.CustomerPO;
import com.sgs.gpo.facade.model.customer.req.CustomerQueryReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/6 09:51
 */

public interface CustomerMapper extends BaseMapper<CustomerPO> {

    List<CustomerPO> queryByName(@Param("req") CustomerQueryReq req);

    int checkPayerIsSame(@Param("orderIds") List<String> orderIds);

}
