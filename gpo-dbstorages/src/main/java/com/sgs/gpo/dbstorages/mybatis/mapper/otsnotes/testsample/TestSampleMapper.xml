<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testsample.TestSampleMapper" >

  <sql id="baseColumns">
    ts.ID,
    ts.OrderNo,ts.SampleParentID,
    ts.SampleNo,ts.Category,ts.SampleType,ts.SampleSeq,
    ts.Description,
    ts.Composition,
    ts.Color,
    ts.SampleDescforReport,
    ts.SampleRemark,
    ts.EndUse,
    ts.Material,
    ts.OtherSampleInfo,
    ts.Applicable,
    ts.ReferDataType,
    ts.ExternalSampleId,
	tsg.id as groupId,tsg.SampleGroupID subSampleId,tsg.MainMaterialFlag mainMaterialFlag,
    ts.ActiveIndicator,ts.CreatedDate,ts.CreatedBy,ts.ModifiedDate,ts.ModifiedBy,ts.version
  </sql>


  <resultMap id="BaseResultMap" type="com.sgs.framework.model.test.testsample.TestSampleBO">
    <id column="ID" property="id" jdbcType="VARCHAR"></id>
    <result column="ID" property="testSampleInstanceId" jdbcType="VARCHAR"></result>
    <result column="SampleParentID" property="parentTestSampleId" jdbcType="VARCHAR"></result>
    <result column="SampleNo" property="testSampleNo" jdbcType="VARCHAR"></result>
    <result column="Category" property="category" jdbcType="VARCHAR"></result>
    <result column="SampleType" property="testSampleType" jdbcType="INTEGER"></result>
    <result column="SampleSeq" property="testSampleSeq" jdbcType="INTEGER"></result>
    <result column="ExternalSampleId" property="externalSampleNo" jdbcType="VARCHAR"></result>
    <association property="materialAttr" javaType="com.sgs.framework.model.test.testsample.TestSampleMaterialAttrBO">
      <result column="Description" property="materialDescription" jdbcType="VARCHAR"></result>
      <result column="OtherSampleInfo" property="materialOtherSampleInfo" jdbcType="VARCHAR"></result>
      <result column="Material" property="materialName" jdbcType="VARCHAR"></result>
      <result column="EndUse" property="materialEndUse" jdbcType="VARCHAR"></result>
      <result column="Color" property="materialColor" jdbcType="VARCHAR"></result>
      <result column="Composition" property="materialComposition" jdbcType="VARCHAR"></result>
      <result column="SampleRemark" property="materialRemark" jdbcType="VARCHAR"></result>
    </association>
    <collection property="testSampleGroupList" column="groupId"
                javaType="List" ofType="com.sgs.framework.model.test.testsample.TestSampleGroupBO">
      <result column="subSampleId" property="testSampleInstanceId" jdbcType="VARCHAR"></result>
      <result column="mainMaterialFlag" property="mainSampleFlag" jdbcType="INTEGER"></result>
    </collection>

  </resultMap>

  <select id="query" resultMap="BaseResultMap">
    select
        <include refid="baseColumns"></include>
    FROM
      tb_test_sample ts
      left join tb_general_order_instance goi on ts.OrderNo = goi.OrderNo
      left join tb_test_sample_group tsg on ts.id = tsg.SampleID
    <where>
      ts.ActiveIndicator = 1
      <if test="orderId!=null and orderId!=''">
        and goi.id =  #{orderId}
      </if>
      <if test="orderNo!=null and orderNo!=''">
        and goi.orderNo =  #{orderNo}
      </if>
      <if test="testSampleInstanceIdList != null and testSampleInstanceIdList.size() > 0">
      and ts.id in
        <foreach collection="testSampleInstanceIdList" item="id" open="(" separator="," close=")">
          #{id}
        </foreach>
      </if>
      <if test="categoryList != null and categoryList.size() > 0">
        and ts.Category in
        <foreach collection="categoryList" item="category" open="(" separator="," close=")">
          #{category}
        </foreach>
      </if>
    </where>
        order by ts.SampleSeq
  </select>

  <select id="queryListByTestLine" resultType="com.sgs.framework.model.test.testline.v2.TestLineSampleBO">
    SELECT distinct
      ts.ID testSampleInstanceId,
      ts.SampleNo testSampleNo,
      tm.ActiveIndicator matrixStatus
    FROM
      tb_test_sample ts
        INNER JOIN tb_test_matrix tm ON ts.ID = tm.TestSampleID
        AND tm.ActiveIndicator = 1
        INNER JOIN tb_test_line_instance tli ON tli.ID = tm.TestLineInstanceID
    <if test="testLineInstanceId != null and testLineInstanceId.size() > 0">
        AND tm.TestLineInstanceID in
      <foreach collection="testLineInstanceId" item="id" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
    ORDER BY
    tli.ID,
    ts.SampleSeq
  </select>

  <select id="querySampleTestLineList" resultType="com.sgs.framework.model.test.testsample.v2.TestSampleTestLineBO">
      SELECT
      tli.ID testLineInstanceId,
      tli.TestItemNo testItemNo
      FROM
      tb_test_sample ts
      INNER JOIN tb_test_matrix tm ON ts.ID = tm.TestSampleID
      AND tm.ActiveIndicator = 1
      INNER JOIN tb_test_line_instance tli ON tli.ID = tm.TestLineInstanceID
      <if test="testSampleInstanceIdList != null and testSampleInstanceIdList.size() > 0">
          and ts.id in
          <foreach collection="testSampleInstanceIdList" item="id" open="(" separator="," close=")">
              #{id}
          </foreach>
      </if>
  </select>


</mapper>