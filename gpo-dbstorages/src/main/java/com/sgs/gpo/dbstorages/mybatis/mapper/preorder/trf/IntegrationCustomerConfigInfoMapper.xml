<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.preorder.trf.IntegrationCustomerConfigInfoMapper" >
    <resultMap id="BaseResultMap" type="com.sgs.gpo.dbstorages.mybatis.model.preorder.trf.IntegrationCustomerConfigPO" >
        <id column="Id" property="id" jdbcType="INTEGER" />
        <result column="ProductLineId" property="productLineId" jdbcType="INTEGER" />
        <result column="CustomerGroupCode" property="customerGroupCode" jdbcType="VARCHAR" />
        <result column="RefSystemId" property="refSystemId" jdbcType="INTEGER" />
        <result column="RefSystemName" property="refSystemName" jdbcType="VARCHAR" />
        <result column="RefObjectType" property="refObjectType" jdbcType="INTEGER" />
        <result column="SceneType" property="sceneType" jdbcType="INTEGER" />
        <result column="BindRule" property="bindRule" jdbcType="INTEGER" />
        <result column="BindOrderRule" property="bindOrderRule" jdbcType="INTEGER" />
        <result column="SendType" property="sendType" jdbcType="INTEGER" />
        <result column="Desc" property="desc" jdbcType="VARCHAR" />
        <result column="DffFormGroupId" property="dffFormGroupId" jdbcType="VARCHAR" />
        <result column="GridFormGroupId" property="gridFormGroupId" jdbcType="VARCHAR" />
        <result column="RefSystemLabelName" property="refSystemLabelName" jdbcType="VARCHAR" />
        <result column="Status" property="status" jdbcType="INTEGER" />
        <result column="RefCheck" property="refCheck" jdbcType="INTEGER" />
        <result column="CreateDate" property="createDate" jdbcType="TIMESTAMP" />
        <result column="CustomerRules" property="customerRules" jdbcType="LONGVARCHAR" />
    </resultMap>
    <sql id="Base_Column_List" >
        Id, ProductLineId, CustomerGroupCode, RefSystemId, RefSystemName, RefObjectType,
        SceneType, BindRule, BindOrderRule, SendType, `Desc`, DffFormGroupId, GridFormGroupId,
        RefSystemLabelName, `Status`, RefCheck, CreateDate,CustomerRules
    </sql>
    <select id="queryTrfConfigList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tb_integration_customer_config
        <where>
            <if test="integrationChannel != null and integrationChannel != ''">
                and JSON_UNQUOTE(JSON_EXTRACT(CustomerRules, '$.integrationChannel')) = #{integrationChannel}
            </if>
        </where>
    </select>
</mapper>