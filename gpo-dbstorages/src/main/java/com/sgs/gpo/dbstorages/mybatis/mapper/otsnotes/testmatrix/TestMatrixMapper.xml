<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.testmatrix.TestMatrixMapper">

    <sql id="BaseColumns">
        tm.ID,tm.MatrixNo,
        tm.GeneralOrderInstanceID,tm.TestLineInstanceID,tm.TestSchemeId,tm.TestSampleID,
        tm.TestConditionGroupID,tm.ConditionID,tm.ConditionName,
        tm.MatrixGroupId,tm.MatrixStatus,
        tm.ActiveIndicator,tm.CreatedDate,tm.CreatedBy,tm.ModifiedDate,tm.ModifiedBy
    </sql>

    <resultMap id="BaseColumnMap" type="com.sgs.framework.model.test.testmatrix.TestMatrixBO">
        <id column="id" property="id" jdbcType="VARCHAR"></id>
        <result column="id" property="testMatrixId" jdbcType="VARCHAR"></result>
<!--        <result column="MatrixNo" property="matrixNo" jdbcType="VARCHAR" />-->

        <result column="TestLineInstanceID" property="testLineInstanceId" jdbcType="VARCHAR" />
        <result column="TestSampleID" property="testSampleInstanceId" jdbcType="VARCHAR" />
        <result column="TestConditionGroupID" property="testConditionGroupId" jdbcType="VARCHAR" />
        <result column="MatrixStatus" property="matrixStatus" jdbcType="INTEGER" />

<!--        <result column="active_indicator" property="activeIndicator" jdbcType="BIT" />-->
<!--        <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />-->
<!--        <result column="created_by" property="createdBy" jdbcType="VARCHAR" />-->
<!--        <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />-->
<!--        <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />-->

    </resultMap>


    <sql id="Where">
        <if test="orderIdList!=null and orderIdList.size()>0 ">
            and goi.GeneralOrderInstanceID in
            <foreach collection="orderIdList" item="orderId" open="(" separator="," close=")">
                #{orderId}
            </foreach>
        </if>
        <if test="orderNoList!=null and orderNoList.size()>0 ">
            and goi.orderNO in
            <foreach collection="orderNoList" item="orderNo" open="(" separator="," close=")">
                #{orderNo}
            </foreach>
        </if>
        <if test="testLineInstanceIdList!=null and testLineInstanceIdList.size()>0 ">
            and tli.id in
            <foreach collection="testLineInstanceIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="testItemNoList!=null and testItemNoList.size()>0 ">
            and tli.TestItemNo in
            <foreach collection="testItemNoList" item="testItemNo" open="(" separator="," close=")">
                #{testItemNo}
            </foreach>
        </if>
        <if test="testMatrixIdList!=null and testMatrixIdList.size()>0 ">
            and tm.ID in
            <foreach collection="testMatrixIdList" item="testMatrixId" open="(" separator="," close=")">
                #{testMatrixId}
            </foreach>
        </if>
        <if test="productLineCode != null and productLineCode != ''">
            AND o.BUCode = #{productLineCode}
        </if>
    </sql>

    <select id="select" resultMap="BaseColumnMap">
        SELECT
            <include refid="BaseColumns"></include>
        FROM
            tb_test_matrix tm
                LEFT JOIN tb_general_order_instance goi ON tm.GeneralOrderInstanceID = goi.id
                left join gpo.tb_general_order o on o.OrderNo = goi.OrderNo
                LEFT JOIN tb_test_sample ts ON tm.TestSampleID = ts.id
                LEFT JOIN tb_test_line_instance tli ON tm.TestLineInstanceID = tli.id
                LEFT JOIN tre_report_matrix_relationship rmr ON rmr.TestMatrixID = tm.id
                LEFT JOIN tb_report tr ON rmr.ReportID = tr.id
        <where>
            and tm.ActiveIndicator = 1
            <include refid="Where"></include>
        </where>
            GROUP BY tm.id
    </select>

    <select id="selectTestMatrixSample" resultType="com.sgs.gpo.facade.model.otsnotes.testmatrix.rsp.TestMatrixSampleRsp">
        SELECT ttm.GeneralOrderInstanceID generalOrderInstanceId, ttm.ID testMatrixId, ttm.TestLineInstanceID testLineInstanceId, ttm.TestSampleID testSampleId,
            tts.SampleNo sampleNo, tts.OrderNo orderNo
        FROM tb_test_matrix ttm
        inner join tb_test_sample tts on ttm.TestSampleID = tts.ID
        <where>
            and ttm.ActiveIndicator = 1
            and tts.ActiveIndicator = 1
            <choose>
                <when test="orderIdList != null and orderIdList.size()>0">
                    and ttm.GeneralOrderInstanceID in
                    <foreach collection="orderIdList" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                </when>
                <when test="orderNoList != null and orderNoList.size()>0">
                    and tts.OrderNo in
                    <foreach collection="orderNoList" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    and 1!=1
                </otherwise>
            </choose>
        </where>
        order by ttm.GeneralOrderInstanceID, tts.SampleNo
    </select>

</mapper>