<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.workflow.WorkflowRuleMapper" >

  <sql id="baseColumns">
    id,product_line_code as productLineCode,product_line_id as productLineId,lab_id as labId,
    process_code as processCode, current_node as currentNode,
    next_node as nextNode,  action,  `condition`, tips
  </sql>

  <select id="select" resultType="com.sgs.gpo.dbstorages.mybatis.model.workflow.WorkflowRulePO">
    select
    <include refid="baseColumns"></include>
    from tb_workflow_rule
    <where>
      <if test="productLineId!=null">
        and product_line_id =  #{productLineId}
      </if>
      <if test="labId!=null">
        and lab_id =  #{labId}
      </if>
        and process_code =  #{processCode}
      <if test="currentNode!=null and currentNode!=''">
        and current_node =  #{currentNode}
      </if>
      <if test="nextNode!=null and nextNode!=''">
        and next_node =  #{nextNode}
      </if>
      <if test="action!=null and action!=''">
        and action =  #{action}
      </if>
    </where>
  </select>

</mapper>