<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.gpo.dbstorages.mybatis.mapper.showcase.ShowcaseMapper">

    <sql id="baseColumns">
        id,user_name as userName,age,product_line_code as productLineCode,
        active_indicator as activeIndicator , created_date as createdDate,created_by as createdBy,
        modified_date as modifiedDate,modified_by as modifiedBy
    </sql>

    <select id="select" resultType="com.sgs.gpo.dbstorages.mybatis.model.showcase.ShowcasePO">
        select
        <include refid="baseColumns"></include>
        from tb_showcase
        <where>
            <if test="userName!=null and userName!=''">
                and user_name =  #{userName}
            </if>
        </where>
    </select>

</mapper>