package com.sgs.gpo.dbstorages.mybatis.mapper.otsnotes.subreport;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sgs.gpo.dbstorages.mybatis.model.otsnotes.subreport.SubReportPO;
import com.sgs.gpo.facade.model.otsnotes.subreport.req.SubReportQueryReq;

import java.util.List;

/**
 *
 */
public interface SubReportMapper extends BaseMapper<SubReportPO> {

    List<SubReportPO> select(SubReportQueryReq subReportQueryRe);

    List<SubReportPO> selectSubReportsNoMatrix(SubReportQueryReq subReportQueryRe);
}
