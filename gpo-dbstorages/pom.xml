<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>gpo-micro-service</artifactId>
        <groupId>com.sgs.gpo</groupId>
        <version>0.1.89-af</version>
    </parent>
    <version>${gpo-micro-service.version}</version>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>gpo-dbstorages</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>gpo-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sgs.gpo</groupId>
            <artifactId>gpo-facade-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sgs.framework</groupId>
            <artifactId>sgs-framework-mybatisplus</artifactId>
            <version>${sgs.framework.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-core</artifactId>
                </exclusion>
                        <exclusion>
                            <groupId>com.sgs.framework</groupId>
                            <artifactId>sgs-framework-tool</artifactId>
                        </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis.plus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sgs.framework</groupId>
            <artifactId>sgs-framework-clickhouse</artifactId>
            <version>${sgs.framework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
            <version>5.2.3</version>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <!-- src/main/java下的指定资源放行 -->
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>