package com.sgs.gpo.facade.impl.temp;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.biz.otsnotes.report.IReportBizService;
import com.sgs.gpo.biz.preorder.order.IOrderBizService;
import com.sgs.gpo.domain.service.preorder.order.IOrderDomainService;
import com.sgs.gpo.facade.model.preorder.order.req.OrderSyncTrfReq;
import com.sgs.gpo.facade.model.sci.req.GpoSciOrderToTrfReq;
import com.sgs.gpo.facade.model.sci.req.GpoSciTrfSyncReq;
import com.sgs.gpo.facade.model.sci.rsp.Order2TrfRsp;
import com.sgs.gpo.facade.model.sci.rsp.OrderSyncTrfRsp;
import com.sgs.gpo.facade.sci.SciFacade;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @title: SciFacadeImpl
 * @projectName gpo-micro-service
 * @description: TODO
 * @date 2024/1/17 23:34
 */
@Component("sciFacade")
@AllArgsConstructor
public class SciFacadeImpl implements SciFacade {
    private IOrderDomainService orderDomainService;
    private IOrderBizService orderBizService;
    private IReportBizService reportBizService;
    @Override
    public BaseResponse<Order2TrfRsp> order2Trf(GpoSciOrderToTrfReq gpoSciOrderToTrfReq) {
        return  orderDomainService.order2Trf(gpoSciOrderToTrfReq);
    }

    @Override
    public BaseResponse<Boolean> unBindSciTrf(GpoSciTrfSyncReq sciTrfSyncReq) {
        return  orderDomainService.unBindSciTrf(sciTrfSyncReq);
    }

    @Override
    public BaseResponse<List<OrderSyncTrfRsp>> orderSyncTrf(OrderSyncTrfReq orderSyncTrfReq) {
        return orderBizService.syncToSgsMart(orderSyncTrfReq);
    }
}
