package com.sgs.gpo.facade.impl.task;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.command.BaseExecutor;
import com.sgs.framework.security.context.SecurityContextHolder;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import com.sgs.gpo.domain.service.common.task.command.TaskCreateCMD;
import com.sgs.gpo.domain.service.common.task.context.TaskCreateContext;
import com.sgs.gpo.facade.model.task.req.TaskCreateReq;
import com.sgs.gpo.facade.task.ScheduleTaskFacade;
import org.springframework.stereotype.Component;

@Component("scheduleTaskFacade")
public class ScheduleTaskFacadeImpl implements ScheduleTaskFacade {

    @Override
    public BaseResponse createScheduleTask(TaskCreateReq taskCreateReq) {
        TaskCreateContext taskCreateContext = new TaskCreateContext();
        taskCreateContext.setParam(taskCreateReq);
        taskCreateContext.setUserInfo(SecurityContextHolder.getUserInfoFillSystem());
        taskCreateContext.setToken(SecurityContextHolder.getSgsToken());
        taskCreateContext.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        return BaseExecutor.start(TaskCreateCMD.class,taskCreateContext);
    }

}
