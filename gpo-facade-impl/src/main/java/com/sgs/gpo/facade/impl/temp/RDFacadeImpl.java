package com.sgs.gpo.facade.impl.temp;

import com.sgs.framework.core.base.BaseResponse;
import com.sgs.gpo.biz.intergration.rd.IRdBizService;
import com.sgs.gpo.facade.model.rd.BatchExistReq;
import com.sgs.gpo.facade.model.rd.CancelReportReq;
import com.sgs.gpo.facade.model.rd.ImportReportReq;
import com.sgs.gpo.facade.rd.RDFacade;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component("rdFacade")
public class RDFacadeImpl implements RDFacade {

    @Resource
    private IRdBizService rdBizService;

    @Override
    public BaseResponse importReportData(ImportReportReq importReportReq) {
        return rdBizService.importReport(importReportReq);
    }

    @Override
    public BaseResponse cancelReportData(CancelReportReq cancelReportReq) {
        return rdBizService.cancelReport(cancelReportReq);
    }

    @Override
    public BaseResponse batchExist(BatchExistReq batchExistReq) {
        return rdBizService.batchExist(batchExistReq);
    }
}
